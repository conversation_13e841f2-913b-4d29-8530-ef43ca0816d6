const httpStatus = require('http-status');
const { stripe } = require('../config/config');
const { Transaction, ServiceOffer, Account, Business, Referral } = require('../models');
const ApiError = require('../utils/ApiError');
const validateId = require('./shared/validateId');
const { businessEnums, currencies } = require('../config/constants');
const logger = require('../config/logger');
const config = require('../config/config');
const Order = require('../models/order.model');
const User = require('../models/user.model');
const { uploadCloudinaryFiles } = require('./file.service');
const { scheduleDeleteTemporaryCloudinaryFiles } = require('../utils/jobs/jobSchedulers');

const calculateOrderAmount = async (service) => {
  // service is an object (or array of objects) of the form: { serviceId, plan }
  const services = Array.isArray(service) ? service : [service];

  services.map((s) => validateId(s?.serviceId, 'Services'));

  const serviceOffers = await ServiceOffer.find({
    service: { $in: services.map((s) => s.serviceId) },
    name: businessEnums.requiredOffers.PRICE[0],
  }).populate({ path: 'service', populate: [{ path: 'provider' }, { path: 'media', select: 'url' }] });

  // const
  serviceOffers.forEach((serviceOffer, index) => {
    serviceOffers[index].value = serviceOffers[index].value.find((value) => value.plan === services[index].plan);
    if (!serviceOffers[index].value) {
      throw new ApiError(httpStatus.BAD_REQUEST, `Service Plan not found for service: ${services[index].plan}`);
    }
  }); // Each serviceOffer will now have only one object in the value array

  if (serviceOffers.length === 0) {
    throw new ApiError(httpStatus.NOT_FOUND, `Service or Offer not found`);
  }
  if (serviceOffers.length !== services.length) {
    throw new ApiError(httpStatus.BAD_REQUEST, `Some services do not have pricing`);
  }

  const currency = [...new Set(serviceOffers.map((serviceOffer) => String(serviceOffer.service.currency)))];
  if (currency.length > 1) {
    throw new ApiError(httpStatus.BAD_REQUEST, `Service Plans must be of the same currency`);
  }
  return {
    amount: serviceOffers.reduce((total, serviceOffer) => {
      return total + Number(serviceOffer.value[0].offer);
    }, 0),
    currency: serviceOffers[0].service.currency,
    serviceOffers: serviceOffers.map((s) => s.toJSON()),
    service: serviceOffers[0].service,
  };
};

const initializePayment = async (userParam, serviceId) => {
  // service => { serviceId, plan }
  const user = userParam;

  // Calculate the order amount. Throw NOT FOUND if order isn't found
  const { amount, currency, serviceOffers, service } = await calculateOrderAmount(serviceId); // service: { serviceId, plan }

  /* * Uncomment to disallow ordering from own service * */
  if (serviceOffers.map((s) => s.service.provider.contactPerson.toString()).includes(user._id.toString())) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'You cannot place an order for your own service.');
  }

  // Create Stripe Customer if it doesn't exist
  if (!user.stripeCustomerId) {
    const customer = await stripe.client.customers.create({
      email: user.email,
      name: `${user.firstName} ${user.lastName}`,
      metadata: {
        userId: user._id,
      },
    });
    user.stripeCustomerId = customer.id;
    await user.save();
  }

  return { user, amount, currency, service };
};

// // service should one object at a time not array.
// const createPaymentIntent = async (userParam, body) => {
//   const { user, amount, currency } = await initializePayment(userParam, body);
//   if (Number(amount) <= 0) {
//     throw new ApiError(httpStatus.BAD_REQUEST, 'Amount must be greater than 0');
//   }

//   const paymentIntent = await stripe.client.paymentIntents.create({
//     amount: Number(amount) * 100,
//     currency,
//     customer: user.stripeCustomerId,
//     metadata: {
//       userId: user._id.toString(),
//       ...body,
//     },
//   });

//   const userAccount = await Account.find({ user: user._id });
//   if (userAccount.length !== 1) {
//     throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'An error occurred while processing your request');
//   }
//   if (!userAccount.length) {
//     userAccount[0] = await Account.create({
//       user: user._id,
//       balance: 0,
//       currency,
//     });
//     user.account = userAccount[0]._id;
//     await user.save();
//   }

//   const transaction = await Transaction.create({
//     account: user.account,
//     paymentIntentId: paymentIntent.id,
//     amount: paymentIntent.amount,
//     currency: paymentIntent.currency.toUpperCase(),
//     paymentProvider: 'stripe',
//     status: paymentIntent.status,
//     clientSecret: paymentIntent.client_secret,
//     type: businessEnums.transactionTypes.CREDIT_DEBIT,
//   });

//   userAccount[0].transactions.push(transaction._id);
//   await userAccount[0].save();
//   return { paymentIntentId: paymentIntent.id, clientSecret: paymentIntent.client_secret };
// };

const createCheckoutSession = async (user, body) => {
  const { amount, currency, service } = await initializePayment(user, body);

  const price = parseInt(amount, 10);
  let serviceCharge = Math.round(0.055 * price);
  serviceCharge = price < 10000 ? serviceCharge + 300 : serviceCharge;

  if (price <= 0) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'This service cannot be purchased at the moment');
  }

  const productData = { name: service.name };

  if (service.media.length > 0) {
    const files = await uploadCloudinaryFiles(
      service.media.map((m) => m.url),
      'services',
    );
    productData.images = files.map((f) => f.url);
    await scheduleDeleteTemporaryCloudinaryFiles({ publicIds: files.map((f) => f.publicId) });
  }

  const referral = await Referral.findOne({ referredUser: user._id, service: service._id, order: { $exists: false } });

  if (referral) {
    const referralBonus = Math.round(0.03 * amount, 2);
    const referralBonusType = businessEnums.referralBonusTypes.AMOUNT;
    await Referral.findByIdAndUpdate(referral._id, { referralBonus, referralBonusType });
  }

  const session = await stripe.client.checkout.sessions.create({
    payment_method_types: ['card'],
    ui_mode: 'embedded', // 'embedded' || hosted
    customer: user.stripeCustomerId,
    line_items: [
      {
        price_data: {
          currency,
          product_data: productData,
          unit_amount: price + serviceCharge,
        },
        quantity: 1,
      },
    ],
    mode: 'payment',

    // success_url: `${config.client.baseUrl}/payment/success?session_id={CHECKOUT_SESSION_ID}`, // comment
    // cancel_url: `${config.client.baseUrl}/payment/cancel`, // comment
    return_url: `${config.client.baseUrl}/services/${service._id}/checkout/return?session_id={CHECKOUT_SESSION_ID}`, // uncomment

    // automatic_tax: { enabled: true },
    metadata: {
      serviceCharge,
      userId: user._id.toString(),
      ...body,
      referralId: referral?._id?.toString(),
    },
  });

  // return session;

  // return { sessionId: session.id, url: session.url }; // comment
  return { sessionId: session.id, clientSecret: session.client_secret }; // uncomment
};

// const getPaymentIntentStatus = async (paymentIntentId, requestingUserId) => {
//   let paymentIntent;
//   try {
//     paymentIntent = await stripe.client.paymentIntents.retrieve(paymentIntentId);
//   } catch (error) {
//     if (error.type === 'StripeInvalidRequestError') {
//       throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid or non-existent Payment Intent ID');
//     } else {
//       logger.error(`An unexpected error occurred: ${error.message}`);
//       throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error retrieving Payment Intent');
//     }
//   }
//   if (paymentIntent.status === businessEnums.transactionStatuses.SUCCEEDED) {
//     // eslint-disable-next-line global-require
//     await require('./order.service').createOrder(paymentIntent, requestingUserId.toString());
//   }
//   return paymentIntent.status;
// };

const createTransactionHelper = async (session) => {
  const existingTransaction = await Transaction.findOne({ checkoutSessionId: session.id });
  if (existingTransaction) {
    return;
  }
  const user = await User.findById(session.metadata.userId);
  const userAccount = await Account.find({ user: user._id });
  if (userAccount.length > 1) {
    logger.error('Multiple accounts found');
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'An error occurred while processing your request');
  }
  if (!userAccount.length) {
    userAccount[0] = await Account.create({
      user: user._id,
      balance: 0,
      currency: session.currency,
    });
    user.account = userAccount[0]._id;
    await user.save();
  }

  const transaction = await Transaction.create({
    account: user.account,
    checkoutSessionId: session.id,
    amount: session.amount_total - (session.metadata.serviceCharge || 0),
    currency: session.currency.toUpperCase(),
    paymentProvider: 'stripe',
    status: session.status,
    clientSecret: session.client_secret,
    type: businessEnums.transactionTypes.CREDIT_DEBIT,
    transactionFee: session.metadata.serviceCharge || 0,
    comment: businessEnums.transactionComments.ORDER_PLACEMENT,
  });

  userAccount[0].transactions.push(transaction._id);
  await userAccount[0].save();

  return transaction;
};

const createTransaction = async (checkoutSessionParam, requestingUserId) => {
  let checkoutSession;
  try {
    checkoutSession =
      checkoutSessionParam.object === 'checkout.session'
        ? checkoutSessionParam
        : await stripe.client.checkout.sessions.retrieve(checkoutSessionParam);
  } catch (error) {
    if (error.type === 'StripeInvalidRequestError') {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid or non-existent Payment Intent ID');
    } else {
      logger.error(`An unexpected error occurred: ${error.message}`);
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error retrieving Payment Intent');
    }
  }

  if (checkoutSession.metadata.userId !== requestingUserId) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You cannot perform this operation.');
  }

  let transaction = await Transaction.find({ checkoutSessionId: checkoutSession.id });
  if (transaction.length === 0) {
    transaction[0] = await createTransactionHelper(checkoutSession);
  }
  if (transaction.length > 1) {
    logger.error('Multiple transactions found for session');
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'An error occurred while processing your request');
  }
  [transaction] = transaction;
  transaction.status = checkoutSession.status;
  await transaction.save();

  if (transaction.status !== businessEnums.transactionStatuses.COMPLETE) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Payment verification failed');
  }

  return transaction;
};

const getSessionStatus = async (sessionId, requestingUserId, action) => {
  // createOrder is a boolean that determines if an order should be created after the session is complete
  // getSessionStatus is also used in premium.subscriber.service where subscription instead of order is created

  let session;
  try {
    session = await stripe.client.checkout.sessions.retrieve(sessionId);
  } catch (error) {
    if (error.type === 'StripeInvalidRequestError') {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid or non-existent session ID');
    } else {
      logger.error(`An unexpected error occurred: ${error.message}`);
      logger.error(error);
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error retrieving checkout session');
    }
  }
  if (session.status === businessEnums.transactionStatuses.COMPLETE) {
    const transaction = await createTransaction(session, requestingUserId);
    if (action === 'create-order') {
      // eslint-disable-next-line global-require
      await require('./order.service').createOrder(session, transaction);
    } else if (action === 'create-premium-subscription') {
      // eslint-disable-next-line global-require
      await require('./premium.subscriber.service').createSubscription(session, transaction);
    }
  }
  if (String(session.metadata.userId) !== String(requestingUserId)) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You are not authorized to view this session');
  }

  return { status: session.status, ...session.metadata };
};

const WebhookHandler = {
  webhook: async (req) => {
    const event = stripe.client.webhooks.constructEvent(
      req.body,
      req.headers['stripe-signature'],
      config.stripe.webhookSecret,
    );

    const handlers = {
      // 'payment_intent.succeeded': WebhookHandler._paymentIntentSucceeded,
      // 'payment_intent.payment_failed': WebhookHandler._paymentIntentFailed,
      // 'checkout.session.completed': WebhookHandler._paymentIntentSucceeded, // Waiting for payment to succeed or fail
      'checkout.session.async_payment_succeeded': WebhookHandler._checkoutSessionSucceeded, // Payment succeeded
      // 'checkout.session.async_payment_failed': WebhookHandler._paymentIntentFailed, // Payment failed
      // 'account.updated': WebhookHandler._accountUpdated,

      'charge.dispute.created': WebhookHandler._disputeCreated,
    };

    if (handlers[event.type]) {
      return handlers[event.type](event.data.object);
    }
    logger.info(`Unhandled event type: ${event.type}`);
  },

  // _paymentIntentSucceeded: async (paymentIntent) => {
  //   logger.info('Webhook fired... Creating Order');
  //   // eslint-disable-next-line global-require
  //   await require('./order.service').createOrder(paymentIntent, paymentIntent.metadata.userId);
  //   logger.info('Done creating order');
  // },

  _checkoutSessionSucceeded: async (session) => {
    logger.info('Webhook fired... Creating Order');

    await createTransaction(session);

    // eslint-disable-next-line global-require
    const order = await require('./order.service').createOrder(session, session.metadata.userId);
    return order;
  },

  _disputeCreated: async (dispute) => {
    // Implement chargeback handling
    logger.info(dispute);
  },

  // _paymentIntentFailed: async (paymentIntent) => {
  //   const transaction = await Transaction.findOne({ paymentIntentId: paymentIntent.id });
  //   if (!transaction) {
  //     throw new ApiError(httpStatus.NOT_FOUND, 'Transaction not found');
  //   }
  //   transaction.status = paymentIntent.status;
  //   await transaction.save();
  // },

  // _accountUpdated: async (account) => {
  //   const business = await Business.findOne({ stripeAccountId: account.id });

  //   if (!business) {
  //     throw new ApiError(httpStatus.NOT_FOUND, 'Business not found');
  //   }
  //   if (account.details_submitted) {
  //     business.stripeIsLinked = true;
  //     await business.save();
  //   } else {
  //     business.stripeIsLinked = false;
  //     await business.save();
  //   }
  // },
};

const getTransactionByPaymentIntentId = async (paymentIntentId) => {
  const transaction = await Transaction.findOne({ paymentIntentId });
  if (!transaction) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Transaction not found');
  }
  return transaction;
};

const verifyPayment = async (paymentIntentId) => {
  try {
    const paymentIntent = await stripe.client.paymentIntents.retrieve(paymentIntentId);
    return paymentIntent;
  } catch (error) {
    logger.error(`Error retrieving payment intent: ${error.code}`);
    logger.error('Error retrieving payment intent:', error);
  }
};

const getTransactions = async (filterParam, options, user) => {
  const filter = filterParam;
  filter.$or = [];

  filter.createdAt = { $gte: filter.startDate, $lte: filter.endDate };
  delete filter.startDate;
  delete filter.endDate;

  if (filter.provider) {
    // Request is from a business
    if (user.business?.toString() !== filter.provider) {
      throw new ApiError(httpStatus.FORBIDDEN, 'You are not authorized to view transactions for this provider');
    }

    const orders = await Order.find({ provider: filter.provider });
    filter.$or.push({ _id: { $in: orders.map((order) => order.transaction) } });
    filter.$or.push({ account: (await Account.findOne({ business: filter.provider }))?._id?.toString() });
    delete filter.provider;
  } else {
    // Request is from a user
    const account = await Account.findOne({ user: user._id });
    filter.account = account._id;
  }

  const transactions = await Transaction.paginate(filter, options);
  transactions.results.forEach((transaction, index) => {
    transactions.results[index]._doc.amount = parseFloat(transaction.amount);
  });
  return transactions;
};

const getTransactionById = async (user, transactionId) => {
  validateId(transactionId, 'Transaction');
  const transaction = await Transaction.findById(transactionId).populate('account');
  if (!transaction) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Transaction not found');
  }

  if (
    (transaction.account.user && transaction.account.user.toString() !== user._id.toString()) ||
    (transaction.account.business && transaction.account.business.toString() !== user.business?.toString())
  ) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You are not authorized to view this transaction');
  }

  transaction._doc.amount = parseFloat(transaction.amount);
  delete transaction._doc.account;
  return transaction;
};

const createStripeAccount = async (businessId) => {
  if (!businessId) {
    throw new ApiError(httpStatus.BAD_REQUEST, "You're not a business contact person");
  }
  const business = await Business.findById(businessId).populate('country');

  if (business.stripeAccountId) {
    return { account: business.stripeAccountId };
  }

  try {
    const account = await stripe.client.accounts.create({
      controller: {
        stripe_dashboard: {
          type: 'none',
        },
        fees: {
          payer: 'application',
        },
        losses: {
          payments: 'application',
        },
        requirement_collection: 'application',
      },
      capabilities: {
        transfers: { requested: true },
      },
      country: business.country?.iso2 || 'US',
      tos_acceptance: business.country?.iso2 !== 'US' ? { service_agreement: 'recipient' } : undefined,
    });

    business.stripeAccountId = account.id;
    await business.save();
    return { account: account.id };
  } catch (error) {
    if (error.code === 'country_unsupported') {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Sorry, your country of business is unsupported by Stripe at the moment');
    }
    logger.error(`Error creating Stripe account: ${error.code}`);
    logger.error(error);
    logger.error('An error occurred when calling the Stripe API to create an account');
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'An error occurred while connecting to Stripe');
  }
};

const connectStripeAccount = async (stripeAccountId, origin) => {
  const business = await Business.findOne({ stripeAccountId });
  if (!business) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Invalid Stripe Account ID');
  }
  if (business.stripeIsLinked) {
    throw new ApiError(httpStatus.ALREADY_REPORTED, 'Stripe account already linked');
  }

  logger.info(`Creating account link for ${stripeAccountId}. Origin: ${origin}`);
  try {
    const accountLink = await stripe.client.accountLinks.create({
      account: stripeAccountId,
      return_url: `${origin || config.client.baseUrl}/counselor/account-link/return/${stripeAccountId}`,
      refresh_url: `${origin || config.client.baseUrl}/counselor/account-link/refresh/${stripeAccountId}`,
      type: 'account_onboarding',
    });

    return accountLink;
  } catch (error) {
    logger.error(error.code);
    logger.error('An error occurred when calling the Stripe API to create an account link');
    logger.error(error);
    throw new ApiError(httpStatus.BAD_REQUEST, 'An error occurred while connecting to Stripe');
  }
};

const getStripeConnectStatus = async (user, stripeAccountId) => {
  const business = await Business.findOne({ stripeAccountId });

  if (!business) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Invalid Stripe Account ID');
  }

  if (business.contactPerson.toString() !== user._id.toString()) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You are not authorized to view this account');
  }

  const account = await stripe.client.accounts.retrieve(stripeAccountId);
  if (account.details_submitted) {
    business.stripeIsLinked = true;
    // if (account.business_type === 'individual') {
    //   business.stripeIndividual = {
    //     first_name: account.individual.first_name,
    //     middle_name: account.individual.middle_name,
    //     last_name: account.individual.last_name,
    //   };
    // } else if (account.business_type === 'company') {
    //   business.stripeBusiness = {
    //     name: account.company.name,
    //   };
    // }
    await business.save();
  } else {
    business.stripeIsLinked = false;
    await business.save();
  }

  return { business: business._id, stripeIsLinked: business.stripeIsLinked };
};

const getWithdrawableBalance = async (businessId) => {
  const completedOrders = await Order.find({
    provider: businessId,
    status: businessEnums.orderStatuses.COMPLETED,
  }).populate('transaction');

  const withdrawableBalance = completedOrders.reduce((acc, order) => acc + parseFloat(order.transaction.amount), 0);

  const withdrawn = (
    await Transaction.find({
      account: (await Account.findOne({ business: businessId }))._id,
      $or: [{ type: businessEnums.transactionTypes.DEBIT }, { type: businessEnums.transactionTypes.REFUND }],
    })
  ).reduce((acc, transaction) => acc + parseFloat(transaction.amount), 0);

  return withdrawableBalance - withdrawn;
};

const withdraw = async (user, amount) => {
  const business = await Business.findById(user.business);
  const withdrawableBalance = await getWithdrawableBalance(business._id);

  if (!business.stripeIsLinked) {
    throw new ApiError(httpStatus.PRECONDITION_FAILED, 'Connect your Stripe account.');
  }

  if (withdrawableBalance < amount) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Insufficient funds');
  }

  if (process.env.STRIPE_LIVE_TEST !== 'true' && user.createdAt < new Date('2024-11-19')) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'You are not eligible to withdraw funds');
  }

  const account = await Account.findOne({ business: business._id });

  logger.info(`Withdrawable: ${withdrawableBalance}, Account.withdrawableBalance${account.withdrawableBalance}`);
  if (withdrawableBalance !== account.withdrawableBalance) {
    logger.error('Account balance does not match');
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'An error occurred while processing your request');
  }

  let transfer;
  try {
    transfer = await stripe.client.transfers.create({
      amount: Math.round(amount * 0.85), // 15% service charge
      currency: 'usd',
      destination: business.stripeAccountId,
    });
  } catch (error) {
    logger.error(error);
    logger.error(error.code);
    if (error.code === 'resource_missing') {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Sorry, you cannot withdraw funds at this time. You may Contact Support.');
    }
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'An error occurred while processing your request');
  }

  const transaction = await Transaction.create({
    account: account._id,
    amount,
    currency: currencies.USD,
    paymentProvider: 'stripe',
    status: businessEnums.transactionStatuses.COMPLETE,
    type: businessEnums.transactionTypes.DEBIT,
    transferId: transfer.id,
    transactionFee: Math.round(0.15 * amount), // 15% service charge
    comment: businessEnums.transactionComments.WITHDRAWAL,
  });

  await Account.findByIdAndUpdate(account._id, {
    $inc: { withdrawableBalance: -amount, totalBalance: -amount, allTimeWithdrawal: amount },
    $addToSet: { transactions: transaction._id },
  });
};

const getReferralBonus = async ({ referrerBusiness, referrerUser }) => {
  // referrerBusiness and referrerUser are IDs
  const query = referrerBusiness ? { referrerBusiness } : { referrerUser }; // assumption is that either of them would be passed and not both
  const result = await Referral.aggregate([
    {
      $match: { hasBoughtService: true, hasWithdrawnBonus: false, ...query, bonusIsWithdrawable: true },
    },
    {
      $group: { _id: '$referralBonusType', totalBonus: { $sum: '$referralBonus' } },
    },
    {
      $project: { referralBonusType: '$_id', totalBonus: 1, _id: 0 },
    },
  ]);

  const resultTransformed = result.reduce((acc, bonus) => ({ ...acc, [bonus.referralBonusType]: bonus.totalBonus }), {});

  return resultTransformed;
};

const withdrawReferralBonus = async (user, queryParam) => {
  const query = { $or: [] };
  const bonusParams = {};

  if (queryParam.user === 'true') {
    query.$or.push({ user: user._id });
    bonusParams.referrerUser = user._id;
  }
  if (queryParam.business === 'true') {
    query.$or.push({ business: user.business });
    bonusParams.referrerBusiness = user.business;
  }
  if (query.$or.length === 0) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid query parameters');
  }

  const account = await Account.findOne(query).select('-transactions');
  if (!account) {
    // eslint-disable-next-line no-nested-ternary
    const accountType = queryParam.user === 'true' ? 'user' : queryParam.business === 'true' ? 'business' : '';
    throw new ApiError(httpStatus.NOT_FOUND, `Account for this ${accountType} not found`);
  }

  const referralBonuses = await getReferralBonus(bonusParams);
  const totalSum = referralBonuses[businessEnums.referralBonusTypes.AMOUNT] || 0;

  account.withdrawableBalance += totalSum;
  account.totalBalance += totalSum;
  await account.save();

  const referralUpdateQuery = {
    hasBoughtService: true,
    hasWithdrawnBonus: false,
    bonusIsWithdrawable: true,
    ...bonusParams,
  };
  await Referral.updateMany(referralUpdateQuery, { hasWithdrawnBonus: true });
};

const getReferralBonusBalance = async (user, queryParam, bonusParams) => {
  // get the total bonus, total withdrawn, total referrals
  // the output should be structured thus: totalBonus, totalWithdrawn, totalReferrals
  let totalWithdrawnQuery = { hasWithdrawnBonus: true };
  let totalReferralsQuery = {};

  if (queryParam.user === 'true') {
    totalReferralsQuery = { referrerUser: queryParam.user._id };
    totalWithdrawnQuery = { ...totalWithdrawnQuery, referrerUser: user._id };
  } else if (queryParam.business === 'true') {
    totalReferralsQuery = { referrerBusiness: user.business };
    totalWithdrawnQuery = { ...totalWithdrawnQuery, referrerBusiness: user.business };
  }

  const totalWithdrawnResult = await Referral.aggregate([
    { $match: totalWithdrawnQuery },
    { $group: { _id: null, totalWithdrawn: { $sum: '$referralBonus' } } },
  ]);
  const totalReferralsCount = await Referral.countDocuments(totalReferralsQuery);

  const referralBonuses = await getReferralBonus(bonusParams);

  return {
    totalBonus: referralBonuses[businessEnums.referralBonusTypes.AMOUNT] || 0,
    totalWithdrawn: totalWithdrawnResult[0]?.totalWithdrawn || 0,
    totalReferrals: totalReferralsCount || 0,
  };
};

const getAccountBalance = async (user, queryParam) => {
  const query = { $or: [] };
  const bonusParams = {};

  if (queryParam.user === 'true') {
    query.$or.push({ user: user._id });
    bonusParams.referrerUser = user._id;
  }
  if (queryParam.business === 'true') {
    query.$or.push({ business: user.business });
    bonusParams.referrerBusiness = user.business;
  }
  if (query.$or.length === 0) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid query parameters');
  }

  let result;
  if (queryParam.referralBonus === 'true') {
    result = await getReferralBonusBalance(user, queryParam, bonusParams);
  } else {
    const account = await Account.find(query).select('-transactions');
    if (account.length > 1) {
      logger.error('Multiple accounts found');
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'An error occurred while processing your request');
    }
    // eslint-disable-next-line prefer-destructuring
    result = account[0];
  }

  return result;
};

const getReferralHistory = async (user, queryParams) => {
  // the result should contain the date, bonus amount, referredUser, order, service, hasBoughService, hasWithdrawnBonus
  const filterQuery = { referralBonusType: businessEnums.referralBonusTypes.AMOUNT };

  if (queryParams.user === 'true') filterQuery.referreruser = user._id;
  else if (queryParams.business === 'true') filterQuery.referrerBusiness = user.business;

  if (queryParams.startDate || queryParams.endDate) {
    filterQuery.createdAt = {};
    if (queryParams.startDate) filterQuery.createdAt.$gte = new Date(queryParams.startDate);
    if (queryParams.endDate) filterQuery.createdAt.$lte = new Date(queryParams.endDate);
  }

  const skip = (queryParams.page - 1) * queryParams.limit;

  const referralHistory = await Referral.aggregate([
    { $match: filterQuery },
    {
      $lookup: {
        from: 'businesses',
        localField: 'referrerBusiness',
        foreignField: '_id',
        as: 'businessDetails',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'referrerUser',
        foreignField: '_id',
        as: 'userDetails',
      },
    },
    {
      $lookup: {
        from: 'orders',
        localField: 'order',
        foreignField: '_id',
        as: 'orderDetails',
      },
    },
    { $unwind: { path: '$orderDetails', preserveNullAndEmptyArrays: true } },
    {
      $addFields: {
        referrer: {
          $cond: {
            if: { $ne: ['referrerBusiness', null] },
            then: { type: 'business', details: { $arrayElemAt: ['$businessDetails', 0] } },
            else: { type: 'user', details: { $arrayElemAt: ['$userDetails', 0] } },
          },
        },
      },
    },
    {
      $project: {
        _id: 1,
        date: '$createdAt',
        bonusAmount: '$referralBonus',
        referrerUser: 1,
        order: { id: '$orderDetails._id', status: '$orderDetails.status' },
        service: 1,
        hasBoughService: 1,
        hasWithdrawnBonus: 1,
        referrer: 1,
      },
    },

    { $sort: { date: -1 } },
    { $skip: skip },
    { $limit: queryParams.limit },
  ]);

  const totalRecords = await Referral.countDocuments(filterQuery);

  return {
    currentPage: queryParams.page,
    totalPages: Math.ceil(totalRecords / queryParams.limit),
    totalRecords,
    data: referralHistory,
  };
};

const createAccountSession = async (user) => {
  if (!user.business) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'You are not a business contact person');
  }
  const business = await Business.findById(user.business);
  if (!business.stripeAccountId || !business.stripeIsLinked) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Connect your Stripe account');
  }

  try {
    const accountSession = await stripe.client.accountSessions.create({
      account: business.stripeAccountId,
      components: {
        payouts: {
          enabled: true,
        },
        account_onboarding: {
          enabled: true,
        },
        account_management: {
          enabled: true,
        },
        notification_banner: {
          enabled: true,
        },
      },
    });

    return { client_secret: accountSession.client_secret };
  } catch (error) {
    logger.error(`Create account session Error: ${error.code}`);
    logger.error(error);
    // Catch some known errors
    if (error.code === 'account_session_already_exists') {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Account session already exists');
    } else if (error.code === 'account_session_unsupported') {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Account session not supported');
    }
    if (error.code === 'resource_missing') {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Sorry, you cannot create an account session at this time');
    }

    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'An error occurred while processing your request');
  }
};

module.exports = {
  createCheckoutSession,
  getSessionStatus,
  createTransaction,
  webhook: WebhookHandler.webhook,
  getTransactionByPaymentIntentId,
  verifyPayment,
  getTransactions,
  getTransactionById,
  connectStripeAccount,
  createStripeAccount,
  getStripeConnectStatus,
  withdraw,
  getAccountBalance,
  createAccountSession,
  withdrawReferralBonus,
  getReferralHistory,
};
