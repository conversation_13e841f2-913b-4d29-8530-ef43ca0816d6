<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>

<body>
  <h1>Hello socket</h1>
  <h3>655d8517b769b1da138dc531 - <PERSON></h3>
  <button onclick="fetchData()">Click me!</button>
  <button onclick="fetchData(joinGroupUrl)">Join Group!</button>
</body>

<script src="https://cdn.socket.io/4.7.2/socket.io.min.js"
  integrity="sha384-mZLF4UVrpi/QTWPA7BjNPEnkIfRFn4ZEO3Qt/HFklTJBj/gBOV8G3HcKn4NfQblz" crossorigin="anonymous"></script>
<script>
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOnsidXNlcklkIjoiNjU1ZDg1MTdiNzY5YjFkYTEzOGRjNTMxIiwicm9sZXMiOlsic3R1ZGVudCJdfSwiaWF0IjoxNzAyMjk0MjkzLCJleHAiOjE5NzAyMjk0MjkzLCJ0eXBlIjoiYWNjZXNzIn0.c6MXpbcpifrwcutPpPsC2mqaK87Ul_Iqbea9zTSkJ5Y';

  const url = 'http://localhost:4000';
  const socket = io(url, {
    auth: {
      token: `${token}`,
    },
  });

  socket.on('connect', () => {
    isSocketConnected = true;
    console.log('Connection established');
  });

  socket.on('notification', (data) => {
    console.log('connected');
    console.log('data: ', data);
    console.log(socket.user);
  });

  const apiUrl = `${url}/v1/users/651bb5bf4eb9240327ea9d56/follow`; // Replace with your API URL
  const authToken = token; // Replace with your actual authorization token

  const joinGroupUrl = `${url}/v1/groups/65699eb8ff316eff38c4264d/join`; // Replace with your API URL

  const fetchData = async (apiUrl) => {
    try {
      const response = await fetch(apiUrl, {
        method: 'POST', // Change the method according to your API endpoint
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}` // Include the authorization token in the headers
        }
      });

      if (!response.ok) {
        throw new Error('Network response was not ok.');
      }

      const data = await response.json();
      return 3;

    } catch (error) {
      console.error('Error:', error.message);
    }
  };


</script>

</html>