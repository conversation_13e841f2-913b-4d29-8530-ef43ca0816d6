const Joi = require('joi');
const { fileSchema } = require('./file.validation');
const { postVisibilityTypes, reactionTypes } = require('../config/constants');

const createPost = {
  query: Joi.object().keys({
    group: Joi.string().valid('true', 'false').default('false'),
  }),
  body: Joi.object().keys({
    parentPostId: Joi.string(),
    text: Joi.string(),
    group: Joi.string(),
    visibility: Joi.string()
      .valid(...Object.values(postVisibilityTypes))
      .default(postVisibilityTypes.PUBLIC),
    tags: Joi.alternatives().try(Joi.string().trim(), Joi.array().items(Joi.string().trim())),
  }),
  files: Joi.array().items(fileSchema(100)),
};

const bookmarkPost = {
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
};

const getPost = {
  params: Joi.object().keys({
    id: Joi.string(),
  }),
};

const getPosts = {
  query: Joi.object().keys({
    userId: Joi.string(),
    parentPostId: Joi.string(),
    sortBy: Joi.string().valid('createdAt:asc', 'createdAt:desc', 'updatedAt:asc', 'updatedAt:desc'),
    limit: Joi.number().integer(),
    page: Joi.number().integer(),
    group: Joi.string().default(null),
    tags: Joi.alternatives().try(Joi.string().trim(), Joi.array().items(Joi.string().trim())),
    tab: Joi.string().valid('all', 'bookmarks', 'reposts', 'reactions').default('all'),
    feedsPage: Joi.string().valid('true', 'false').default('false'),
  }),
};

const deletePost = {
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
};

const updatePost = {
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
  body: Joi.object().keys({
    text: Joi.string(),
    deletedUrls: Joi.string(),
    visibility: Joi.string()
      .valid(...Object.values(postVisibilityTypes))
      .default(postVisibilityTypes.PUBLIC),
    tags: Joi.alternatives().try(Joi.string().trim(), Joi.array().items(Joi.string().trim())),
  }),
  files: Joi.array().items(fileSchema()),
};

const getUserReactions = {
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
  query: Joi.object().keys({
    tab: Joi.string().valid('reactions', 'reposts').default('reactions'),
  }),
};

const reactToPost = {
  body: Joi.object()
    .keys({
      commentId: Joi.string(),
      postId: Joi.string(),
      reactionType: Joi.string()
        .valid(...Object.values(reactionTypes))
        .required(),
    })
    .xor('commentId', 'postId'),
};

const reactToPostBySocket = Joi.object().keys({
  postId: Joi.string(),
  commentId: Joi.string(),
  reactionType: Joi.string()
    .valid(...Object.values(reactionTypes))
    .required(),
});

const createRepost = {
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
};

module.exports = {
  bookmarkPost,
  createPost,
  getPost,
  getPosts,
  updatePost,
  deletePost,
  createRepost,
  reactToPost,
  reactToPostBySocket,
  getUserReactions,
};
