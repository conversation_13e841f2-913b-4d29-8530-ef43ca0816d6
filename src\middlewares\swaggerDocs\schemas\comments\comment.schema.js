module.export = {
  type: 'object',
  properties: {
    id: {
      type: 'string',
      example: '6123456789abcdef01234567',
    },
    text: {
      type: 'string',
      example: 'This is a sample comment text.',
    },
    post: {
      type: 'string',
      example: '6123456789abcdef01234568',
    },
    parentComment: {
      type: 'string',
      example: '6123456789abcdef01234569',
    },
    user: {
      type: 'string',
      example: '6123456789abcdef0123456a',
    },
    likes: {
      type: 'array',
      items: {
        type: 'string',
        example: '6123456789abcdef0123456b',
      },
    },
    replies: {
      type: 'array',
      items: {
        type: 'string',
        example: '6123456789abcdef0123456c',
      },
    },
    media: {
      type: 'array',
      items: {
        type: 'string',
        example: '6123456789abcdef0123456d',
      },
    },
    createdAt: {
      type: 'string',
      format: 'date-time',
      example: '2024-03-16T12:00:00Z',
    },
    updatedAt: {
      type: 'string',
      format: 'date-time',
      example: '2024-03-16T12:00:00Z',
    },
  },
  required: ['text', 'post', 'user'],
};
