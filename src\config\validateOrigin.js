const config = require('./config');

let allowedRegExpOrigins = [
  // add all acceptable origins to this file
  /https:\/\/(www\.)?unyked\.com$/,
  /https:\/\/nextunyked[-a-zA-Z]*\.azurewebsites\.net$/,
];

if (config.env === 'development' || process.env.ENVIRON === 'development') {
  allowedRegExpOrigins = [
    ...allowedRegExpOrigins,
    /http:\/\/localhost:[0-9]{4,5}$/, // this regex is a superset of regexp above
    /http:\/\/127.0.0.1:[0-9]{4,5}$/, // this regex is a superset of regexp above
    /https:\/\/(www\.)?dev[0-9]*\.unyked\.com$/,
  ];
}

const validateOrigin = (origin) => {
  const validationStatus = Array.from(allowedRegExpOrigins, (regExpPattern) => regExpPattern.test(origin)).some(Bo<PERSON>an);
  return validationStatus;
};

module.exports = validateOrigin;
