const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { subscriberService: SubscriberService } = require('../services');
const { pick } = require('../utils/pick');

const getSubscribers = catchAsync(async (req, res) => {
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const filter = pick(req.query, ['subscribedFor']);
  const subscribers = await SubscriberService.getSubscribers(filter, options);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: subscribers });
});

const addSubscriber = catchAsync(async (req, res) => {
  const subscriber = await SubscriberService.addSubscriber(req.body);
  let message;
  if (subscriber.subsribedFor === 'counselor') {
    message = 'You have successfully subscribed for counselor';
  } else {
    message = 'You have successfully subscribed for newsletter';
  }

  res.status(httpStatus.CREATED).json({ status: 'SUCCESS', message, data: subscriber });
});

const unsubscribe = catchAsync(async (req, res) => {
  await SubscriberService.unsubscribe(req.body);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Unsubscribe successful' });
});

module.exports = {
  getSubscribers,
  addSubscriber,
  unsubscribe,
};
