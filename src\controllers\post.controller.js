const httpStatus = require('http-status');
const { postService: PostService } = require('../services');
const catchAsync = require('../utils/catchAsync');
const { pick } = require('../utils/pick');

const createPost = catchAsync(async (req, res) => {
  await PostService.createPost(req);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Post created successfully' });
});

const bookmarkPost = catchAsync(async (req, res) => {
  await PostService.bookmarkPost(req);
  res.status(httpStatus.CREATED).json({ status: 'SUCCESS', message: 'Post bookmarked successfully' });
});

const getPosts = catchAsync(async (req, res) => {
  const posts = await PostService.getPosts(req);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: posts, message: 'Posts retrieved successfully' });
});

const getPostById = catchAsync(async (req, res) => {
  const post = await PostService.getPostById(req?.params?.id, req.user?._id);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: post, message: 'Post retrieved successfully' });
});

const updatePost = catchAsync(async (req, res) => {
  const post = await PostService.updatePost(req);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: post, message: 'Post updated successfully' });
});

const deletePost = catchAsync(async (req, res) => {
  const postId = req?.params?.id;
  const deletedPost = await PostService.deletePost(postId);
  res.status(httpStatus.OK).json({ message: `Post with ID ${deletedPost._id} deleted`, status: 'SUCCESS' });
});

const getUserProfilePosts = catchAsync(async (req, res) => {
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const posts = await PostService.getUserProfilePosts(options);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: posts, message: 'Posts retrieved successfully' });
});

const reactToPost = catchAsync(async (req, res) => {
  await PostService.reactToPost(req.body, req.user);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Post reacted to successfully' });
});

const getUsersReactions = catchAsync(async (req, res) => {
  // to get users' likes and reposts
  const data = await PostService.getUsersReaction(req?.params?.id, req?.query?.tab);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data, message: 'Users who like post retrieved successfully' });
});

module.exports = {
  createPost,
  bookmarkPost,
  getPostById,
  getPosts,
  updatePost,
  deletePost,
  getUserProfilePosts,
  getUsersReactions,
  reactToPost,
};
