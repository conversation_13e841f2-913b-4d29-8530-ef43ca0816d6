const httpStatus = require('http-status');
const stopword = require('stopword');
const Async = require('async');
const ApiError = require('../utils/ApiError');
const ActivityLogService = require('./activity.log.service');
const { uploadAzureBlob, saveFile, deleteManyFiles } = require('./azure.file.service');
const { azureContainers, groupSettingsEnums } = require('../config/constants');
const { Group, File, Post, User, GroupSetting } = require('../models');
const { pick } = require('../utils/pick');
const { removeQuotes } = require('./shared');
const NotificationService = require('./notification.service');
const UserService = require('./user.service');
const validateId = require('./shared/validateId');
const { capitalizeEachWord } = require('../utils/stringFormatting');

// TODO
// update endpoint should have permissions

const defaultSettings = { posts: groupSettingsEnums.ALL, messages: groupSettingsEnums.ALL };

const processFileUpload = async (file, containerName) => {
  const { filename, url } = await uploadAzureBlob(file, containerName);
  const savedFile = await saveFile(url, filename, containerName);
  return savedFile._id;
};

const getRandomItemFromArray = (arr) => {
  const randomIndex = Math.floor(Math.random() * arr.length);
  return arr[randomIndex];
};

const createGroup = async (data, files) => {
  const groupBody = { ...data };
  if (data.rules) {
    groupBody.rules = removeQuotes(groupBody.rules).split('~^$');
  } else {
    delete groupBody.rules;
  }

  if (files.coverPhoto) {
    groupBody.coverPhoto = await processFileUpload(files.coverPhoto[0], azureContainers.groupsCoverImg);
  }

  if (files.profilePhoto) {
    groupBody.profilePhoto = await processFileUpload(files.profilePhoto[0], azureContainers.groupsProfileImg);
  }

  const createdGroup = await Group.create(groupBody);

  // create Group Settings
  const groupSetting = new GroupSetting({ group: createdGroup._id });
  groupSetting.users.push({
    user: createdGroup.admin,
    settings: defaultSettings,
  });
  await groupSetting.save();

  return createdGroup;
};

const addUserGroupSettings = async (groupId, userId) => {
  const groupSetting = await GroupSetting.findOne({ group: groupId });
  groupSetting.users.push({ user: userId.toString(), settings: defaultSettings });

  await groupSetting.save();
};

const joinGroup = async (groupId, user) => {
  const userId = user._id; // I changed it to this so as to use 'user' in the notifications below and not have to call the db again
  const group = await Group.findById(groupId);
  let status;

  if (!group) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Group not found');
  }

  const members = [...group.members, ...group.coAdmins, group.admin].map((member) => String(member));

  if (members.includes(String(userId))) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'You are already a member of the group');
  }

  if (group.requests.includes(userId)) {
    group.requests.pull(userId);
    status = 'CANCELLED';
    // log cancelled request
    const activityType = 'CANCEL_REQUEST';
    await ActivityLogService.logActivity(userId, activityType, group._id);
    // throw new ApiError(httpStatus.BAD_REQUEST, 'You have already requested to join the group');
  } else if (group.type === 'private') {
    await Group.findByIdAndUpdate(groupId, { $addToSet: { requests: userId } });
    // group.requests.push(userId);
    status = 'PENDING';
    // send notification to group admins of a new request
    // log join request made
    const activityType = 'JOIN_REQUEST';
    await ActivityLogService.logActivity(userId, activityType, group._id);
    const admins = [group.admin, ...group.coAdmins];
    await NotificationService.createJoinGroupNotification(user, admins, { groupId: group._id, groupName: group.name });
  } else {
    await Group.findByIdAndUpdate(groupId, { $addToSet: { members: userId } });
    // group.members.push(userId);
    await addUserGroupSettings(groupId, userId);
    status = 'SUCCESS';
    // log join success
    const activityType = 'JOIN_SUCCESS';
    await ActivityLogService.logActivity(userId, activityType, group._id);
  }

  await group.save();
  await user.save();
  return status;
};

const getGroupByIdForPublic = async (groupId) => {
  validateId(groupId, 'Group');

  const group = await Group.findById(groupId)
    .select('name description coverPhoto profilePhoto')
    .populate([
      { path: 'coverPhoto', select: 'url -_id' },
      { path: 'profilePhoto', select: 'url -_id' },
    ]);

  if (!group) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Group record not found');
  }
  Object.assign(group, { name: capitalizeEachWord(group.name) });
  return group;
};

const getGroupById = async (groupId, requestingUserId, userIsMember = false) => {
  validateId(groupId, 'Group');

  const select = userIsMember ? '-__v -enableInvitation' : '-__v -enableInvitation -posts';

  const group = await Group.findById(groupId)
    .populate([
      { path: 'coverPhoto', select: 'url -_id' },
      { path: 'profilePhoto', select: 'url -_id' },
      { path: 'admin', ...UserService.basicUserPopulate },
      { path: 'coAdmins', ...UserService.basicUserPopulate },
      {
        path: 'members',
        select: 'firstName lastName photo username _id followers following online',
        populate: { path: 'photo', select: 'url -_id' },
      },
    ])
    .select(select);
  if (!group) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Group record not found');
  }

  group._doc.isRequested = group.requests.includes(requestingUserId);
  delete group.requests;

  group._doc.allMembers = [...group.members, ...group.coAdmins, group.admin].map((member) => String(member._id));
  group._doc.isMember = group._doc.allMembers.includes(String(requestingUserId));

  return group;
};

const wordsArrayToFilterArray = (searchTextArray, key) => {
  const resultArray = [];
  searchTextArray.forEach((searchText) => {
    resultArray.push({ [key]: { $regex: searchText, $options: 'i' } });
  });

  return resultArray;
};

const getUserGroups = async (req) => {
  const filter = pick(req.query, ['type', 'name']);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const select = 'name profilePhoto cathups members requests createdAt updatedAt';

  const counts = {};
  if (req.query.filter === 'my_groups') {
    counts.requestedGroupsCount = await Group.countDocuments({ ...{ filter }, requests: req.user._id });

    filter.$and = filter.$and || [];
    filter.$and.push({
      $or: [{ members: { $in: [req.user._id] } }, { admin: req.user._id }, { coAdmins: { $in: [req.user._id] } }],
    });
  } else if (req.query.filter === 'requested_groups') {
    counts.myGroupsCount = await Group.countDocuments({
      ...filter,
      $or: [{ members: { $in: [req.user._id] } }, { admin: req.user._id }, { coAdmins: { $in: [req.user._id] } }],
    });

    filter.requests = req.user._id;
  } else if (req.query.filter === 'discover_groups') {
    counts.requestedGroupsCount = await Group.countDocuments({ ...filter, requests: req.user._id });
    counts.myGroupsCount = await Group.countDocuments({
      ...filter,
      $or: [{ members: { $in: [req.user._id] } }, { admin: req.user._id }, { coAdmins: { $in: [req.user._id] } }],
    });

    // TODO: Implement more sophisticated algorithm for discover groups (Recommended Groups)
    ['members', 'requests', 'admin', 'coAdmins'].forEach((entry) => {
      filter[entry] = { $ne: req.user._id };
    });
  }

  filter.$and = filter.$and || [];
  if (req.query.searchText) {
    const searchText = stopword.removeStopwords(req.query.searchText.split(' '));
    const filterObject = [];
    // posts: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Post' }],
    ['name', 'description'].forEach((property) => {
      // filterObject.push({ [property]: { $regex: searchText, $options: 'i' } });
      filterObject.push({ $and: wordsArrayToFilterArray(searchText, property) });
    });
    // filterObject.push({ rules: { $elemMatch: { $regex: searchText, $options: 'i' } } });
    filterObject.push({ $and: wordsArrayToFilterArray(searchText, '$elemMatch').map((item) => ({ rules: item })) });
    filter.$and.push({ $or: filterObject });
  }

  if (filter.$and.length === 0) {
    delete filter.$and;
  }

  // const userSelect = 'firstName lastName photo username _id tagLine stateOfResidence countryOfResidence followers following';

  const groups = await Group.paginate(filter, {
    ...options,
    populate: [{ path: 'profilePhoto', select: 'url -_id' }],
    select,
  });

  groups.requestedGroupsCount = groups.totalResults; // Will be overwritten if counts.requestedGroupsCount is set
  groups.myGroupsCount = groups.totalResults; // will be overwritten if counts.myGroupsCount is set

  return { ...groups, ...counts };
};

const searchGroup = async (req) => {
  const group = await Group.findById(req.params.id);
  if (!group) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Group not found');
  }

  const { searchText } = req.query.searchText;
  const posts = await Post.find({
    group: group._id,
    $or: [{ text: { $regex: searchText, $options: 'i' } }, { 'comments.text': { $regex: searchText, $options: 'i' } }],
  }).populate('comments');
  const users = await User.find({
    _id: { $in: group.members },
    $or: [
      { firstName: { $regex: searchText, $options: 'i' } },
      { lastName: { $regex: searchText, $options: 'i' } },
      { username: { $regex: searchText, $options: 'i' } },
    ],
  })
    .select('firstName lastName photo username _id')
    .populate('photo');

  return { posts, users };
};

const updateGroupById = async (req) => {
  const { files, resourceRecord, body: updateBody } = req;
  const group = resourceRecord || (await Group.findById(req?.params?.id));

  if (updateBody.rules) {
    updateBody.rules = removeQuotes(updateBody.rules).split('~^$');
  } else {
    delete updateBody.rules;
  }
  if (!group) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Group record not found');
  }

  if (files?.coverPhoto) {
    const file = await File.findById(group.coverPhoto);
    if (file) {
      file.remove();
    }
    updateBody.coverPhoto = await processFileUpload(files.coverPhoto[0], azureContainers.groupsCoverImg);
  }

  if (files?.profilePhoto) {
    const file = await File.findById(group.profilePhoto);
    if (file) {
      file.remove();
    }
    updateBody.profilePhoto = await processFileUpload(files.profilePhoto[0], azureContainers.groupsProfileImg);
  }

  Object.assign(group, updateBody);
  await group.save();
  return group;
};

const deleteGroup = async (groupId) => {
  const group = await Group.findById(groupId);
  if (!group) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Group record not found');
  }
  const fileIds = [];

  if (group.profilePhoto) {
    fileIds.push(group.profilePhoto);
  }

  if (group.coverPhoto) {
    fileIds.push(group.coverPhoto);
  }

  if (fileIds.length > 0) {
    await deleteManyFiles(fileIds);
  }

  // TODO: Delete all posts, comments, and file associated with the group
  await GroupSetting.findOneAndDelete({ group: group._id });
  await group.remove();
  return group;
};

const getGroupMembers = async (req, options) => {
  const { id: groupId } = req.params;

  const populatedGroup = await Group.findById(groupId)
    .select('members')
    .populate({
      path: 'members',
      select: 'firstName middleName lastName photo username email _id',
      options,
      populate: { path: 'photo', select: 'url -_id' },
    });

  return populatedGroup.members;
};

const getGroupRequests = async (req, options) => {
  const groupId = req?.params?.id;

  // Middleware handles permissions and whether the group exists

  const populatedGroup = await Group.findById(groupId)
    .select('requests')
    .populate({
      path: 'requests',
      select: 'firstName middleName lastName photo username email tagLine online _id',
      options,
      populate: { path: 'photo', select: 'url -_id' },
    });
  return populatedGroup.requests;
};

const handleMembershipRequest = async (req) => {
  const { id: groupId } = req.params;
  const { action, userId, all } = req.query;
  validateId(groupId, 'Group');
  if (all !== 'true') validateId(userId, 'User');

  const group = req.resourceRecord || (await Group.findById(groupId).populate('profilePhoto'));

  let recipient;
  if (userId) {
    recipient = await UserService.getUserById(userId);
    if (!recipient) {
      throw new ApiError(httpStatus.NOT_FOUND, 'User not found');
    }
  }

  const processRequest = async (requestUserId, accept = false) => {
    if (!group.members.includes(requestUserId)) {
      group.requests = group.requests.filter((formerRequestId) => String(formerRequestId) !== String(requestUserId));
      if (accept) {
        group.members.push(requestUserId);
        await addUserGroupSettings(groupId, requestUserId);
      }
      recipient = await UserService.getUserById(requestUserId);
      if (!recipient) {
        throw new ApiError(httpStatus.NOT_FOUND, 'User not found');
      }
      await NotificationService.createAcceptRejectGroupRequestNotification(
        recipient,
        { groupId, actor: userId, groupName: group?.name, profilePhoto: group?.profilePhoto },
        accept,
      );
    }
  };

  if (action === 'approve') {
    if (all === 'true') {
      const requestsProcessing = group.requests.map((requestUserId) => processRequest(requestUserId, true));
      await Promise.all(requestsProcessing);
    } else {
      if (!group.requests.includes(userId)) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'User has not requested to join the group');
      }
      await processRequest(userId, true);
      group.requests = group.requests.filter((requestUserId) => String(requestUserId) !== String(userId));
    }
  } else if (action === 'reject') {
    if (all === 'true') {
      const requestsProcessing = group.requests.map((requestUserId) => processRequest(requestUserId));
      await Promise.all(requestsProcessing);
    } else {
      if (!group.requests.includes(userId)) {
        throw new ApiError(httpStatus.NOT_FOUND, 'User did not make a request to join the group');
      }
      group.requests = group.requests.filter((requestUserId) => String(requestUserId) !== String(userId));
      await NotificationService.createAcceptRejectGroupRequestNotification(
        recipient,
        { groupId, actor: userId, groupName: group?.name, profilePhoto: group?.profilePhoto },
        false,
      );
    }
  } else if (action === 'coAdmin') {
    if (group.admin.toString() !== req.user._id.toString()) {
      // Only admin can make a member co-admin
      throw new ApiError(httpStatus.FORBIDDEN, 'Only the administrator can perform this action');
    }
    if (group.coAdmins.includes(userId)) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'User is already a co-administrator');
    }

    group.coAdmins.push(userId);
    group.members = group.members.filter((memberId) => String(memberId) !== String(userId));
    const sender = await UserService.getUserById(group.admin);
    await NotificationService.createNewCoAdminNotification(sender, recipient, {
      groupId,
      actor: userId,
      groupName: group.name,
    });
  }

  await group.save();
  return group;
};

const removeUsers = async (groupId, userIds) => {
  const group = await Group.findById(groupId);
  if (!group) {
    throw new Error('Group not found');
  }

  group.members = group.members.filter((user) => !userIds.includes(user.toString()));
  await group.save();
};

const handleCoAdmins = async (groupId, userIds, action, actor) => {
  const group = await Group.findById(groupId);
  if (!group) {
    throw new ApiError('Group not found');
  }

  if (action === 'add') {
    // Add co-admins
    const newCoAdmins = userIds.filter(
      // user is not a coAdmin/admin and user is a member
      (userId) => !group.coAdmins.includes(userId) && group.admin.toString() !== userId && group.members.includes(userId),
    );
    group.coAdmins.push(...newCoAdmins);
    group.members = group.members.filter((memberId) => !userIds.includes(memberId.toString()));

    await Async.eachOfSeries(newCoAdmins, async (userId) => {
      const sender = await UserService.getUserById(actor._id);
      const recipient = await UserService.getUserById(userId);
      await NotificationService.createNewCoAdminNotification(sender, recipient, {
        groupId,
        actor: userId,
        groupName: group.name,
      });
    });
  } else if (action === 'remove') {
    // Remove co-admins
    await Async.eachOfSeries(userIds, async (userId) => {
      if (group.coAdmins.includes(userId)) {
        group.coAdmins = group.coAdmins.filter((coAdminId) => coAdminId.toString() !== userId);
        group.members.push(userId);
      }
    });
  }

  await group.save();
};

const leaveGroup = async (req) => {
  const { userId } = req.params;
  let { newAdminId } = req.body;
  const group = req.resourceRecord; // || (await Group.findById(groupId));

  const newAdminSupplied = !!newAdminId;
  // Middleware handles permissions and whether the group exists
  const adminLeaving = group.admin.toString() === userId;
  if (adminLeaving) {
    if (!newAdminId) {
      newAdminId = getRandomItemFromArray([...group.members, ...group.coAdmins]);
      if (!newAdminId) {
        // No other group member
        await group.remove();
        return group;
      }
    }
    if (!newAdminId) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Specified new admin is not a group member');
    }
    group.admin = newAdminId;
    group.members.pull(newAdminId);
    group.coAdmins.pull(newAdminId);
  } else {
    group.coAdmins.pull(userId); // Remove user if they're a co-admin
    group.members.pull(userId); // Remove user if they're a member
    group.requests.pull(userId); // Undo request to join group
  }
  if (adminLeaving) {
    const sender = await UserService.getUserById(userId);
    const recipient = await UserService.getUserById(group.admin);
    const isExistingAdmin = !newAdminSupplied;
    await NotificationService.createNewAdminNotification(
      sender,
      recipient,
      { groupId: group._id, groupName: group.name },
      isExistingAdmin,
    );
  }

  await group.save();

  // delete user group settings
  const userGroupSettings = await GroupSetting.findOne({ group: group._id });
  userGroupSettings.users = userGroupSettings.users.filter((user) => user.user.toString() !== userId);
  await userGroupSettings.save();

  return group;
};

const getUserGroupSettings = async (userId, groupId) => {
  let groupSetting = await GroupSetting.findOne({ group: groupId });

  // create group settings for the user if it doesn't exist
  if (!groupSetting) {
    groupSetting = new GroupSetting({
      group: groupId,
      users: [{ user: userId, settings: defaultSettings }],
    });
    await groupSetting.save();
  } else {
    // If groupSetting exists but the user is not in the users array, add them
    const userInGroup = groupSetting.users.find((user) => user.user.toString() === userId.toString());
    if (!userInGroup) {
      groupSetting.users.push({ user: userId.toString(), settings: defaultSettings });
      await groupSetting.save();
    }
  }

  const userGroupSettings = groupSetting.users.find((user) => user.user.toString() === userId.toString());
  return userGroupSettings;
};

const updateGroupSettings = async (userId, groupId, updateBody) => {
  let groupSetting = await GroupSetting.findOne({ group: groupId });

  if (!groupSetting) {
    groupSetting = new GroupSetting({
      group: groupId,
      users: [
        {
          user: userId,
          settings: updateBody,
        },
      ],
    });
  } else {
    const userExists = groupSetting.users.find((user) => user.user.toString() === userId.toString());

    if (!userExists) {
      const newUserSettings = {
        user: userId,
        settings: {},
      };
      groupSetting.users.push(newUserSettings);
    } else {
      Object.assign(userExists.settings, updateBody);
    }
  }

  await groupSetting.save();
};

const sendInvite = async (groupId, sender, reqBody) => {
  const { mutuals, inviteUrl } = reqBody;
  const group = await Group.findById(groupId);
  if (mutuals?.length) {
    await Promise.all(
      mutuals.map(async (mutual) => {
        const recipient = await User.findById(mutual);
        NotificationService.createGroupInviteNotification(sender, recipient, inviteUrl, group);
      }),
    );
  }
};

module.exports = {
  createGroup,
  joinGroup,
  getGroupByIdForPublic,
  getGroupById,
  getUserGroups,
  updateGroupById,
  searchGroup,
  deleteGroup,
  getGroupMembers,
  getGroupRequests,
  handleMembershipRequest,
  removeUsers,
  handleCoAdmins,
  leaveGroup,
  updateGroupSettings,
  getUserGroupSettings,
  sendInvite,
};
