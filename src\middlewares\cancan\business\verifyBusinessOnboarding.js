const httpStatus = require('http-status');
const ApiError = require('../../../utils/ApiError');

const verifyBusinessOnboarding = () => async (req, res, next) => {
  if (req.user && req.user.pendingBusiness !== 'false') {
    return next(
      new ApiError(httpStatus.PRECONDITION_FAILED, 'You need to complete onboarding', undefined, undefined, {
        user: {
          pendingStudent: req.user.pendingStudent,
          pendingBusiness: req.user.pendingBusiness,
          registrationStatus: req.user.registrationStatus,
        },
        code: 'ONBOARDING_REQUIRED',
      }),
    );
  }
  next();
};

module.exports = verifyBusinessOnboarding;
