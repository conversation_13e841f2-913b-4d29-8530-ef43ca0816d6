const httpStatus = require('http-status');
const { ActivityLog } = require('../models');
const { activityMap } = require('../config/constants');
const ApiError = require('../utils/ApiError');

const getUserActivityLogs = async (filter, options) => {
  const activityLogs = await ActivityLog.paginate(filter, { ...options, select: '-user' });
  return activityLogs;
};

const createNewActivityLog = async (user, text) => {
  const activityLog = new ActivityLog({ user, text });
  if (!activityLog) throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error creating activity log');
  await activityLog.save();
  return activityLog;
};

const logActivity = async (userId, activityType, resourceId, postId) => {
  const { prefix, text } = activityMap[activityType] || {};

  let message;
  if (prefix && text) {
    message =
      prefix === 'C'
        ? `You ${text} a comment on this @P{${String(postId)}} @C{${String(resourceId)}}`
        : `You ${text} this @${prefix}{${resourceId}}`;
    await createNewActivityLog(userId, message);
  } else {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid activity type');
  }
};

module.exports = {
  getUserActivityLogs,
  logActivity,
};
