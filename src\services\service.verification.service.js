const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { BusinessVerification, Service, User } = require('../models');
const validateId = require('./shared/validateId');
const { basicUserPopulate } = require('./user.service');
// const { orderPopulatePaths } = require('./order.service');
const emailService = require('./email.service');
const { businessEnums } = require('../config/constants');
const { roleRights } = require('../config/roles');

// const fetchUnverifiedServices = async (filter, options) => {
//   const queryFilter = { ...filter };
//   const { displayName, name } = queryFilter;

//   if (displayName) queryFilter.displayName = { $regex: displayName, $options: 'i' };
//   if (name) queryFilter.name = { $regex: name, $options: 'i' };
//   if (!queryFilter.verificationStatus) {
//     queryFilter.verificationStatus = { $in: Object.values(businessEnums.businessVerificationStatuses) };
//   }

//   const servicesPromise = Service.paginate(queryFilter, {
//     ...options,
//     select: 'name verificationStatus _id createdAt',
//   });

//   const countPipeline = [{ $group: { _id: '$verificationStatus', count: { $sum: 1 } } }];
//   const countPromise = Service.aggregate(countPipeline);
//   const [services, counts] = await Promise.all([servicesPromise, countPromise]);

//   const statusCounts = counts.reduce((acc, { _id, count }) => {
//     acc[_id] = count;
//     return acc;
//   }, {});

//   return { services, statusCounts };
// };

const fetchServiceVerifications = async (filter, options) => {
  const queryFilter = { ...filter };
  const { displayName, serviceName: name } = queryFilter;
  delete queryFilter?.serviceName;
  delete queryFilter?.displayName;

  if (name) {
    queryFilter.name = { $regex: name, $options: 'i' };
  }

  const servicesPipeline = [
    { $match: { ...queryFilter, status: { $ne: businessEnums.serviceStatuses.DRAFT } } },
    {
      $lookup: {
        from: 'businesses',
        localField: 'provider',
        foreignField: '_id',
        as: 'businessDetails',
      },
    },
    { $unwind: '$businessDetails' },
    ...(displayName ? [{ $match: { 'businessDetails.displayName': { $regex: displayName, $options: 'i' } } }] : []),
    {
      $project: {
        _id: 1,
        name: 1,
        createdAt: 1,
        verificationStatus: 1,
        provider: {
          _id: '$businessDetails._id',
          displayName: '$businessDetails.displayName',
        },
      },
    },
    {
      $facet: {
        data: [{ $skip: (options.page - 1) * options.limit }, { $limit: options.limit }],
        total: [{ $count: 'totalRecords' }],
      },
    },
  ];

  // pagination and sorting
  if (options.sortBy) {
    const [sortField, sortOrder] = options.sortBy.split(':');
    servicesPipeline.push({ $sort: { [sortField]: sortOrder === 'asc' ? 1 : -1 } });
  }

  // if (options.page && options.limit) {
  //   servicesPipeline.push(
  //     { $skip: (options.page -1) * options.limit },
  //     { $limit: options.limit },
  //   );
  // }

  const countPipeline = [{ $group: { _id: '$verificationStatus', count: { $sum: 1 } } }];

  const [servicesResults, counts] = await Promise.all([
    Service.aggregate(servicesPipeline).exec(),
    Service.aggregate(countPipeline).exec(),
  ]);

  const services = servicesResults[0]?.data || [];
  const totalRecords = servicesResults[0]?.total[0]?.totalRecords || 0;

  const statusCounts = counts.reduce((acc, { _id, count }) => {
    if (_id !== null) {
      acc[_id] = count;
    }
    return acc;
  }, {});

  return { services, totalRecords, statusCounts };
};

const assignServiceVerifier = async (serviceId, assignerId, verifierId, parentVerification) => {
  validateId(serviceId, 'Service');
  validateId(verifierId, 'User');

  const user = await User.findById(verifierId);
  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Verifier not found');
  }

  const service = await Service.findById(serviceId);
  if (!service) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Service not found');
  }

  const userPermissions = user.roles.reduce((acc, role) => [...acc, ...roleRights.get(role)], []);
  if (!userPermissions.includes('verifyServices')) {
    throw new ApiError(httpStatus.FORBIDDEN, 'User does not have the right to verify services');
  }

  if (service.verificationStatus === businessEnums.businessVerificationStatuses.APPROVED) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Service is already verified');
  }

  const serviceVerification = await BusinessVerification.create({
    business: service.provider,
    service: service._id,
    parentVerification,
    assignee: verifierId,
    assigner: assignerId,
    assignedAt: Date.now(),
    verificationStatus: businessEnums.businessVerificationStatuses.IN_PROGRESS,
  });

  service.verifications = [...(service.verifications || []), serviceVerification._id];
  service.verificationStatus = businessEnums.businessVerificationStatuses.IN_PROGRESS;
  await service.save();
  // notify verifier
  emailService.sendServiceVerifierAssignedEmail(user, { service, serviceVerification });
};

const verifyService = async (serviceId, verifierId, data) => {
  validateId(serviceId, 'Service');
  const service = await Service.findById(serviceId).populate([
    {
      path: 'provider',
      select: '_id displayName',
      populate: [{ path: 'contactPerson', select: 'email firstName lastName' }],
    },
  ]);

  if (!service) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Service not found');
  }

  const { serviceVerification: serviceVerificationId, verificationStatus, comment } = data;
  validateId(serviceVerificationId, 'BusinessVerification');
  const serviceVerification = await BusinessVerification.findById(serviceVerificationId);
  if (!serviceVerification) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Service verification not found');
  }

  if (serviceVerification.assignee.toString() !== verifierId.toString()) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You do not have the right to verify this service');
  }

  if (service.verificationStatus === businessEnums.businessVerificationStatuses.APPROVED) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Service is already verified');
  }

  if (comment) serviceVerification.comment = comment;
  serviceVerification.reviewedAt = Date.now();

  let action;
  if (verificationStatus === businessEnums.businessVerificationStatuses.APPROVED) {
    serviceVerification.verifiedAt = Date.now();
    action = businessEnums.businessVerificationStatuses.APPROVED;
    service.status = businessEnums.serviceStatuses.PUBLISHED;
  } else if (verificationStatus === businessEnums.businessVerificationStatuses.CHANGES_REQUESTED) {
    action = businessEnums.businessVerificationStatuses.CHANGES_REQUESTED;
    service.status = businessEnums.serviceStatuses.CHANGES_REQUESTED;
  }

  serviceVerification.verificationStatus = action;
  service.verificationStatus = action;

  await Promise.all([serviceVerification.save(), service.save()]);
  // notify provider of service verification status
  emailService.sendServiceVerificationResult(service?.provider?.contactPerson, {
    service,
    status: verificationStatus,
    action,
    comment: serviceVerification.comment,
  });
};

const fetchServiceVerificationInfo = async (serviceId) => {
  validateId(serviceId, 'Service');

  const service = await Service.findById(serviceId).populate([
    { path: 'provider', select: '_id displayName email' },
    { path: 'offers' },
    { path: 'media' },
    { path: 'requirements' },
  ]);
  if (!service) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Service verification not found');
  }

  const serviceVerification = await BusinessVerification.findOne({ service: serviceId })
    .populate([
      { path: 'assignee', select: basicUserPopulate.select },
      { path: 'assigner', select: basicUserPopulate.select },
    ])
    .sort({ createdAt: -1 });

  return { service, serviceVerification };
};

const fetchParentVerification = async (serviceVerificationId) => {
  validateId(serviceVerificationId, 'ServiceVerification');

  const serviceVerification = await BusinessVerification.findById(serviceVerificationId).select(
    '-assignee -assigner -business -service',
  );

  if (!serviceVerification) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Service Verification not found');
  }

  return serviceVerification;
};

module.exports = {
  fetchServiceVerifications,
  assignServiceVerifier,
  verifyService,
  fetchServiceVerificationInfo,
  fetchParentVerification,
  // fetchUnverifiedServices,
};
