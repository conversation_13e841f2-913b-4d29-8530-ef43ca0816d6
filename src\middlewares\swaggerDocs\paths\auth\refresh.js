module.exports = {
  post: {
    summary: 'Refresh access token',
    description: 'Refresh the access token using the refresh token.',
    tags: ['Authentication'],
    parameters: [
      {
        in: 'query',
        name: 'refreshToken',
        required: true,
        schema: {
          type: 'string',
          example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        },
        description: 'Refresh token obtained during login',
      },
    ],
    responses: {
      201: {
        description: 'New access token generated successfully.',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string', example: 'SUCCESS' },
                message: { type: 'string', example: 'Token refreshed' },
                data: {
                  type: 'object',
                  properties: {
                    tokens: {
                      type: 'object',
                      properties: {
                        access: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' },
                        refresh: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' },
                      },
                    },
                    userId: { type: 'string', example: '651bb5bf4eb9240327ea9d56' },
                  },
                },
              },
            },
          },
        },
      },
      401: {
        description: 'Unauthorized: Cookie refresh token must exist.',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  example: 'Unauthorized: Cookie refresh token must exist',
                },
              },
            },
          },
        },
      },
      404: {
        description: 'User not found.',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: {
                  type: 'string',
                  example: 'FAILED',
                },
                message: {
                  type: 'string',
                  example: 'User not found',
                },
              },
            },
          },
        },
      },
    },
  },
};
