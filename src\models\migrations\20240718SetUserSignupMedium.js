/* eslint-disable no-param-reassign */
const Async = require('async');
const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const User = require('../user.model');

const loadMessagesToConversations = async () => {
  const migrated = await GlobalVariable.findOne({ name: 'Set User signup medium' });

  if (migrated) {
    return;
  }

  const users = await User.find();
  await Async.eachOfSeries(users, async (user) => {
    if (!user.password) {
      user.signupMedium = 'google';
      await user.save();
      logger.info(`Google signup updated: ${user.firstName} ${user.lastName}, email: ${user.email}`);
    } else {
      logger.info(`signupMedium: ${user.signupMedium}`);
      user.signupMedium = 'email';
      await user.save();
    }
  });
  await GlobalVariable.create({ name: 'Set User signup medium', value: 'true' });
  logger.info('User signup medium updated');
};

module.exports = loadMessagesToConversations;
