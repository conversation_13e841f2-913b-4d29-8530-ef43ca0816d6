const Joi = require('joi');
const { objectId } = require('./custom.validation');

// const { notificationTypes } = require('../services/shared/notification.handler');

const getNotifications = {
  query: Joi.object().keys({
    sortBy: Joi.string().valid('createdAt:asc', 'createdAt:desc').default('createdAt:desc'),
    limit: Joi.number().integer(),
    page: Joi.number().integer(),
    group: Joi.string().custom(objectId),
    // type: Joi.string().valid(...notificationTypes.values()),
  }),
};

const getUnreadCount = {
  query: Joi.object().keys({
    group: Joi.string().custom(objectId),
  }),
};

const toggleRead = {
  params: Joi.object().keys({
    id: Joi.alternatives().try(Joi.string().custom(objectId), Joi.string().valid('all-read', 'all-unread')),
  }),
};

const updateNotification = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId),
  }),
  body: Joi.object().keys({
    status: Joi.boolean().valid(true).required(),
  }),
};

module.exports = {
  getNotifications,
  updateNotification,
  toggleRead,
  getUnreadCount,
};
