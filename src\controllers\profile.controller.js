const httpStatus = require('http-status');
const { profileService: ProfileService } = require('../services');
const catchAsync = require('../utils/catchAsync');

const createProfile = catchAsync(async (req, res) => {
  await ProfileService.createProfile(req.user, req.body);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Profile created successfully' });
});

const getProfiles = catchAsync(async (req, res) => {
  const profiles = await ProfileService.getProfiles();
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: profiles, message: 'Profiles retrieved successfully' });
});

const getProfile = catchAsync(async (req, res) => {
  const profileId = req?.user?.profile;
  const profile = await ProfileService.getProfile(profileId);
  profile.userId = req.user._id;
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: profile, message: 'Profile retrieved successfully' });
});

// const getProfileById = catchAsync(async (req, res) => {
//   const userId = req?.params?.userId;
//   const profile = await ProfileService.getProfileById(userId);
//   // profile.userId = req.user._id;
//   res.status(httpStatus.OK).json({ status: 'SUCCESS', data: profile, message: 'Profile retrieved successfully' });
// });

const updateBasicInformation = catchAsync(async (req, res) => {
  const profileId = req.user.profile;
  await ProfileService.updateBasicInformation(profileId, req.body);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Profile basic information updated successfully' });
});

// const addEducationInfo = catchAsync(async (req, res) => {
//   await ProfileService.addEducationInfo(req?.user?.profile, req.body);
//   res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Education added successfully' });
// });

// const updateEducationInfo = catchAsync(async (req, res) => {
//   const profileId = req?.user?.profile;
//   const educationId = req?.params?.id;
//   await ProfileService.updateEducationInfo(profileId, educationId, req.body);
//   res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Education information updated successfully' });
// });

const addResourceInfo = catchAsync(async (req, res) => {
  const profileId = req?.user?.profile;
  const resourceName = req?.query?.resourceName;
  await ProfileService.addResourceInfo(profileId, resourceName, req.body);
  res.status(httpStatus.CREATED).json({ status: 'SUCCESS', message: `${resourceName} added successfully` });
});

const getResourceInfo = catchAsync(async (req, res) => {
  const resourceId = req?.params?.id;
  const resourceName = req?.query?.resourceName;
  const data = await ProfileService.getResourceInfo(resourceId, resourceName);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data, message: `${resourceName} information retrieved successfully` });
});

const updateResourceInfo = catchAsync(async (req, res) => {
  const profileId = req?.user?.profile;
  const resourceId = req?.params?.id;
  const resourceName = req?.query?.resourceName;
  await ProfileService.updateResourceInfo(profileId, resourceId, resourceName, req.body);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: `${resourceName} information updated successfully` });
});

const deleteResourceInfo = catchAsync(async (req, res) => {
  const profileId = req?.user?.profile;
  const resourceId = req?.params?.id;
  const resourceName = req?.query?.resourceName;
  await ProfileService.deleteResourceInfo(profileId, resourceId, resourceName);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: `${resourceName} information deleted successfully` });
});

module.exports = {
  createProfile,
  getProfiles,
  getProfile,
  updateBasicInformation,
  addResourceInfo,
  updateResourceInfo,
  deleteResourceInfo,
  getResourceInfo,
  // getProfileById,
};
