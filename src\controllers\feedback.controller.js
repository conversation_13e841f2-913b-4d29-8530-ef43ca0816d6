const httpStatus = require('http-status');
const { feedbackService } = require('../services');
const catchAsync = require('../utils/catchAsync');
const { pick } = require('../utils/pick');

const createFeedback = catchAsync(async (req, res) => {
  const feedback = await feedbackService.createFeedback(req.user, req.body);
  res.status(httpStatus.CREATED).json({ status: 'SUCCESS', data: feedback, message: 'Feedback created successfully' });
});

const getFeedbackById = catchAsync(async (req, res) => {
  const feedback = await feedbackService.getFeedbackById(req.params.id);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: feedback, message: 'Feedback retrieved successfully' });
});

const getFeedbacks = catchAsync(async (req, res) => {
  const filter = pick(req.query, ['rating']);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const feedbacks = await feedbackService.getFeedbacks(filter, options);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: feedbacks, message: 'Feedbacks retrieved successfully' });
});

module.exports = {
  createFeedback,
  getFeedbackById,
  getFeedbacks,
};
