const httpStatus = require('http-status');
const { transactionService } = require('../services');
const catchAsync = require('../utils/catchAsync');
const { pick } = require('../utils/pick');

const createCheckoutSession = catchAsync(async (req, res) => {
  const clientSecret = await transactionService.createCheckoutSession(req.user, req.body);
  res.status(httpStatus.CREATED).json({ data: clientSecret, status: 'SUCCESS' });
});

const getSessionStatus = catchAsync(async (req, res) => {
  const sessionStatus = await transactionService.getSessionStatus(req.query.sessionId, req.user._id, 'create-order');

  res.status(httpStatus.OK).json({ data: sessionStatus, status: 'SUCCESS' });
});

const webhook = catchAsync(async (req, res) => {
  const response = await transactionService.webhook(req);
  res.status(httpStatus.OK).json({ data: response, status: 'SUCCESS' });
});

const getTransactions = catchAsync(async (req, res) => {
  const filter = pick(req.query, ['provider', 'startDate', 'endDate']);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const transactions = await transactionService.getTransactions(filter, options, req.user);
  res.status(httpStatus.OK).json({ data: transactions, status: 'SUCCESS', message: 'Transactions retrieved successfully' });
});

const getTransaction = catchAsync(async (req, res) => {
  const transaction = await transactionService.getTransactionById(req.user, req.params.id);
  res.status(httpStatus.OK).json({ data: transaction, status: 'SUCCESS' });
});

const createStripeAccount = catchAsync(async (req, res) => {
  const account = await transactionService.createStripeAccount(req.user.business);
  res.status(httpStatus.CREATED).json({ data: account, status: 'SUCCESS' });
});

const connectStripeAccount = catchAsync(async (req, res) => {
  const accountLink = await transactionService.connectStripeAccount(req.params.account, req.headers.origin);
  res.status(httpStatus.OK).json({ data: accountLink, status: 'SUCCESS' });
});

const getStripeConnectStatus = catchAsync(async (req, res) => {
  const account = await transactionService.getStripeConnectStatus(req.user, req.params.stripeAccountId);
  res.status(httpStatus.OK).json({ data: account, status: 'SUCCESS' });
});

const withdraw = catchAsync(async (req, res) => {
  await transactionService.withdraw(req.user, req.query.amount);
  res.status(httpStatus.OK).json({ status: 'SUCCESS' });
});

const getAccountBalance = catchAsync(async (req, res) => {
  const balance = await transactionService.getAccountBalance(req.user, req.query);
  res.status(httpStatus.OK).json({ data: balance, status: 'SUCCESS' });
});

const getReferralHistory = catchAsync(async (req, res) => {
  const referralHistory = await transactionService.getReferralHistory(req.user, req.query);
  res.status(httpStatus.OK).json({ data: referralHistory, status: 'SUCCESS' });
});

const withdrawReferralBonus = catchAsync(async (req, res) => {
  await transactionService.withdrawReferralBonus(req.user, req.query);
  res.status(httpStatus.OK).json({ status: 'SUCCESS' });
});

const createAccountSession = catchAsync(async (req, res) => {
  const accountSession = await transactionService.createAccountSession(req.user);
  res.status(httpStatus.CREATED).json({ data: accountSession, status: 'SUCCESS' });
});

module.exports = {
  createCheckoutSession,
  getSessionStatus,
  webhook,
  getTransactions,
  createStripeAccount,
  connectStripeAccount,
  getStripeConnectStatus,
  withdraw,
  getAccountBalance,
  getTransaction,
  withdrawReferralBonus,
  createAccountSession,
  getReferralHistory,
};
