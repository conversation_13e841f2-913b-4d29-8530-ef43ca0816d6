module.exports = {
  post: {
    summary: 'Handle Membership Request',
    description: 'Handle membership requests such as approve, reject, or assign as co-administrator.',
    tags: ['Groups'],
    parameters: [
      {
        in: 'query',
        name: 'action',
        description: 'Action to perform (approve, reject, coAdmin)',
        required: true,
        schema: {
          type: 'string',
          enum: ['approve', 'reject', 'coAdmin'],
        },
      },
      {
        in: 'path',
        name: 'id',
        description: 'ID of the group to handle membership request for',
        required: true,
        schema: {
          type: 'string',
          example: '507f1f77bcf86cd799439011',
        },
      },
      {
        in: 'path',
        name: 'userId',
        description: 'ID of the user for whom the request is being handled',
        required: true,
        schema: {
          type: 'string',
        },
      },
    ],
    responses: {
      200: {
        description: 'Operation successful',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Operation successful',
            },
          },
        },
      },
    },
  },
};
