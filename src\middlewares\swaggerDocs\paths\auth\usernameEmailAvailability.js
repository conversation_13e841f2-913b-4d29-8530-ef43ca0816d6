module.exports = {
  get: {
    summary: 'Check Username and Email Availability',
    description: 'Checks if the provided username and/or email is available',
    tags: ['Authentication'],
    parameters: [
      {
        name: 'email',
        in: 'query',
        description: 'Email to check availability',
        schema: {
          type: 'string',
          format: 'email',
          example: '<EMAIL>',
        },
      },
      {
        name: 'username',
        in: 'query',
        description: 'Username to check availability',
        schema: {
          type: 'string',
          example: 'username123',
        },
      },
    ],
    responses: {
      200: {
        description: 'Check successful',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                data: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Check successful' },
                status: { type: 'string', example: 'SUCCESS' },
              },
            },
          },
        },
      },
    },
  },
};
