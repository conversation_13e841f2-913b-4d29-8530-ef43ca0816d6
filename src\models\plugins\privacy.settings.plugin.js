/* eslint-disable no-param-reassign */
const Setting = require('../setting.model');
const { visibilityTypes } = require('../../config/constants');
// use constants for publ, priv, and unis

// const User = require('../user.model');
// const formatSortBy = require('./formatSortBy');

// options: {userType:string e.g. user, university; userId:string, settingsId:Sring}
// if this.modelName is 'User', otherResourceIds will be 'settingsIfd' else 'userId'
// check if the req.user._id === settings.user, the fields will all be public

// get the settings using the req.user._id

const privacy = (schema) => {
  schema.statics.privacy = async function (options) {
    const { resourceId } = options;
    let { userProfileRecord } = options;
    // let otherResourceModel;
    // if (this.modelName === 'User') {
    //   otherResourceModel = User;
    // } else {
    //   otherResourceModel = Setting;
    // }
    const setting = await Setting.findOne({ user: resourceId });
    const filteredProfile = {};
    userProfileRecord = userProfileRecord.toJSON();

    Object.keys(userProfileRecord).forEach((key) => {
      if (key !== 'setting' && key !== '_id') {
        if (setting[key] !== visibilityTypes.PRIVATE && setting[key] !== visibilityTypes.UNIVERSITIES) {
          filteredProfile[key] = userProfileRecord[key];
        }
      }
      if (key === 'profile') {
        const profileData = userProfileRecord.profile;
        const filteredProfileData = {};

        Object.keys(profileData).forEach((profileKey) => {
          if (profileKey !== '_id') {
            if (setting[profileKey] === visibilityTypes.PUBLIC) {
              filteredProfileData[profileKey] = profileData[profileKey];
            }
          }
        });

        filteredProfile.profile = { ...filteredProfileData };
      }
    });
    ['createdAt', 'updatedAt', 'isEmailVerified', 'registrationStatus', 'lastLogin'].forEach(
      (field) => delete filteredProfile[field],
    );
    return filteredProfile;
  };
};

module.exports = privacy;
