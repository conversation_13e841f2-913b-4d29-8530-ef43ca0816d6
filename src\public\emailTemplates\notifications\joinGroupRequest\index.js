const config = require('../../../../config/config');
const { capitalizeFirstChar } = require('../../../../utils/stringFormatting');

const year = new Date().getFullYear();

module.exports = (sender, recipient, details) => {
  const recipientFirstName = capitalizeFirstChar(recipient?.firstName || '');
  const senderFirstName = capitalizeFirstChar(sender?.firstName || '');
  const senderLastName = capitalizeFirstChar(sender?.lastName || '');
  const senderImageUrl =
    sender?.photo?.url || 'https://res.cloudinary.com/emmii/image/upload/v1664549397/general/unisex-avatar_ksg8nn.jpg';
  const senderProfileUrl = `${config.client.baseUrl}/profile/${sender?._id}`;
  const senderTagLine = sender?.tagLine || '';

  const groupName = details?.groupName || '';
  const groupUrl = `${config.client.baseUrl}/groups/${details?.groupId}`;

  const html = `
  <!DOCTYPE html
  PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html" charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@300;400;500;600;700;800&display=swap"
    rel="stylesheet">
  <title>New Follower</title>

</head>

<body style="margin: 0;font-family: 'Plus Jakarta Sans', sans-serif;background: white;">
  <center class="wrapper" style="position: relative; height: 100%; table-layout: fixed;">
    <div class="webkit" style="width: 80%;box-shadow: 0px 10px 20px rgba(207, 203, 222, 0.42);border-radius: 10px;">
      <table class="outer"
        style="line-height: 1.65rem;border-spacing: 0;margin: 0 auto;width: 100%;color: #4a4a4a;border-radius: 10px;"
        align="center">
        <tr>
          <td style="text-align: center; border-bottom: 2px solid rgb(243 244 246);">
            <center style="background: #f6f9fc;border-radius: 10px 10px 0 0;">
              <a href="https://dev04172.unyked.com"
                style="text-decoration:none;font-weight: bolder;padding: 15px;background: #f6f9fc;">
                <img src="https://res.cloudinary.com/emmii/image/upload/v1696345178/unyked/sv1zvadt6si3uwkevzki.png"
                  width="100px" alt="" style="border: 0;padding: 15px;">
              </a>
            </center>
          </td>
        </tr>

        <tr>
          <td style="padding:0 10%;background: #f6f9fc;">
            <table style="width: 100%;border-spacing: 0;">
              <tr>
                <td style="padding: 0;">
                  <p style="margin-top: 50px;">Hello ${recipientFirstName},</p>
                  <p>${senderFirstName} ${senderLastName} requested to join ${groupName}.</p>
                </td>
              </tr>

              <tr>
                <td>
                  <center>
                    <table>
                      <tr>
                        <td>
                          <a href="${senderProfileUrl}" target="_blank">
                            <img src="${senderImageUrl}" alt=""
                              style="border-radius: 50%; width: 60px; height: 60px; margin-right: 10px;">
                          </a>
                        </td>

                        <td style="margin-left: 10px; font-size: 16px;">
                          <a href="${senderProfileUrl}" target="_blank"
                            style="text-decoration: none; color: black;  font-weight: bold; font-size:15px;">
                            <p style=" margin:0;">${senderFirstName} ${senderLastName}</p>
                          </a>
                          <a href="${senderProfileUrl}" target="_blank"
                            style="text-decoration: none; color:#4a4a4a;">
                            <small>${senderTagLine}</small>
                          </a>
                        </td>
                      </tr>

                    </table>
                  </center>
                </td>
              </tr>
              <tr>
                <td style="padding-top:20px">
                  <center>
                    <a href="${groupUrl}" target="_blank"
                    style="padding:10px; padding-left: 20px; padding-right: 20px; background: #3772ff;border: none;font-weight: bold;color: white;border-radius: 5px;font-size: 1rem;text-align: center;text-decoration: none;">View Request</a>
                  </center>
                </td>
              </tr>
            </table>
          </td>
        </tr>

        <tr>
          <td style="padding:0 10%;background: #f6f9fc;">
            <p>
              Regards, <br>
              The UnykEd Team.
            </p>
          </td>
        </tr>

        <tr>
          <td style="height: 50px;padding: 0;background: #f6f9fc;"></td>
        </tr>

        <tr>
          <td style="height: 10px;background-color: #d7e3ff;padding: 0;border-radius: 0 0 10px 10px;">
            <p style="font-size: smaller;padding: 30px 10%;">&copy;${year} UnykEd Inc.</p>
          </td>
        </tr>
      </table>
    </div>

  </center>
</body>

</html>`;
  return html;
};
