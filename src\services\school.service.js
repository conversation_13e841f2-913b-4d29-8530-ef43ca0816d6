const httpStatus = require('http-status');
const stopword = require('stopword');
const Async = require('async');
const Joi = require('joi');
const ApiError = require('../utils/ApiError');
const { azureContainers, schoolsEnums, currencies } = require('../config/constants');
const validateId = require('./shared/validateId');
const { Country, School, SchoolProgram, File, State, City } = require('../models');
const { wordsArrayToFilterArray } = require('./university.service');
const { schoolValidation } = require('../validations');
const { processFileUpload } = require('./file.service');
const logger = require('../config/logger');
const { objectId } = require('../validations/custom.validation');
const { removeUndefinedKeys } = require('../utils/pick');

const schoolImagesPopulate = [
  { path: 'logo', select: 'url' },
  { path: 'banner', select: 'url' },
];

const schoolPopulate = [
  ...schoolImagesPopulate,
  { path: 'programs' },
  { path: 'country', select: 'name' },
  { path: 'state', select: 'name' },
  { path: 'city', select: 'name' },
];

const addSchoolProgram = async (updateBody, schoolId, createdBy, findSchoolInDB = true) => {
  const body = { ...updateBody, createdBy };

  validateId(String(schoolId), 'School');
  const schoolExists = !findSchoolInDB || (await School.findById(schoolId));
  if (!schoolExists) {
    throw new ApiError(httpStatus.NOT_FOUND, 'School not found');
  }

  if (body.funding) {
    body.funding = typeof body.funding === 'string' ? [body.funding] : body.funding;
    // eslint-disable-next-line no-param-reassign
    body.funding.forEach((f) => delete f._id);
  }

  logger.info(body);
  const createdProgram = await SchoolProgram.create({ ...body, school: schoolId });
  return createdProgram;
};

const addSchoolPrograms = async (programBody, schoolId, createdBy, findSchoolInDB = false) => {
  // updateBody must have been validated
  validateId(String(schoolId), 'School');
  const schoolExists = await School.findById(schoolId);

  if (!schoolExists) {
    throw new ApiError(httpStatus.NOT_FOUND, 'School not found');
  }

  const programs = Array.isArray(programBody.programs) ? programBody.programs : [programBody.programs];
  const createdPrograms = [];
  await Promise.all(
    programs.map(async (program) => {
      const createdProgram = await addSchoolProgram({ ...program }, schoolId, createdBy, findSchoolInDB);
      await School.findByIdAndUpdate(schoolId, { $addToSet: { programs: createdProgram._id } });
      createdPrograms.push(createdProgram);
    }),
  );

  return createdPrograms;
};

const validateCountryStateCity = async (countryId, stateId, cityId) => {
  let country;
  let state;
  let city;
  if (countryId !== undefined) {
    validateId(countryId, 'country');
    country = await Country.findById(countryId).populate({ path: 'states', populate: { path: 'cities' } });
    if (!country) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Country not found');
    }
  }
  if (stateId !== undefined) {
    state = country ? country.states.find((s) => s._id.toString() === stateId) : await State.findById(stateId);
    if (!state) {
      throw new ApiError(httpStatus.NOT_FOUND, 'State not found');
    }
  }
  if (cityId !== undefined) {
    city = state ? state.cities.find((c) => c._id.toString() === cityId) : await City.findById(city);
    if (!city) {
      throw new ApiError(httpStatus.NOT_FOUND, 'City not found');
    }
  }

  return { country, state, city };
};

const createSchool = async (bodyParam, files, createdBy) => {
  // Upload files to Azure Blob Storage  //
  const logoId = await processFileUpload(files.logo[0], azureContainers.schools);
  const bannerId = await processFileUpload(files.banner[0], azureContainers.schools);
  const { programs: programsString, ...body } = bodyParam;

  const schoolExisting = await School.findOne({ name: body.name });
  if (schoolExisting) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'School with name already exists.');
  }

  await validateCountryStateCity(body.country, body.state, body.city);

  let programsArray;
  if (programsString) {
    let programs;
    try {
      programs = JSON.parse(programsString);
    } catch {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid format for programs');
    }
    const { value, error } = Joi.array().items(schoolValidation.addSchoolProgram).validate(programs);
    programsArray = value;
    if (error) {
      throw new ApiError(httpStatus.BAD_REQUEST, error.details[0].message);
    }
  }

  const schoolBody = { ...body, logo: logoId, banner: bannerId, createdBy };
  const createdSchool = await School.create(schoolBody);

  if (programsString) {
    const createdPrograms = await addSchoolPrograms({ programs: programsArray }, createdSchool._id, createdBy);
    Object.assign(createdSchool, { programs: createdPrograms });
  }

  return createdSchool;
};

const getschoolsEnums = async () => {
  return {
    currencies: Object.values(currencies),
    programTypes: Object.values(schoolsEnums.programTypes),
    fundingCategories: Object.values(schoolsEnums.fundingCategories),
    fundingTypes: Object.values(schoolsEnums.fundingTypes),
    applicationSeasons: Object.values(schoolsEnums.applicationSeasons),
  };
};

const getSchools = async (filterParam, options, sendBrief) => {
  const filter = filterParam;
  filter.$and = [];
  filter.$or = [];
  if (filter.name) {
    filter.name = { $regex: filter.name, $options: 'i' };
  }

  ['country', 'state', 'city'].forEach((key) => {
    if (filter[key]) {
      filter[key] = Array.isArray(filter[key]) ? filter[key] : [filter[key]];
      filter[key].forEach((id) => validateId(id, key));
      filter[key] = { $in: filter[key] };
    }
  });

  if (filter.searchText) {
    const searchText = stopword.removeStopwords(filter.searchText.split(' '));
    const searchTextFilter = [];
    ['name', 'abbreviation', 'about'].forEach((field) => {
      searchTextFilter.push({ $and: wordsArrayToFilterArray(searchText, field) });
    });

    filter.$or.push(...searchTextFilter);
    delete filter.searchText;
  }

  if (filter.$or.length === 0) delete filter.$or;
  if (filter.$and.length === 0) delete filter.$and;

  const select = sendBrief ? '_id name about logo programs' : '-createdAt -updatedAt';
  const populate = sendBrief
    ? [
        { path: 'logo', select: 'url' },
        { path: 'programs', select: 'programType funding applicationPeriod -_id' },
      ]
    : schoolPopulate;
  const schools = await School.paginate(filter, { ...options, populate, select });

  return schools;
};

const getSchoolById = async (id) => {
  validateId(id, 'School');
  const school = await School.findById(id).populate(schoolPopulate).select('-createdAt -updatedAt');
  if (!school) {
    throw new ApiError(httpStatus.NOT_FOUND, 'School not found');
  }
  return school;
};

const getSchoolProgram = async (id) => {
  validateId(id, 'SchoolProgram');

  const program = await SchoolProgram.findById(id).populate([
    {
      path: 'school',
      select: 'name logo banner country state city',
      populate: schoolPopulate.filter((p) => p.path !== 'programs'),
    },
  ]);
  if (!program) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Program not found');
  }
  return program;
};

const updateSchoolProgram = async (id, updateBody) => {
  validateId(id, 'SchoolProgram');
  const program = await SchoolProgram.findById(id);
  if (!program) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Program not found');
  }

  Object.assign(program, updateBody);
  const programJSON = program.toJSON();
  const { error } = Joi.object()
    .keys({ ...schoolValidation.schoolProgramSchemaObject, _id: Joi.string() })
    .unknown(true)
    .validate({
      ...program.toJSON(),
      school: String(programJSON.school),
      applicationPeriod: programJSON.applicationPeriod.map((p) => ({ ...p, _id: String(p._id) })),
      funding: programJSON.funding.map((f) => ({ ...f, _id: String(f._id) })),
    });
  if (error) {
    throw new ApiError(httpStatus.BAD_REQUEST, error.details[0].message);
  }
  await program.save();
  return program;
};

const updateSchool = async (id, updateBody, files) => {
  validateId(id, 'School');
  const school = await School.findById(id);
  if (!school) {
    throw new ApiError(httpStatus.NOT_FOUND, 'School not found');
  }

  const location = await validateCountryStateCity(
    objectId(updateBody.country),
    objectId(updateBody.state),
    objectId(updateBody.city),
  );

  removeUndefinedKeys(location);

  const body = { ...updateBody, ...location };
  ['country', 'state', 'city'].forEach((key) => delete body[key]);
  if (files?.logo) {
    await File.deleteOne({ _id: school.logo });
    const logoId = await processFileUpload(files.logo[0], azureContainers.schools);
    body.logo = logoId;
  }

  if (files?.banner) {
    await File.deleteOne({ _id: school.banner });
    const bannerId = await processFileUpload(files.banner[0], azureContainers.schools);
    body.banner = bannerId;
  }

  Object.assign(school, body);
  const schoolJSON = school.toJSON();
  const { error } = Joi.object()
    .keys({ ...schoolValidation.schoolSchemaBodyObject, programs: Joi.array().items(Joi.string()) })
    .unknown()
    .validate({
      ...schoolJSON,
      state: String(schoolJSON.state),
      city: String(schoolJSON.city),
      country: String(schoolJSON.country),
      programs: schoolJSON.programs.map((p) => String(p)),
    });
  if (error) {
    throw new ApiError(httpStatus.BAD_REQUEST, error.details[0].message);
  }
  await school.save();
  return school;
};

const deleteSchool = async (id) => {
  validateId(id, 'School');
  const school = await School.findById(id);

  if (!school) {
    throw new ApiError(httpStatus.NOT_FOUND, 'School not found');
  }

  await Async.eachOfSeries(['logo', 'banner'], async (key) => {
    const file = await File.findOne({ _id: school[key] });
    if (file) {
      await file.remove();
    }
  });

  await SchoolProgram.deleteMany({ school: id });

  await school.remove();
};

const deleteSchoolProgram = async (id) => {
  validateId(id, 'SchoolProgram');
  const program = await SchoolProgram.findById(id);
  if (!program) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Program not found');
  }

  await program.remove();
};

const getSchoolStats = async () => {
  const schoolsCount = await School.countDocuments();
  const programsCount = await SchoolProgram.countDocuments();
  const today = new Date();
  const openPrograms = await SchoolProgram.countDocuments({
    applicationPeriod: {
      $elemMatch: {
        duration: {
          $size: 2,
        },
        $and: [{ 'duration.0': { $lte: today } }, { 'duration.1': { $gte: today } }],
      },
    },
  });
  return { schoolsCount, programsCount, openPrograms, closedPrograms: programsCount - openPrograms };
};

module.exports = {
  createSchool,
  addSchoolProgram,
  addSchoolPrograms,
  getschoolsEnums,
  getSchools,
  getSchoolById,
  getSchoolProgram,
  updateSchool,
  updateSchoolProgram,
  deleteSchool,
  deleteSchoolProgram,
  getSchoolStats,
  validateCountryStateCity,
};
