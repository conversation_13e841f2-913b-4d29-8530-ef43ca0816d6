const Joi = require('joi');

const createFeedback = {
  body: Joi.object().keys({
    text: Joi.string().required(),
    rating: Joi.number().min(0).max(5).required(),
  }),
};

const getFeedback = {
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
};

const getFeedbacks = {
  query: Joi.object().keys({
    rating: Joi.number().min(0).max(5),
    sortBy: Joi.string(),
    limit: Joi.number().integer(),
    page: Joi.number().integer(),
  }),
};

module.exports = {
  createFeedback,
  getFeedback,
  getFeedbacks,
};
