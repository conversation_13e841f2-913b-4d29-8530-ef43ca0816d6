const httpStatus = require('http-status');
const { Post, Comment } = require('../../../models');
const ApiError = require('../../../utils/ApiError');
const { checkOptionalAuth } = require('../../../services/auth.service');
const validateId = require('../../../services/shared/validateId');

const canGetGroupPost =
  (isPostComment = false) =>
  async (req, res, next) => {
    const postId = req.params.id || req.body.postId || req.query.postId;

    try {
      validateId(postId, 'Post');
    } catch (error) {
      return next(error);
    }

    let post = await Post.findById(postId); // Handle for post and comment
    if (isPostComment) {
      const comment = await Comment.findById(req.params.id).populate('post');
      if (!comment) {
        return next(new ApiError(httpStatus.NOT_FOUND, 'Comment not found'));
      }
      post = comment.post;
    }

    let group;

    if (!post) return next(new ApiError(httpStatus.NOT_FOUND, 'Post not found'));

    if (post.group) {
      try {
        await checkOptionalAuth(req);
      } catch (error) {
        return next(error);
      }
      // group = await Group.findById(post.group);
      // if (
      //   !(
      //     group?.admin.toString() === req.user?._id.toString() ||
      //     group?.coAdmins.includes(req.user?._id) ||
      //     group?.members.includes(req.user?._id)
      //   )
      // ) {
      //   return next(new ApiError(httpStatus.FORBIDDEN, 'Forbidden: You are not allowed to perform this operation'));
      // }
    }

    req.group = group;
    next();
  };

module.exports = canGetGroupPost;
