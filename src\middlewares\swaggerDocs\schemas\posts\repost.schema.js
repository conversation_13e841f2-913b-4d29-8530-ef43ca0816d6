module.exports = {
  type: 'object',
  properties: {
    userId: {
      type: 'object',
      properties: {
        firstName: { type: 'string' },
        lastName: { type: 'string' },
        middleName: { type: 'string' },
        username: { type: 'string' },
        email: { type: 'string' },
        targetedCountries: {
          type: 'array',
          items: { type: 'string' },
        },
        interestedInScholarship: { type: 'boolean' },
        roles: {
          type: 'array',
          items: { type: 'string' },
        },
        isEmailVerified: { type: 'boolean' },
        registrationStatus: { type: 'string' },
        id: { type: 'string' },
      },
    },
    postId: {
      type: 'object',
      properties: {
        userId: { type: 'string' },
        text: { type: 'string' },
        media: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              _id: { type: 'string' },
              filename: { type: 'string' },
              url: { type: 'string' },
              publicId: { type: 'string' },
              __v: { type: 'integer' },
            },
          },
        },
        comments: { type: 'array' },
        reposts: { type: 'integer' },
        id: { type: 'string' },
      },
    },
    id: { type: 'string' },
  },
};
