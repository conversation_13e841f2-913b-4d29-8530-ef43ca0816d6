module.exports = {
  get: {
    summary: 'Get a list of (user) groups',
    tags: ['Groups'],
    parameters: [
      {
        in: 'query',
        name: 'user',
        schema: {
          type: 'string',
          example: '507f1f77bcf86cd799439011',
        },
        description: 'Filter groups by user',
      },
      {
        in: 'query',
        name: 'sortBy',
        schema: {
          type: 'string',
          default: 'createdAt',
        },
        description: 'Sort groups by a specific field',
      },
      {
        in: 'query',
        name: 'limit',
        schema: {
          type: 'integer',
          example: 20,
        },
        description: 'Limit the number of results per page',
      },
      {
        in: 'query',
        name: 'page',
        schema: {
          type: 'integer',
          example: 2,
        },
        description: 'Select a specific page of results',
      },
    ],
    responses: {
      200: {
        description: 'Groups retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string', example: 'SUCCESS' },
                data: {
                  type: 'object',
                  properties: {
                    results: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          _id: { type: 'string' },
                          name: { type: 'string' },
                          description: { type: 'string' },
                          admin: { type: 'string' },
                          coverPhoto: { type: 'string' },
                          profilePhoto: { type: 'string' },
                        },
                      },
                    },
                    page: { type: 'integer', example: 2 },
                    limit: { type: 'integer', example: 10 },
                    totalPages: { type: 'integer', example: 10 },
                    totalResults: { type: 'integer', example: 2 },
                  },
                },
                message: { type: 'string', example: 'Groups retrieved successfully' },
              },
            },
          },
        },
      },
    },
  },
};
