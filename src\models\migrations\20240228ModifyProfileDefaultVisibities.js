const Async = require('async');
const GlobalVariable = require('../global.variable.model');
const Setting = require('../setting.model');
const logger = require('../../config/logger');
const { userDefaultSettings } = require('../../config/constants');

const renameVolunteerToVolunteering = async () => {
  const renamed = await GlobalVariable.findOne({ name: 'update profile default visibilities' });
  if (renamed) {
    return;
  }
  const defaultSettings = userDefaultSettings();

  await Async.eachSeries(await Setting.find(), async (settingsParam) => {
    const settings = settingsParam;
    Object.keys(defaultSettings).forEach((key) => {
      settings[key] = defaultSettings[key];
    });
    await settings.save();
  });

  await GlobalVariable.create({ name: 'update profile default visibilities', value: 'true' });
  logger.info('Default profile visibilities updated for existing profiles.');
};

module.exports = renameVolunteerToVolunteering;
