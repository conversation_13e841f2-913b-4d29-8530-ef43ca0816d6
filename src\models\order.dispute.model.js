const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const { businessEnums } = require('../config/constants');

const { ObjectId } = mongoose.SchemaTypes;

const orderDisputeSchema = new mongoose.Schema(
  {
    order: { type: ObjectId, ref: 'Order', required: true },
    reporter: { type: ObjectId, ref: 'User', required: true },
    comment: { type: String },
    status: { type: String, enum: Object.values(businessEnums.orderDisputeStatuses), required: true },
    supportDocuments: [{ type: ObjectId, ref: 'File' }],
    resolution: { type: String, enum: Object.values(businessEnums.orderDisputeResolutions) },
    resolutionComment: { type: String },
  },
  {
    timestamps: true,
  },
);

orderDisputeSchema.index({ createdAt: 1 });

orderDisputeSchema.plugin(toJSON);
orderDisputeSchema.plugin(paginate);

const OrderDispute = mongoose.model('OrderDispute', orderDisputeSchema);

module.exports = OrderDispute;
