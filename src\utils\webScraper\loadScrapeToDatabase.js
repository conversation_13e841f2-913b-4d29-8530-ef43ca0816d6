const fs = require('fs').promises;
const Async = require('async');
// const path = require('path');
const { University, Country, Enum, City } = require('../../models');
const { formatToCamelCase, capitalizeFirstChar } = require('../stringFormatting');
const { getUniversityEnums } = require('../../services/university.service');
const logger = require('../../config/logger');

// Countries data source: https://github.com/dr5hn/countries-states-cities-database
// https://github.com/dr5hn/countries-states-cities-database/blob/master/countries%2Bstates%2Bcities.json

const loadDataFromFile = async (filePath) => {
  let data = await fs.readFile(filePath, 'utf8');
  try {
    data = JSON.parse(data);
  } catch (parseError) {
    logger.error('Error parsing JSON: ', parseError);
  }

  return data;
};

const loadCountriesToDatabase = async (countries) => {
  // const existingCountries = await Country.find({});
  const numberOfCities = countries.reduce(
    (acc, country) => acc + country.states.reduce((acc2, state) => acc2 + state.cities.length, 0),
    0,
  );
  logger.info(`Total Number of cities: ${numberOfCities}`);
  const start = false;
  await Async.eachSeries(countries, async (country) => {
    const existingCountry = await Country.find({ name: country.name });
    logger.info(`Existing country: ${existingCountry[0].name}, ${existingCountry?.length}`);
    if (existingCountry?.length > 1) {
      throw new Error(`Duplicate country found: ${country.name}`);
    } else if (existingCountry?.length === 1) {
      // if (country.name === 'Turkey') {
      //   start = true;
      // }
      if (!start) {
        logger.info(`Skipping country: ${country.name}`);
        return;
      }
      logger.info(`Reloading country: ${country.name}`);
      await Country.deleteOne({ name: country.name });
      const result = await City.deleteMany({ 'country.name': country.name });
      logger.info(`Deleted ${result.deletedCount} cities for country: ${country.name}`);
    }
    const createdCountry = await Country.create({
      name: country.name,
      iso3: country.iso3,
      iso2: country.iso2,
      phoneCode: country.phone_code,
      capital: country.capital,
    });
    await Async.eachSeries(country.states, async (state) => {
      const createdState = await createdCountry.states.create({
        name: state.name,
        stateCode: state.state_code,
      });
      await Async.eachSeries(state.cities, async (city) => {
        const createdCity = await City.create({
          name: city.name,
          latitude: city.latitude,
          longitude: city.longitude,
          country: { _id: createdCountry._id, name: createdCountry.name },
        });
        createdState.cities.push(createdCity._id);
      });
      createdCountry.states.push(createdState);
    });
    await createdCountry.save();
  });
};

const loadUniversitiesData = async (data) => {
  try {
    const universitiesData = data || (await loadDataFromFile('./src/utils/webScraper/universities.json'));

    (async () => {
      const schemaPaths = Object.keys(University.schema.paths);

      let skipCount = 0;
      const skipped = [];
      await Async.eachOfSeries(universitiesData, async (university, index) => {
        const crawlId = university.filter((item) => item['Crawl Id'] !== undefined)[0]['Crawl Id'];
        const existingUni = await University.findOne({ crawlId });

        if (existingUni) {
          logger.info(`University already exists: ${crawlId}, index: ${index}`);
          return;
        }

        let skip = false;
        const universityObject = university.reduce((acc, entry) => {
          const item = entry;
          // eslint-disable-next-line prefer-const
          let [obKey, obValue] = Object.entries(item)[0]; // {crawlId: '1'} => ['crawlId', '1']
          let key = formatToCamelCase(item.key || obKey);
          if (existingUni) {
            if (item.value === '' && existingUni[key]?.value !== '' && typeof item.value !== 'object') {
              logger.error(`Risky replacement skipped: ${key}, ${item.value}, ${existingUni[key].value}`);
              return acc;
            }
            if (item.value === existingUni[key]) {
              logger.info(`Replacement skipped for equal values:, ${key}, ${item.value}, ${existingUni[key]?.value}`);
              return acc;
            }
          }

          if (key === 'generalInformation') {
            key = 'tel';
          } else if (key === 'cityState') {
            key = 'addressBrief';
          } else if (key?.includes('facultyInfo')) {
            const formatKey = formatToCamelCase(item.key.split(', ')[1]);
            key = `facultyInfo${capitalizeFirstChar(formatKey)}`;
          } else if (key === 'awardsOffered') {
            item.value = item.value.split('\n');
          } else if (key === '' && item.value === '') {
            return acc;
          } else if (key.includes('thisInstitutionDidNotMeetTheDepartments9010RevenueRequirementForThe')) {
            key = 'note';
            obValue = obValue ? `${obValue}\n${item.key}` : item.key;
          }

          if ((key === 'name' && item.value === '') || (key === 'address' && item.value === '')) {
            skipped.push(crawlId);
            logger.warn(`Skipping University without name and/or address: ${crawlId}`);
            skip = true;
            skipCount += 1;
          }

          if (!skip && !(schemaPaths.includes(key) || schemaPaths.includes(`${key}.key`))) {
            logger.error(`With loading university with crawlId: ${crawlId}`);
            throw Error(`Property *${key}* not found in schema paths.`);
          }
          return {
            ...acc,
            [key]: item.value ? { key: item.key, value: item.value } : obValue,
          };
        }, {});

        if (!skip) {
          if (existingUni) {
            // TODO: Make sure address in university schema is unique
            // await University.updateOne({ _id: existingUni._id }, universityObject);
            logger.info('University Model updated');
          } else {
            await University.create(universityObject);
            await University.updateOne({ crawlId }, { $set: { awardsOffered: universityObject.awardsOffered } });
            logger.info('University entry created');
          }
        }
      });

      logger.info(`Skip Count: ${skipCount}`);
      logger.info(`Skipped: ${skipped}`);
      const universitiesEnum = await getUniversityEnums();

      await Enum.deleteOne({ enumFor: 'university' });
      await Enum.create({ ...universitiesEnum, enumFor: 'university' });

      const existingCountries = await Country.find({});
      const countries = await loadDataFromFile('./src/utils/webScraper/countries+states+cities.json');

      if (existingCountries.length < countries.length) {
        await loadCountriesToDatabase(countries);
        logger.info('Countries data loaded');
      }
    })();
  } catch (err) {
    logger.error(`Error loading file, ${err}`);
  }
};
module.exports = loadUniversitiesData;
