module.exports = {
  post: {
    tags: ['Authentication'],
    summary: 'Send verification email',
    description:
      "Send an email to the user the verify their email. This email is sent automatically when a user signs up. But the endpoint allows the user to request for the email to be sent again in case they didn't receive the previous email for some reason.",
    requestBody: {
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              email: { type: 'string', format: 'email', example: '<EMAIL>' },
            },
            required: ['email'],
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Account verification email sent successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: {
                  type: 'string',
                  example: 'SUCCESS',
                },
                message: {
                  type: 'string',
                  example: 'Kindly check your email to complete account verification.',
                },
              },
            },
          },
        },
      },
      208: {
        description: 'Tells that email is already verified.',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: {
                  type: 'string',
                  example: 'FAILED',
                },
                message: {
                  type: 'string',
                  example: 'Email is already verified.',
                },
              },
            },
          },
        },
      },
      401: {
        description: 'Provided token is invalid.',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: {
                  type: 'string',
                  example: 'FAILED',
                },
                message: {
                  type: 'string',
                  example: 'Please authenticate.',
                },
              },
            },
          },
        },
      },
    },
  },
};
