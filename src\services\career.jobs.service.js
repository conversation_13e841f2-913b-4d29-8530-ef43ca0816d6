const httpStatus = require('http-status');
const stopword = require('stopword');
const logger = require('../config/logger');
const ApiError = require('../utils/ApiError');
const validateId = require('./shared/validateId');
const emailService = require('./email.service');
const { validateJobSocialLinks } = require('../validations/career.jobs.validation');
const { CareerJobs, JobApplication, User } = require('../models');
const { careerJobsEnums, currencies } = require('../config/constants');
const { processFileUpload } = require('./azure.file.service');
const { azureContainers } = require('../config/constants');

const addCareerJob = async (requestBody) => {
  const careerJobData = { ...requestBody };
  if (careerJobData.publishStatus === careerJobsEnums.careerJobPublishStatus.PUBLISHED) {
    careerJobData.publishedDate = new Date();
  }

  if (!careerJobData.applicationEndDate) {
    careerJobData.rollingApplication = true;
  }

  if (careerJobData.jobCategories) {
    careerJobData.jobCategories = Array.isArray(careerJobData.jobCategories)
      ? careerJobData.jobCategories
      : [careerJobData.jobCategories];
  }

  const careerJob = await CareerJobs.create(careerJobData);
  if (!careerJob) {
    logger.info(`Unable to create career job posting`);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, `Unable to create career job posting`);
  }
  return careerJob;
};

const getCareerJobs = async (filterParam, options, isAdmin = false) => {
  const filter = { ...filterParam };

  if (filter.title) {
    const title = stopword.removeStopwords(filter.title.split(' '));
    filter.title = { $regex: title.join('|'), $options: 'i' };
  }

  if (filter.searchText) {
    const searchText = stopword.removeStopwords(filter.searchText.split(' '));
    filter.$or = [{ title: { $regex: searchText.join('|'), $options: 'i' } }];
  }

  if (filter.publishStatus === careerJobsEnums.careerJobPublishStatus.ALL_JOBS) {
    filter.publishStatus = {
      $in: [careerJobsEnums.careerJobPublishStatus.PUBLISHED, careerJobsEnums.careerJobPublishStatus.UNPUBLISHED],
    };
  } else if (filter.publishStatus === careerJobsEnums.careerJobPublishStatus.UNPUBLISHED) {
    filter.publishStatus = careerJobsEnums.careerJobPublishStatus.UNPUBLISHED;
  } else {
    filter.publishStatus = careerJobsEnums.careerJobPublishStatus.PUBLISHED;
  }
  if (filter.jobCategory) {
    filter.jobCategories = Array.isArray(filter.jobCategory) ? { $in: filter.jobCategory } : { $in: [filter.jobCategory] };
  }

  if (!isAdmin) {
    const today = new Date();
    // ensure that the applicationEndDate is gretater than today or rollingApplication is true

    filter.$or = [{ applicationEndDate: { $gte: today } }, { rollingApplication: true }];
    filter.publishStatus = careerJobsEnums.careerJobPublishStatus.PUBLISHED;
  }

  const selectFields = !isAdmin
    ? `title location applicationEndDate jobType jobMode publishedDate publishStatus createdAt updatedAt jobCategories rollingApplication`
    : `title location applicationEndDate jobType jobMode publishStatus publishedDate createdAt updatedAt rollingApplication jobCategories applications applicationStatus`;

  const careerJobs = await CareerJobs.paginate(filter, { ...options, select: selectFields });
  return careerJobs;
};

const getCareerJob = async (careerJobId, isAdmin) => {
  validateId(careerJobId, 'CareerJob');
  const today = new Date();
  const query = isAdmin
    ? { _id: careerJobId }
    : {
        _id: careerJobId,
        publishedStatus: careerJobsEnums.careerJobPublishStatus.PUBLISHED,
        applicationEndDate: { $gte: today },
      };
  const careerJob = await CareerJobs.findOne(query).select(
    isAdmin ? '' : '-applications -applicationStatus -currency -proposedAnnualSalary',
  );
  if (!careerJob) {
    logger.info(`Career with id: ${careerJobId} not found`);
    throw new ApiError(httpStatus.NOT_FOUND, `Career record not found`);
  }
  return careerJob;
};

const getJobAnalytics = async () => {
  const result = await CareerJobs.aggregate([
    {
      $group: {
        _id: null,
        totalJobs: { $sum: 1 },
        publishedJobs: {
          $sum: { $cond: [{ $eq: ['$publishStatus', careerJobsEnums.careerJobPublishStatus.PUBLISHED] }, 1, 0] },
        },
        unpublishedJobs: {
          $sum: { $cond: [{ $eq: ['$publishStatus', careerJobsEnums.careerJobPublishStatus.UNPUBLISHED] }, 1, 0] },
        },
      },
    },
  ]);
  if (result.length) delete result[0]._id;
  return result[0];
};

const updateCareerJob = async (careerJobId, updateBodyParam) => {
  const updateBody = { ...updateBodyParam };
  validateId(careerJobId, 'CareerJob');
  const existingCareerJob = await CareerJobs.findById(careerJobId);
  if (!existingCareerJob) {
    logger.info(`Career with id: ${careerJobId} not found`);
    throw new ApiError(httpStatus.NOT_FOUND, `Career record not found`);
  }

  if (updateBody.publishStatus === careerJobsEnums.careerJobPublishStatus.PUBLISHED) {
    // check that the existingCareerJob's applicationend date is >= today's date
    // if updateBody has an applicationend date in it, check it

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    existingCareerJob.publishedDate = new Date(); // set the publishedDate to the day it was re-published

    if (existingCareerJob.applicationEndDate < today) existingCareerJob.applicationEndDate = today;

    if (updateBody.applicationEndDate && new Date(updateBody.applicationEndDate) < today)
      throw new ApiError(httpStatus.BAD_REQUEST, 'The updated application end date is in the past.');

    // if applicationEndDate is supplied, set rollingApplication to false
    if (updateBody.applicationEndDate) {
      updateBody.rollingApplication = false;
    }
  }

  Object.assign(existingCareerJob, updateBody);
  await existingCareerJob.save();

  return existingCareerJob;
};

const deleteCareerJob = async (careerJobId) => {
  validateId(careerJobId, 'CareerJob');
  const careerJob = await CareerJobs.findById(careerJobId);
  if (!careerJob) {
    logger.info(`Career with id: ${careerJobId} not found`);
    throw new ApiError(httpStatus.NOT_FOUND, `Career record not found`);
  }

  await careerJob.delete();
};

const getCareerJobEnums = async () => {
  const enums = Object.keys(careerJobsEnums).reduce((acc, key) => {
    acc[key] = Object.values(careerJobsEnums[key]);
    return acc;
  }, {});
  return { ...enums, currencies: Object.values(currencies) };
};

// job applications

const createJobApplication = async (requestBody, files) => {
  validateId(requestBody.careerJob, 'CareerJob');

  const validatedData = { ...requestBody };
  try {
    const { error, value: socialLinks } = validateJobSocialLinks.validate(JSON.parse(requestBody.socialLinks));
    if (error) {
      if (error.details.find((detail) => detail.message.includes('duplicate value'))) {
        logger.info('Duplicate Social Links');
        throw new ApiError(httpStatus.BAD_REQUEST, 'Duplicate Social Links');
      }
      logger.info(`Invalid Social Links: ${error.message}`);
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }

    validatedData.socialLinks = socialLinks;
  } catch (error) {
    if (error instanceof ApiError) throw error;

    logger.error(`Error: ${error.message}`);
    throw new ApiError(httpStatus.BAD_REQUEST, `Error: Invalid format for Social Links`);
  }

  const careerJob = await CareerJobs.findById(validatedData.careerJob);
  if (!careerJob || careerJob.publishStatus === careerJobsEnums.careerJobPublishStatus.UNPUBLISHED) {
    logger.error('Invalid Job Application');
    throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid Job Application');
  }

  const emailExists = await JobApplication.findOne({ email: validatedData.email, careerJob: validatedData.careerJob });
  if (emailExists) {
    logger.info(`Email: ${validatedData.email} already exists for this job application`);
    throw new ApiError(httpStatus.CONFLICT, 'Email already exists for this job application');
  }

  const resumeFileId = await processFileUpload(files.resume[0], azureContainers.jobResumes);
  if (files.coverLetter && files.coverLetter[0]) {
    const coverLetterFileId = await processFileUpload(files.coverLetter[0], azureContainers.jobCoverLetters);
    validatedData.coverLetter = coverLetterFileId;
  }

  const newJobApplication = await JobApplication.create({ ...validatedData, resume: resumeFileId });
  careerJob.applications.push(newJobApplication._id);
  careerJob.applicationStatus.inReview.push(newJobApplication._id);
  await careerJob.save();

  // notify super-admin
  const superAdmin = await User.findOne({ roles: { $in: ['superAdmin'] } });
  await emailService.sendNewJobApplicantEmail(superAdmin, { newJobApplication, careerJob });

  return newJobApplication;
};

const getJobApplications = async (filterParams, options) => {
  const filter = { ...filterParams };
  if (filter.name) {
    const name = stopword.removeStopwords(filter.name.split(' '));
    filter.$and = [
      {
        $or: [
          { firstName: { $regex: name.join('|'), $options: 'i' } },
          { lastName: { $regex: name.join('|'), $options: 'i' } },
        ],
      },
    ];
  }
  if (filter.careerJob) validateId(filter.careerJob, 'CareerJob');
  const populate = [
    { path: 'resume', select: 'url' },
    { path: 'coverLetter', select: 'url' },
  ];
  return JobApplication.paginate(filter, { ...options, populate });
};

const getJobApplication = async (jobApplicationId) => {
  validateId(jobApplicationId, 'JobApplication');
  const jobApplication = await JobApplication.findById(jobApplicationId).populate([
    { path: 'resume', select: 'url' },
    { path: 'coverLetter', select: 'url' },
  ]);
  return jobApplication;
};

const updateJobApplication = async (jobApplicationId, updateBody) => {
  validateId(jobApplicationId, 'JobApplication');
  // update the job application status
  const jobApplication = await JobApplication.findById(jobApplicationId);
  if (!jobApplication) {
    logger.info(`Job Application with id: ${jobApplicationId} not found`);
    throw new ApiError(httpStatus.NOT_FOUND, `Job Application record not found`);
  }

  const { status: newStatus } = updateBody;
  jobApplication.status = newStatus;
  const careerJob = await CareerJobs.findById(jobApplication.careerJob);
  const statuses = ['rejected', 'inReview', 'interview', 'hired'];

  statuses.forEach((status) => {
    const index = careerJob.applicationStatus[status].findIndex((id) => id.toString() === jobApplication._id.toString());
    if (index !== -1) careerJob.applicationStatus[status].splice(index, 1);
  });

  if (newStatus === careerJobsEnums.jobApplicationStatus.IN_REVIEW) {
    // the value is 'in-review' and not 'inReview' which is the field name
    careerJob.applicationStatus.inReview.push(jobApplicationId);
  } else careerJob.applicationStatus[newStatus].push(jobApplicationId);

  await Promise.all([jobApplication.save(), careerJob.save()]);
  return jobApplication;
};

module.exports = {
  addCareerJob,
  getCareerJobs,
  getCareerJob,
  updateCareerJob,
  deleteCareerJob,
  getCareerJobEnums,
  getJobAnalytics,

  createJobApplication,
  getJobApplications,
  getJobApplication,
  updateJobApplication,
};
