const Joi = require('joi');
const { businessEnums } = require('../config/constants');

const createPaymentIntent = {
  body: Joi.object().keys({
    serviceId: Joi.string().required(),
    plan: Joi.string()
      .valid(...Object.values(businessEnums.servicePlans))
      .required(),
    // referralCode: Joi.string(),
  }),
};

const getSessionStatus = {
  query: Joi.object().keys({
    sessionId: Joi.string(),
  }),
};

const getPaymentIntentStatus = {
  query: Joi.object().keys({
    paymentIntentId: Joi.string(),
  }),
};

const getTransactions = {
  query: Joi.object().keys({
    provider: Joi.string(),
    sortBy: Joi.string().valid('createdAt:asc', 'createdAt:desc', 'status:asc', 'status:desc').default('createdAt:desc'),
    startDate: Joi.date().iso().default(new Date(0).toISOString()),
    endDate: Joi.date()
      .iso()
      .default(new Date(new Date().getTime() + ********).toISOString()), // Add 1 day for possible deployment timezone differences
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
  }),
};

const createStripeAccount = {
  body: Joi.object().keys({
    email: Joi.string().email().required(),
  }),
};

const withdraw = {
  query: Joi.object().keys({
    amount: Joi.number().min(50), // Minimum 20 USD
  }),
};

const getAccountBalance = {
  query: Joi.object()
    .keys({
      user: Joi.string().valid('true'),
      business: Joi.string().valid('true'),
      referralBonus: Joi.string().valid('true'),
    })
    .xor('user', 'business')
    .messages({ 'object.xor': 'Specify either user or business but not both' })
    .required(),
};

const getReferralHistory = {
  query: Joi.object()
    .keys({
      user: Joi.string().valid('true'),
      business: Joi.string().valid('true'),
      referralBonus: Joi.string().valid('true'),
      startDate: Joi.date(),
      endDate: Joi.date(),
      page: Joi.number().default(1),
      limit: Joi.number().default(10),
    })
    .xor('user', 'business')
    .custom((value, helpers) => {
      const { startDate, endDate } = value;

      if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
        return helpers.error('any.invalid', { message: 'startDate must be less than or equal to endDate' });
      }

      return value; // If no issues, return the value
    })
    .messages({ 'object.xor': 'Specify either user or business but not both' })
    .required(),
};

module.exports = {
  createPaymentIntent,
  getSessionStatus,
  getPaymentIntentStatus,
  getTransactions,
  createStripeAccount,
  withdraw,
  getAccountBalance,
  getReferralHistory,
};
