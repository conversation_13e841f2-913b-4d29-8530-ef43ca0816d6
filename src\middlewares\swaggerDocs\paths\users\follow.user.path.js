module.exports = {
  post: {
    tags: ['Users'],
    summary: 'Follow or unfollow a user',
    parameters: [
      {
        in: 'path',
        name: 'id',
        required: true,
        description: 'ID of the user to follow or unfollow',
        schema: {
          type: 'string',
          format: 'ObjectId',
        },
      },
    ],
    responses: {
      200: {
        description: 'Successfully followed user',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'string' },
              },
            },
          },
        },
      },
      400: {
        description: 'Cannot perform operation',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'string' },
              },
            },
          },
        },
      },
      502: {
        description: 'Already following this user',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'string' },
              },
            },
          },
        },
      },
    },
  },
};
