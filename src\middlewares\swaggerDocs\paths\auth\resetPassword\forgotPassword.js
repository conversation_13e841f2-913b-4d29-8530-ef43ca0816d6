module.exports = {
  post: {
    summary: 'Forgot Password',
    description: 'Initiates the process to reset the user password by sending a reset password email',
    tags: ['Authentication'],
    requestBody: {
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              email: { type: 'string', format: 'email', example: '<EMAIL>' },
            },
            required: ['email'],
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Reset password email sent successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  example: 'Kindly follow the instructions in your email to reset your password',
                },
                status: { type: 'string', example: 'SUCCESS' },
              },
            },
          },
        },
      },
      404: {
        description: 'User not found',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: {
                  type: 'string',
                  example: 'FAILED',
                },
                message: {
                  type: 'string',
                  example: 'User not found.',
                },
              },
            },
          },
        },
      },
    },
  },
};
