const createProfile = require('./create.profile.path');
const getProfile = require('./get.profile.path');
const getProfileResource = require('./get.profile.resource.path');
const updateProfile = require('./update.profile.path');
const addProfileResource = require('./add.resource.profile.path');
const updateProfileResource = require('./update.resource.profile.path');
const deleteProfileResource = require('./delete.resource.profile.path');

module.exports = {
  '/profile': { ...createProfile, ...getProfile, ...updateProfile },
  '/profile/resource': { ...addProfileResource },
  '/profile/resource/{id}': { ...getProfileResource, ...updateProfileResource, ...deleteProfileResource },
};
