/* eslint-disable no-param-reassign */
const Async = require('async');
const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const Order = require('../order.model');
const ServiceOffer = require('../service.offer.model');
const { businessEnums } = require('../../config/constants');

const addOrderNumberToExistingOrders = async () => {
  const migrated = await GlobalVariable.findOne({ name: 'Add Order number for existing Orders' });

  if (migrated) {
    return;
  }

  const orders = await Order.find();

  await Async.eachOfSeries(orders, async (order, index) => {
    const completionDaysOffer = await ServiceOffer.findOne({
      service: order.service,
      name: businessEnums.requiredOffers.COMPLETION_DAYS[0],
    }).select({
      value: { $elemMatch: { plan: order.plan } },
    });

    await Order.updateOne(
      { _id: order._id },
      {
        orderNumber: 1000 + index,
        dueAt: new Date(order.startedAt.getTime() + Number(completionDaysOffer.value[0].offer) * 24 * 60 * 60 * 1000),
        createdAt: new Date(order.startedAt),
      },
    );
  });

  await GlobalVariable.create({ name: 'Add Order number for existing Orders', value: 'true' });
  logger.info('Order number for existing Orders added');
};

module.exports = addOrderNumberToExistingOrders;
