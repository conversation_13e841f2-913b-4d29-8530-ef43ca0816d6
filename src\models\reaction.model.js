const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const { reactionTypes } = require('../config/constants');

const reactionSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  post: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Post',
  },
  comment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Comment',
  },
  type: {
    type: String,
    enum: Object.values(reactionTypes),
    required: true,
  },
});

reactionSchema.plugin(toJSON);
reactionSchema.plugin(paginate);

const Reaction = mongoose.model('Reaction', reactionSchema);

module.exports = Reaction;
