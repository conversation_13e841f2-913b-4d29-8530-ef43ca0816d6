const educationSchemaSwagger = {
  type: 'object',
  properties: {
    id: { type: 'string', example: '6009c0eee65f6dce28fb3e50' },
    schoolName: {
      type: 'string',
      description: 'Name of the school',
      example: 'University of XYZ',
    },
    degreeType: {
      type: 'string',
      description: 'Type of degree',
      example: 'Bachelor of Science',
    },
    major: {
      type: 'string',
      description: 'Area of major',
      example: 'Computer Science',
    },
    start: {
      type: 'string',
      format: 'date',
      description: 'Start date of education',
      example: '2022-01-01',
    },
    end: {
      type: 'string',
      format: 'date',
      description: 'End date of education',
      example: '2026-01-01',
    },
    current: {
      type: 'boolean',
      description: 'Indicates if the education is ongoing',
      example: true,
    },
  },
};

module.exports = educationSchemaSwagger;
