module.exports.authController = require('./auth.controller');
module.exports.activityLogController = require('./activity.log.controller');
module.exports.countryController = require('./country.controller');
module.exports.blogPostController = require('./blog.post.controller');
module.exports.botRecordController = require('./bot.record.controller');
module.exports.businessController = require('./business.controller');
module.exports.careerJobsController = require('./career.jobs.controller');
module.exports.feedbackController = require('./feedback.controller');
module.exports.forumController = require('./forum.controller');
module.exports.groupController = require('./group.controller');
module.exports.globalController = require('./global.controller');
module.exports.commentController = require('./comment.controller');
module.exports.messageController = require('./message.controller');
module.exports.notificationController = require('./notification.controller');
module.exports.orderController = require('./order.controller');
module.exports.postController = require('./post.controller');
module.exports.profileController = require('./profile.controller');
module.exports.premiumSubscriberController = require('./premium.subscriber.controller');
module.exports.resourceLibraryController = require('./resource.library.controller');
module.exports.userController = require('./user.controller');
module.exports.universityController = require('./university.controller');
module.exports.scholarshipController = require('./scholarship.controller');
module.exports.serviceController = require('./service.controller');
module.exports.settingController = require('./setting.controller');
module.exports.schoolController = require('./school.controller');
module.exports.subscriberController = require('./subscriber.controller');
module.exports.issueReportController = require('./issue.report.controller');
module.exports.transactionController = require('./transaction.controller');
