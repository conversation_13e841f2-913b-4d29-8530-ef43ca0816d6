const httpStatus = require('http-status');
const ApiError = require('../../utils/ApiError');
const activityLogService = require('../activity.log.service');
const notificationService = require('../notification.service');
const { User, Reaction } = require('../../models');
const validateId = require('./validateId');
const { reactionsActionMap } = require('../../config/constants');

const reactToAnItem = async (resourceModel, resourceId, user, reactionType) => {
  validateId(resourceId, resourceModel?.modelName);
  const resource = await resourceModel?.findById(resourceId).populate([
    { path: 'reactions', select: 'user type' },
    ...(resourceModel.modelName === 'Post' ? [{ path: 'group', select: '_id admin coAdmins members' }] : []), // populate group for post
    ...(resourceModel.modelName === 'Comment' // populate post and it's group for comment
      ? [{ path: 'post', select: 'group', populate: { path: 'group', select: '_id admin coAdmins members' } }]
      : []),
  ]);

  if (resource.group || resource.post?.group) {
    const group = resource.group || resource.post.group;
    const groupMembers = [group.admin, ...group.coAdmins, ...group.members].map((member) => member?.toString());
    if (!groupMembers.includes(user._id.toString())) {
      throw new ApiError(httpStatus.FORBIDDEN, 'You are not a member of this group');
    }
  }
  const modelName = resourceModel?.modelName?.toLowerCase();

  if (!resource) {
    throw new ApiError(httpStatus.NOT_FOUND, `${modelName} not found`);
  }

  const reacted = await Reaction.findOne({ [modelName]: resourceId, user: user._id });
  if (reacted) {
    if (reacted.type === reactionType) {
      await Reaction.findByIdAndRemove(reacted._id);
      resource.reactions.pull(reacted._id);
      // user.reactions.pull(reacted._id);
      await resource.save();
    } else {
      reacted.type = reactionType;
      await reacted.save();
    }
  } else {
    const createdReaction = await Reaction.create({
      user: user._id,
      type: reactionType,
      [modelName]: resourceId,
    });

    resource.reactions.push(createdReaction._id);
    // user.reactions.push(createdReaction._id);

    await resource.save();
    // await user.save();

    await activityLogService.logActivity(
      user._id,
      `${modelName.toUpperCase()}_${reactionType.toUpperCase()}`,
      resource._id,
      resource.post?._id,
    );

    const details = {
      resourceId: resource.id,
      resourceName: resourceModel.modelName,
      action: reactionsActionMap[reactionType.toUpperCase()],
      type: reactionType,
    };
    if (modelName === 'comment') {
      details.postId = resource.post._id;
      if (resource.parentComment) {
        details.parentCommentId = resource.parentComment;
      }
    }

    const recipient = await User.findById(resource.user._id);
    await notificationService.createReactionNotification(user, recipient, details);
  }

  if (modelName === 'post') {
    // eslint-disable-next-line global-require
    return require('../post.service').getPostById(resourceId, user._id, true);
  }
  if (modelName === 'comment') {
    // eslint-disable-next-line global-require
    return require('../comment.service').getCommentById(resourceId);
  }
};

module.exports = {
  reactToAnItem,
};
