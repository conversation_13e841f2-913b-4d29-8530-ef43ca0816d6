/* eslint-disable no-param-reassign */
const Async = require('async');
const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const Service = require('../service.model');
const Review = require('../review.model');

const varName = 'Update Review Array in Service';

const updateReviewsArrayInServices = async () => {
  const migrated = await GlobalVariable.findOne({ name: varName });

  if (migrated) {
    return;
  }

  const reviews = await Review.find();

  await Async.eachSeries(reviews, async (review) => {
    await Service.findOneAndUpdate({ _id: review.service }, { $addToSet: { reviews: review._id } });
  });

  await GlobalVariable.create({ name: varName, value: 'true' });
  logger.info('Update Review Array in Service completed');
};

module.exports = updateReviewsArrayInServices;
