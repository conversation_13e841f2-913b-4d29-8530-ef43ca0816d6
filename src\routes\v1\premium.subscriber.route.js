const express = require('express');

const router = express.Router();
const { auth } = require('../../middlewares/auth');
const validate = require('../../middlewares/validate');
const { premiumSubscriberController } = require('../../controllers');
const { premiumSubscriberValidation } = require('../../validations');

router.use(auth());

router.post('/', validate(premiumSubscriberValidation.createSubscriber), premiumSubscriberController.createSubscriber);
router.patch('/', validate(premiumSubscriberValidation.updateSubscription), premiumSubscriberController.updateSubscription);

router.get(
  '/session-status',
  validate(premiumSubscriberValidation.getSessionStatus),
  premiumSubscriberController.getSessionStatus,
);

router.get(
  '/subscriptions',
  validate(premiumSubscriberValidation.getSubscriptions),
  premiumSubscriberController.getSubscriptions,
);

router.get('/latest-subscription', premiumSubscriberController.getLatestSubscription);

router.get(
  '/subscriptions/admin',
  auth('internal'),
  validate(premiumSubscriberValidation.getSubscriptions),
  premiumSubscriberController.getSubscriptionsByAdmin,
);

router.get('/subscriptions/:subscriptionId', premiumSubscriberController.getSubscription);

// Get Subscriptions by user

module.exports = router;
