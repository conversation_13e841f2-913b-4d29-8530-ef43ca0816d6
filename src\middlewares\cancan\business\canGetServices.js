const httpStatus = require('http-status');
const ApiError = require('../../../utils/ApiError');
const { businessEnums } = require('../../../config/constants');

// This middleware is used in both authenticated and unauthenticated routes
const canGetServices = () => async (req, res, next) => {
  if (req.query.status !== businessEnums.serviceStatuses.PUBLISHED) {
    if (!req.query.provider || String(req.user?.business) !== req.query.provider) {
      return next(new ApiError(httpStatus.FORBIDDEN, 'Forbidden: You are not allowed to perform this operation'));
    }
  }
  next();
};

module.exports = canGetServices;
