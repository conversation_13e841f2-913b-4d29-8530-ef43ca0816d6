const Async = require('async');
const GlobalVariable = require('../global.variable.model');
const Profile = require('../profile/profile.model');
const Setting = require('../setting.model');
const logger = require('../../config/logger');

const renameVolunteerToVolunteering = async () => {
  const renamed = await GlobalVariable.findOne({ name: 'rename volunteer' });
  if (renamed) {
    return;
  }
  await Async.eachSeries(await Profile.find(), async (profileParam) => {
    const profile = profileParam;
    if (profile._doc.volunteer) {
      profile.volunteering = profile._doc.volunteer;
      await profile.save();
    }
  });
  await Async.eachSeries(await Setting.find(), async (settingParam) => {
    const setting = settingParam;
    if (setting._doc.volunteer) {
      setting.volunteering = setting._doc.volunteer;
      await setting.save();
    }
  });

  await GlobalVariable.create({ name: 'rename volunteer', value: 'true' });
  logger.info('Volunteer renamed to Volunteering');
};

module.exports = renameVolunteerToVolunteering;
