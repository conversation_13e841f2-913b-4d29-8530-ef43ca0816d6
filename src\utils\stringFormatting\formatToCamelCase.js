const formatToCamelCase = (string) => {
  let result = '';
  let capitalizeNext = false;
  for (let i = 0; i < string.length; i += 1) {
    if (/[^a-zA-Z0-9]/.test(string[i])) {
      if (string[i] !== "'") capitalizeNext = true;
      // eslint-disable-next-line no-continue
      continue;
    }
    if (capitalizeNext) {
      result += string[i].toUpperCase();
      capitalizeNext = false;
    } else {
      result += string[i].toLowerCase();
    }
  }
  return result;
};

module.exports = formatToCamelCase;
