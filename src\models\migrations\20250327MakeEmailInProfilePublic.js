const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const { visibilityTypes } = require('../../config/constants');
const Setting = require('../setting.model');

const migrationName = 'Make email public in profile 2';

const setExistingServicesVerificationStatusesAndVerifications = async () => {
  const migrated = await GlobalVariable.findOne({ name: migrationName });
  if (migrated) return;

  await Setting.updateMany(
    {},
    {
      $set: {
        email: visibilityTypes.PUBLIC,
        nationality: visibilityTypes.PUBLIC,
        gender: visibilityTypes.PUBLIC,
        stateOfResidence: visibilityTypes.PUBLIC,
      },
    },
  );

  await GlobalVariable.create({ name: migrationName, value: 'true' });
  logger.info('Email in profile settings marked as public');
};

module.exports = setExistingServicesVerificationStatusesAndVerifications;
