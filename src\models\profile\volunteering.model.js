const mongoose = require('mongoose');
const { toJSON, paginate } = require('../plugins');

const volunteeringSchema = new mongoose.Schema({
  title: { type: String, trim: true, required: true },
  companyName: { type: String, trim: true, required: true },
  start: { type: mongoose.Schema.Types.Date, trim: true, required: true },
  end: { type: mongoose.Schema.Types.Date, trim: true },
  current: { type: Boolean },
  description: { type: String, trim: true },
});

volunteeringSchema.plugin(toJSON);
volunteeringSchema.plugin(paginate);

const Volunteer = mongoose.model('Volunteering', volunteeringSchema);
module.exports = Volunteer;
