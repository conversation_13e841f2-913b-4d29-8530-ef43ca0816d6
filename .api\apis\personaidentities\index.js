"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var oas_1 = __importDefault(require("oas"));
var core_1 = __importDefault(require("api/dist/core"));
var openapi_json_1 = __importDefault(require("./openapi.json"));
var SDK = /** @class */ (function () {
    function SDK() {
        this.spec = oas_1.default.init(openapi_json_1.default);
        this.core = new core_1.default(this.spec, 'personaidentities/2023-01-05 (api/6.1.2)');
    }
    /**
     * Optionally configure various options that the SDK allows.
     *
     * @param config Object of supported SDK options and toggles.
     * @param config.timeout Override the default `fetch` request timeout of 30 seconds. This number
     * should be represented in milliseconds.
     */
    SDK.prototype.config = function (config) {
        this.core.setConfig(config);
    };
    /**
     * If the API you're using requires authentication you can supply the required credentials
     * through this method and the library will magically determine how they should be used
     * within your API request.
     *
     * With the exception of OpenID and MutualTLS, it supports all forms of authentication
     * supported by the OpenAPI specification.
     *
     * @example <caption>HTTP Basic auth</caption>
     * sdk.auth('username', 'password');
     *
     * @example <caption>Bearer tokens (HTTP or OAuth 2)</caption>
     * sdk.auth('myBearerToken');
     *
     * @example <caption>API Keys</caption>
     * sdk.auth('myApiKey');
     *
     * @see {@link https://spec.openapis.org/oas/v3.0.3#fixed-fields-22}
     * @see {@link https://spec.openapis.org/oas/v3.1.0#fixed-fields-22}
     * @param values Your auth credentials for the API; can specify up to two strings or numbers.
     */
    SDK.prototype.auth = function () {
        var _a;
        var values = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            values[_i] = arguments[_i];
        }
        (_a = this.core).setAuth.apply(_a, values);
        return this;
    };
    /**
     * If the API you're using offers alternate server URLs, and server variables, you can tell
     * the SDK which one to use with this method. To use it you can supply either one of the
     * server URLs that are contained within the OpenAPI definition (along with any server
     * variables), or you can pass it a fully qualified URL to use (that may or may not exist
     * within the OpenAPI definition).
     *
     * @example <caption>Server URL with server variables</caption>
     * sdk.server('https://{region}.api.example.com/{basePath}', {
     *   name: 'eu',
     *   basePath: 'v14',
     * });
     *
     * @example <caption>Fully qualified server URL</caption>
     * sdk.server('https://eu.api.example.com/v14');
     *
     * @param url Server URL
     * @param variables An object of variables to replace into the server URL.
     */
    SDK.prototype.server = function (url, variables) {
        if (variables === void 0) { variables = {}; }
        this.core.setServer(url, variables);
    };
    /**
     * Returns a list of your organization's <<glossary:account>>(s).
     *
     * @summary List all Accounts
     * @throws FetchError<400, types.ListAllAccountsResponse400> Bad Request
     * @throws FetchError<401, types.ListAllAccountsResponse401> Unauthorized
     * @throws FetchError<403, types.ListAllAccountsResponse403> Forbidden
     * @throws FetchError<404, types.ListAllAccountsResponse404> Not Found
     * @throws FetchError<429, types.ListAllAccountsResponse429> Too Many Requests
     */
    SDK.prototype.listAllAccounts = function (metadata) {
        return this.core.fetch('/accounts', 'get', metadata);
    };
    /**
     * Creates a new Account for your organization.
     *
     * @summary Create an Account
     * @throws FetchError<400, types.CreateAnAccountResponse400> Bad Request
     * @throws FetchError<401, types.CreateAnAccountResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAnAccountResponse403> Forbidden
     * @throws FetchError<409, types.CreateAnAccountResponse409> Conflict
     * @throws FetchError<422, types.CreateAnAccountResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAnAccountResponse429> Too Many Requests
     */
    SDK.prototype.createAnAccount = function (body, metadata) {
        return this.core.fetch('/accounts', 'post', body, metadata);
    };
    /**
     * Permanently deletes personally identifiable information (PII) for an Account and all
     * associated Inquiries, Verifications and Reports. The response indicates a successful
     * redaction of the Account. Redaction of the Account's associated child objects are done
     * asynchronously and may take  some time before all associated child objects are fully
     * redacted. **This action cannot be undone**.
     *
     * This endpoint can be used to comply with privacy regulations such as GDPR / CCPA or to
     * enforce data privacy.
     *
     * Note: An account is still updatable after redaction. If you want to delete data
     * continuously, please reach out to us to help you setup a retention policy.
     *
     * @summary Redact an Account
     * @throws FetchError<400, types.RedactAnAccountResponse400> Bad Request
     * @throws FetchError<401, types.RedactAnAccountResponse401> Unauthorized
     * @throws FetchError<403, types.RedactAnAccountResponse403> Forbidden
     * @throws FetchError<404, types.RedactAnAccountResponse404> Not Found
     * @throws FetchError<409, types.RedactAnAccountResponse409> Conflict
     * @throws FetchError<422, types.RedactAnAccountResponse422> Unprocessable Entity
     * @throws FetchError<429, types.RedactAnAccountResponse429> Too Many Requests
     */
    SDK.prototype.redactAnAccount = function (metadata) {
        return this.core.fetch('/accounts/{account-id}', 'delete', metadata);
    };
    /**
     * Retrieves the details of an existing Account.
     *
     * @summary Retrieve an Account
     * @throws FetchError<400, types.RetrieveAnAccountResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAnAccountResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAnAccountResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAnAccountResponse404> Not Found
     * @throws FetchError<429, types.RetrieveAnAccountResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAnAccount = function (metadata) {
        return this.core.fetch('/accounts/{account-id}', 'get', metadata);
    };
    SDK.prototype.updateAnAccount = function (body, metadata) {
        return this.core.fetch('/accounts/{account-id}', 'patch', body, metadata);
    };
    SDK.prototype.accountsAddTag = function (body, metadata) {
        return this.core.fetch('/accounts/{account-id}/add-tag', 'post', body, metadata);
    };
    SDK.prototype.consolidateIntoAnAccount = function (body, metadata) {
        return this.core.fetch('/accounts/{account-id}/consolidate', 'post', body, metadata);
    };
    SDK.prototype.accountsRemoveTag = function (body, metadata) {
        return this.core.fetch('/accounts/{account-id}/remove-tag', 'post', body, metadata);
    };
    SDK.prototype.accountsSetAllTags = function (body, metadata) {
        return this.core.fetch('/accounts/{account-id}/set-tags', 'post', body, metadata);
    };
    /**
     * Returns a list of your organization's API keys.
     *
     * @summary List all API keys
     * @throws FetchError<400, types.ListAllApiKeysResponse400> Bad Request
     * @throws FetchError<401, types.ListAllApiKeysResponse401> Unauthorized
     * @throws FetchError<403, types.ListAllApiKeysResponse403> Forbidden
     * @throws FetchError<429, types.ListAllApiKeysResponse429> Too Many Requests
     */
    SDK.prototype.listAllApiKeys = function (metadata) {
        return this.core.fetch('/api-keys', 'get', metadata);
    };
    /**
     * Creates a new API key with response defaults and permissions.
     *
     * @summary Create an API key
     * @throws FetchError<400, types.CreateAnApiKeyResponse400> Bad Request
     * @throws FetchError<401, types.CreateAnApiKeyResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAnApiKeyResponse403> Forbidden
     * @throws FetchError<409, types.CreateAnApiKeyResponse409> Conflict
     * @throws FetchError<422, types.CreateAnApiKeyResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAnApiKeyResponse429> Too Many Requests
     */
    SDK.prototype.createAnApiKey = function (body, metadata) {
        return this.core.fetch('/api-keys', 'post', body, metadata);
    };
    /**
     * Retrieves the information for an existing API key, including its value.
     *
     * @summary Retrieve an API key
     * @throws FetchError<400, types.RetrieveAnApiKeyResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAnApiKeyResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAnApiKeyResponse403> Forbidden
     * @throws FetchError<429, types.RetrieveAnApiKeyResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAnApiKey = function (metadata) {
        return this.core.fetch('/api-keys/{api-key-id}', 'get', metadata);
    };
    SDK.prototype.updateAnApiKey = function (body, metadata) {
        return this.core.fetch('/api-keys/{api-key-id}', 'patch', body, metadata);
    };
    SDK.prototype.expireAnApiKey = function (body, metadata) {
        return this.core.fetch('/api-keys/{api-key-id}/expire', 'post', body, metadata);
    };
    /**
     * Returns a list of your organization's API Logs.
     *
     * @summary List all API Logs
     * @throws FetchError<400, types.ListAllApiLogsResponse400> Bad Request
     * @throws FetchError<401, types.ListAllApiLogsResponse401> Unauthorized
     * @throws FetchError<403, types.ListAllApiLogsResponse403> Forbidden
     * @throws FetchError<404, types.ListAllApiLogsResponse404> Not Found
     * @throws FetchError<429, types.ListAllApiLogsResponse429> Too Many Requests
     */
    SDK.prototype.listAllApiLogs = function (metadata) {
        return this.core.fetch('/api-logs', 'get', metadata);
    };
    /**
     * Retrieves the details of an existing API Log.
     *
     * @summary Retrieve an API Log
     * @throws FetchError<400, types.RetrieveAnApiLogResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAnApiLogResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAnApiLogResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAnApiLogResponse404> Not Found
     * @throws FetchError<429, types.RetrieveAnApiLogResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAnApiLog = function (metadata) {
        return this.core.fetch('/api-logs/{api-log-id}', 'get', metadata);
    };
    /**
     * Returns a list of your organization's cases.
     *
     * Note that this endpoint aggregates cases across all <<glossary:case template>>(s). See
     * [Pagination](https://docs.withpersona.com/reference/pagination)for more details about
     * handling the response.
     *
     * @summary List all Cases
     * @throws FetchError<400, types.ListAllCasesResponse400> Bad Request
     * @throws FetchError<401, types.ListAllCasesResponse401> Unauthorized
     * @throws FetchError<403, types.ListAllCasesResponse403> Forbidden
     * @throws FetchError<404, types.ListAllCasesResponse404> Not Found
     * @throws FetchError<429, types.ListAllCasesResponse429> Too Many Requests
     */
    SDK.prototype.listAllCases = function (metadata) {
        return this.core.fetch('/cases', 'get', metadata);
    };
    /**
     * Creates a new case in your organization.
     *
     * @summary Create a Case
     * @throws FetchError<400, types.CreateACaseResponse400> Bad Request
     * @throws FetchError<401, types.CreateACaseResponse401> Unauthorized
     * @throws FetchError<403, types.CreateACaseResponse403> Forbidden
     * @throws FetchError<404, types.CreateACaseResponse404> Not Found
     * @throws FetchError<409, types.CreateACaseResponse409> Conflict
     * @throws FetchError<422, types.CreateACaseResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateACaseResponse429> Too Many Requests
     */
    SDK.prototype.createACase = function (body, metadata) {
        return this.core.fetch('/cases', 'post', body, metadata);
    };
    /**
     * Permanently redacts a <<glossary:Case>> and its fields. Case objects must be redacted
     * individually. **This action cannot be undone**.
     *
     * @summary Redact a Case
     * @throws FetchError<400, types.RedactACaseResponse400> Bad Request
     * @throws FetchError<401, types.RedactACaseResponse401> Unauthorized
     * @throws FetchError<403, types.RedactACaseResponse403> Forbidden
     * @throws FetchError<404, types.RedactACaseResponse404> Not Found
     * @throws FetchError<409, types.RedactACaseResponse409> Conflict
     * @throws FetchError<422, types.RedactACaseResponse422> Unprocessable Entity
     * @throws FetchError<429, types.RedactACaseResponse429> Too Many Requests
     */
    SDK.prototype.redactACase = function (metadata) {
        return this.core.fetch('/cases/{case-id}', 'delete', metadata);
    };
    /**
     * Retrieves the details of an existing <<glossary:Case>>.
     *
     * @summary Retrieve a Case
     * @throws FetchError<400, types.RetrieveCaseResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveCaseResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveCaseResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveCaseResponse404> Not Found
     * @throws FetchError<429, types.RetrieveCaseResponse429> Too Many Requests
     */
    SDK.prototype.retrieveCase = function (metadata) {
        return this.core.fetch('/cases/{case-id}', 'get', metadata);
    };
    SDK.prototype.updateACase = function (body, metadata) {
        return this.core.fetch('/cases/{case-id}', 'patch', body, metadata);
    };
    SDK.prototype.addPersonaObjects = function (body, metadata) {
        return this.core.fetch('/cases/{case-id}/add-objects', 'post', body, metadata);
    };
    /**
     * Assigns a <<glossary:Case>> to a Persona user.
     *
     * @summary Assign a Case
     * @throws FetchError<400, types.AssignACaseResponse400> Bad Request
     * @throws FetchError<401, types.AssignACaseResponse401> Unauthorized
     * @throws FetchError<403, types.AssignACaseResponse403> Forbidden
     * @throws FetchError<404, types.AssignACaseResponse404> Not Found
     * @throws FetchError<409, types.AssignACaseResponse409> Conflict
     * @throws FetchError<422, types.AssignACaseResponse422> Unprocessable Entity
     * @throws FetchError<429, types.AssignACaseResponse429> Too Many Requests
     */
    SDK.prototype.assignACase = function (body, metadata) {
        return this.core.fetch('/cases/{case-id}/assign', 'post', body, metadata);
    };
    SDK.prototype.setStatusForACase = function (body, metadata) {
        return this.core.fetch('/cases/{case-id}/set-status', 'post', body, metadata);
    };
    SDK.prototype.addTag = function (body, metadata) {
        return this.core.fetch('/cases/{case-id}/add-tag', 'post', body, metadata);
    };
    SDK.prototype.removeTag = function (body, metadata) {
        return this.core.fetch('/cases/{case-id}/remove-tag', 'post', body, metadata);
    };
    SDK.prototype.setTags = function (body, metadata) {
        return this.core.fetch('/cases/{case-id}/set-tags', 'post', body, metadata);
    };
    /**
     * Retrieves a list of Devices.
     *
     * @summary List all Devices
     * @throws FetchError<400, types.ListAllDevicesResponse400> Bad Request
     * @throws FetchError<401, types.ListAllDevicesResponse401> Unauthorized
     * @throws FetchError<403, types.ListAllDevicesResponse403> Forbidden
     * @throws FetchError<429, types.ListAllDevicesResponse429> Too Many Requests
     */
    SDK.prototype.listAllDevices = function (metadata) {
        return this.core.fetch('/devices', 'get', metadata);
    };
    /**
     * Retrieves details of a specific Device.
     *
     * @summary Retrieve a Device
     * @throws FetchError<400, types.RetrieveADeviceResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveADeviceResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveADeviceResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveADeviceResponse404> Not Found
     * @throws FetchError<429, types.RetrieveADeviceResponse429> Too Many Requests
     */
    SDK.prototype.retrieveADevice = function (metadata) {
        return this.core.fetch('/devices/{device-id}', 'get', metadata);
    };
    /**
     * Creates a new generic document.
     *
     * @summary Create a Generic Document
     * @throws FetchError<400, types.CreateADocumentResponse400> Bad Request
     * @throws FetchError<401, types.CreateADocumentResponse401> Unauthorized
     * @throws FetchError<403, types.CreateADocumentResponse403> Forbidden
     * @throws FetchError<404, types.CreateADocumentResponse404> Not Found
     * @throws FetchError<409, types.CreateADocumentResponse409> Conflict
     * @throws FetchError<422, types.CreateADocumentResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateADocumentResponse429> Too Many Requests
     */
    SDK.prototype.createADocument = function (body, metadata) {
        return this.core.fetch('/document/generics', 'post', body, metadata);
    };
    /**
     * Retrieves the details of a generic document that has been previously created.
     *
     * @summary Retrieve a Generic Document
     * @throws FetchError<400, types.RetrieveAGenericDocumentResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAGenericDocumentResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAGenericDocumentResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAGenericDocumentResponse404> Not Found
     * @throws FetchError<429, types.RetrieveAGenericDocumentResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAGenericDocument = function (metadata) {
        return this.core.fetch('/document/generics/{document-id}', 'get', metadata);
    };
    /**
     * Updates an existing generic document. Can only update `initiated` documents.
     *
     * @summary Update a Generic Document
     * @throws FetchError<400, types.UpdateADocumentResponse400> Bad Request
     * @throws FetchError<401, types.UpdateADocumentResponse401> Unauthorized
     * @throws FetchError<403, types.UpdateADocumentResponse403> Forbidden
     * @throws FetchError<404, types.UpdateADocumentResponse404> Not Found
     * @throws FetchError<409, types.UpdateADocumentResponse409> Conflict
     * @throws FetchError<422, types.UpdateADocumentResponse422> Unprocessable Entity
     * @throws FetchError<429, types.UpdateADocumentResponse429> Too Many Requests
     */
    SDK.prototype.updateADocument = function (body, metadata) {
        return this.core.fetch('/document/generics/{document-id}', 'patch', body, metadata);
    };
    /**
     * Submits a generic document for processing. Can only submit `initiated` documents with
     * files attached.
     *
     * @summary Submit a Generic Document
     * @throws FetchError<400, types.SubmitADocumentResponse400> Bad Request
     * @throws FetchError<401, types.SubmitADocumentResponse401> Unauthorized
     * @throws FetchError<403, types.SubmitADocumentResponse403> Forbidden
     * @throws FetchError<404, types.SubmitADocumentResponse404> Not Found
     * @throws FetchError<409, types.SubmitADocumentResponse409> Conflict
     * @throws FetchError<422, types.SubmitADocumentResponse422> Unprocessable Entity
     * @throws FetchError<429, types.SubmitADocumentResponse429> Too Many Requests
     */
    SDK.prototype.submitADocument = function (metadata) {
        return this.core.fetch('/document/generics/{document-id}/submit', 'post', metadata);
    };
    /**
     * Creates a new government ID document
     *
     * @summary Create a Government ID Document
     * @throws FetchError<400, types.CreateAGovernmentIdDocumentResponse400> Bad Request
     * @throws FetchError<401, types.CreateAGovernmentIdDocumentResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAGovernmentIdDocumentResponse403> Forbidden
     * @throws FetchError<404, types.CreateAGovernmentIdDocumentResponse404> Not Found
     * @throws FetchError<409, types.CreateAGovernmentIdDocumentResponse409> Conflict
     * @throws FetchError<422, types.CreateAGovernmentIdDocumentResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAGovernmentIdDocumentResponse429> Too Many Requests
     */
    SDK.prototype.createAGovernmentIdDocument = function (body, metadata) {
        return this.core.fetch('/document/government-ids', 'post', body, metadata);
    };
    /**
     * Retrieves the details of a government-id document that has been previously created.
     *
     * @summary Retrieve a Government Id Document
     * @throws FetchError<400, types.RetrieveAGovernmentIdDocumentResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAGovernmentIdDocumentResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAGovernmentIdDocumentResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAGovernmentIdDocumentResponse404> Not Found
     * @throws FetchError<429, types.RetrieveAGovernmentIdDocumentResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAGovernmentIdDocument = function (metadata) {
        return this.core.fetch('/document/government-ids/{document-id}', 'get', metadata);
    };
    /**
     * Updates an existing government ID document. Can only update `initiated` documents.
     *
     * @summary Update a Government ID Document
     * @throws FetchError<400, types.UpdateAGovernmentIdDocumentResponse400> Bad Request
     * @throws FetchError<401, types.UpdateAGovernmentIdDocumentResponse401> Unauthorized
     * @throws FetchError<403, types.UpdateAGovernmentIdDocumentResponse403> Forbidden
     * @throws FetchError<404, types.UpdateAGovernmentIdDocumentResponse404> Not Found
     * @throws FetchError<409, types.UpdateAGovernmentIdDocumentResponse409> Conflict
     * @throws FetchError<422, types.UpdateAGovernmentIdDocumentResponse422> Unprocessable Entity
     * @throws FetchError<429, types.UpdateAGovernmentIdDocumentResponse429> Too Many Requests
     */
    SDK.prototype.updateAGovernmentIdDocument = function (body, metadata) {
        return this.core.fetch('/document/government-ids/{document-id}', 'patch', body, metadata);
    };
    /**
     * Submits a government ID document for processing. Can only submit `initiated` documents
     * with photos of the ID attached.
     *
     * @summary Submit a Government ID Document
     * @throws FetchError<400, types.SubmitAGovernmentIdDocumentResponse400> Bad Request
     * @throws FetchError<401, types.SubmitAGovernmentIdDocumentResponse401> Unauthorized
     * @throws FetchError<403, types.SubmitAGovernmentIdDocumentResponse403> Forbidden
     * @throws FetchError<404, types.SubmitAGovernmentIdDocumentResponse404> Not Found
     * @throws FetchError<409, types.SubmitAGovernmentIdDocumentResponse409> Conflict
     * @throws FetchError<422, types.SubmitAGovernmentIdDocumentResponse422> Unprocessable Entity
     * @throws FetchError<429, types.SubmitAGovernmentIdDocumentResponse429> Too Many Requests
     */
    SDK.prototype.submitAGovernmentIdDocument = function (metadata) {
        return this.core.fetch('/document/government-ids/{document-id}/submit', 'post', metadata);
    };
    /**
     * Retrieves the details of a Document.
     *
     * @summary Retrieve a Document
     * @throws FetchError<400, types.RetrieveADocumentResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveADocumentResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveADocumentResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveADocumentResponse404> Not Found
     * @throws FetchError<429, types.RetrieveADocumentResponse429> Too Many Requests
     */
    SDK.prototype.retrieveADocument = function (metadata) {
        return this.core.fetch('/documents/{document-id}', 'get', metadata);
    };
    /**
     * Returns a list of your organization's events.
     *
     * @summary List all Events
     * @throws FetchError<400, types.ListAllEventsResponse400> Bad Request
     * @throws FetchError<401, types.ListAllEventsResponse401> Unauthorized
     * @throws FetchError<403, types.ListAllEventsResponse403> Forbidden
     * @throws FetchError<429, types.ListAllEventsResponse429> Too Many Requests
     */
    SDK.prototype.listAllEvents = function (metadata) {
        return this.core.fetch('/events', 'get', metadata);
    };
    /**
     * Retrieves the details of an existing event.
     *
     * @summary Retrieve an Event
     * @throws FetchError<400, types.RetrieveAnEventResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAnEventResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAnEventResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAnEventResponse404> Not Found
     * @throws FetchError<429, types.RetrieveAnEventResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAnEvent = function (metadata) {
        return this.core.fetch('/events/{event-id}', 'get', metadata);
    };
    /**
     * Create a Graph Query
     *
     * @summary Create a Graph Query
     * @throws FetchError<400, types.CreateAGraphQueryResponse400> Bad Request
     * @throws FetchError<401, types.CreateAGraphQueryResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAGraphQueryResponse403> Forbidden
     * @throws FetchError<404, types.CreateAGraphQueryResponse404> Not Found
     * @throws FetchError<409, types.CreateAGraphQueryResponse409> Conflict
     * @throws FetchError<422, types.CreateAGraphQueryResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAGraphQueryResponse429> Too Many Requests
     */
    SDK.prototype.createAGraphQuery = function (body, metadata) {
        return this.core.fetch('/graph-queries', 'post', body, metadata);
    };
    /**
     * Retrieve a Graph Query
     *
     * @summary Retrieve a Graph Query
     * @throws FetchError<400, types.RetrieveAGraphQueryResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAGraphQueryResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAGraphQueryResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAGraphQueryResponse404> Not Found
     * @throws FetchError<429, types.RetrieveAGraphQueryResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAGraphQuery = function (metadata) {
        return this.core.fetch('/graph-queries/{graph-query-id}', 'get', metadata);
    };
    /**
     * Returns a list of your organization's importers.
     *
     * @summary List all Importers
     * @throws FetchError<400, types.ListAllImportersResponse400> Bad Request
     * @throws FetchError<401, types.ListAllImportersResponse401> Unauthorized
     * @throws FetchError<403, types.ListAllImportersResponse403> Forbidden
     * @throws FetchError<429, types.ListAllImportersResponse429> Too Many Requests
     */
    SDK.prototype.listAllImporters = function (metadata) {
        return this.core.fetch('/importers', 'get', metadata);
    };
    /**
     * Retrieve the details of an existing importer.
     *
     * @summary Retrieve an Importer
     * @throws FetchError<400, types.RetrieveAnImporterResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAnImporterResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAnImporterResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAnImporterResponse404> Not Found
     * @throws FetchError<429, types.RetrieveAnImporterResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAnImporter = function (metadata) {
        return this.core.fetch('/importers/{importer-id}', 'get', metadata);
    };
    /**
     * Bulk import accounts by uploading a CSV file.
     *
     * Each row should be the details for a new account. The columns we allow are:
     *   - reference_id
     *   - name_first
     *   - name_middle
     *   - name_last
     *   - birthdate
     *   - social_security_number
     *   - tags
     *
     * @summary Import Accounts
     * @throws FetchError<400, types.ImportAnAccountResponse400> Bad Request
     * @throws FetchError<401, types.ImportAnAccountResponse401> Unauthorized
     * @throws FetchError<403, types.ImportAnAccountResponse403> Forbidden
     * @throws FetchError<409, types.ImportAnAccountResponse409> Conflict
     * @throws FetchError<422, types.ImportAnAccountResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ImportAnAccountResponse429> Too Many Requests
     */
    SDK.prototype.importAnAccount = function (body, metadata) {
        return this.core.fetch('/importer/accounts', 'post', body, metadata);
    };
    /**
     * Bulk import email address List Items by uploading a CSV file.
     *
     * Each row should be the details for a new List Item. The columns we allow are:
     *   - value
     *   - match_type (either 'email_address' or 'domain')
     *
     * A match_type of 'email_address' will need to match the entire email address of an
     * individual, while a match_type of 'domain' will match on the email address domain of an
     * individual (i.e. all email addresses with domain 'gmail.com').
     *
     * @summary Import Email Address Lists
     * @throws FetchError<400, types.ImportEmailAddressListsResponse400> Bad Request
     * @throws FetchError<401, types.ImportEmailAddressListsResponse401> Unauthorized
     * @throws FetchError<403, types.ImportEmailAddressListsResponse403> Forbidden
     * @throws FetchError<409, types.ImportEmailAddressListsResponse409> Conflict
     * @throws FetchError<422, types.ImportEmailAddressListsResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ImportEmailAddressListsResponse429> Too Many Requests
     */
    SDK.prototype.importEmailAddressLists = function (body, metadata) {
        return this.core.fetch('/importer/list-item/email-addresses', 'post', body, metadata);
    };
    /**
     * Bulk import face List Items by uploading image files.
     *
     * Each file being a new List Item. We recommend uploading max 50 images (with average
     * image size of 500KB) at a time.
     *
     * IMPORTANT: You must ensure proper consent for processing of biometrics is in place prior
     * to using face Lists.
     *
     * @summary Import Face Lists
     * @throws FetchError<400, types.ImportFaceListsResponse400> Bad Request
     * @throws FetchError<401, types.ImportFaceListsResponse401> Unauthorized
     * @throws FetchError<403, types.ImportFaceListsResponse403> Forbidden
     * @throws FetchError<409, types.ImportFaceListsResponse409> Conflict
     * @throws FetchError<422, types.ImportFaceListsResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ImportFaceListsResponse429> Too Many Requests
     */
    SDK.prototype.importFaceLists = function (body, metadata) {
        return this.core.fetch('/importer/list-item/faces', 'post', body, metadata);
    };
    /**
     * Bulk import geolocation List Items by uploading a CSV file.
     *
     * Each row should be the details for a new List Item. The columns we allow are:
     *   - latitude
     *   - longitude
     *   - radius_meters
     *
     * @summary Import Geolocation Lists
     * @throws FetchError<400, types.ImportGeolocationListsResponse400> Bad Request
     * @throws FetchError<401, types.ImportGeolocationListsResponse401> Unauthorized
     * @throws FetchError<403, types.ImportGeolocationListsResponse403> Forbidden
     * @throws FetchError<409, types.ImportGeolocationListsResponse409> Conflict
     * @throws FetchError<422, types.ImportGeolocationListsResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ImportGeolocationListsResponse429> Too Many Requests
     */
    SDK.prototype.importGeolocationLists = function (body, metadata) {
        return this.core.fetch('/importer/list-item/geolocations', 'post', body, metadata);
    };
    /**
     * Bulk import government ID number List Items by uploading a CSV file.
     *
     * Each row should be the details for a new List Item. The columns we allow are:
     *   - id_number
     *   - id_class
     *
     * Common values for id_class include `pp` for passport and `dl` for driver license. Please
     * contact us or reach out to [<EMAIL>](mailto:<EMAIL>) if
     * you need help getting id_class values.
     *
     * @summary Import Government ID Number Lists
     * @throws FetchError<400, types.ImportGovernmentIdNumberListsResponse400> Bad Request
     * @throws FetchError<401, types.ImportGovernmentIdNumberListsResponse401> Unauthorized
     * @throws FetchError<403, types.ImportGovernmentIdNumberListsResponse403> Forbidden
     * @throws FetchError<409, types.ImportGovernmentIdNumberListsResponse409> Conflict
     * @throws FetchError<422, types.ImportGovernmentIdNumberListsResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ImportGovernmentIdNumberListsResponse429> Too Many Requests
     */
    SDK.prototype.importGovernmentIdNumberLists = function (body, metadata) {
        return this.core.fetch('/importer/list-item/government-id-numbers', 'post', body, metadata);
    };
    /**
     * Bulk import IP address List Items by uploading a CSV file.
     *
     * Each row should be the details for a new List Item. The columns we allow are:
     *   - value
     *
     * Both IPv4 and IPv6 are supported.
     *
     * @summary Import IP Address Lists
     * @throws FetchError<400, types.ImportIpAddressListsResponse400> Bad Request
     * @throws FetchError<401, types.ImportIpAddressListsResponse401> Unauthorized
     * @throws FetchError<403, types.ImportIpAddressListsResponse403> Forbidden
     * @throws FetchError<409, types.ImportIpAddressListsResponse409> Conflict
     * @throws FetchError<422, types.ImportIpAddressListsResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ImportIpAddressListsResponse429> Too Many Requests
     */
    SDK.prototype.importIpAddressLists = function (body, metadata) {
        return this.core.fetch('/importer/list-item/ip-addresses', 'post', body, metadata);
    };
    /**
     * Bulk import name List Items by uploading a CSV file.
     *
     * Each row should be the details for a new list item. The columns we allow are:
     *   - name_first
     *   - name_last
     *
     * @summary Import Name Lists
     * @throws FetchError<400, types.ImportNameListsResponse400> Bad Request
     * @throws FetchError<401, types.ImportNameListsResponse401> Unauthorized
     * @throws FetchError<403, types.ImportNameListsResponse403> Forbidden
     * @throws FetchError<409, types.ImportNameListsResponse409> Conflict
     * @throws FetchError<422, types.ImportNameListsResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ImportNameListsResponse429> Too Many Requests
     */
    SDK.prototype.importNameLists = function (body, metadata) {
        return this.core.fetch('/importer/list-item/names', 'post', body, metadata);
    };
    /**
     * Bulk import phone number List Items by uploading a CSV file.
     *
     * Each row should be the details for a new list item. The columns we allow are:
     *   - value
     *
     * @summary Import Phone Number Lists
     * @throws FetchError<400, types.ImportPhoneNumberListsResponse400> Bad Request
     * @throws FetchError<401, types.ImportPhoneNumberListsResponse401> Unauthorized
     * @throws FetchError<403, types.ImportPhoneNumberListsResponse403> Forbidden
     * @throws FetchError<409, types.ImportPhoneNumberListsResponse409> Conflict
     * @throws FetchError<422, types.ImportPhoneNumberListsResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ImportPhoneNumberListsResponse429> Too Many Requests
     */
    SDK.prototype.importPhoneNumberLists = function (body, metadata) {
        return this.core.fetch('/importer/list-item/phone-numbers', 'post', body, metadata);
    };
    /**
     * Returns a list of your organization's inquiries.
     *
     * Note that this endpoint aggregates inquiries across all <<glossary:inquiry
     * template>>(s). See [Pagination](https://docs.withpersona.com/reference/pagination) for
     * more details about handling the response.
     *
     * @summary List all Inquiries
     * @throws FetchError<400, types.ListAllInquiriesResponse400> Bad Request
     * @throws FetchError<401, types.ListAllInquiriesResponse401> Unauthorized
     * @throws FetchError<403, types.ListAllInquiriesResponse403> Forbidden
     * @throws FetchError<429, types.ListAllInquiriesResponse429> Too Many Requests
     */
    SDK.prototype.listAllInquiries = function (metadata) {
        return this.core.fetch('/inquiries', 'get', metadata);
    };
    /**
     * Creates a new inquiry with optional pre-filled attributes.
     *
     * See [Sessions](https://docs.withpersona.com/docs/inquiry-sessions) for how to continue
     * the inquiry in [Embedded Flow](https://docs.withpersona.com/docs/embedded-flow) or
     * [Hosted Flow](https://docs.withpersona.com/docs/hosted-flow).
     *
     * @summary Create an Inquiry
     * @throws FetchError<400, types.CreateAnInquiryResponse400> Bad Request
     * @throws FetchError<401, types.CreateAnInquiryResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAnInquiryResponse403> Forbidden
     * @throws FetchError<404, types.CreateAnInquiryResponse404> Not Found
     * @throws FetchError<409, types.CreateAnInquiryResponse409> Conflict
     * @throws FetchError<422, types.CreateAnInquiryResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAnInquiryResponse429> Too Many Requests
     */
    SDK.prototype.createAnInquiry = function (body, metadata) {
        return this.core.fetch('/inquiries', 'post', body, metadata);
    };
    /**
     * Permanently deletes personally identifiable information (PII) for an Inquiry and all
     * associated Verifications, Reports, or other Persona resources. The response indicates a
     * successful redaction of the Inquiry. Redaction of the Inquiry's associated child objects
     * are done asynchronously and may take some time before all associated child objects are
     * fully redacted. **This action cannot be undone**.
     *
     * This endpoint can be used to comply with privacy regulations such as GDPR / CCPA or to
     * enforce data privacy.
     *
     * @summary Redact an Inquiry
     * @throws FetchError<400, types.RedactAnInquiryResponse400> Bad Request
     * @throws FetchError<401, types.RedactAnInquiryResponse401> Unauthorized
     * @throws FetchError<403, types.RedactAnInquiryResponse403> Forbidden
     * @throws FetchError<404, types.RedactAnInquiryResponse404> Not Found
     * @throws FetchError<409, types.RedactAnInquiryResponse409> Conflict
     * @throws FetchError<422, types.RedactAnInquiryResponse422> Unprocessable Entity
     * @throws FetchError<429, types.RedactAnInquiryResponse429> Too Many Requests
     */
    SDK.prototype.redactAnInquiry = function (metadata) {
        return this.core.fetch('/inquiries/{inquiry-id}', 'delete', metadata);
    };
    /**
     * Retrieves the details of an existing Inquiry.
     *
     * In the [Embedded Flow](https://docs.withpersona.com/docs/embedded-flow), the
     * `inquiry-id` is the first parameter of the onStart callback. In the [Hosted
     * Flow](https://docs.withpersona.com/docs/hosted-flow), the `inquiry-id` is a query
     * parameter in the onComplete callback.
     *
     * Template information will be found in `data.relationships.inquiry-template` if the
     * inquiry is a Dynamic Flow inquiry, and in `data.relationships.template` if the inquiry
     * is a Legacy 2.0 inquiry. For more information, see [Dynamic Flow vs. Legacy
     * Templates](https://docs.withpersona.com/docs/inquiry-templates#dynamic-flow-vs-legacy-templates).
     *
     * @summary Retrieve an Inquiry
     * @throws FetchError<400, types.RetrieveAnInquiryResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAnInquiryResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAnInquiryResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAnInquiryResponse404> Not Found
     * @throws FetchError<429, types.RetrieveAnInquiryResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAnInquiry = function (metadata) {
        return this.core.fetch('/inquiries/{inquiry-id}', 'get', metadata);
    };
    SDK.prototype.updateAnInquiry = function (body, metadata) {
        return this.core.fetch('/inquiries/{inquiry-id}', 'patch', body, metadata);
    };
    SDK.prototype.inquiriesAddTag = function (body, metadata) {
        return this.core.fetch('/inquiries/{inquiry-id}/add-tag', 'post', body, metadata);
    };
    SDK.prototype.approveAnInquiry = function (body, metadata) {
        return this.core.fetch('/inquiries/{inquiry-id}/approve', 'post', body, metadata);
    };
    SDK.prototype.declineAnInquiry = function (body, metadata) {
        return this.core.fetch('/inquiries/{inquiry-id}/decline', 'post', body, metadata);
    };
    /**
     * Expires an Inquiry and all sessions on the Inquiry. Cancels any pending Verifications on
     * the inquiry.
     *
     * The Inquiry can still be resumed after expiry.
     *
     * @summary Expire an Inquiry
     * @throws FetchError<400, types.ExpireAnInquiryResponse400> Bad Request
     * @throws FetchError<401, types.ExpireAnInquiryResponse401> Unauthorized
     * @throws FetchError<403, types.ExpireAnInquiryResponse403> Forbidden
     * @throws FetchError<404, types.ExpireAnInquiryResponse404> Not Found
     * @throws FetchError<409, types.ExpireAnInquiryResponse409> Conflict
     * @throws FetchError<422, types.ExpireAnInquiryResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ExpireAnInquiryResponse429> Too Many Requests
     */
    SDK.prototype.expireAnInquiry = function (metadata) {
        return this.core.fetch('/inquiries/{inquiry-id}/expire', 'post', metadata);
    };
    SDK.prototype.generateAOneTimeLink = function (body, metadata) {
        return this.core.fetch('/inquiries/{inquiry-id}/generate-one-time-link', 'post', body, metadata);
    };
    /**
     * Prints an Inquiry as PDF.
     *
     * @summary Print Inquiry PDF
     * @throws FetchError<400, types.PrintAnInquiryPdfResponse400> Bad Request
     * @throws FetchError<401, types.PrintAnInquiryPdfResponse401> Unauthorized
     * @throws FetchError<403, types.PrintAnInquiryPdfResponse403> Forbidden
     * @throws FetchError<404, types.PrintAnInquiryPdfResponse404> Not Found
     * @throws FetchError<409, types.PrintAnInquiryPdfResponse409> Conflict
     * @throws FetchError<429, types.PrintAnInquiryPdfResponse429> Too Many Requests
     */
    SDK.prototype.printAnInquiryPdf = function (metadata) {
        return this.core.fetch('/inquiries/{inquiry-id}/print', 'get', metadata);
    };
    SDK.prototype.inquiriesRemoveTag = function (body, metadata) {
        return this.core.fetch('/inquiries/{inquiry-id}/remove-tag', 'post', body, metadata);
    };
    /**
     * Creates a new inquiry session and returns `meta.session-token`. If the inquiry's status
     * is `expired`, changes the status to `pending`. The `session-token` must be included when
     * loading the inquiry flow if the inquiry's status is `pending`. For more information, see
     * [Resuming Inquiries](https://docs.withpersona.com/docs/inquiries-resuming-inquiries).
     *
     * @summary Resume an Inquiry
     * @throws FetchError<400, types.ResumeAnInquiryResponse400> Bad Request
     * @throws FetchError<401, types.ResumeAnInquiryResponse401> Unauthorized
     * @throws FetchError<403, types.ResumeAnInquiryResponse403> Forbidden
     * @throws FetchError<404, types.ResumeAnInquiryResponse404> Not Found
     * @throws FetchError<409, types.ResumeAnInquiryResponse409> Conflict
     * @throws FetchError<422, types.ResumeAnInquiryResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ResumeAnInquiryResponse429> Too Many Requests
     */
    SDK.prototype.resumeAnInquiry = function (metadata) {
        return this.core.fetch('/inquiries/{inquiry-id}/resume', 'post', metadata);
    };
    SDK.prototype.inquiriesSetAllTags = function (body, metadata) {
        return this.core.fetch('/inquiries/{inquiry-id}/set-tags', 'post', body, metadata);
    };
    /**
     * Performs a series of simulated actions on a Sandbox Inquiry.
     *
     * @summary Perform Simulate Actions
     * @throws FetchError<400, types.InquiriesPerformSimulateActionsResponse400> Bad Request
     * @throws FetchError<401, types.InquiriesPerformSimulateActionsResponse401> Unauthorized
     * @throws FetchError<403, types.InquiriesPerformSimulateActionsResponse403> Forbidden
     * @throws FetchError<404, types.InquiriesPerformSimulateActionsResponse404> Not Found
     * @throws FetchError<409, types.InquiriesPerformSimulateActionsResponse409> Conflict
     * @throws FetchError<422, types.InquiriesPerformSimulateActionsResponse422> Unprocessable Entity
     * @throws FetchError<429, types.InquiriesPerformSimulateActionsResponse429> Too Many Requests
     */
    SDK.prototype.inquiriesPerformSimulateActions = function (body, metadata) {
        return this.core.fetch('/inquiries/{inquiry-id}/perform-simulate-actions', 'post', body, metadata);
    };
    /**
     * Sets simulated inputs on a Sandbox Inquiry.
     *
     * @summary Set Simulated Data
     * @throws FetchError<400, types.InquiriesSetSimulateStubsResponse400> Bad Request
     * @throws FetchError<401, types.InquiriesSetSimulateStubsResponse401> Unauthorized
     * @throws FetchError<403, types.InquiriesSetSimulateStubsResponse403> Forbidden
     * @throws FetchError<404, types.InquiriesSetSimulateStubsResponse404> Not Found
     * @throws FetchError<409, types.InquiriesSetSimulateStubsResponse409> Conflict
     * @throws FetchError<422, types.InquiriesSetSimulateStubsResponse422> Unprocessable Entity
     * @throws FetchError<429, types.InquiriesSetSimulateStubsResponse429> Too Many Requests
     */
    SDK.prototype.inquiriesSetSimulateStubs = function (body, metadata) {
        return this.core.fetch('/inquiries/{inquiry-id}/set-simulate-stubs', 'patch', body, metadata);
    };
    /**
     * Retrieves a list of Inquiry Sessions.
     *
     * @summary List all Inquiry Sessions
     * @throws FetchError<400, types.ListAllInquirySessionsResponse400> Bad Request
     * @throws FetchError<401, types.ListAllInquirySessionsResponse401> Unauthorized
     * @throws FetchError<403, types.ListAllInquirySessionsResponse403> Forbidden
     * @throws FetchError<429, types.ListAllInquirySessionsResponse429> Too Many Requests
     */
    SDK.prototype.listAllInquirySessions = function (metadata) {
        return this.core.fetch('/inquiry-sessions', 'get', metadata);
    };
    /**
     * Creates a new Inquiry Session. By default, we only allow up to 25 sessions per Inquiry.
     *
     * @summary Create an Inquiry Session
     * @throws FetchError<400, types.CreateAnInquirySessionResponse400> Bad Request
     * @throws FetchError<401, types.CreateAnInquirySessionResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAnInquirySessionResponse403> Forbidden
     * @throws FetchError<404, types.CreateAnInquirySessionResponse404> Not Found
     * @throws FetchError<409, types.CreateAnInquirySessionResponse409> Conflict
     * @throws FetchError<422, types.CreateAnInquirySessionResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAnInquirySessionResponse429> Too Many Requests
     */
    SDK.prototype.createAnInquirySession = function (body, metadata) {
        return this.core.fetch('/inquiry-sessions', 'post', body, metadata);
    };
    /**
     * Expires all active Inquiry Sessions for a given set of Inquiries.
     *
     * @summary Expire Inquiry Sessions
     * @throws FetchError<400, types.ExpireInquirySessionsResponse400> Bad Request
     * @throws FetchError<401, types.ExpireInquirySessionsResponse401> Unauthorized
     * @throws FetchError<403, types.ExpireInquirySessionsResponse403> Forbidden
     * @throws FetchError<409, types.ExpireInquirySessionsResponse409> Conflict
     * @throws FetchError<422, types.ExpireInquirySessionsResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ExpireInquirySessionsResponse429> Too Many Requests
     */
    SDK.prototype.expireInquirySessions = function (body, metadata) {
        return this.core.fetch('/inquiry-sessions/expire-all', 'post', body, metadata);
    };
    /**
     * Retrieves details of a specific Inquiry Session.
     *
     * @summary Retrieve an Inquiry Session
     * @throws FetchError<400, types.RetrieveAnInquirySessionResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAnInquirySessionResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAnInquirySessionResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAnInquirySessionResponse404> Not Found
     * @throws FetchError<429, types.RetrieveAnInquirySessionResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAnInquirySession = function (metadata) {
        return this.core.fetch('/inquiry-sessions/{inquiry-session-id}', 'get', metadata);
    };
    /**
     * Expires a specific Inquiry Session.
     *
     * @summary Expire an Inquiry Session
     * @throws FetchError<400, types.ExpireAnInquirySessionResponse400> Bad Request
     * @throws FetchError<401, types.ExpireAnInquirySessionResponse401> Unauthorized
     * @throws FetchError<403, types.ExpireAnInquirySessionResponse403> Forbidden
     * @throws FetchError<404, types.ExpireAnInquirySessionResponse404> Not Found
     * @throws FetchError<409, types.ExpireAnInquirySessionResponse409> Conflict
     * @throws FetchError<422, types.ExpireAnInquirySessionResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ExpireAnInquirySessionResponse429> Too Many Requests
     */
    SDK.prototype.expireAnInquirySession = function (metadata) {
        return this.core.fetch('/inquiry-sessions/{inquiry-session-id}/expire', 'post', metadata);
    };
    /**
     * Generates a one-time link for a specific Inquiry Session.
     *
     * @summary Generate a one-time link for Inquiry Session
     * @throws FetchError<400, types.GenerateAOneTimeLinkForAnInquirySessionResponse400> Bad Request
     * @throws FetchError<401, types.GenerateAOneTimeLinkForAnInquirySessionResponse401> Unauthorized
     * @throws FetchError<403, types.GenerateAOneTimeLinkForAnInquirySessionResponse403> Forbidden
     * @throws FetchError<404, types.GenerateAOneTimeLinkForAnInquirySessionResponse404> Not Found
     * @throws FetchError<409, types.GenerateAOneTimeLinkForAnInquirySessionResponse409> Conflict
     * @throws FetchError<422, types.GenerateAOneTimeLinkForAnInquirySessionResponse422> Unprocessable Entity
     * @throws FetchError<429, types.GenerateAOneTimeLinkForAnInquirySessionResponse429> Too Many Requests
     */
    SDK.prototype.generateAOneTimeLinkForAnInquirySession = function (metadata) {
        return this.core.fetch('/inquiry-sessions/{inquiry-session-id}/generate-one-time-link', 'post', metadata);
    };
    /**
     * Create a new item in a <<glossary:list>>.
     *
     * @summary Create a Browser Fingerprint List Item
     * @throws FetchError<400, types.CreateABrowserFingerprintListItemResponse400> Bad Request
     * @throws FetchError<401, types.CreateABrowserFingerprintListItemResponse401> Unauthorized
     * @throws FetchError<403, types.CreateABrowserFingerprintListItemResponse403> Forbidden
     * @throws FetchError<404, types.CreateABrowserFingerprintListItemResponse404> Not Found
     * @throws FetchError<409, types.CreateABrowserFingerprintListItemResponse409> Conflict
     * @throws FetchError<422, types.CreateABrowserFingerprintListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateABrowserFingerprintListItemResponse429> Too Many Requests
     */
    SDK.prototype.createABrowserFingerprintListItem = function (body, metadata) {
        return this.core.fetch('/list-item/browser-fingerprints', 'post', body, metadata);
    };
    /**
     * Retrieves the details of an existing browser fingerprint List Item.
     *
     * @summary Retrieve a Browser Fingerprint List Item
     * @throws FetchError<400, types.RetrieveABrowserFingerprintListItemResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveABrowserFingerprintListItemResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveABrowserFingerprintListItemResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveABrowserFingerprintListItemResponse404> Not Found
     * @throws FetchError<409, types.RetrieveABrowserFingerprintListItemResponse409> Conflict
     * @throws FetchError<422, types.RetrieveABrowserFingerprintListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.RetrieveABrowserFingerprintListItemResponse429> Too Many Requests
     */
    SDK.prototype.retrieveABrowserFingerprintListItem = function (metadata) {
        return this.core.fetch('/list-item/browser-fingerprints/{list-item-id}', 'get', metadata);
    };
    /**
     * Archived items are not matched against new inquiries.
     *
     * @summary Archive a Browser Fingerprint List Item
     * @throws FetchError<400, types.ArchiveABrowserFingerprintListItemResponse400> Bad Request
     * @throws FetchError<401, types.ArchiveABrowserFingerprintListItemResponse401> Unauthorized
     * @throws FetchError<403, types.ArchiveABrowserFingerprintListItemResponse403> Forbidden
     * @throws FetchError<404, types.ArchiveABrowserFingerprintListItemResponse404> Not Found
     * @throws FetchError<409, types.ArchiveABrowserFingerprintListItemResponse409> Conflict
     * @throws FetchError<422, types.ArchiveABrowserFingerprintListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ArchiveABrowserFingerprintListItemResponse429> Too Many Requests
     */
    SDK.prototype.archiveABrowserFingerprintListItem = function (metadata) {
        return this.core.fetch('/list-item/browser-fingerprints/{list-item-id}', 'delete', metadata);
    };
    /**
     * Create a new item in a <<glossary:list>>.
     *
     * @summary Create a Country List Item
     * @throws FetchError<400, types.CreateACountryListItemResponse400> Bad Request
     * @throws FetchError<401, types.CreateACountryListItemResponse401> Unauthorized
     * @throws FetchError<403, types.CreateACountryListItemResponse403> Forbidden
     * @throws FetchError<404, types.CreateACountryListItemResponse404> Not Found
     * @throws FetchError<409, types.CreateACountryListItemResponse409> Conflict
     * @throws FetchError<422, types.CreateACountryListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateACountryListItemResponse429> Too Many Requests
     */
    SDK.prototype.createACountryListItem = function (body, metadata) {
        return this.core.fetch('/list-item/countries', 'post', body, metadata);
    };
    /**
     * Retrieves the details of an existing country List Item.
     *
     * @summary Retrieve a Country List Item
     * @throws FetchError<400, types.RetrieveACountryListItemResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveACountryListItemResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveACountryListItemResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveACountryListItemResponse404> Not Found
     * @throws FetchError<409, types.RetrieveACountryListItemResponse409> Conflict
     * @throws FetchError<422, types.RetrieveACountryListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.RetrieveACountryListItemResponse429> Too Many Requests
     */
    SDK.prototype.retrieveACountryListItem = function (metadata) {
        return this.core.fetch('/list-item/countries/{list-item-id}', 'get', metadata);
    };
    /**
     * Archived items are not matched against new inquiries.
     *
     * @summary Archive a Country List Item
     * @throws FetchError<400, types.ArchiveACountryListItemResponse400> Bad Request
     * @throws FetchError<401, types.ArchiveACountryListItemResponse401> Unauthorized
     * @throws FetchError<403, types.ArchiveACountryListItemResponse403> Forbidden
     * @throws FetchError<404, types.ArchiveACountryListItemResponse404> Not Found
     * @throws FetchError<409, types.ArchiveACountryListItemResponse409> Conflict
     * @throws FetchError<422, types.ArchiveACountryListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ArchiveACountryListItemResponse429> Too Many Requests
     */
    SDK.prototype.archiveACountryListItem = function (metadata) {
        return this.core.fetch('/list-item/countries/{list-item-id}', 'delete', metadata);
    };
    /**
     * Create a new item in a <<glossary:list>>.
     *
     * @summary Create a Device Fingerprint List Item
     * @throws FetchError<400, types.CreateADeviceFingerprintListItemResponse400> Bad Request
     * @throws FetchError<401, types.CreateADeviceFingerprintListItemResponse401> Unauthorized
     * @throws FetchError<403, types.CreateADeviceFingerprintListItemResponse403> Forbidden
     * @throws FetchError<404, types.CreateADeviceFingerprintListItemResponse404> Not Found
     * @throws FetchError<409, types.CreateADeviceFingerprintListItemResponse409> Conflict
     * @throws FetchError<422, types.CreateADeviceFingerprintListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateADeviceFingerprintListItemResponse429> Too Many Requests
     */
    SDK.prototype.createADeviceFingerprintListItem = function (body, metadata) {
        return this.core.fetch('/list-item/device-fingerprints', 'post', body, metadata);
    };
    /**
     * Retrieves the details of an existing device fingerprint List Item.
     *
     * @summary Retrieve a Device Fingerprint List Item
     * @throws FetchError<400, types.RetrieveADeviceFingerprintListItemResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveADeviceFingerprintListItemResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveADeviceFingerprintListItemResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveADeviceFingerprintListItemResponse404> Not Found
     * @throws FetchError<409, types.RetrieveADeviceFingerprintListItemResponse409> Conflict
     * @throws FetchError<422, types.RetrieveADeviceFingerprintListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.RetrieveADeviceFingerprintListItemResponse429> Too Many Requests
     */
    SDK.prototype.retrieveADeviceFingerprintListItem = function (metadata) {
        return this.core.fetch('/list-item/device-fingerprints/{list-item-id}', 'get', metadata);
    };
    /**
     * Archived items are not matched against new inquiries.
     *
     * @summary Archive a Device Fingerprint List Item
     * @throws FetchError<400, types.ArchiveADeviceFingerprintListItemResponse400> Bad Request
     * @throws FetchError<401, types.ArchiveADeviceFingerprintListItemResponse401> Unauthorized
     * @throws FetchError<403, types.ArchiveADeviceFingerprintListItemResponse403> Forbidden
     * @throws FetchError<404, types.ArchiveADeviceFingerprintListItemResponse404> Not Found
     * @throws FetchError<409, types.ArchiveADeviceFingerprintListItemResponse409> Conflict
     * @throws FetchError<422, types.ArchiveADeviceFingerprintListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ArchiveADeviceFingerprintListItemResponse429> Too Many Requests
     */
    SDK.prototype.archiveADeviceFingerprintListItem = function (metadata) {
        return this.core.fetch('/list-item/device-fingerprints/{list-item-id}', 'delete', metadata);
    };
    /**
     * Create a new item in a <<glossary:list>>.
     *
     * @summary Create an Email Address List Item
     * @throws FetchError<400, types.CreateAnEmailAddressListItemResponse400> Bad Request
     * @throws FetchError<401, types.CreateAnEmailAddressListItemResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAnEmailAddressListItemResponse403> Forbidden
     * @throws FetchError<404, types.CreateAnEmailAddressListItemResponse404> Not Found
     * @throws FetchError<409, types.CreateAnEmailAddressListItemResponse409> Conflict
     * @throws FetchError<422, types.CreateAnEmailAddressListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAnEmailAddressListItemResponse429> Too Many Requests
     */
    SDK.prototype.createAnEmailAddressListItem = function (body, metadata) {
        return this.core.fetch('/list-item/email-addresses', 'post', body, metadata);
    };
    /**
     * Retrieves the details of an existing email address List Item.
     *
     * @summary Retrieve an Email Address List Item
     * @throws FetchError<400, types.RetrieveAnEmailAddressListItemResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAnEmailAddressListItemResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAnEmailAddressListItemResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAnEmailAddressListItemResponse404> Not Found
     * @throws FetchError<409, types.RetrieveAnEmailAddressListItemResponse409> Conflict
     * @throws FetchError<422, types.RetrieveAnEmailAddressListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.RetrieveAnEmailAddressListItemResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAnEmailAddressListItem = function (metadata) {
        return this.core.fetch('/list-item/email-addresses/{list-item-id}', 'get', metadata);
    };
    /**
     * Archived items are not matched against new inquiries.
     *
     * @summary Archive an Email Address List Item
     * @throws FetchError<400, types.ArchiveAnEmailAddressListItemResponse400> Bad Request
     * @throws FetchError<401, types.ArchiveAnEmailAddressListItemResponse401> Unauthorized
     * @throws FetchError<403, types.ArchiveAnEmailAddressListItemResponse403> Forbidden
     * @throws FetchError<404, types.ArchiveAnEmailAddressListItemResponse404> Not Found
     * @throws FetchError<409, types.ArchiveAnEmailAddressListItemResponse409> Conflict
     * @throws FetchError<422, types.ArchiveAnEmailAddressListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ArchiveAnEmailAddressListItemResponse429> Too Many Requests
     */
    SDK.prototype.archiveAnEmailAddressListItem = function (metadata) {
        return this.core.fetch('/list-item/email-addresses/{list-item-id}', 'delete', metadata);
    };
    /**
     * Create a new item in a <<glossary:list>>.
     *
     * IMPORTANT: You must ensure proper consent for processing of biometrics is in place prior
     * to using face Lists.
     *
     * @summary Create a Face List Item
     * @throws FetchError<400, types.CreateAFaceListItemResponse400> Bad Request
     * @throws FetchError<401, types.CreateAFaceListItemResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAFaceListItemResponse403> Forbidden
     * @throws FetchError<404, types.CreateAFaceListItemResponse404> Not Found
     * @throws FetchError<409, types.CreateAFaceListItemResponse409> Conflict
     * @throws FetchError<422, types.CreateAFaceListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAFaceListItemResponse429> Too Many Requests
     */
    SDK.prototype.createAFaceListItem = function (body, metadata) {
        return this.core.fetch('/list-item/faces', 'post', body, metadata);
    };
    /**
     * Retrieves the details of an existing face List Item.
     *
     * IMPORTANT: You must ensure proper consent for processing of biometrics is in place prior
     * to using face Lists.
     *
     * @summary Retrieve a Face List Item
     * @throws FetchError<400, types.RetrieveAFaceListItemResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAFaceListItemResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAFaceListItemResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAFaceListItemResponse404> Not Found
     * @throws FetchError<409, types.RetrieveAFaceListItemResponse409> Conflict
     * @throws FetchError<422, types.RetrieveAFaceListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.RetrieveAFaceListItemResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAFaceListItem = function (metadata) {
        return this.core.fetch('/list-item/faces/{list-item-id}', 'get', metadata);
    };
    /**
     * Archived items are not matched against new inquiries.
     *
     * IMPORTANT: You must ensure proper consent for processing of biometrics is in place prior
     * to using face Lists.
     *
     * @summary Archive a Face List Item
     * @throws FetchError<400, types.ArchiveAFaceListItemResponse400> Bad Request
     * @throws FetchError<401, types.ArchiveAFaceListItemResponse401> Unauthorized
     * @throws FetchError<403, types.ArchiveAFaceListItemResponse403> Forbidden
     * @throws FetchError<404, types.ArchiveAFaceListItemResponse404> Not Found
     * @throws FetchError<409, types.ArchiveAFaceListItemResponse409> Conflict
     * @throws FetchError<422, types.ArchiveAFaceListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ArchiveAFaceListItemResponse429> Too Many Requests
     */
    SDK.prototype.archiveAFaceListItem = function (metadata) {
        return this.core.fetch('/list-item/faces/{list-item-id}', 'delete', metadata);
    };
    /**
     * Create a new item in a <<glossary:list>>.
     *
     * @summary Create a Field List Item
     * @throws FetchError<400, types.CreateAFieldListItemResponse400> Bad Request
     * @throws FetchError<401, types.CreateAFieldListItemResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAFieldListItemResponse403> Forbidden
     * @throws FetchError<404, types.CreateAFieldListItemResponse404> Not Found
     * @throws FetchError<409, types.CreateAFieldListItemResponse409> Conflict
     * @throws FetchError<422, types.CreateAFieldListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAFieldListItemResponse429> Too Many Requests
     */
    SDK.prototype.createAFieldListItem = function (body, metadata) {
        return this.core.fetch('/list-item/fields', 'post', body, metadata);
    };
    /**
     * Retrieves the details of an existing field List Item.
     *
     * @summary Retrieve a Field List Item
     * @throws FetchError<400, types.RetrieveAFieldListItemResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAFieldListItemResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAFieldListItemResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAFieldListItemResponse404> Not Found
     * @throws FetchError<409, types.RetrieveAFieldListItemResponse409> Conflict
     * @throws FetchError<422, types.RetrieveAFieldListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.RetrieveAFieldListItemResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAFieldListItem = function (metadata) {
        return this.core.fetch('/list-item/fields/{list-item-id}', 'get', metadata);
    };
    /**
     * Archived items are not matched against new inquiries.
     *
     * @summary Archive a Field List Item
     * @throws FetchError<400, types.ArchiveAFieldListItemResponse400> Bad Request
     * @throws FetchError<401, types.ArchiveAFieldListItemResponse401> Unauthorized
     * @throws FetchError<403, types.ArchiveAFieldListItemResponse403> Forbidden
     * @throws FetchError<404, types.ArchiveAFieldListItemResponse404> Not Found
     * @throws FetchError<409, types.ArchiveAFieldListItemResponse409> Conflict
     * @throws FetchError<422, types.ArchiveAFieldListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ArchiveAFieldListItemResponse429> Too Many Requests
     */
    SDK.prototype.archiveAFieldListItem = function (metadata) {
        return this.core.fetch('/list-item/fields/{list-item-id}', 'delete', metadata);
    };
    /**
     * Create a new item in a <<glossary:list>>.
     *
     * @summary Create a Geolocation List Item
     * @throws FetchError<400, types.CreateAGeolocationListItemResponse400> Bad Request
     * @throws FetchError<401, types.CreateAGeolocationListItemResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAGeolocationListItemResponse403> Forbidden
     * @throws FetchError<404, types.CreateAGeolocationListItemResponse404> Not Found
     * @throws FetchError<409, types.CreateAGeolocationListItemResponse409> Conflict
     * @throws FetchError<422, types.CreateAGeolocationListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAGeolocationListItemResponse429> Too Many Requests
     */
    SDK.prototype.createAGeolocationListItem = function (body, metadata) {
        return this.core.fetch('/list-item/geolocations', 'post', body, metadata);
    };
    /**
     * Retrieves the details of an existing geolocation List Item.
     *
     * @summary Retrieve a Geolocation List Item
     * @throws FetchError<400, types.RetrieveAGeolocationListItemResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAGeolocationListItemResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAGeolocationListItemResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAGeolocationListItemResponse404> Not Found
     * @throws FetchError<409, types.RetrieveAGeolocationListItemResponse409> Conflict
     * @throws FetchError<422, types.RetrieveAGeolocationListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.RetrieveAGeolocationListItemResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAGeolocationListItem = function (metadata) {
        return this.core.fetch('/list-item/geolocations/{list-item-id}', 'get', metadata);
    };
    /**
     * Archived items are not matched against new inquiries.
     *
     * @summary Archive a Geolocation List Item
     * @throws FetchError<400, types.ArchiveAGeolocationListItemResponse400> Bad Request
     * @throws FetchError<401, types.ArchiveAGeolocationListItemResponse401> Unauthorized
     * @throws FetchError<403, types.ArchiveAGeolocationListItemResponse403> Forbidden
     * @throws FetchError<404, types.ArchiveAGeolocationListItemResponse404> Not Found
     * @throws FetchError<409, types.ArchiveAGeolocationListItemResponse409> Conflict
     * @throws FetchError<422, types.ArchiveAGeolocationListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ArchiveAGeolocationListItemResponse429> Too Many Requests
     */
    SDK.prototype.archiveAGeolocationListItem = function (metadata) {
        return this.core.fetch('/list-item/geolocations/{list-item-id}', 'delete', metadata);
    };
    /**
     * Create a new item in a <<glossary:list>>.
     *
     * @summary Create a Government ID Number List Item
     * @throws FetchError<400, types.CreateAGovernmentIdNumberListItemResponse400> Bad Request
     * @throws FetchError<401, types.CreateAGovernmentIdNumberListItemResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAGovernmentIdNumberListItemResponse403> Forbidden
     * @throws FetchError<404, types.CreateAGovernmentIdNumberListItemResponse404> Not Found
     * @throws FetchError<409, types.CreateAGovernmentIdNumberListItemResponse409> Conflict
     * @throws FetchError<422, types.CreateAGovernmentIdNumberListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAGovernmentIdNumberListItemResponse429> Too Many Requests
     */
    SDK.prototype.createAGovernmentIdNumberListItem = function (body, metadata) {
        return this.core.fetch('/list-item/government-id-numbers', 'post', body, metadata);
    };
    /**
     * Retrieves the details of an existing government ID number List Item.
     *
     * @summary Retrieve a Government ID Number List Item
     * @throws FetchError<400, types.RetrieveAGovernmentIdNumberListItemResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAGovernmentIdNumberListItemResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAGovernmentIdNumberListItemResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAGovernmentIdNumberListItemResponse404> Not Found
     * @throws FetchError<409, types.RetrieveAGovernmentIdNumberListItemResponse409> Conflict
     * @throws FetchError<422, types.RetrieveAGovernmentIdNumberListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.RetrieveAGovernmentIdNumberListItemResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAGovernmentIdNumberListItem = function (metadata) {
        return this.core.fetch('/list-item/government-id-numbers/{list-item-id}', 'get', metadata);
    };
    /**
     * Archived items are not matched against new inquiries.
     *
     * @summary Archive a Government ID Number List Item
     * @throws FetchError<400, types.ArchiveAGovernmentIdNumberListItemResponse400> Bad Request
     * @throws FetchError<401, types.ArchiveAGovernmentIdNumberListItemResponse401> Unauthorized
     * @throws FetchError<403, types.ArchiveAGovernmentIdNumberListItemResponse403> Forbidden
     * @throws FetchError<404, types.ArchiveAGovernmentIdNumberListItemResponse404> Not Found
     * @throws FetchError<409, types.ArchiveAGovernmentIdNumberListItemResponse409> Conflict
     * @throws FetchError<422, types.ArchiveAGovernmentIdNumberListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ArchiveAGovernmentIdNumberListItemResponse429> Too Many Requests
     */
    SDK.prototype.archiveAGovernmentIdNumberListItem = function (metadata) {
        return this.core.fetch('/list-item/government-id-numbers/{list-item-id}', 'delete', metadata);
    };
    /**
     * Create a new item in a <<glossary:list>>.
     *
     * @summary Create an IP Address List Item
     * @throws FetchError<400, types.CreateAnIpAddressListItemResponse400> Bad Request
     * @throws FetchError<401, types.CreateAnIpAddressListItemResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAnIpAddressListItemResponse403> Forbidden
     * @throws FetchError<404, types.CreateAnIpAddressListItemResponse404> Not Found
     * @throws FetchError<409, types.CreateAnIpAddressListItemResponse409> Conflict
     * @throws FetchError<422, types.CreateAnIpAddressListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAnIpAddressListItemResponse429> Too Many Requests
     */
    SDK.prototype.createAnIpAddressListItem = function (body, metadata) {
        return this.core.fetch('/list-item/ip-addresses', 'post', body, metadata);
    };
    /**
     * Retrieves the details of an existing IP address List Item.
     *
     * @summary Retrieve an IP Address List Item
     * @throws FetchError<400, types.RetrieveAnIpAddressListItemResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAnIpAddressListItemResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAnIpAddressListItemResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAnIpAddressListItemResponse404> Not Found
     * @throws FetchError<409, types.RetrieveAnIpAddressListItemResponse409> Conflict
     * @throws FetchError<422, types.RetrieveAnIpAddressListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.RetrieveAnIpAddressListItemResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAnIpAddressListItem = function (metadata) {
        return this.core.fetch('/list-item/ip-addresses/{list-item-id}', 'get', metadata);
    };
    /**
     * Archived items are not matched against new inquiries.
     *
     * @summary Archive an IP Address List Item
     * @throws FetchError<400, types.ArchiveAnIpAddressListItemResponse400> Bad Request
     * @throws FetchError<401, types.ArchiveAnIpAddressListItemResponse401> Unauthorized
     * @throws FetchError<403, types.ArchiveAnIpAddressListItemResponse403> Forbidden
     * @throws FetchError<404, types.ArchiveAnIpAddressListItemResponse404> Not Found
     * @throws FetchError<409, types.ArchiveAnIpAddressListItemResponse409> Conflict
     * @throws FetchError<422, types.ArchiveAnIpAddressListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ArchiveAnIpAddressListItemResponse429> Too Many Requests
     */
    SDK.prototype.archiveAnIpAddressListItem = function (metadata) {
        return this.core.fetch('/list-item/ip-addresses/{list-item-id}', 'delete', metadata);
    };
    /**
     * Create a new item in a <<glossary:list>>.
     *
     * @summary Create a Name List Item
     * @throws FetchError<400, types.CreateANameListItemResponse400> Bad Request
     * @throws FetchError<401, types.CreateANameListItemResponse401> Unauthorized
     * @throws FetchError<403, types.CreateANameListItemResponse403> Forbidden
     * @throws FetchError<404, types.CreateANameListItemResponse404> Not Found
     * @throws FetchError<409, types.CreateANameListItemResponse409> Conflict
     * @throws FetchError<422, types.CreateANameListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateANameListItemResponse429> Too Many Requests
     */
    SDK.prototype.createANameListItem = function (body, metadata) {
        return this.core.fetch('/list-item/names', 'post', body, metadata);
    };
    /**
     * Retrieves the details of an existing name List Item.
     *
     * @summary Retrieve a Name List Item
     * @throws FetchError<400, types.RetrieveANameListItemResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveANameListItemResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveANameListItemResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveANameListItemResponse404> Not Found
     * @throws FetchError<409, types.RetrieveANameListItemResponse409> Conflict
     * @throws FetchError<422, types.RetrieveANameListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.RetrieveANameListItemResponse429> Too Many Requests
     */
    SDK.prototype.retrieveANameListItem = function (metadata) {
        return this.core.fetch('/list-item/names/{list-item-id}', 'get', metadata);
    };
    /**
     * Archived items are not matched against new inquiries.
     *
     * @summary Archive a Name List Item
     * @throws FetchError<400, types.ArchiveANameListItemResponse400> Bad Request
     * @throws FetchError<401, types.ArchiveANameListItemResponse401> Unauthorized
     * @throws FetchError<403, types.ArchiveANameListItemResponse403> Forbidden
     * @throws FetchError<404, types.ArchiveANameListItemResponse404> Not Found
     * @throws FetchError<409, types.ArchiveANameListItemResponse409> Conflict
     * @throws FetchError<422, types.ArchiveANameListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ArchiveANameListItemResponse429> Too Many Requests
     */
    SDK.prototype.archiveANameListItem = function (metadata) {
        return this.core.fetch('/list-item/names/{list-item-id}', 'delete', metadata);
    };
    /**
     * Create a new item in a <<glossary:list>>.
     *
     * @summary Create a Phone Number List Item
     * @throws FetchError<400, types.CreateAPhoneNumberListItemResponse400> Bad Request
     * @throws FetchError<401, types.CreateAPhoneNumberListItemResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAPhoneNumberListItemResponse403> Forbidden
     * @throws FetchError<404, types.CreateAPhoneNumberListItemResponse404> Not Found
     * @throws FetchError<409, types.CreateAPhoneNumberListItemResponse409> Conflict
     * @throws FetchError<422, types.CreateAPhoneNumberListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAPhoneNumberListItemResponse429> Too Many Requests
     */
    SDK.prototype.createAPhoneNumberListItem = function (body, metadata) {
        return this.core.fetch('/list-item/phone-numbers', 'post', body, metadata);
    };
    /**
     * Retrieves the details of an existing phone number List Item.
     *
     * @summary Retrieve a Phone Number List Item
     * @throws FetchError<400, types.RetrieveAPhoneNumberListItemResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAPhoneNumberListItemResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAPhoneNumberListItemResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAPhoneNumberListItemResponse404> Not Found
     * @throws FetchError<409, types.RetrieveAPhoneNumberListItemResponse409> Conflict
     * @throws FetchError<422, types.RetrieveAPhoneNumberListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.RetrieveAPhoneNumberListItemResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAPhoneNumberListItem = function (metadata) {
        return this.core.fetch('/list-item/phone-numbers/{list-item-id}', 'get', metadata);
    };
    /**
     * Archived items are not matched against new inquiries.
     *
     * @summary Archive a Phone Number List Item
     * @throws FetchError<400, types.ArchiveAPhoneNumberListItemResponse400> Bad Request
     * @throws FetchError<401, types.ArchiveAPhoneNumberListItemResponse401> Unauthorized
     * @throws FetchError<403, types.ArchiveAPhoneNumberListItemResponse403> Forbidden
     * @throws FetchError<404, types.ArchiveAPhoneNumberListItemResponse404> Not Found
     * @throws FetchError<409, types.ArchiveAPhoneNumberListItemResponse409> Conflict
     * @throws FetchError<422, types.ArchiveAPhoneNumberListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ArchiveAPhoneNumberListItemResponse429> Too Many Requests
     */
    SDK.prototype.archiveAPhoneNumberListItem = function (metadata) {
        return this.core.fetch('/list-item/phone-numbers/{list-item-id}', 'delete', metadata);
    };
    /**
     * Create a new item in a <<glossary:list>>.
     *
     * @summary Create a String List Item
     * @throws FetchError<400, types.CreateAStringListItemResponse400> Bad Request
     * @throws FetchError<401, types.CreateAStringListItemResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAStringListItemResponse403> Forbidden
     * @throws FetchError<404, types.CreateAStringListItemResponse404> Not Found
     * @throws FetchError<409, types.CreateAStringListItemResponse409> Conflict
     * @throws FetchError<422, types.CreateAStringListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAStringListItemResponse429> Too Many Requests
     */
    SDK.prototype.createAStringListItem = function (body, metadata) {
        return this.core.fetch('/list-item/strings', 'post', body, metadata);
    };
    /**
     * Retrieves the details of an existing string List Item.
     *
     * @summary Retrieve a String List Item
     * @throws FetchError<400, types.RetrieveAStringListItemResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAStringListItemResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAStringListItemResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAStringListItemResponse404> Not Found
     * @throws FetchError<409, types.RetrieveAStringListItemResponse409> Conflict
     * @throws FetchError<422, types.RetrieveAStringListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.RetrieveAStringListItemResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAStringListItem = function (metadata) {
        return this.core.fetch('/list-item/strings/{list-item-id}', 'get', metadata);
    };
    /**
     * Archived items are not matched against new inquiries.
     *
     * @summary Archive a String List Item
     * @throws FetchError<400, types.ArchiveAStringListItemResponse400> Bad Request
     * @throws FetchError<401, types.ArchiveAStringListItemResponse401> Unauthorized
     * @throws FetchError<403, types.ArchiveAStringListItemResponse403> Forbidden
     * @throws FetchError<404, types.ArchiveAStringListItemResponse404> Not Found
     * @throws FetchError<409, types.ArchiveAStringListItemResponse409> Conflict
     * @throws FetchError<422, types.ArchiveAStringListItemResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ArchiveAStringListItemResponse429> Too Many Requests
     */
    SDK.prototype.archiveAStringListItem = function (metadata) {
        return this.core.fetch('/list-item/strings/{list-item-id}', 'delete', metadata);
    };
    /**
     * Create a new <<glossary:list>> for your organization.
     *
     * @summary Create a Browser Fingerprint List
     * @throws FetchError<400, types.CreateABrowserFingerprintListResponse400> Bad Request
     * @throws FetchError<401, types.CreateABrowserFingerprintListResponse401> Unauthorized
     * @throws FetchError<403, types.CreateABrowserFingerprintListResponse403> Forbidden
     * @throws FetchError<409, types.CreateABrowserFingerprintListResponse409> Conflict
     * @throws FetchError<422, types.CreateABrowserFingerprintListResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateABrowserFingerprintListResponse429> Too Many Requests
     */
    SDK.prototype.createABrowserFingerprintList = function (body, metadata) {
        return this.core.fetch('/list/browser-fingerprints', 'post', body, metadata);
    };
    /**
     * Create a new <<glossary:list>> for your organization.
     *
     * @summary Create a Country List
     * @throws FetchError<400, types.CreateACountryListResponse400> Bad Request
     * @throws FetchError<401, types.CreateACountryListResponse401> Unauthorized
     * @throws FetchError<403, types.CreateACountryListResponse403> Forbidden
     * @throws FetchError<409, types.CreateACountryListResponse409> Conflict
     * @throws FetchError<422, types.CreateACountryListResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateACountryListResponse429> Too Many Requests
     */
    SDK.prototype.createACountryList = function (body, metadata) {
        return this.core.fetch('/list/countries', 'post', body, metadata);
    };
    /**
     * Create a new <<glossary:list>> for your organization.
     *
     * @summary Create an Email Address List
     * @throws FetchError<400, types.CreateAnEmailAddressListResponse400> Bad Request
     * @throws FetchError<401, types.CreateAnEmailAddressListResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAnEmailAddressListResponse403> Forbidden
     * @throws FetchError<409, types.CreateAnEmailAddressListResponse409> Conflict
     * @throws FetchError<422, types.CreateAnEmailAddressListResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAnEmailAddressListResponse429> Too Many Requests
     */
    SDK.prototype.createAnEmailAddressList = function (body, metadata) {
        return this.core.fetch('/list/email-addresses', 'post', body, metadata);
    };
    /**
     * Create a new <<glossary:list>> for your organization.
     *
     * IMPORTANT: You must ensure proper consent for processing of biometrics is in place prior
     * to using face Lists.
     *
     * @summary Create a Face List
     * @throws FetchError<400, types.CreateAFaceListResponse400> Bad Request
     * @throws FetchError<401, types.CreateAFaceListResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAFaceListResponse403> Forbidden
     * @throws FetchError<409, types.CreateAFaceListResponse409> Conflict
     * @throws FetchError<422, types.CreateAFaceListResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAFaceListResponse429> Too Many Requests
     */
    SDK.prototype.createAFaceList = function (body, metadata) {
        return this.core.fetch('/list/faces', 'post', body, metadata);
    };
    /**
     * Create a new <<glossary:list>> for your organization.
     *
     * @summary Create a Geolocation List
     * @throws FetchError<400, types.CreateAGeolocationListResponse400> Bad Request
     * @throws FetchError<401, types.CreateAGeolocationListResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAGeolocationListResponse403> Forbidden
     * @throws FetchError<409, types.CreateAGeolocationListResponse409> Conflict
     * @throws FetchError<422, types.CreateAGeolocationListResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAGeolocationListResponse429> Too Many Requests
     */
    SDK.prototype.createAGeolocationList = function (body, metadata) {
        return this.core.fetch('/list/geolocations', 'post', body, metadata);
    };
    /**
     * Create a new <<glossary:list>> for your organization.
     *
     * @summary Create a Government ID Number List
     * @throws FetchError<400, types.CreateAGovernmentIdNumberListResponse400> Bad Request
     * @throws FetchError<401, types.CreateAGovernmentIdNumberListResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAGovernmentIdNumberListResponse403> Forbidden
     * @throws FetchError<409, types.CreateAGovernmentIdNumberListResponse409> Conflict
     * @throws FetchError<422, types.CreateAGovernmentIdNumberListResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAGovernmentIdNumberListResponse429> Too Many Requests
     */
    SDK.prototype.createAGovernmentIdNumberList = function (body, metadata) {
        return this.core.fetch('/list/government-id-numbers', 'post', body, metadata);
    };
    /**
     * Create a new <<glossary:list>> for your organization.
     *
     * @summary Create an IP Address List
     * @throws FetchError<400, types.CreateAnIpAddressListResponse400> Bad Request
     * @throws FetchError<401, types.CreateAnIpAddressListResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAnIpAddressListResponse403> Forbidden
     * @throws FetchError<409, types.CreateAnIpAddressListResponse409> Conflict
     * @throws FetchError<422, types.CreateAnIpAddressListResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAnIpAddressListResponse429> Too Many Requests
     */
    SDK.prototype.createAnIpAddressList = function (body, metadata) {
        return this.core.fetch('/list/ip-addresses', 'post', body, metadata);
    };
    /**
     * Create a new <<glossary:list>> for your organization.
     *
     * @summary Create a Name List
     * @throws FetchError<400, types.CreateANameListResponse400> Bad Request
     * @throws FetchError<401, types.CreateANameListResponse401> Unauthorized
     * @throws FetchError<403, types.CreateANameListResponse403> Forbidden
     * @throws FetchError<409, types.CreateANameListResponse409> Conflict
     * @throws FetchError<422, types.CreateANameListResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateANameListResponse429> Too Many Requests
     */
    SDK.prototype.createANameList = function (body, metadata) {
        return this.core.fetch('/list/names', 'post', body, metadata);
    };
    /**
     * Create a new <<glossary:list>> for your organization.
     *
     * @summary Create a Phone Number List
     * @throws FetchError<400, types.CreateAPhoneNumberListResponse400> Bad Request
     * @throws FetchError<401, types.CreateAPhoneNumberListResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAPhoneNumberListResponse403> Forbidden
     * @throws FetchError<409, types.CreateAPhoneNumberListResponse409> Conflict
     * @throws FetchError<422, types.CreateAPhoneNumberListResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAPhoneNumberListResponse429> Too Many Requests
     */
    SDK.prototype.createAPhoneNumberList = function (body, metadata) {
        return this.core.fetch('/list/phone-numbers', 'post', body, metadata);
    };
    /**
     * Returns a list of your organization's <<glossary: list>>s.
     *
     * @summary List all Lists
     * @throws FetchError<400, types.ListAllListsResponse400> Bad Request
     * @throws FetchError<401, types.ListAllListsResponse401> Unauthorized
     * @throws FetchError<403, types.ListAllListsResponse403> Forbidden
     * @throws FetchError<404, types.ListAllListsResponse404> Not Found
     * @throws FetchError<429, types.ListAllListsResponse429> Too Many Requests
     */
    SDK.prototype.listAllLists = function (metadata) {
        return this.core.fetch('/lists', 'get', metadata);
    };
    /**
     * Archive an existing <<glossary:list>>. Archived lists are still retrievable, but will no
     * longer match on inquiries.
     *
     * @summary Archive a List
     * @throws FetchError<400, types.ArchiveAListResponse400> Bad Request
     * @throws FetchError<401, types.ArchiveAListResponse401> Unauthorized
     * @throws FetchError<403, types.ArchiveAListResponse403> Forbidden
     * @throws FetchError<404, types.ArchiveAListResponse404> Not Found
     * @throws FetchError<409, types.ArchiveAListResponse409> Conflict
     * @throws FetchError<422, types.ArchiveAListResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ArchiveAListResponse429> Too Many Requests
     */
    SDK.prototype.archiveAList = function (metadata) {
        return this.core.fetch('/lists/{list-id}', 'delete', metadata);
    };
    /**
     * Retrieves the details of an existing <<glossary:list>>.
     *
     * @summary Retrieve a List
     * @throws FetchError<400, types.RetrieveAListResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAListResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAListResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAListResponse404> Not Found
     * @throws FetchError<429, types.RetrieveAListResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAList = function (metadata) {
        return this.core.fetch('/lists/{list-id}', 'get', metadata);
    };
    /**
     * Authorizes another Organization to access your Inquiry, Verifications, or other Persona
     * resources.
     *
     * @summary Create Authorization
     * @throws FetchError<400, types.CreateAuthorizationResponse400> Bad Request
     * @throws FetchError<401, types.CreateAuthorizationResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAuthorizationResponse403> Forbidden
     * @throws FetchError<404, types.CreateAuthorizationResponse404> Not Found
     * @throws FetchError<409, types.CreateAuthorizationResponse409> Conflict
     * @throws FetchError<422, types.CreateAuthorizationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAuthorizationResponse429> Too Many Requests
     */
    SDK.prototype.createAuthorization = function (body, metadata) {
        return this.core.fetch('/oauth/authorize', 'post', body, metadata);
    };
    /**
     * Creates an access token using an authorization code.
     *
     * @summary Create Access Token
     * @throws FetchError<400, types.CreateAccessTokenResponse400> Bad Request
     * @throws FetchError<401, types.CreateAccessTokenResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAccessTokenResponse403> Forbidden
     * @throws FetchError<404, types.CreateAccessTokenResponse404> Not Found
     * @throws FetchError<409, types.CreateAccessTokenResponse409> Conflict
     * @throws FetchError<422, types.CreateAccessTokenResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAccessTokenResponse429> Too Many Requests
     */
    SDK.prototype.createAccessToken = function (body, metadata) {
        return this.core.fetch('/oauth/token', 'post', body, metadata);
    };
    /**
     * Returns a list of all your organization's Reports.
     *
     * @summary List all Reports
     * @throws FetchError<400, types.ListAllReportsResponse400> Bad Request
     * @throws FetchError<401, types.ListAllReportsResponse401> Unauthorized
     * @throws FetchError<403, types.ListAllReportsResponse403> Forbidden
     * @throws FetchError<429, types.ListAllReportsResponse429> Too Many Requests
     */
    SDK.prototype.listAllReports = function (metadata) {
        return this.core.fetch('/reports', 'get', metadata);
    };
    /**
     * Creates a new Report of any type.
     *
     * @summary Create a Report
     * @throws FetchError<400, types.CreateAReportResponse400> Bad Request
     * @throws FetchError<401, types.CreateAReportResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAReportResponse403> Forbidden
     * @throws FetchError<404, types.CreateAReportResponse404> Not Found
     * @throws FetchError<408, types.CreateAReportResponse408> Request Timeout
     * @throws FetchError<409, types.CreateAReportResponse409> Conflict
     * @throws FetchError<422, types.CreateAReportResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAReportResponse429> Too Many Requests
     */
    SDK.prototype.createAReport = function (body, metadata) {
        return this.core.fetch('/reports', 'post', body, metadata);
    };
    /**
     * Permanently deletes personally identifiable information (PII) for a Report.
     *
     * This endpoint can be used to comply with privacy regulations such as GDPR / CCPA or to
     * enforce data privacy.
     *
     * Note that this will only delete the report -- it does not delete associated accounts,
     * inquiries, verifications, or other Persona resources.
     *
     * @summary Redact a Report
     * @throws FetchError<400, types.RedactAReportResponse400> Bad Request
     * @throws FetchError<401, types.RedactAReportResponse401> Unauthorized
     * @throws FetchError<403, types.RedactAReportResponse403> Forbidden
     * @throws FetchError<404, types.RedactAReportResponse404> Not Found
     * @throws FetchError<409, types.RedactAReportResponse409> Conflict
     * @throws FetchError<422, types.RedactAReportResponse422> Unprocessable Entity
     * @throws FetchError<429, types.RedactAReportResponse429> Too Many Requests
     */
    SDK.prototype.redactAReport = function (metadata) {
        return this.core.fetch('/reports/{report-id}', 'delete', metadata);
    };
    /**
     * Retrieves the details of an existing Report.
     *
     * @summary Retrieve a Report
     * @throws FetchError<400, types.RetrieveAReportResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAReportResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAReportResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAReportResponse404> Not Found
     * @throws FetchError<429, types.RetrieveAReportResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAReport = function (metadata) {
        return this.core.fetch('/reports/{report-id}', 'get', metadata);
    };
    SDK.prototype.reportsAddTag = function (body, metadata) {
        return this.core.fetch('/reports/{report-id}/add-tag', 'post', body, metadata);
    };
    /**
     * Dismisses active matches for supported report types
     *
     * @summary Report Action: Dismiss Matches
     * @throws FetchError<400, types.DismissMatchesResponse400> Bad Request
     * @throws FetchError<401, types.DismissMatchesResponse401> Unauthorized
     * @throws FetchError<403, types.DismissMatchesResponse403> Forbidden
     * @throws FetchError<404, types.DismissMatchesResponse404> Not Found
     * @throws FetchError<409, types.DismissMatchesResponse409> Conflict
     * @throws FetchError<422, types.DismissMatchesResponse422> Unprocessable Entity
     * @throws FetchError<429, types.DismissMatchesResponse429> Too Many Requests
     */
    SDK.prototype.dismissMatches = function (body, metadata) {
        return this.core.fetch('/reports/{report-id}/dismiss', 'post', body, metadata);
    };
    /**
     * Pauses continuous monitoring on a report. Requires additional permissions.
     *
     * @summary Report Action: Pause Continuous Monitoring
     * @throws FetchError<400, types.ReportActionPauseContinuousMonitoringResponse400> Bad Request
     * @throws FetchError<401, types.ReportActionPauseContinuousMonitoringResponse401> Unauthorized
     * @throws FetchError<403, types.ReportActionPauseContinuousMonitoringResponse403> Forbidden
     * @throws FetchError<404, types.ReportActionPauseContinuousMonitoringResponse404> Not Found
     * @throws FetchError<409, types.ReportActionPauseContinuousMonitoringResponse409> Conflict
     * @throws FetchError<422, types.ReportActionPauseContinuousMonitoringResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ReportActionPauseContinuousMonitoringResponse429> Too Many Requests
     */
    SDK.prototype.reportActionPauseContinuousMonitoring = function (metadata) {
        return this.core.fetch('/reports/{report-id}/pause', 'post', metadata);
    };
    /**
     * Prints a report in PDF format.
     *
     * @summary Print Report PDF
     * @throws FetchError<400, types.PrintReportPdfResponse400> Bad Request
     * @throws FetchError<401, types.PrintReportPdfResponse401> Unauthorized
     * @throws FetchError<403, types.PrintReportPdfResponse403> Forbidden
     * @throws FetchError<404, types.PrintReportPdfResponse404> Not Found
     * @throws FetchError<409, types.PrintReportPdfResponse409> Conflict
     * @throws FetchError<429, types.PrintReportPdfResponse429> Too Many Requests
     */
    SDK.prototype.printReportPdf = function (metadata) {
        return this.core.fetch('/reports/{report-id}/print', 'get', metadata);
    };
    SDK.prototype.reportsRemoveTag = function (body, metadata) {
        return this.core.fetch('/reports/{report-id}/remove-tag', 'post', body, metadata);
    };
    /**
     * Resumes continuous monitoring on paused report. Requires additional permissions.
     *
     * @summary Report Action: Resume Continuous Monitoring
     * @throws FetchError<400, types.ReportActionResumeContinuousMonitoringResponse400> Bad Request
     * @throws FetchError<401, types.ReportActionResumeContinuousMonitoringResponse401> Unauthorized
     * @throws FetchError<403, types.ReportActionResumeContinuousMonitoringResponse403> Forbidden
     * @throws FetchError<404, types.ReportActionResumeContinuousMonitoringResponse404> Not Found
     * @throws FetchError<409, types.ReportActionResumeContinuousMonitoringResponse409> Conflict
     * @throws FetchError<422, types.ReportActionResumeContinuousMonitoringResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ReportActionResumeContinuousMonitoringResponse429> Too Many Requests
     */
    SDK.prototype.reportActionResumeContinuousMonitoring = function (metadata) {
        return this.core.fetch('/reports/{report-id}/resume', 'post', metadata);
    };
    /**
     * Re-runs a continuously monitored report immediately, outside of its existing recurrence
     * schedule.
     *
     * @summary Report Action: Re-run Report
     * @throws FetchError<400, types.ReportActionReRunReportResponse400> Bad Request
     * @throws FetchError<401, types.ReportActionReRunReportResponse401> Unauthorized
     * @throws FetchError<403, types.ReportActionReRunReportResponse403> Forbidden
     * @throws FetchError<404, types.ReportActionReRunReportResponse404> Not Found
     * @throws FetchError<409, types.ReportActionReRunReportResponse409> Conflict
     * @throws FetchError<422, types.ReportActionReRunReportResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ReportActionReRunReportResponse429> Too Many Requests
     */
    SDK.prototype.reportActionReRunReport = function (metadata) {
        return this.core.fetch('/reports/{report-id}/run', 'post', metadata);
    };
    SDK.prototype.reportsSetAllTags = function (body, metadata) {
        return this.core.fetch('/reports/{report-id}/set-tags', 'post', body, metadata);
    };
    /**
     * Creates a new <<glossary:Transaction>> for a specific <<glossary:transaction type>> in
     * your organization.
     *
     * @summary Create a Transaction
     * @throws FetchError<400, types.CreateATransactionResponse400> Bad Request
     * @throws FetchError<401, types.CreateATransactionResponse401> Unauthorized
     * @throws FetchError<403, types.CreateATransactionResponse403> Forbidden
     * @throws FetchError<404, types.CreateATransactionResponse404> Not Found
     * @throws FetchError<409, types.CreateATransactionResponse409> Conflict
     * @throws FetchError<422, types.CreateATransactionResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateATransactionResponse429> Too Many Requests
     */
    SDK.prototype.createATransaction = function (body, metadata) {
        return this.core.fetch('/transactions', 'post', body, metadata);
    };
    /**
     * Returns a list of your organization's transactions. Note that this endpoint aggregates
     * transactions across all <<glossary:transaction type>>(s). See
     * [Pagination](https://docs.withpersona.com/reference/pagination) for more details about
     * handling the response.
     *
     * @summary List all Transactions
     * @throws FetchError<400, types.ListAllTransactionsResponse400> Bad Request
     * @throws FetchError<401, types.ListAllTransactionsResponse401> Unauthorized
     * @throws FetchError<403, types.ListAllTransactionsResponse403> Forbidden
     * @throws FetchError<404, types.ListAllTransactionsResponse404> Not Found
     * @throws FetchError<429, types.ListAllTransactionsResponse429> Too Many Requests
     */
    SDK.prototype.listAllTransactions = function (metadata) {
        return this.core.fetch('/transactions', 'get', metadata);
    };
    /**
     * Permanently deletes personally identifiable information (PII) for a
     * <<glossary:Transaction>>. The response indicates a successful redaction of the
     * Transaction. Redaction of the Transaction's associated child objects are done
     * asynchronously and may take some time before all associated child objects are fully
     * redacted. **This action cannot be undone**.
     *
     * @summary Redact a Transaction
     * @throws FetchError<400, types.RedactATransactionResponse400> Bad Request
     * @throws FetchError<401, types.RedactATransactionResponse401> Unauthorized
     * @throws FetchError<403, types.RedactATransactionResponse403> Forbidden
     * @throws FetchError<404, types.RedactATransactionResponse404> Not Found
     * @throws FetchError<409, types.RedactATransactionResponse409> Conflict
     * @throws FetchError<422, types.RedactATransactionResponse422> Unprocessable Entity
     * @throws FetchError<429, types.RedactATransactionResponse429> Too Many Requests
     */
    SDK.prototype.redactATransaction = function (metadata) {
        return this.core.fetch('/transactions/{transaction-id}', 'delete', metadata);
    };
    /**
     * Retrieves the details of an existing <<glossary:Transaction>>.
     *
     * @summary Retrieve a Transaction
     * @throws FetchError<400, types.RetrieveATransactionResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveATransactionResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveATransactionResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveATransactionResponse404> Not Found
     * @throws FetchError<429, types.RetrieveATransactionResponse429> Too Many Requests
     */
    SDK.prototype.retrieveATransaction = function (metadata) {
        return this.core.fetch('/transactions/{transaction-id}', 'get', metadata);
    };
    SDK.prototype.updateATransaction = function (body, metadata) {
        return this.core.fetch('/transactions/{transaction-id}', 'patch', body, metadata);
    };
    SDK.prototype.transactionsAddTag = function (body, metadata) {
        return this.core.fetch('/transactions/{transaction-id}/add-tag', 'post', body, metadata);
    };
    SDK.prototype.createATransactionLabel = function (body, metadata) {
        return this.core.fetch('/transactions/{transaction-id}/label', 'post', body, metadata);
    };
    SDK.prototype.transactionsRemoveTag = function (body, metadata) {
        return this.core.fetch('/transactions/{transaction-id}/remove-tag', 'post', body, metadata);
    };
    SDK.prototype.transactionsSetTags = function (body, metadata) {
        return this.core.fetch('/transactions/{transaction-id}/set-tags', 'post', body, metadata);
    };
    /**
     * Permanently deletes biometric data for a <<glossary:Transaction>> AND all its associated
     * objects. **This action cannot be undone**.
     *
     * @summary Redact Transaction biometrics
     * @throws FetchError<400, types.RedactTransactionBiometricsResponse400> Bad Request
     * @throws FetchError<401, types.RedactTransactionBiometricsResponse401> Unauthorized
     * @throws FetchError<403, types.RedactTransactionBiometricsResponse403> Forbidden
     * @throws FetchError<404, types.RedactTransactionBiometricsResponse404> Not Found
     * @throws FetchError<409, types.RedactTransactionBiometricsResponse409> Conflict
     * @throws FetchError<422, types.RedactTransactionBiometricsResponse422> Unprocessable Entity
     * @throws FetchError<429, types.RedactTransactionBiometricsResponse429> Too Many Requests
     */
    SDK.prototype.redactTransactionBiometrics = function (metadata) {
        return this.core.fetch('/transactions/{transaction-id}/redact-biometrics', 'post', metadata);
    };
    /**
     * Returns a list of your organization's user audit logs.
     *
     * @summary List all User Audit Logs
     * @throws FetchError<400, types.ListAllUserAuditLogsResponse400> Bad Request
     * @throws FetchError<401, types.ListAllUserAuditLogsResponse401> Unauthorized
     * @throws FetchError<403, types.ListAllUserAuditLogsResponse403> Forbidden
     * @throws FetchError<404, types.ListAllUserAuditLogsResponse404> Not Found
     * @throws FetchError<429, types.ListAllUserAuditLogsResponse429> Too Many Requests
     */
    SDK.prototype.listAllUserAuditLogs = function (metadata) {
        return this.core.fetch('/user-audit-logs', 'get', metadata);
    };
    /**
     * Retrieves the details of an existing API Log.
     *
     * @summary Retrieve an User Audit Log
     * @throws FetchError<400, types.RetrieveAnUserAuditLogResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAnUserAuditLogResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAnUserAuditLogResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAnUserAuditLogResponse404> Not Found
     * @throws FetchError<429, types.RetrieveAnUserAuditLogResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAnUserAuditLog = function (metadata) {
        return this.core.fetch('/user-audit-logs/{user-audit-log-id}', 'get', metadata);
    };
    /**
     * Create a new AAMVA verification
     *
     * @summary Create an AAMVA Verification
     * @throws FetchError<400, types.CreateAnAamvaVerificationResponse400> Bad Request
     * @throws FetchError<401, types.CreateAnAamvaVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAnAamvaVerificationResponse403> Forbidden
     * @throws FetchError<404, types.CreateAnAamvaVerificationResponse404> Not Found
     * @throws FetchError<409, types.CreateAnAamvaVerificationResponse409> Conflict
     * @throws FetchError<422, types.CreateAnAamvaVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAnAamvaVerificationResponse429> Too Many Requests
     */
    SDK.prototype.createAnAamvaVerification = function (body, metadata) {
        return this.core.fetch('/verification/aamvas', 'post', body, metadata);
    };
    /**
     * Retrieve an AAMVA verification
     *
     * @summary Retrieve an AAMVA Verification
     * @throws FetchError<400, types.RetrieveAnAamvaVerificationResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAnAamvaVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAnAamvaVerificationResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAnAamvaVerificationResponse404> Not Found
     * @throws FetchError<429, types.RetrieveAnAamvaVerificationResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAnAamvaVerification = function (metadata) {
        return this.core.fetch('/verification/aamvas/{verification-id}', 'get', metadata);
    };
    /**
     * Updates an existing AAMVA verification. Can only update `initiated` verifications.
     *
     * @summary Update an AAMVA Verification
     * @throws FetchError<400, types.UpdateAnAamvaVerificationResponse400> Bad Request
     * @throws FetchError<401, types.UpdateAnAamvaVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.UpdateAnAamvaVerificationResponse403> Forbidden
     * @throws FetchError<404, types.UpdateAnAamvaVerificationResponse404> Not Found
     * @throws FetchError<409, types.UpdateAnAamvaVerificationResponse409> Conflict
     * @throws FetchError<422, types.UpdateAnAamvaVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.UpdateAnAamvaVerificationResponse429> Too Many Requests
     */
    SDK.prototype.updateAnAamvaVerification = function (body, metadata) {
        return this.core.fetch('/verification/aamvas/{verification-id}', 'patch', body, metadata);
    };
    /**
     * Submit an AAMVA verification for processing. Can only submit `initiated` verifications.
     *
     * @summary Submit an AAMVA Verification
     * @throws FetchError<400, types.SubmitAnAamvaVerificationResponse400> Bad Request
     * @throws FetchError<401, types.SubmitAnAamvaVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.SubmitAnAamvaVerificationResponse403> Forbidden
     * @throws FetchError<404, types.SubmitAnAamvaVerificationResponse404> Not Found
     * @throws FetchError<409, types.SubmitAnAamvaVerificationResponse409> Conflict
     * @throws FetchError<422, types.SubmitAnAamvaVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.SubmitAnAamvaVerificationResponse429> Too Many Requests
     */
    SDK.prototype.submitAnAamvaVerification = function (metadata) {
        return this.core.fetch('/verification/aamvas/{verification-id}/submit', 'post', metadata);
    };
    /**
     * Create a new eCBSV database verification
     *
     * @summary Create an eCBSV Database Verification
     * @throws FetchError<400, types.CreateAnEcbsvDatabaseVerificationResponse400> Bad Request
     * @throws FetchError<401, types.CreateAnEcbsvDatabaseVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAnEcbsvDatabaseVerificationResponse403> Forbidden
     * @throws FetchError<404, types.CreateAnEcbsvDatabaseVerificationResponse404> Not Found
     * @throws FetchError<409, types.CreateAnEcbsvDatabaseVerificationResponse409> Conflict
     * @throws FetchError<422, types.CreateAnEcbsvDatabaseVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAnEcbsvDatabaseVerificationResponse429> Too Many Requests
     */
    SDK.prototype.createAnEcbsvDatabaseVerification = function (body, metadata) {
        return this.core.fetch('/verification/database-ecbsvs', 'post', body, metadata);
    };
    /**
     * Retrieve an eCBSV database verification
     *
     * @summary Retrieve an eCBSV Database Verification
     * @throws FetchError<400, types.RetrieveAnEcbsvDatabaseVerificationResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAnEcbsvDatabaseVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAnEcbsvDatabaseVerificationResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAnEcbsvDatabaseVerificationResponse404> Not Found
     * @throws FetchError<429, types.RetrieveAnEcbsvDatabaseVerificationResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAnEcbsvDatabaseVerification = function (metadata) {
        return this.core.fetch('/verification/database-ecbsvs/{verification-id}', 'get', metadata);
    };
    SDK.prototype.updateAnEcbsvDatabaseVerification = function (body, metadata) {
        return this.core.fetch('/verification/database-ecbsvs/{verification-id}', 'patch', body, metadata);
    };
    /**
     * Submit an eCBSV database verification for processing. Can only submit `initiated`
     * verifications
     *
     * @summary Submit an eCBSV Database Verification
     * @throws FetchError<400, types.SubmitAnEcbsvDatabaseVerificationResponse400> Bad Request
     * @throws FetchError<401, types.SubmitAnEcbsvDatabaseVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.SubmitAnEcbsvDatabaseVerificationResponse403> Forbidden
     * @throws FetchError<404, types.SubmitAnEcbsvDatabaseVerificationResponse404> Not Found
     * @throws FetchError<409, types.SubmitAnEcbsvDatabaseVerificationResponse409> Conflict
     * @throws FetchError<422, types.SubmitAnEcbsvDatabaseVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.SubmitAnEcbsvDatabaseVerificationResponse429> Too Many Requests
     */
    SDK.prototype.submitAnEcbsvDatabaseVerification = function (metadata) {
        return this.core.fetch('/verification/database-ecbsvs/{verification-id}/submit', 'post', metadata);
    };
    /**
     * Create a new phone carrier database verification
     *
     * @summary Create a Phone Carrier Database Verification
     * @throws FetchError<400, types.CreateAPhoneCarrierDatabaseVerificationResponse400> Bad Request
     * @throws FetchError<401, types.CreateAPhoneCarrierDatabaseVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAPhoneCarrierDatabaseVerificationResponse403> Forbidden
     * @throws FetchError<404, types.CreateAPhoneCarrierDatabaseVerificationResponse404> Not Found
     * @throws FetchError<409, types.CreateAPhoneCarrierDatabaseVerificationResponse409> Conflict
     * @throws FetchError<422, types.CreateAPhoneCarrierDatabaseVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAPhoneCarrierDatabaseVerificationResponse429> Too Many Requests
     */
    SDK.prototype.createAPhoneCarrierDatabaseVerification = function (body, metadata) {
        return this.core.fetch('/verification/database-phone-carriers', 'post', body, metadata);
    };
    /**
     * Retrieve a phone carrier database verification
     *
     * @summary Retrieve a Phone Carrier Database Verification
     * @throws FetchError<400, types.RetrieveAPhoneCarrierDatabaseVerificationResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAPhoneCarrierDatabaseVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAPhoneCarrierDatabaseVerificationResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAPhoneCarrierDatabaseVerificationResponse404> Not Found
     * @throws FetchError<429, types.RetrieveAPhoneCarrierDatabaseVerificationResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAPhoneCarrierDatabaseVerification = function (metadata) {
        return this.core.fetch('/verification/database-phone-carriers/{verification-id}', 'get', metadata);
    };
    SDK.prototype.updateAPhoneCarrierDatabaseVerification = function (body, metadata) {
        return this.core.fetch('/verification/database-phone-carriers/{verification-id}', 'patch', body, metadata);
    };
    /**
     * Submit a phone carrier database verification for processing. Can only submit `initiated`
     * verifications
     *
     * @summary Submit a Phone Carrier Database Verification
     * @throws FetchError<400, types.SubmitAPhoneCarrierDatabaseVerificationResponse400> Bad Request
     * @throws FetchError<401, types.SubmitAPhoneCarrierDatabaseVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.SubmitAPhoneCarrierDatabaseVerificationResponse403> Forbidden
     * @throws FetchError<404, types.SubmitAPhoneCarrierDatabaseVerificationResponse404> Not Found
     * @throws FetchError<409, types.SubmitAPhoneCarrierDatabaseVerificationResponse409> Conflict
     * @throws FetchError<422, types.SubmitAPhoneCarrierDatabaseVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.SubmitAPhoneCarrierDatabaseVerificationResponse429> Too Many Requests
     */
    SDK.prototype.submitAPhoneCarrierDatabaseVerification = function (metadata) {
        return this.core.fetch('/verification/database-phone-carriers/{verification-id}/submit', 'post', metadata);
    };
    /**
     * Create a new Serpro database verification
     *
     * @summary Create a Serpro Database Verification
     * @throws FetchError<400, types.CreateASerproDatabaseVerificationResponse400> Bad Request
     * @throws FetchError<401, types.CreateASerproDatabaseVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.CreateASerproDatabaseVerificationResponse403> Forbidden
     * @throws FetchError<404, types.CreateASerproDatabaseVerificationResponse404> Not Found
     * @throws FetchError<409, types.CreateASerproDatabaseVerificationResponse409> Conflict
     * @throws FetchError<422, types.CreateASerproDatabaseVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateASerproDatabaseVerificationResponse429> Too Many Requests
     */
    SDK.prototype.createASerproDatabaseVerification = function (body, metadata) {
        return this.core.fetch('/verification/database-serpros', 'post', body, metadata);
    };
    /**
     * Retrieve a Serpro database verification
     *
     * @summary Retrieve a Serpro Database Verification
     * @throws FetchError<400, types.RetrieveASerproDatabaseVerificationResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveASerproDatabaseVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveASerproDatabaseVerificationResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveASerproDatabaseVerificationResponse404> Not Found
     * @throws FetchError<429, types.RetrieveASerproDatabaseVerificationResponse429> Too Many Requests
     */
    SDK.prototype.retrieveASerproDatabaseVerification = function (metadata) {
        return this.core.fetch('/verification/database-serpros/{verification-id}', 'get', metadata);
    };
    /**
     * Updates an existing Serpro Database verification. Can only update `initiated`
     * verifications.
     *
     * @summary Update a Serpro Database Verification
     * @throws FetchError<400, types.UpdateASerproDatabaseVerificationResponse400> Bad Request
     * @throws FetchError<401, types.UpdateASerproDatabaseVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.UpdateASerproDatabaseVerificationResponse403> Forbidden
     * @throws FetchError<404, types.UpdateASerproDatabaseVerificationResponse404> Not Found
     * @throws FetchError<409, types.UpdateASerproDatabaseVerificationResponse409> Conflict
     * @throws FetchError<422, types.UpdateASerproDatabaseVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.UpdateASerproDatabaseVerificationResponse429> Too Many Requests
     */
    SDK.prototype.updateASerproDatabaseVerification = function (body, metadata) {
        return this.core.fetch('/verification/database-serpros/{verification-id}', 'patch', body, metadata);
    };
    /**
     * Submit a Serpro database verification for processing. Can only submit `initiated`
     * verifications
     *
     * @summary Submit a Serpro Database Verification
     * @throws FetchError<400, types.SubmitASerproDatabaseVerificationResponse400> Bad Request
     * @throws FetchError<401, types.SubmitASerproDatabaseVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.SubmitASerproDatabaseVerificationResponse403> Forbidden
     * @throws FetchError<404, types.SubmitASerproDatabaseVerificationResponse404> Not Found
     * @throws FetchError<409, types.SubmitASerproDatabaseVerificationResponse409> Conflict
     * @throws FetchError<422, types.SubmitASerproDatabaseVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.SubmitASerproDatabaseVerificationResponse429> Too Many Requests
     */
    SDK.prototype.submitASerproDatabaseVerification = function (metadata) {
        return this.core.fetch('/verification/database-serpros/{verification-id}/submit', 'post', metadata);
    };
    /**
     * Create a new database standard verification
     *
     * @summary Create a Database Standard Verification
     * @throws FetchError<400, types.CreateADatabaseStandardVerificationResponse400> Bad Request
     * @throws FetchError<401, types.CreateADatabaseStandardVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.CreateADatabaseStandardVerificationResponse403> Forbidden
     * @throws FetchError<404, types.CreateADatabaseStandardVerificationResponse404> Not Found
     * @throws FetchError<409, types.CreateADatabaseStandardVerificationResponse409> Conflict
     * @throws FetchError<422, types.CreateADatabaseStandardVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateADatabaseStandardVerificationResponse429> Too Many Requests
     */
    SDK.prototype.createADatabaseStandardVerification = function (body, metadata) {
        return this.core.fetch('/verification/database-standards', 'post', body, metadata);
    };
    /**
     * Retrieve a database standard verification
     *
     * @summary Retrieve a Database Standard Verification
     * @throws FetchError<400, types.RetrieveADatabaseStandardVerificationResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveADatabaseStandardVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveADatabaseStandardVerificationResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveADatabaseStandardVerificationResponse404> Not Found
     * @throws FetchError<429, types.RetrieveADatabaseStandardVerificationResponse429> Too Many Requests
     */
    SDK.prototype.retrieveADatabaseStandardVerification = function (metadata) {
        return this.core.fetch('/verification/database-standards/{verification-id}', 'get', metadata);
    };
    /**
     * Submit a database verification for processing. Can only submit `initiated` verifications
     *
     * @summary Submit a Database Standard Verification
     * @throws FetchError<400, types.SubmitADatabaseStandardVerificationResponse400> Bad Request
     * @throws FetchError<401, types.SubmitADatabaseStandardVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.SubmitADatabaseStandardVerificationResponse403> Forbidden
     * @throws FetchError<404, types.SubmitADatabaseStandardVerificationResponse404> Not Found
     * @throws FetchError<409, types.SubmitADatabaseStandardVerificationResponse409> Conflict
     * @throws FetchError<422, types.SubmitADatabaseStandardVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.SubmitADatabaseStandardVerificationResponse429> Too Many Requests
     */
    SDK.prototype.submitADatabaseStandardVerification = function (metadata) {
        return this.core.fetch('/verification/database-standards/{verification-id}/submit', 'post', metadata);
    };
    /**
     * Create a new TIN database verification
     *
     * @summary Create a TIN Database Verification
     * @throws FetchError<400, types.CreateATinDatabaseVerificationResponse400> Bad Request
     * @throws FetchError<401, types.CreateATinDatabaseVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.CreateATinDatabaseVerificationResponse403> Forbidden
     * @throws FetchError<404, types.CreateATinDatabaseVerificationResponse404> Not Found
     * @throws FetchError<409, types.CreateATinDatabaseVerificationResponse409> Conflict
     * @throws FetchError<422, types.CreateATinDatabaseVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateATinDatabaseVerificationResponse429> Too Many Requests
     */
    SDK.prototype.createATinDatabaseVerification = function (body, metadata) {
        return this.core.fetch('/verification/database-tins', 'post', body, metadata);
    };
    /**
     * Retrieve a TIN database verification
     *
     * @summary Retrieve a TIN Database Verification
     * @throws FetchError<400, types.RetrieveATinDatabaseVerificationResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveATinDatabaseVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveATinDatabaseVerificationResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveATinDatabaseVerificationResponse404> Not Found
     * @throws FetchError<429, types.RetrieveATinDatabaseVerificationResponse429> Too Many Requests
     */
    SDK.prototype.retrieveATinDatabaseVerification = function (metadata) {
        return this.core.fetch('/verification/database-tins/{verification-id}', 'get', metadata);
    };
    SDK.prototype.updateATinDatabaseVerification = function (body, metadata) {
        return this.core.fetch('/verification/database-tins/{verification-id}', 'patch', body, metadata);
    };
    /**
     * Submit a TIN database verification for processing. Can only submit `initiated`
     * verifications
     *
     * @summary Submit a TIN Database Verification
     * @throws FetchError<400, types.SubmitATinDatabaseVerificationResponse400> Bad Request
     * @throws FetchError<401, types.SubmitATinDatabaseVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.SubmitATinDatabaseVerificationResponse403> Forbidden
     * @throws FetchError<404, types.SubmitATinDatabaseVerificationResponse404> Not Found
     * @throws FetchError<409, types.SubmitATinDatabaseVerificationResponse409> Conflict
     * @throws FetchError<422, types.SubmitATinDatabaseVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.SubmitATinDatabaseVerificationResponse429> Too Many Requests
     */
    SDK.prototype.submitATinDatabaseVerification = function (metadata) {
        return this.core.fetch('/verification/database-tins/{verification-id}/submit', 'post', metadata);
    };
    /**
     * Create a new database verification
     *
     * @summary Create a Database Verification
     * @throws FetchError<400, types.CreateADatabaseVerificationResponse400> Bad Request
     * @throws FetchError<401, types.CreateADatabaseVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.CreateADatabaseVerificationResponse403> Forbidden
     * @throws FetchError<404, types.CreateADatabaseVerificationResponse404> Not Found
     * @throws FetchError<409, types.CreateADatabaseVerificationResponse409> Conflict
     * @throws FetchError<422, types.CreateADatabaseVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateADatabaseVerificationResponse429> Too Many Requests
     */
    SDK.prototype.createADatabaseVerification = function (body, metadata) {
        return this.core.fetch('/verification/databases', 'post', body, metadata);
    };
    /**
     * Retrieve a database verification
     *
     * @summary Retrieve a Database Verification
     * @throws FetchError<400, types.RetrieveADatabaseVerificationResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveADatabaseVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveADatabaseVerificationResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveADatabaseVerificationResponse404> Not Found
     * @throws FetchError<429, types.RetrieveADatabaseVerificationResponse429> Too Many Requests
     */
    SDK.prototype.retrieveADatabaseVerification = function (metadata) {
        return this.core.fetch('/verification/databases/{verification-id}', 'get', metadata);
    };
    /**
     * Updates an existing database verification. Can only update `initiated` verifications.
     *
     * @summary Update a Database Verification
     * @throws FetchError<400, types.UpdateADatabaseVerificationResponse400> Bad Request
     * @throws FetchError<401, types.UpdateADatabaseVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.UpdateADatabaseVerificationResponse403> Forbidden
     * @throws FetchError<404, types.UpdateADatabaseVerificationResponse404> Not Found
     * @throws FetchError<409, types.UpdateADatabaseVerificationResponse409> Conflict
     * @throws FetchError<422, types.UpdateADatabaseVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.UpdateADatabaseVerificationResponse429> Too Many Requests
     */
    SDK.prototype.updateADatabaseVerification = function (body, metadata) {
        return this.core.fetch('/verification/databases/{verification-id}', 'patch', body, metadata);
    };
    /**
     * Submit a database verification for processing. Can only submit `initiated` verifications
     *
     * @summary Submit a Database Verification
     * @throws FetchError<400, types.SubmitADatabaseVerificationResponse400> Bad Request
     * @throws FetchError<401, types.SubmitADatabaseVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.SubmitADatabaseVerificationResponse403> Forbidden
     * @throws FetchError<404, types.SubmitADatabaseVerificationResponse404> Not Found
     * @throws FetchError<409, types.SubmitADatabaseVerificationResponse409> Conflict
     * @throws FetchError<422, types.SubmitADatabaseVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.SubmitADatabaseVerificationResponse429> Too Many Requests
     */
    SDK.prototype.submitADatabaseVerification = function (metadata) {
        return this.core.fetch('/verification/databases/{verification-id}/submit', 'post', metadata);
    };
    /**
     * Create a verification of a generic document.
     *
     * @summary Create a Document Verification
     * @throws FetchError<400, types.CreateADocumentVerificationResponse400> Bad Request
     * @throws FetchError<401, types.CreateADocumentVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.CreateADocumentVerificationResponse403> Forbidden
     * @throws FetchError<404, types.CreateADocumentVerificationResponse404> Not Found
     * @throws FetchError<409, types.CreateADocumentVerificationResponse409> Conflict
     * @throws FetchError<422, types.CreateADocumentVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateADocumentVerificationResponse429> Too Many Requests
     */
    SDK.prototype.createADocumentVerification = function (body, metadata) {
        return this.core.fetch('/verification/documents', 'post', body, metadata);
    };
    /**
     * Retrieves the details of a specific document verification
     *
     * @summary Retrieve a Document Verification
     * @throws FetchError<400, types.RetrieveADocumentVerificationResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveADocumentVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveADocumentVerificationResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveADocumentVerificationResponse404> Not Found
     * @throws FetchError<429, types.RetrieveADocumentVerificationResponse429> Too Many Requests
     */
    SDK.prototype.retrieveADocumentVerification = function (metadata) {
        return this.core.fetch('/verification/documents/{verification-id}', 'get', metadata);
    };
    /**
     * Submit a document verification for processing. Can only submit `initiated`
     * verifications.
     *
     * @summary Submit a Document Verification
     * @throws FetchError<400, types.SubmitADocumentVerificationResponse400> Bad Request
     * @throws FetchError<401, types.SubmitADocumentVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.SubmitADocumentVerificationResponse403> Forbidden
     * @throws FetchError<404, types.SubmitADocumentVerificationResponse404> Not Found
     * @throws FetchError<409, types.SubmitADocumentVerificationResponse409> Conflict
     * @throws FetchError<422, types.SubmitADocumentVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.SubmitADocumentVerificationResponse429> Too Many Requests
     */
    SDK.prototype.submitADocumentVerification = function (metadata) {
        return this.core.fetch('/verification/documents/{verification-id}/submit', 'post', metadata);
    };
    /**
     * Retrieve a email address verification
     *
     * @summary Retrieve a Email Address Verification
     * @throws FetchError<400, types.RetrieveAEmailAddressVerificationResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAEmailAddressVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAEmailAddressVerificationResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAEmailAddressVerificationResponse404> Not Found
     * @throws FetchError<429, types.RetrieveAEmailAddressVerificationResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAEmailAddressVerification = function (metadata) {
        return this.core.fetch('/verification/email-addresses/{verification-id}', 'get', metadata);
    };
    /**
     * Create a new email address verification
     *
     * @summary Create an Email Address Verification
     * @throws FetchError<400, types.CreateAnEmailAddressVerificationResponse400> Bad Request
     * @throws FetchError<401, types.CreateAnEmailAddressVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAnEmailAddressVerificationResponse403> Forbidden
     * @throws FetchError<404, types.CreateAnEmailAddressVerificationResponse404> Not Found
     * @throws FetchError<409, types.CreateAnEmailAddressVerificationResponse409> Conflict
     * @throws FetchError<422, types.CreateAnEmailAddressVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAnEmailAddressVerificationResponse429> Too Many Requests
     */
    SDK.prototype.createAnEmailAddressVerification = function (body, metadata) {
        return this.core.fetch('/verification/email-addresses', 'post', body, metadata);
    };
    /**
     * Confirm the confirmation code sent to the email address. Can only confirm `initiated`
     * verifications.
     *
     * @summary Confirm an Email Address Verification
     * @throws FetchError<400, types.ConfirmAnEmailAddressVerificationResponse400> Bad Request
     * @throws FetchError<401, types.ConfirmAnEmailAddressVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.ConfirmAnEmailAddressVerificationResponse403> Forbidden
     * @throws FetchError<404, types.ConfirmAnEmailAddressVerificationResponse404> Not Found
     * @throws FetchError<409, types.ConfirmAnEmailAddressVerificationResponse409> Conflict
     * @throws FetchError<422, types.ConfirmAnEmailAddressVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ConfirmAnEmailAddressVerificationResponse429> Too Many Requests
     */
    SDK.prototype.confirmAnEmailAddressVerification = function (body, metadata) {
        return this.core.fetch('/verification/email-addresses/{verification-id}/confirm', 'post', body, metadata);
    };
    SDK.prototype.sendAnEmail = function (body, metadata) {
        return this.core.fetch('/verification/email-addresses/{verification-id}/send-confirmation-code', 'post', body, metadata);
    };
    /**
     * Submit an email address verification for processing. Can only submit `initiated`
     * verifications.
     *
     * @summary Submit an Email Address Verification
     * @throws FetchError<400, types.SubmitAnEmailAddressVerificationResponse400> Bad Request
     * @throws FetchError<401, types.SubmitAnEmailAddressVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.SubmitAnEmailAddressVerificationResponse403> Forbidden
     * @throws FetchError<404, types.SubmitAnEmailAddressVerificationResponse404> Not Found
     * @throws FetchError<409, types.SubmitAnEmailAddressVerificationResponse409> Conflict
     * @throws FetchError<422, types.SubmitAnEmailAddressVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.SubmitAnEmailAddressVerificationResponse429> Too Many Requests
     */
    SDK.prototype.submitAnEmailAddressVerification = function (metadata) {
        return this.core.fetch('/verification/email-addresses/{verification-id}/submit', 'post', metadata);
    };
    /**
     * Create a new government ID verification
     *
     * @summary Create a Government ID Verification
     * @throws FetchError<400, types.CreateAGovIdVerificationResponse400> Bad Request
     * @throws FetchError<401, types.CreateAGovIdVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAGovIdVerificationResponse403> Forbidden
     * @throws FetchError<404, types.CreateAGovIdVerificationResponse404> Not Found
     * @throws FetchError<409, types.CreateAGovIdVerificationResponse409> Conflict
     * @throws FetchError<422, types.CreateAGovIdVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAGovIdVerificationResponse429> Too Many Requests
     */
    SDK.prototype.createAGovIdVerification = function (body, metadata) {
        return this.core.fetch('/verification/government-ids', 'post', body, metadata);
    };
    /**
     * Retrieve a government ID verification
     *
     * @summary Retrieve a Government Id Verification
     * @throws FetchError<400, types.RetrieveAGovernmentIdVerificationResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAGovernmentIdVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAGovernmentIdVerificationResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAGovernmentIdVerificationResponse404> Not Found
     * @throws FetchError<429, types.RetrieveAGovernmentIdVerificationResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAGovernmentIdVerification = function (metadata) {
        return this.core.fetch('/verification/government-ids/{verification-id}', 'get', metadata);
    };
    /**
     * Updates an existing government ID verification. Can only update `initiated`
     * verifications.
     *
     * @summary Update a Government ID Verification
     * @throws FetchError<400, types.UpdateAGovernmentIdVerificationResponse400> Bad Request
     * @throws FetchError<401, types.UpdateAGovernmentIdVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.UpdateAGovernmentIdVerificationResponse403> Forbidden
     * @throws FetchError<404, types.UpdateAGovernmentIdVerificationResponse404> Not Found
     * @throws FetchError<409, types.UpdateAGovernmentIdVerificationResponse409> Conflict
     * @throws FetchError<422, types.UpdateAGovernmentIdVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.UpdateAGovernmentIdVerificationResponse429> Too Many Requests
     */
    SDK.prototype.updateAGovernmentIdVerification = function (body, metadata) {
        return this.core.fetch('/verification/government-ids/{verification-id}', 'patch', body, metadata);
    };
    /**
     * Submit a government ID verification for processing. Can only submit `initiated`
     * verifications with photos of the ID attached.
     *
     * @summary Submit a Government ID Verification
     * @throws FetchError<400, types.SubmitAGovernmentIdVerificationResponse400> Bad Request
     * @throws FetchError<401, types.SubmitAGovernmentIdVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.SubmitAGovernmentIdVerificationResponse403> Forbidden
     * @throws FetchError<404, types.SubmitAGovernmentIdVerificationResponse404> Not Found
     * @throws FetchError<409, types.SubmitAGovernmentIdVerificationResponse409> Conflict
     * @throws FetchError<422, types.SubmitAGovernmentIdVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.SubmitAGovernmentIdVerificationResponse429> Too Many Requests
     */
    SDK.prototype.submitAGovernmentIdVerification = function (metadata) {
        return this.core.fetch('/verification/government-ids/{verification-id}/submit', 'post', metadata);
    };
    /**
     * Retrieve a Government ID NFC verification
     *
     * @summary Retrieve a Government Id NFC Verification
     * @throws FetchError<400, types.RetrieveAGovernmentIdNfcVerificationResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAGovernmentIdNfcVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAGovernmentIdNfcVerificationResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAGovernmentIdNfcVerificationResponse404> Not Found
     * @throws FetchError<429, types.RetrieveAGovernmentIdNfcVerificationResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAGovernmentIdNfcVerification = function (metadata) {
        return this.core.fetch('/verification/government-id-nfcs/{verification-id}', 'get', metadata);
    };
    /**
     * Create a new phone number verification
     *
     * @summary Create a Phone Number Verification
     * @throws FetchError<400, types.CreateAPhoneNumberVerificationResponse400> Bad Request
     * @throws FetchError<401, types.CreateAPhoneNumberVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAPhoneNumberVerificationResponse403> Forbidden
     * @throws FetchError<404, types.CreateAPhoneNumberVerificationResponse404> Not Found
     * @throws FetchError<409, types.CreateAPhoneNumberVerificationResponse409> Conflict
     * @throws FetchError<422, types.CreateAPhoneNumberVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAPhoneNumberVerificationResponse429> Too Many Requests
     */
    SDK.prototype.createAPhoneNumberVerification = function (body, metadata) {
        return this.core.fetch('/verification/phone-numbers', 'post', body, metadata);
    };
    /**
     * Retrieve a phone number verification
     *
     * @summary Retrieve a Phone Number Verification
     * @throws FetchError<400, types.RetrieveAPhoneNumberVerificationResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAPhoneNumberVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAPhoneNumberVerificationResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAPhoneNumberVerificationResponse404> Not Found
     * @throws FetchError<429, types.RetrieveAPhoneNumberVerificationResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAPhoneNumberVerification = function (metadata) {
        return this.core.fetch('/verification/phone-numbers/{verification-id}', 'get', metadata);
    };
    /**
     * Updates an existing phone number Verification. Can only update `initiated`
     * verifications.
     *
     * @summary Update a Phone Number Verification
     * @throws FetchError<400, types.UpdateAPhoneNumberVerificationResponse400> Bad Request
     * @throws FetchError<401, types.UpdateAPhoneNumberVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.UpdateAPhoneNumberVerificationResponse403> Forbidden
     * @throws FetchError<404, types.UpdateAPhoneNumberVerificationResponse404> Not Found
     * @throws FetchError<409, types.UpdateAPhoneNumberVerificationResponse409> Conflict
     * @throws FetchError<422, types.UpdateAPhoneNumberVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.UpdateAPhoneNumberVerificationResponse429> Too Many Requests
     */
    SDK.prototype.updateAPhoneNumberVerification = function (body, metadata) {
        return this.core.fetch('/verification/phone-numbers/{verification-id}', 'patch', body, metadata);
    };
    /**
     * Confirm the confirmation code sent to the phone number. Can only confirm `initiated`
     * verifications.
     *
     * @summary Confirm a Phone Number Verification
     * @throws FetchError<400, types.ConfirmAPhoneNumberVerificationResponse400> Bad Request
     * @throws FetchError<401, types.ConfirmAPhoneNumberVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.ConfirmAPhoneNumberVerificationResponse403> Forbidden
     * @throws FetchError<404, types.ConfirmAPhoneNumberVerificationResponse404> Not Found
     * @throws FetchError<409, types.ConfirmAPhoneNumberVerificationResponse409> Conflict
     * @throws FetchError<422, types.ConfirmAPhoneNumberVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ConfirmAPhoneNumberVerificationResponse429> Too Many Requests
     */
    SDK.prototype.confirmAPhoneNumberVerification = function (body, metadata) {
        return this.core.fetch('/verification/phone-numbers/{verification-id}/confirm', 'post', body, metadata);
    };
    SDK.prototype.sendAnSms = function (body, metadata) {
        return this.core.fetch('/verification/phone-numbers/{verification-id}/send-confirmation-code', 'post', body, metadata);
    };
    /**
     * Submit a phone number verification for processing. Can only submit `initiated`
     * verifications.
     *
     * @summary Submit a Phone Number Verification
     * @throws FetchError<400, types.SubmitAPhoneNumberVerificationResponse400> Bad Request
     * @throws FetchError<401, types.SubmitAPhoneNumberVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.SubmitAPhoneNumberVerificationResponse403> Forbidden
     * @throws FetchError<404, types.SubmitAPhoneNumberVerificationResponse404> Not Found
     * @throws FetchError<409, types.SubmitAPhoneNumberVerificationResponse409> Conflict
     * @throws FetchError<422, types.SubmitAPhoneNumberVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.SubmitAPhoneNumberVerificationResponse429> Too Many Requests
     */
    SDK.prototype.submitAPhoneNumberVerification = function (body, metadata) {
        return this.core.fetch('/verification/phone-numbers/{verification-id}/submit', 'post', body, metadata);
    };
    /**
     * Create a new selfie verification
     *
     * @summary Create a Selfie Verification
     * @throws FetchError<400, types.CreateASelfieVerificationResponse400> Bad Request
     * @throws FetchError<401, types.CreateASelfieVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.CreateASelfieVerificationResponse403> Forbidden
     * @throws FetchError<404, types.CreateASelfieVerificationResponse404> Not Found
     * @throws FetchError<409, types.CreateASelfieVerificationResponse409> Conflict
     * @throws FetchError<422, types.CreateASelfieVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateASelfieVerificationResponse429> Too Many Requests
     */
    SDK.prototype.createASelfieVerification = function (body, metadata) {
        return this.core.fetch('/verification/selfies', 'post', body, metadata);
    };
    /**
     * Retrieve a selfie ID verification
     *
     * @summary Retrieve a Selfie Verification
     * @throws FetchError<400, types.RetrieveASelfieVerificationResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveASelfieVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveASelfieVerificationResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveASelfieVerificationResponse404> Not Found
     * @throws FetchError<429, types.RetrieveASelfieVerificationResponse429> Too Many Requests
     */
    SDK.prototype.retrieveASelfieVerification = function (metadata) {
        return this.core.fetch('/verification/selfies/{verification-id}', 'get', metadata);
    };
    /**
     * Submit a selfie verification for processing. Can only submit `initiated` verifications
     *
     * @summary Submit a Selfie Verification
     * @throws FetchError<400, types.SubmitASelfieVerificationResponse400> Bad Request
     * @throws FetchError<401, types.SubmitASelfieVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.SubmitASelfieVerificationResponse403> Forbidden
     * @throws FetchError<404, types.SubmitASelfieVerificationResponse404> Not Found
     * @throws FetchError<409, types.SubmitASelfieVerificationResponse409> Conflict
     * @throws FetchError<422, types.SubmitASelfieVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.SubmitASelfieVerificationResponse429> Too Many Requests
     */
    SDK.prototype.submitASelfieVerification = function (metadata) {
        return this.core.fetch('/verification/selfies/{verification-id}/submit', 'post', metadata);
    };
    /**
     * Retrieves the details of an existing Verification.
     *
     * @summary Retrieve a Verification
     * @throws FetchError<400, types.RetrieveAVerificationResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAVerificationResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAVerificationResponse404> Not Found
     * @throws FetchError<429, types.RetrieveAVerificationResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAVerification = function (metadata) {
        return this.core.fetch('/verifications/{verification-id}', 'get', metadata);
    };
    /**
     * Permanently deletes personally identifiable information (PII) for a Verification. **This
     * action cannot be undone**. This endpoint can be used to comply with privacy regulations
     * such as GDPR / CCPA or to enforce data privacy.
     *
     * @summary Redact a Verification
     * @throws FetchError<400, types.RedactAVerificationResponse400> Bad Request
     * @throws FetchError<401, types.RedactAVerificationResponse401> Unauthorized
     * @throws FetchError<403, types.RedactAVerificationResponse403> Forbidden
     * @throws FetchError<404, types.RedactAVerificationResponse404> Not Found
     * @throws FetchError<409, types.RedactAVerificationResponse409> Conflict
     * @throws FetchError<422, types.RedactAVerificationResponse422> Unprocessable Entity
     * @throws FetchError<429, types.RedactAVerificationResponse429> Too Many Requests
     */
    SDK.prototype.redactAVerification = function (metadata) {
        return this.core.fetch('/verifications/{verification-id}', 'delete', metadata);
    };
    /**
     * Prints a verification in PDF format.
     *
     * @summary Print Verification PDF
     * @throws FetchError<400, types.PrintAVerificationAsPdfResponse400> Bad Request
     * @throws FetchError<401, types.PrintAVerificationAsPdfResponse401> Unauthorized
     * @throws FetchError<403, types.PrintAVerificationAsPdfResponse403> Forbidden
     * @throws FetchError<404, types.PrintAVerificationAsPdfResponse404> Not Found
     * @throws FetchError<429, types.PrintAVerificationAsPdfResponse429> Too Many Requests
     */
    SDK.prototype.printAVerificationAsPdf = function (metadata) {
        return this.core.fetch('/verifications/{verification-id}/print', 'get', metadata);
    };
    /**
     * Returns a list of your environment's webhooks.
     *
     * @summary List all Webhooks
     * @throws FetchError<400, types.ListAllWebhooksResponse400> Bad Request
     * @throws FetchError<401, types.ListAllWebhooksResponse401> Unauthorized
     * @throws FetchError<403, types.ListAllWebhooksResponse403> Forbidden
     * @throws FetchError<429, types.ListAllWebhooksResponse429> Too Many Requests
     */
    SDK.prototype.listAllWebhooks = function (metadata) {
        return this.core.fetch('/webhooks', 'get', metadata);
    };
    /**
     * Creates a new webhook with response defaults.
     *
     * @summary Create a Webhook
     * @throws FetchError<400, types.CreateAWebhookResponse400> Bad Request
     * @throws FetchError<401, types.CreateAWebhookResponse401> Unauthorized
     * @throws FetchError<403, types.CreateAWebhookResponse403> Forbidden
     * @throws FetchError<409, types.CreateAWebhookResponse409> Conflict
     * @throws FetchError<422, types.CreateAWebhookResponse422> Unprocessable Entity
     * @throws FetchError<429, types.CreateAWebhookResponse429> Too Many Requests
     */
    SDK.prototype.createAWebhook = function (body, metadata) {
        return this.core.fetch('/webhooks', 'post', body, metadata);
    };
    /**
     * Retrieves the details of an existing webhook, including its secret.
     *
     * @summary Retrieve a Webhook
     * @throws FetchError<400, types.RetrieveAWebhookResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAWebhookResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAWebhookResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAWebhookResponse404> Not Found
     * @throws FetchError<429, types.RetrieveAWebhookResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAWebhook = function (metadata) {
        return this.core.fetch('/webhooks/{webhook-id}', 'get', metadata);
    };
    SDK.prototype.updateAWebhook = function (body, metadata) {
        return this.core.fetch('/webhooks/{webhook-id}', 'patch', body, metadata);
    };
    /**
     * Archives a webhook.
     *
     * @summary Archive a Webhook
     * @throws FetchError<400, types.ArchiveAWebhookResponse400> Bad Request
     * @throws FetchError<401, types.ArchiveAWebhookResponse401> Unauthorized
     * @throws FetchError<403, types.ArchiveAWebhookResponse403> Forbidden
     * @throws FetchError<404, types.ArchiveAWebhookResponse404> Not Found
     * @throws FetchError<409, types.ArchiveAWebhookResponse409> Conflict
     * @throws FetchError<422, types.ArchiveAWebhookResponse422> Unprocessable Entity
     * @throws FetchError<429, types.ArchiveAWebhookResponse429> Too Many Requests
     */
    SDK.prototype.archiveAWebhook = function (metadata) {
        return this.core.fetch('/webhooks/{webhook-id}/archive', 'post', metadata);
    };
    /**
     * Enables a webhook.
     *
     * @summary Enable a Webhook
     * @throws FetchError<400, types.EnableAWebhookResponse400> Bad Request
     * @throws FetchError<401, types.EnableAWebhookResponse401> Unauthorized
     * @throws FetchError<403, types.EnableAWebhookResponse403> Forbidden
     * @throws FetchError<404, types.EnableAWebhookResponse404> Not Found
     * @throws FetchError<409, types.EnableAWebhookResponse409> Conflict
     * @throws FetchError<422, types.EnableAWebhookResponse422> Unprocessable Entity
     * @throws FetchError<429, types.EnableAWebhookResponse429> Too Many Requests
     */
    SDK.prototype.enableAWebhook = function (metadata) {
        return this.core.fetch('/webhooks/{webhook-id}/enable', 'post', metadata);
    };
    /**
     * Disables a webhook.
     *
     * @summary Disable a Webhook
     * @throws FetchError<400, types.DisableAWebhookResponse400> Bad Request
     * @throws FetchError<401, types.DisableAWebhookResponse401> Unauthorized
     * @throws FetchError<403, types.DisableAWebhookResponse403> Forbidden
     * @throws FetchError<404, types.DisableAWebhookResponse404> Not Found
     * @throws FetchError<409, types.DisableAWebhookResponse409> Conflict
     * @throws FetchError<422, types.DisableAWebhookResponse422> Unprocessable Entity
     * @throws FetchError<429, types.DisableAWebhookResponse429> Too Many Requests
     */
    SDK.prototype.disableAWebhook = function (metadata) {
        return this.core.fetch('/webhooks/{webhook-id}/disable', 'post', metadata);
    };
    SDK.prototype.rotateAWebhookSecret = function (body, metadata) {
        return this.core.fetch('/webhooks/{webhook-id}/rotate-secret', 'post', body, metadata);
    };
    SDK.prototype.createAWorkflowRun = function (body, metadata) {
        return this.core.fetch('/workflows/{workflow-id}/trigger', 'post', body, metadata);
    };
    /**
     * Retrieves the details of an existing workflow run.
     *
     * @summary Retrieve a Workflow Run
     * @throws FetchError<400, types.RetrieveAWorkflowRunResponse400> Bad Request
     * @throws FetchError<401, types.RetrieveAWorkflowRunResponse401> Unauthorized
     * @throws FetchError<403, types.RetrieveAWorkflowRunResponse403> Forbidden
     * @throws FetchError<404, types.RetrieveAWorkflowRunResponse404> Not Found
     * @throws FetchError<429, types.RetrieveAWorkflowRunResponse429> Too Many Requests
     */
    SDK.prototype.retrieveAWorkflowRun = function (metadata) {
        return this.core.fetch('/workflow-runs/{workflow-run-id}', 'get', metadata);
    };
    /**
     * Returns a list of your environment's workflow runs.
     *
     * @summary List all Workflow Runs
     * @throws FetchError<400, types.ListAllWorkflowRunsResponse400> Bad Request
     * @throws FetchError<401, types.ListAllWorkflowRunsResponse401> Unauthorized
     * @throws FetchError<403, types.ListAllWorkflowRunsResponse403> Forbidden
     * @throws FetchError<429, types.ListAllWorkflowRunsResponse429> Too Many Requests
     */
    SDK.prototype.listAllWorkflowRuns = function (metadata) {
        return this.core.fetch('/workflow-runs', 'get', metadata);
    };
    return SDK;
}());
var createSDK = (function () { return new SDK(); })();
module.exports = createSDK;
