const bcrypt = require('bcryptjs');
const httpStatus = require('http-status');
const { User, File, Setting, Account, Business, DeletedUser, Profile } = require('../models');
const { userDefaultSettings, azureContainers, registrationStatuses, clientUserTypes } = require('../config/constants');
const { uploadAzureBlob, saveFile } = require('./azure.file.service');
const { scheduleDeleteUserInfo } = require('../utils/jobs/jobSchedulers');
const validateId = require('./shared/validateId');
const ApiError = require('../utils/ApiError');
const emailService = require('./email.service');
const messageService = require('./message.service');
const profileService = require('./profile.service');
const tokenService = require('./token.service');
const notificationService = require('./notification.service');

const mongodbQueryUserPropsToRemove = '-password -__v';

const basicUserPopulate = {
  select: 'firstName lastName middleName username photo tagLine business online',
  populate: [
    { path: 'photo', select: 'url' },
    { path: 'business', select: 'verificationStatus' },
  ],
};

const emailExists = async (email) => {
  const user = await User.findOne({ email });
  return !!user;
};

const usernameExists = async (username) => {
  const user = await User.findOne({ username });
  return !!user;
};

const usernameEmailAvail = async (email, username) => {
  const user = await User.findOne({
    $or: [{ username: { $regex: username, $options: 'i' } }, { email: { $regex: email, $options: 'i' } }],
  });
  return !user;
};

const processFileUpload = async (file, containerName) => {
  const { filename, url } = await uploadAzureBlob(file, containerName);
  const savedFile = await saveFile(url, filename, containerName);
  return savedFile._id;
};

const sendVerificationEmail = async (email, userParam, signupAs, returnUrl) => {
  // Duplicate Signup is for example, a user is signed up as a business contact person and wants to signup as a student
  const user = userParam || (await User.findOne({ email }));
  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, 'User record not found');
  }

  if (user.emailVerificationCount > 5) {
    await User.updateOne({ email }, { $set: { locked: true } });
    throw new ApiError(httpStatus.TOO_MANY_REQUESTS, 'Too many requests. Please, contact Support.');
  }
  await User.updateOne({ email }, { $set: { emailVerificationCount: user.emailVerificationCount + 1 } });

  const verifyEmailToken = await tokenService.generateVerifyEmailToken(user, signupAs);

  if (user.isEmailVerified) {
    throw new ApiError(httpStatus.ALREADY_REPORTED, 'Email already verified');
  } else {
    await emailService.sendVerificationEmail(user, verifyEmailToken, signupAs, returnUrl);
  }
};

const createUser = async (userBody) => {
  const existingUser = await User.isEmailTaken(userBody.email);
  // eslint-disable-next-line no-param-reassign
  userBody.username = userBody.username.toLowerCase();
  if (userBody.username.match(/[^a-zA-Z0-9]/)) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Username can only contain letters and numbers');
  }

  const isUsernameTaken = await User.isUsernameTaken(userBody.username);

  if (existingUser) {
    if (isUsernameTaken) {
      throw new ApiError(httpStatus.CONFLICT, 'Email and username are already taken');
    }
    throw new ApiError(httpStatus.CONFLICT, 'Email already taken');
  }

  if (isUsernameTaken) {
    throw new ApiError(httpStatus.CONFLICT, 'Username already taken');
  }

  return User.create(userBody);
};

const getUserProfile = async (userId, isOwner) => {
  validateId(userId);

  const user = await User.findById(userId)
    .select(
      `${
        !isOwner
          ? 'firstName lastName middleName username email photo banner profile followers following postLikes postBookmarks reposts commentLikes tagLine business online gender countryOfResidence stateOfResidence nationality preferredDegreeOfStudy preferredSchoolsOfStudy preferredCountryOfStudy preferredFieldsOfStudy'
          : ''
      }`,
    )
    .populate([
      {
        path: 'photo',
        select: 'url -_id',
      },
      {
        path: 'banner',
        select: 'url -_id',
      },
      {
        path: 'profile',
        populate: ['education', 'certification', 'testscore', 'experience', 'project', 'volunteering', 'award'],
      },
      {
        path: 'business',
        select: 'displayName username verificationStatus',
      },
    ]);

  if (isOwner) {
    return user;
  }

  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, 'User record not found');
  }

  const userProfileFiltered = await User.privacy({ resourceId: userId, userProfileRecord: user });

  return userProfileFiltered;
};

const onboardStudent = async (user, userDataParam, files) => {
  const userData = userDataParam;
  const photos = {};
  // create profile
  if (user.registrationStatus !== registrationStatuses.ACCOUNT_VERIFIED) {
    if (user.registrationStatus === registrationStatuses.SIGNUP_COMPLETED) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Please activate your account first.');
    }
    throw new ApiError(httpStatus.BAD_REQUEST, 'Onboarding already completed.');
  }

  if (userData.preferredSchoolsOfStudy) {
    userData.preferredSchoolsOfStudy = Array.isArray(userData.preferredSchoolsOfStudy)
      ? userData.preferredSchoolsOfStudy
      : [userData.preferredSchoolsOfStudy];
  }
  if (userData.preferredFieldsOfStudy) {
    userData.preferredFieldsOfStudy = Array.isArray(userData.preferredFieldsOfStudy)
      ? userData.preferredFieldsOfStudy
      : [userData.preferredFieldsOfStudy];
  }

  if (files?.photo) {
    photos.photo = await processFileUpload(files.photo[0], azureContainers.userProfilePhotos);
  }

  if (files?.banner) {
    photos.banner = await processFileUpload(files.banner[0], azureContainers.userProfileBanners);
  }

  const profile = await profileService.createProfile(user, {
    basicInformation: { personalStatement: userData.personalStatement },
  });
  const setting = await Setting.create({ ...userDefaultSettings(), user: user._id });
  Object.assign(user, {
    ...userData,
    ...photos,
    registrationStatus: registrationStatuses.ONBOARDING_COMPLETE,
    profile: profile._id,
    setting: setting._id,
    roles: Array.from(new Set([...user.roles, clientUserTypes.STUDENT])),
    pendingStudent: 'false',
  });
  await user.save();
  const { access, refresh } = await tokenService.generateAuthTokens(user);
  return { user, access, refresh };
};

// const verifyDuplicateSignupEmail = async (email, signupAs, token) => {
//   let user;
//   let returnData = {};

//   try {
//     const verifyTokenDoc = await tokenService.verifyToken(token, tokenTypes.VERIFY_EMAIL);
//     user = await User.findById(verifyTokenDoc.user._id);
//   } catch (error) {
//     user = await User.findOne({ email });
//     if (
//       user &&
//       ((signupAs === clientUserTypes.BUSINESS && !user.pendingBusiness) ||
//         (signupAs === clientUserTypes.STUDENT && !user.pendingStudent))
//     ) {
//       // Resend verification email
//       const verifyEmailToken = await tokenService.generateVerifyEmailToken(user);
//       await emailService.sendVerifyDuplicateSignupEmail(user, verifyEmailToken, signupAs);
//       throw new ApiError(httpStatus.TEMPORARY_REDIRECT, 'Link is expired. A new link has been sent to your email.');
//     }
//     throw new ApiError(httpStatus.UNAUTHORIZED, 'Verification failed. Link is invalid');
//   }

//   if (user.email !== email) {
//     throw new ApiError(httpStatus.UNAUTHORIZED, 'Inconsistent data.');
//   }

//   if (!user) {
//     throw new ApiError(httpStatus.NOT_FOUND, 'An error occurred. User record not found');
//   }

//   returnData.isEmailVerified = user.isEmailVerified;
//   if (signupAs === clientUserTypes.BUSINESS) {
//     if (user.pendingBusiness === 'true') {
//       throw new ApiError(httpStatus.CONFLICT, 'Business account already verified');
//     }
//     user.pendingBusiness = 'true';
//     returnData = { ...returnData, pendingBusiness: 'true', nextStep: 'onboard_business' };
//   } else if (signupAs === clientUserTypes.STUDENT) {
//     if (user.pendingStudent === 'true') {
//       throw new ApiError(httpStatus.CONFLICT, 'Student account already verified');
//     }
//     user.pendingStudent = 'true';
//     returnData = { ...returnData, pendingStudent: 'true', nextStep: 'onboard_student' };
//     returnData.pendingStudent = 'true';
//   }

//   await user.save();
//   return returnData;
// };

const queryUsers = async (filter, options) => {
  const users = await User.paginate(filter, options);
  return users;
};

const getUserById = async (userId, isOwner) => {
  const populate = [
    { path: 'photo', select: 'url -_id' },
    { path: 'banner', select: 'url -_id' },
  ];
  if (isOwner) {
    populate.push({
      path: 'business',
      select: 'displayName username profilePhoto coverPhoto tagLine referralCode',
      populate: [
        { path: 'profilePhoto', select: 'url' },
        { path: 'coverPhoto', select: 'url' },
      ],
    });
  }
  const user = await User.findById(userId).populate(populate).select(`${mongodbQueryUserPropsToRemove}`);
  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, 'User record not found');
  }
  return user;
};

const getUserByEmail = async (email, raiseError = true) => {
  const user = await User.findOne({ email: { $regex: email, $options: 'i' } }).populate([
    { path: 'photo', select: 'url -_id' },
    { path: 'banner', select: 'url -_id' },
    { path: 'business', select: 'verificationStatus' },
  ]);
  if (!user) {
    if (raiseError) {
      throw new ApiError(httpStatus.NOT_FOUND, 'User record not found');
    }
    return null;
  }
  return user;
};

const getUserByUsername = async (username) => {
  const user = User.findOne({ username: RegExp(username, 'i') }).lean();
  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, 'User record not found');
  }
  return user;
};

const countFollowership = async (userId) => {
  const user = await User.findById(userId);
  const data = { followers: user.followers.length, following: user.following.length };
  return data;
};

const getUserMetaData = async (userParam) => {
  const userId = userParam._id;
  const metaDataProps =
    'account banner countryOfResidence email emailVerificationCount firstName gender isEmailVerified lastName middleName nationality phoneVerified phoneVerifyCount photo premiumSubscriber profile registrationStatus roles tagLine username';

  const getUserPromise = User.findById(userId)
    .select(`${metaDataProps}`)
    .populate([
      { path: 'photo', select: 'url -_id' },
      { path: 'banner', select: 'url -_id' },
      {
        path: 'business',
        select: 'displayName username profilePhoto coverPhoto tagLine referralCode verificationStatus',
        populate: [
          { path: 'profilePhoto', select: 'url' },
          { path: 'coverPhoto', select: 'url' },
        ],
      },
    ])
    .lean();
  const getUnreadConversationsCountPromise = messageService.countUnreadConversations(userId);
  const getFollowsCountPromise = countFollowership(userId);
  const getUnreadNoificationsCountPromise = notificationService.getNotifications({
    recipient: userId,
    read: false,
  });
  const getSettingPromise = Setting.findOne({ user: userId }).lean();

  const getProfilePromise = Profile.findById(userParam.profile).select('basicInformation').lean();

  const [user, unreadConversationsCount, followsCount, unreadNotificationsCount, setting, profile] = await Promise.all([
    getUserPromise,
    getUnreadConversationsCountPromise,
    getFollowsCountPromise,
    getUnreadNoificationsCountPromise,
    getSettingPromise,
    getProfilePromise,
  ]);

  if (user.business) {
    user.business.id = user.business._id;
    delete user.business._id;
  }
  user.id = user._id;
  delete user._id;

  return {
    user: { ...user, personalStatement: profile?.basicInformation?.personalStatement },
    unreadConversationsCount,
    followsCount,
    unreadNotificationsCount: unreadNotificationsCount.totalResults,
    theme: setting.theme,
  };
};

const updateUserById = async (userId, body, files) => {
  const updateBody = body;
  const user = await getUserById(userId);

  if (updateBody.username) {
    updateBody.username = updateBody.username.toLowerCase();
    const existingUser = await getUserByUsername(updateBody.username);
    if (existingUser && existingUser._id.toString() !== userId) {
      throw new ApiError(httpStatus.CONFLICT, 'Username already taken');
    }
  }

  if (files?.photo) {
    const file = await File.findById(updateBody.photo);
    if (file) {
      file.remove();
    }
    updateBody.photo = await processFileUpload(files.photo[0], azureContainers.userProfilePhotos);
  }

  if (files?.banner) {
    const file = await File.findById(updateBody.banner);
    if (file) {
      file.remove();
    }
    updateBody.banner = await processFileUpload(files.banner[0], azureContainers.userProfileBanners);
  }

  if (updateBody.personalStatement) {
    await Profile.updateOne(
      { _id: user.profile },
      { $set: { 'basicInformation.personalStatement': updateBody.personalStatement } },
    );
    delete updateBody.personalStatement;
  }

  // faux
  Object.assign(user, updateBody);
  await user.save();
  return user;
};

const changeUserPassword = async (userId, updateBody) => {
  const user = await User.findById(userId);
  const { oldPassword, newPassword } = updateBody;

  if (!(await user.isPasswordMatch(oldPassword))) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'Old password incorrect');
  }

  const salt = await bcrypt.genSalt(10);
  const hashedPwd = await bcrypt.hash(newPassword, salt);
  user.password = hashedPwd;
  await user.save();
};

const deleteUserById = async (userId, data) => {
  const user = await User.findById(userId);
  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, 'User record not found');
  }

  const business = await Business.findOne({ contactPerson: userId });
  if (business) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Please delete associated business');
  }

  await scheduleDeleteUserInfo({ userId });

  const promises = [];
  if (user.profile) promises.push(profileService.deleteProfile(user.profile));
  if (user.setting) promises.push(Setting.deleteOne({ user: user._id }));
  if (user.account) promises.push(Account.deleteOne({ user: user._id }));
  if (user.photo) promises.push(File.deleteOne({ _id: user.photo }));
  if (user.banner) promises.push(File.deleteOne({ _id: user.banner }));

  promises.push(User.findByIdAndDelete(userId));

  promises.push(DeletedUser.create({ email: user.email, reason: data.reason || '', accountCreatedAt: user.createdAt }));

  await Promise.all(promises);
  return user;
};

/**
 * Follows or unfollows a user based on the specified type.
 * @param {string} actionType - Specifies the action: 'follow' or 'unfollow'.
 * @param {User} currentUser - The user performing the action.
 * @param {string} userToFollowOrUnfollowId - The ID of the user to follow or unfollow.
 */
const followUser = async (currentUser, userToFollowOrUnfollowId) => {
  if (currentUser._id.toString() === userToFollowOrUnfollowId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'You cannot follow yourself');
  }

  if (currentUser.following.includes(userToFollowOrUnfollowId)) {
    // if you're already following the user, then unfollow
    const userToUnfollow = await getUserById(userToFollowOrUnfollowId);

    // eslint-disable-next-line no-param-reassign
    currentUser.following = currentUser.following.filter((id) => id.toString() !== userToFollowOrUnfollowId);
    await currentUser.save();

    userToUnfollow.followers = userToUnfollow.followers.filter((id) => id.toString() !== currentUser._id.toString());
    await userToUnfollow.save();
  } else if (!currentUser.following.includes(userToFollowOrUnfollowId)) {
    const userToFollow = await getUserById(userToFollowOrUnfollowId);

    currentUser.following.push(userToFollow._id);
    await currentUser.save();

    userToFollow.followers.push(currentUser._id);
    await userToFollow.save();

    await notificationService.createFollowNotification(currentUser, userToFollow);
  }
};

const getFollows = async (type, userId, options) => {
  const { page, limit } = options;
  const skip = (page - 1) * limit;
  // type is either 'followers' or 'following'
  const user = await User.findById(userId).populate([
    {
      path: type,
      select: 'firstName lastName middleName tagLine followers following username countryOfResidence stateOfResidence',
      options: { skip, limit },
      populate: [
        {
          path: 'photo',
          select: 'url -_id',
        },
        {
          path: 'banner',
          select: 'url -_id',
        },
      ],
    },
  ]);
  if (!user) throw new ApiError(httpStatus.NOT_FOUND, 'User record not found');
  const totalCount = (await User.findById(userId))[type].length;
  const data = {
    data: user[type],
    page,
    limit,
    totalPages: Math.ceil(totalCount / limit),
    totalResults: totalCount,
  };
  return data;
};

const getMutuals = async (userId) => {
  const { followers, following: followings } = await User.findById(userId).populate([
    {
      path: 'followers',
      select: 'firstName lastName middleName username',
      populate: [
        {
          path: 'photo',
          select: 'url -_id',
        },
      ],
    },
    {
      path: 'following',
      select: 'firstName lastName middleName username',
      populate: [
        {
          path: 'photo',
          select: 'url -_id',
        },
      ],
    },
  ]);

  const mutuals = followers.filter((follower) =>
    followings.some((following) => following._id.toString() === follower._id.toString()),
  );
  return mutuals;
};

const blockUser = async (userProfile, userToBlockId) => {
  validateId(userToBlockId);
  const { blockedUsers } = userProfile;
  let status = 'blocked';
  if (!blockedUsers.includes(userToBlockId)) {
    Object.assign(userProfile, { ...userProfile, blockedUsers: [...blockedUsers, userToBlockId] });
  } else {
    Object.assign(userProfile, {
      ...userProfile,
      blockedUsers: blockedUsers.filter((id) => id.toString() !== String(userToBlockId)),
    });
    status = 'unblocked';
  }

  await userProfile.save();
  return status;
};

const getDeletedUsers = async () => {
  const deletedUsers = await DeletedUser.find({}).lean();
  return deletedUsers;
};

// Enhanced User Recommendation Weights
const USER_RECOMMENDATION_WEIGHTS = {
  mutualConnections: 5, // Users followed by people you follow
  followerCount: 2, // Popular users (with logarithmic scaling)
  recentActivity: 3, // Recently active users
  profileCompleteness: 2, // Users with complete profiles
  sharedInterests: 4, // Similar study preferences/location
  engagementLevel: 3, // Users who engage with content
  newUser: 1, // Boost for newer users to help them get discovered
  randomness: 1, // Diversity factor
};

/**
 * Apply diversity filtering to user recommendations
 * Ensures variety in recommendations by limiting users from same location/category
 *
 * @param {Array} users - Array of user recommendations
 * @param {number} limit - Final number of users to return
 * @returns {Array} - Filtered array of diverse user recommendations
 */
const applyUserDiversityFiltering = (users, limit, page) => {
  const result = [];
  const locationCounts = new Map();
  const maxPerLocation = Math.max(2, Math.floor(limit / 3)); // Max 2-3 users per location
  const skip = (page - 1) * limit;

  // First pass: Add users respecting location diversity
  for (let i = 0; i < users.length; i += 1) {
    if (i >= skip) {
      if (result.length >= limit) break;

      const user = users[i];
      const location = String(user.countryOfResidence || 'unknown');
      const currentCount = locationCounts.get(location) || 0;

      // Add user if we haven't exceeded location limit or if we need more results
      if (currentCount < maxPerLocation || result.length < Math.floor(limit * 0.7)) {
        result.push(user);
        locationCounts.set(location, currentCount + 1);
      }
    }
  }

  // Second pass: Fill remaining slots if needed
  if (result.length < limit) {
    users.forEach((user, index) => {
      if (index < skip) return;
      if (result.length >= limit) return;
      if (!result.find((u) => u._id.toString() === user._id.toString())) {
        result.push(user);
      }
    });
  }

  return result.slice(0, limit);
};

/**
 * Enhanced user recommendation algorithm
 * Recommends users based on social signals, activity, and profile similarity
 *
 * @param {string} userId - The requesting user's ID
 * @param {number} limit - Number of recommendations to return
 * @returns {Array} - Array of recommended users with ranking scores
 */
const getFollowSuggestions = async (currentUser, options) => {
  const { page, limit } = options;
  const count = 50; // Maximum number of recommendations to make

  // Create exclusion list (already following + blocked users + self)
  const excludeUserIds = [...currentUser.following, ...currentUser.blockedUsers, currentUser._id];

  const followSuggestionsAggregate = [
    {
      $match: {
        _id: { $nin: excludeUserIds },
        isEmailVerified: true,
        locked: false,
        registrationStatus: registrationStatuses.ONBOARDING_COMPLETE,
        photo: { $exists: true },
      },
    },
    {
      $addFields: {
        // Current time for calculations
        now: new Date(),

        // Follower count for influence scoring
        followerCount: { $size: { $ifNull: ['$followers', []] } },
        followingCount: { $size: { $ifNull: ['$following', []] } },

        // Check for mutual connections (people you follow who also follow this user)
        mutualConnectionCount: {
          $size: {
            $setIntersection: [{ $ifNull: ['$followers', []] }, currentUser.following],
          },
        },

        // Profile completeness indicators
        hasPhoto: { $cond: { if: '$photo', then: 1, else: 0 } },
        hasTagLine: { $cond: { if: '$tagLine', then: 1, else: 0 } },
        hasLocation: {
          $cond: {
            if: { $or: ['$countryOfResidence', '$stateOfResidence'] },
            then: 1,
            else: 0,
          },
        },

        // Recent activity (last login within 30 days)
        isRecentlyActive: {
          $cond: {
            if: {
              $gte: [
                '$lastLogin',
                { $subtract: ['$now', 30 * 24 * 60 * 60 * 1000] }, // 30 days ago
              ],
            },
            then: 1,
            else: 0,
          },
        },

        // Account age (newer users get slight boost for discoverability)
        isNewUser: {
          $cond: {
            if: {
              $gte: [
                '$createdAt',
                { $subtract: ['$now', 90 * 24 * 60 * 60 * 1000] }, // 90 days ago
              ],
            },
            then: 1,
            else: 0,
          },
        },

        // Shared interests scoring
        sharedCountry: {
          $cond: {
            if: {
              $and: ['$countryOfResidence', { $eq: ['$countryOfResidence', currentUser.countryOfResidence] }],
            },
            then: 1,
            else: 0,
          },
        },

        sharedState: {
          $cond: {
            if: {
              $and: ['$stateOfResidence', { $eq: ['$stateOfResidence', currentUser.stateOfResidence] }],
            },
            then: 1,
            else: 0,
          },
        },

        // Shared study preferences (if available)
        sharedStudyCountry: {
          $cond: {
            if: {
              $and: ['$preferredCountryOfStudy', { $eq: ['$preferredCountryOfStudy', currentUser.preferredCountryOfStudy] }],
            },
            then: 1,
            else: 0,
          },
        },
      },
    },
    {
      $addFields: {
        // Calculate influence score with simplified tiered scaling (CosmosDB compatible)
        influenceScore: {
          $cond: {
            if: { $gte: ['$followerCount', 1000] },
            then: { $multiply: [USER_RECOMMENDATION_WEIGHTS.followerCount, 3] },
            else: {
              $cond: {
                if: { $gte: ['$followerCount', 100] },
                then: { $multiply: [USER_RECOMMENDATION_WEIGHTS.followerCount, 2] },
                else: USER_RECOMMENDATION_WEIGHTS.followerCount,
              },
            },
          },
        },

        // Profile completeness score
        profileCompletenessScore: {
          $multiply: [
            { $add: ['$hasPhoto', '$hasTagLine', '$hasLocation'] },
            USER_RECOMMENDATION_WEIGHTS.profileCompleteness,
          ],
        },

        // Shared interests score
        sharedInterestsScore: {
          $multiply: [
            { $add: ['$sharedCountry', '$sharedState', '$sharedStudyCountry'] },
            USER_RECOMMENDATION_WEIGHTS.sharedInterests,
          ],
        },

        // Mutual connections score (most important factor)
        mutualConnectionsScore: {
          $multiply: [
            { $min: ['$mutualConnectionCount', 5] }, // Cap at 5 to prevent over-weighting
            USER_RECOMMENDATION_WEIGHTS.mutualConnections,
          ],
        },

        // Activity and engagement scores
        activityScore: {
          $multiply: ['$isRecentlyActive', USER_RECOMMENDATION_WEIGHTS.recentActivity],
        },

        newUserBoost: {
          $multiply: ['$isNewUser', USER_RECOMMENDATION_WEIGHTS.newUser],
        },
      },
    },
    {
      $addFields: {
        // Calculate final recommendation score
        recommendationScore: {
          $add: [
            '$mutualConnectionsScore',
            '$influenceScore',
            '$profileCompletenessScore',
            '$sharedInterestsScore',
            '$activityScore',
            '$newUserBoost',
            // Add small random factor for diversity (CosmosDB compatible approach)
            // { $multiply: [{ $mod: [{ $toInt: '$_id' }, 100] }, 0.01] },
          ],
        },
      },
    },
    {
      $sort: {
        recommendationScore: -1,
        followerCount: -1, // Secondary sort by popularity
      },
    },
    {
      $limit: count * 2, // Get more results for diversity filtering
    },
    {
      $project: {
        _id: 1,
        firstName: 1,
        lastName: 1,
        middleName: 1,
        username: 1,
        photo: 1,
        tagLine: 1,
        countryOfResidence: 1,
        stateOfResidence: 1,
        followerCount: 1,
        followingCount: 1,
        mutualConnectionCount: 1,
        recommendationScore: 1,
        business: 1,
        online: 1,
        lastLogin: 1,
        createdAt: 1,
      },
    },
  ];

  // Execute aggregation
  let suggestedUsers = await User.aggregate(followSuggestionsAggregate);

  // Calculate total results before diversity filtering
  const totalResults = suggestedUsers.length >= count ? count : suggestedUsers.length;

  // Apply diversity filtering to prevent recommending too many similar users
  suggestedUsers = applyUserDiversityFiltering(suggestedUsers, limit, page);

  // Populate photo and business data
  await User.populate(suggestedUsers, [
    { path: 'photo', select: 'url -_id' },
    { path: 'business', select: 'verificationStatus displayName' },
  ]);

  return {
    results: suggestedUsers,
    page,
    limit,
    totalPages: Math.ceil(totalResults / limit),
    totalResults,
  };
};

module.exports = {
  changeUserPassword,
  createUser,
  onboardStudent,
  queryUsers,
  getUserById,
  getUserByEmail,
  getUserByUsername,
  updateUserById,
  deleteUserById,
  sendVerificationEmail,
  // verifyDuplicateSignupEmail,
  getUserProfile,
  emailExists,
  usernameExists,
  usernameEmailAvail,
  followUser,
  getFollows,
  countFollowership,
  getMutuals,
  basicUserPopulate,
  blockUser,
  getUserMetaData,
  getDeletedUsers,

  getFollowSuggestions,
};
