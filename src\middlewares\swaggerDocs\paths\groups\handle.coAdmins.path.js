module.exports = {
  patch: {
    summary: 'Handle co-admins',
    tags: ['Groups'],
    parameters: [
      {
        in: 'path',
        name: 'id',
        description: 'ID of the group to handle co-admins for',
        required: true,
        schema: {
          type: 'string',
          example: '507f1f77bcf86cd799439011',
        },
      },
    ],
    requestBody: {
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              users: {
                type: 'array',
                items: {
                  type: 'string',
                  example: '5f9d1e9a6f3f4b001f6d4c0b',
                },
              },
              action: {
                type: 'string',
                enum: ['add', 'remove'],
                example: 'add',
              },
            },
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Operation successful',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Operation successful',
            },
          },
        },
      },
    },
  },
};
