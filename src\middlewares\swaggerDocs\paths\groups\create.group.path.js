module.exports = {
  post: {
    summary: 'Create a new group',
    tags: ['Groups'],
    parameters: [
      {
        in: 'formData',
        name: 'coverPhoto',
        type: 'string',
        format: 'base64',
        required: false,
        description: 'Group Cover Photo',
      },
      {
        in: 'formData',
        name: 'profilePhoto',
        type: 'string',
        format: 'base64',
        required: false,
        description: 'Group Profile Photo',
      },
      {
        in: 'formData',
        name: 'body',
        required: true,
        description: 'Group data',
        schema: {
          type: 'object',
          properties: {
            name: { type: 'string', description: 'Name of the group', required: true, example: 'IELTS group' },
            description: {
              type: 'string',
              description: 'Description of the group',
              required: true,
              example: 'This is a group for IELTS students',
            },
            rules: { type: 'string', description: 'group rules', example: 'No spamming. No Insults' },
            type: {
              type: 'string',
              enum: ['public', 'private'],
              default: 'public',
              description: 'Type of the group',
              example: 'public',
            },
            enableInvitation: {
              type: 'boolean',
              default: false,
              description: 'Enable invitation for the group',
              example: false,
            },
          },
        },
      },
    ],
    responses: {
      201: {
        description: 'Group created successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: {
                  type: 'string',
                  example: 'SUCCESS',
                },
                message: {
                  type: 'string',
                  example: 'Group created successfully',
                },
              },
            },
          },
        },
      },
      500: {
        description: 'Internal server error',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error500',
            },
          },
        },
      },
    },
  },
};
