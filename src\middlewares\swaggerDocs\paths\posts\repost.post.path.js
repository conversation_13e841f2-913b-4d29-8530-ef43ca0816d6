module.exports = {
  post: {
    summary: 'Repost a post',
    description: 'Repost a post based on the provided post ID',
    tags: ['Posts'],
    parameters: [
      {
        in: 'path',
        name: 'id',
        schema: {
          type: 'string',
          example: '507f1f77bcf86cd799439011',
        },
        description: 'ID of the post to repost',
        required: true,
      },
    ],
    responses: {
      200: {
        description: 'Post reposted successfully',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Post reposted successfully',
            },
          },
        },
      },
    },
  },
};
