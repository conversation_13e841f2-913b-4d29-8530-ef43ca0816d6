const volunteeringSchemaSwagger = {
  type: 'object',
  properties: {
    id: { type: 'string', example: '6009c0eee65f6dce28fb3e50' },
    title: {
      type: 'string',
      description: 'Title of the volunteer experience',
      example: 'Mentor',
    },
    companyName: {
      type: 'string',
      description: 'Name of the organization',
      example: 'Community Care Center',
    },
    start: {
      type: 'string',
      description: 'Start date of the volunteer experience',
      example: '2022-03-15',
    },
    end: {
      type: 'string',
      description: 'End date of the volunteer experience (if applicable)',
      example: '2022-06-30',
    },
    current: {
      type: 'boolean',
      description: 'Indicates if the volunteer experience is current or ongoing',
      example: true,
    },
    description: {
      type: 'string',
      description: 'Description of the volunteer experience',
      example: 'Provided assistance in organizing community events',
    },
  },
};

module.exports = volunteeringSchemaSwagger;
