/* eslint-disable no-console */
// eslint-disable-next-line import/no-unresolved
const puppeteer = require('puppeteer');
const async = require('async');
const fs = require('fs');
const path = require('path');
// const logger = require('../config/logger');

(async () => {
  // Launch the browser and open a new blank page
  const browser = await puppeteer.launch({ headless: false });
  const page = await browser.newPage();
  await page.setViewport({ width: 1200, height: 800 });
  page.setDefaultNavigationTimeout(30000);

  // Navigate the page to NCES college navigator url
  await page.goto('https://nces.ed.gov/collegenavigator');
  const htmlSelectElement = await page.$('#ctl00_cphCollegeNavBody_ucSearchMain_ucMapMain_lstState');

  const universities = [];
  if (htmlSelectElement) {
    const optionElements = await htmlSelectElement.$$('select option:not([value="all"])');

    await async.eachSeries(optionElements, async (option) => {
      // Select the option
      await option.click();

      // Click the show results button
      await page.click('#ctl00_cphCollegeNavBody_ucSearchMain_btnSearch');

      // Navigate to the new page
      await page.waitForNavigation({ waitUntil: 'networkidle0' });

      // Perform actions on the new page here
      const universityElements = await page.$$(
        '#ctl00_cphCollegeNavBody_ucResultsMain_tblResults .resultsW, #ctl00_cphCollegeNavBody_ucResultsMain_tblResults .resultsY',
      );
      console.log('universityElements.length');
      console.log(universityElements.length);

      await async.eachOfSeries(universityElements, async (universityElement) => {
        const university = {};
        const universityLink = await universityElement.$('td:nth-child(2) a');
        // console.log('------------------------------------');
        // console.log('index', index);
        // console.log(await universityLink.evaluate((el) => el.textContent));
        // console.log('------------------------------------');

        await universityLink.click();
        await page.waitForNavigation({ waitUntil: 'domcontentloaded' }); // I newly added this line and it now loads universities of all states. But it still repeats the first university and misses out the last university
        // await Promise.all([page.waitForNavigation({ waitUntil: 'domcontentloaded' }), universityLink.click()]);

        // Click expand all button
        // const expandAllButton = await page.$('.fadeyell .expandcollapse.colorful a');
        // await expandAllButton.click();
        await page.waitForSelector('#RightContent');
        // No need for the expand button. All elements come one the page when the page loads. The button doesn't introduce new elements

        // Get the data from the page
        console.log('------------------------------------');
        university.name = await page.$eval('.collegedash div span.headerlg', (el) => el.textContent);
        // university.address = await page.$eval('.collegedash div span:firs', (el) => el.textContent);
        // university.website = await page.$eval('.collegedash table tr td a', (el) => el.textContent);
        console.log(university);
        console.log('------------------------------------');
        universities.push(university);

        // Go back to the previous page
        await page.goBack({ waitUntil: 'domcontentloaded' });
        await page.waitForSelector('#ctl00_cphCollegeNavBody_ucResultsMain_tblResults');
        await page.$$(
          '#ctl00_cphCollegeNavBody_ucResultsMain_tblResults .resultsW, #ctl00_cphCollegeNavBody_ucResultsMain_tblResults .resultsY',
        );
      });

      // Go back to the previous page
      await page.goBack();
      await page.waitForSelector('select option:not([value="all"])');

      // Reload the optionElements to get fresh references
      await page.$$('select option:not([value="all"])');
    });
  }
  await browser.close();

  const jsonData = JSON.stringify(universities);

  fs.writeFile(`${path.join(__dirname, 'universities.json')}`, jsonData, (err) => {
    if (err) {
      console.log('Error writing to the file:', err);
    } else {
      console.log('Universities JSON data has been saved');
    }
  });
  return universities;
})();
