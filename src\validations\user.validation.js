const Joi = require('joi');
const { password, objectId } = require('./custom.validation');
const { fileSchema } = require('./file.validation');

const getUsers = {
  query: Joi.object().keys({
    name: Joi.string(),
    role: Joi.string(),
    sortBy: Joi.string(),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer(),
  }),
};

const getUser = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId),
  }),
};

const updateUser = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId),
  }),
  body: Joi.object().keys({
    firstName: Joi.string(),
    lastName: Joi.string(),
    middleName: Joi.string().allow(''),
    tagLine: Joi.string().allow(''),
    username: Joi.string(),
    recoveryEmail: Joi.string().email(),
    nationality: Joi.string(),
    phone: Joi.string(),
    gender: Joi.string().valid('male', 'female', 'other'),
    countryOfResidence: Joi.string(),
    stateOfResidence: Joi.string(),
    cityOfResidence: Joi.string(),
    targetedCountries: Joi.array().items(Joi.string()).unique(),
    preferredFieldsOfStudy: Joi.array().items(Joi.string().trim()),
    preferredSchoolsOfStudy: Joi.array().items(Joi.string().trim()),
    preferredDegreeOfStudy: Joi.string(),
    interestedInScholarship: Joi.boolean(),
    interestedInGrant: Joi.boolean(),
    preferredCountryOfStudy: Joi.string(),
    dateOfBirth: Joi.string(),

    personalStatement: Joi.string(),
  }),
  files: Joi.object().keys({
    banner: Joi.array().items(fileSchema(1)).max(1),
    photo: Joi.array().items(fileSchema(1)).max(1),
  }),
};

const onboardStudent = {
  body: Joi.object().keys({
    gender: Joi.string().valid('male', 'female', 'other').required(),
    nationality: Joi.string().required(),
    personalStatement: Joi.string().required(),
    countryOfResidence: Joi.string().required(),
    stateOfResidence: Joi.string(),
    cityOfResidence: Joi.string(),
    preferredDegreeOfStudy: Joi.string(),
    preferredFieldsOfStudy: Joi.alternatives().try(
      Joi.array()
        .items(Joi.string())
        .unique((a, b) => a.toLowerCase().trim() === b.toLowerCase().trim()),
      Joi.string(),
    ),
    preferredSchoolsOfStudy: Joi.alternatives().try(
      Joi.array()
        .items(Joi.string())
        .unique((a, b) => a.toLowerCase().trim() === b.toLowerCase().trim()),
      Joi.string(),
    ),
    preferredCountryOfStudy: Joi.string(),
  }),
  files: Joi.object().keys({
    photo: Joi.array().items(fileSchema(1)).max(1),
    banner: Joi.array().items(fileSchema(1)).max(1),
  }),
};

const updateUserPassword = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId),
  }),
  body: Joi.object()
    .keys({
      oldPassword: Joi.string().custom(password),
      newPassword: Joi.string().custom(password),
      confirmPassword: Joi.string().valid(Joi.ref('newPassword')).required().label('Confirm Password'),
    })
    .min(1),
};

const deleteUser = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId),
  }),
  body: Joi.object().keys({
    reason: Joi.string(),
  }),
};

const followUnfollowSchema = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId),
  }),
};

const followsSchema = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId),
  }),
  query: Joi.object().keys({
    type: Joi.string().valid('followers', 'following').required(),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
  }),
};

const getFollowSuggestions = {
  query: Joi.object().keys({
    limit: Joi.number().integer().min(1).max(10).default(10),
    page: Joi.number().integer().default(1),
  }),
};

module.exports = {
  getUsers,
  getUser,
  updateUser,
  updateUserPassword,
  deleteUser,
  onboardStudent,
  followUnfollowSchema,
  followsSchema,
  getFollowSuggestions,
};
