module.exports = {
  post: {
    summary: 'LinkedIn OAuth',
    description: 'Authenticate user via LinkedIn OAuth',
    tags: ['Authentication'],
    parameters: [
      {
        in: 'query',
        name: 'code',
        schema: {
          type: 'string',
          example: 'linkedin-oauth-code',
        },
        required: true,
        description: 'linkedin oauth code',
      },
    ],
    responses: {
      200: {
        description: 'Successful operation',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string', example: 'SUCCESS' },
                message: { type: 'string', example: 'Login successful' },
                data: {
                  type: 'object',
                  properties: {
                    tokens: {
                      type: 'object',
                      properties: {
                        access: { type: 'string' },
                        refresh: { type: 'string' },
                      },
                    },
                    user: {
                      type: 'object',
                      properties: {
                        username: { type: 'string' },
                        email: { type: 'string', format: 'email' },
                        targeted_countries: { type: 'array', items: { type: 'string' } },
                        roles: { type: 'array', items: { type: 'string' } },
                        isEmailVerified: { type: 'boolean' },
                        createdAt: { type: 'string', format: 'date-time' },
                        updatedAt: { type: 'string', format: 'date-time' },
                        photo: {
                          type: 'object',
                          properties: {
                            url: { type: 'string', format: 'url' },
                          },
                        },
                        firstName: { type: 'string' },
                        lastName: { type: 'string' },
                        commentLikes: { type: 'array', items: { type: 'string' } },
                        dateOfBirth: { type: 'string', format: 'date-time' },
                        gender: { type: 'string', enum: ['male', 'female', 'other'] },
                        groups: { type: 'array', items: { type: 'string' } },
                        interestedInScholarship: { type: 'boolean' },
                        nationality: { type: 'string' },
                        postLikes: { type: 'array', items: { type: 'string' } },
                        preferredDegreeOfStudy: { type: 'string' },
                        preferredFieldOfStudy: { type: 'string' },
                        preferredSchoolOfStudy: { type: 'string' },
                        profile: { type: 'string' },
                        registrationStatus: { type: 'string' },
                        targetedCountries: { type: 'array', items: { type: 'string' } },
                        appliedUniversities: { type: 'array', items: { type: 'string' } },
                        followers: { type: 'array', items: { type: 'string' } },
                        following: { type: 'array', items: { type: 'string' } },
                        hiddenUniversities: { type: 'array', items: { type: 'string' } },
                        savedUniversities: { type: 'array', items: { type: 'string' } },
                        postBookmarks: { type: 'array', items: { type: 'string' } },
                        reposts: { type: 'array', items: { type: 'string' } },
                        setting: { type: 'string' },
                        lastSeen: { type: 'string', format: 'date-time' },
                        locked: { type: 'boolean' },
                        banner: {
                          type: 'object',
                          properties: {
                            url: { type: 'string', format: 'url' },
                          },
                        },
                        tagLine: { type: 'string' },
                        middleName: { type: 'string' },
                        interestedInGrant: { type: 'boolean' },
                        preferredSchoolsOfStudy: { type: 'array', items: { type: 'string' } },
                        preferredFieldsOfStudy: { type: 'array', items: { type: 'string' } },
                        emailVerificationCount: { type: 'integer' },
                        lastLogin: { type: 'string', format: 'date-time' },
                        pendingPasswordResetCount: { type: 'integer' },
                        id: { type: 'string' },
                      },
                    },
                  },
                },
              },
            },
            example: {
              status: 'SUCCESS',
              message: 'Login successful',
              data: {
                tokens: {
                  access: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
                  refresh: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
                },
                user: {
                  type: 'object',
                  properties: {
                    username: 'john_doe',
                    email: '<EMAIL>',
                    targeted_countries: ['USA', 'UK'],
                    roles: ['admin'],
                    isEmailVerified: true,
                    createdAt: '2023-11-15T08:45:12.123Z',
                    updatedAt: '2024-03-14T09:20:30.456Z',
                    photo: {
                      url: 'https://example.com/profile/photos/john_doe.jpg',
                    },
                    firstName: 'John',
                    lastName: 'Doe',
                    commentLikes: ['commentId1', 'commentId2'],
                    dateOfBirth: '1985-07-20T00:00:00.000Z',
                    gender: 'male',
                    groups: ['group1', 'group2'],
                    interestedInScholarship: false,
                    nationality: 'USA',
                    postLikes: ['postId1', 'postId2', 'postId3'],
                    preferredDegreeOfStudy: 'Masters',
                    preferredFieldOfStudy: 'Data Science',
                    preferredSchoolOfStudy: 'Example University',
                    profile: 'profileId',
                    registrationStatus: 'completed',
                    targetedCountries: ['USA', 'UK', 'Australia'],
                    appliedUniversities: ['universityId1', 'universityId2'],
                    followers: ['followerId1', 'followerId2'],
                    following: ['followingId1'],
                    hiddenUniversities: [],
                    savedUniversities: ['savedUniversityId1', 'savedUniversityId2'],
                    postBookmarks: [],
                    reposts: [],
                    setting: 'settingId',
                    lastSeen: '2024-03-14T09:20:30.456Z',
                    locked: false,
                    banner: {
                      url: 'https://example.com/profile/banners/john_doe_banner.jpg',
                    },
                    tagLine: 'Live life to the fullest!',
                    middleName: 'William',
                    interestedInGrant: true,
                    preferredSchoolsOfStudy: ['Example Business School'],
                    preferredFieldsOfStudy: ['Business Administration', 'Economics'],
                    emailVerificationCount: 2,
                    lastLogin: '2024-03-14T09:20:30.456Z',
                    pendingPasswordResetCount: 0,
                    id: '610ad0e66f0f4621387ad6f3',
                  },
                },
              },
            },
          },
        },
      },
    },
  },
};
