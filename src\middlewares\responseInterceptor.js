/* eslint-disable no-param-reassign */
const interceptResponseBody = async (req, res, next) => {
  // adds a new key (reurnUrl) to the response body
  const originalJson = res.json;

  res.json = function (body) {
    if (res.statusCode >= 200 && res.statusCode < 300 && req.query?.returnUrl) {
      body.returnUrl = req.query.returnUrl;
    }
    originalJson.call(this, body);
  };
  next();
};

module.exports = { interceptResponseBody };
