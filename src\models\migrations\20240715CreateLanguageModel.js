const GlobalVariable = require('../global.variable.model');
const Language = require('../language.model');
const logger = require('../../config/logger');
const languages = require('./languages.seed');

const createLanguageModel = async () => {
  const renamed = await GlobalVariable.findOne({ name: 'create language model' });
  if (renamed) {
    return;
  }

  await Promise.all(languages.map((language) => Language.create(language)));

  await GlobalVariable.create({ name: 'create language model', value: 'true' });
  logger.info('Language model created');
};

module.exports = createLanguageModel;
