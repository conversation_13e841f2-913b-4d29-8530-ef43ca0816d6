const Joi = require('joi');
const { fileSchema } = require('./file.validation');
const { businessEnums, currencies } = require('../config/constants');

const getService = {
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
  query: Joi.object().keys({
    referralCode: Joi.string(),
  }),
};

const getServices = {
  query: Joi.object()
    .keys({
      searchText: Joi.string(),
      minimumRating: Joi.number().min(0).max(5).default(0),
      maximumRating: Joi.number()
        .min(0)
        .max(5)
        .default(5)
        .greater(Joi.ref('minimumRating')) // Ensure maximumRating is greater than minimumRating
        .messages({
          'number.greater': '"maximumRating" must be greater than "minimumRating"',
        }),
      category: Joi.alternatives().try(
        Joi.string().valid(...Object.values(businessEnums.serviceCategories)),
        Joi.array().items(Joi.string().valid(...Object.values(businessEnums.serviceCategories))),
      ),
      provider: Joi.string(),
      status: Joi.string()
        .valid(...Object.values(businessEnums.serviceStatuses), 'all')
        .default(businessEnums.serviceStatuses.PUBLISHED),
      createdAtStartDate: Joi.date().iso().default(new Date(0).toISOString()), // lower limit createdAt date range
      createdAtEndDate: Joi.date()
        .iso()
        .default(new Date(new Date().getTime() + 86400000).toISOString()), // Upper limit createdAt date range. Add 1 day for possible deployment timezone differences
      sortBy: Joi.string()
        .valid('createdAt:asc', 'createdAt:desc', 'updatedAt:asc', 'updatedAt:desc', 'name:asc', 'name:desc')
        .default('createdAt:desc'),
      limit: Joi.number().integer().default(10),
      page: Joi.number().integer().default(1),

      minimumPrice: Joi.number().default(0),
      maximumPrice: Joi.number()
        // .default(1000000000000000)
        .greater(Joi.ref('minimumPrice')) // Ensure maximumPrice is greater than minimumPrice
        .messages({
          'number.greater': '"maximumPrice" must be greater than "minimumPrice"',
        }),
      minimumCompletionDays: Joi.number().default(0),
      maximumCompletionDays: Joi.number()
        // .default(1000000000000000)
        .greater(Joi.ref('minimumCompletionDays')) // Ensure maximumCompletionDays is greater than minimumCompletionDays
        .messages({
          'number.greater': '"maximumCompletionDays" must be greater than "minimumCompletionDays"',
        }),
      planType: Joi.alternatives().try(
        Joi.string().valid(...Object.values(businessEnums.servicePlans)),
        Joi.array().items(Joi.string().valid(...Object.values(businessEnums.servicePlans))),
      ),
    })
    .custom((value, helpers) => {
      const { minimumPrice, maximumPrice, minimumCompletionDays, maximumCompletionDays } = value;
      // Make sure that if any the four is provided, the remaining three are also provided
      if (minimumPrice && maximumPrice === undefined) {
        return helpers.message('Specify maximum price');
      }
      if (minimumPrice === undefined && maximumPrice) {
        return helpers.message('Specify minimum price');
      }
      if (minimumCompletionDays && maximumCompletionDays === undefined) {
        return helpers.message('Specify maximum completion days');
      }
      if (minimumCompletionDays === undefined && maximumCompletionDays) {
        return helpers.message('Specify minimum completion days');
      }
      return value;
    }),
};

// reviews

const addReview = {
  body: Joi.object().keys({
    order: Joi.string().required(),
    text: Joi.string(),
    reviews: Joi.object()
      .keys({
        ...Object.values(businessEnums.reviewQuestions).reduce(
          (acc, item) => ({ ...acc, [item[0]]: Joi.number().min(0).max(5).required() }),
          {},
        ),
      })
      .required(),
  }),
};

const getReviews = {
  query: Joi.object()
    .keys({
      service: Joi.string(),
      provider: Joi.string(),
      minRating: Joi.number().min(0).max(5).default(0),
      maxRating: Joi.number().min(0).max(5).default(5),
      sortBy: Joi.string()
        .valid('createdAt:asc', 'createdAt:desc', 'updatedAt:asc', 'updatedAt:desc', 'rating:asc', 'rating:desc')
        .default('createdAt:desc'),
      limit: Joi.number().integer().default(10),
      page: Joi.number().integer().default(1),
    })
    .or('service', 'provider'),
};

const updateReview = {
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
  body: Joi.object().keys({
    rating: Joi.number(),
    text: Joi.string(),
  }),
};

// update
const createService = {
  query: Joi.object().keys({
    stage: Joi.string()
      .valid(...Object.values(businessEnums.serviceStages))
      .required(),
    update: Joi.string().valid('true', 'false'),
    serviceId: Joi.string().when('update', {
      is: 'true',
      then: Joi.string().required(),
      otherwise: Joi.string().optional(),
    }),
  }),
};

const validateOverview = Joi.object({
  name: Joi.string().required(),
  category: Joi.string().valid(...Object.values(businessEnums.serviceCategories)),
  tags: Joi.array().items(Joi.string()).required(),
  description: Joi.string().required(),
});

const validateFAQ = Joi.object({
  faq: Joi.array()
    .items(
      Joi.object({
        question: Joi.string().required(),
        answer: Joi.string().required(),
      }),
    )
    .required(),
});

const validateGallery = Joi.object({
  files: Joi.array().items(fileSchema(1)).max(3).required(),
  deletedUrls: Joi.alternatives().try(Joi.array().items(Joi.string()), Joi.string()).optional(),
});

const offerValue = Joi.array()
  .items(
    Joi.object({
      plan: Joi.string()
        .valid(...Object.values(businessEnums.servicePlans))
        .required(),
      offer: Joi.alternatives().try(Joi.string(), Joi.number()).required(),
    }),
  )
  .required()
  .unique((a, b) => a.plan.toLowerCase() === b.plan.toLowerCase());

const validateOffers = Joi.array()
  .items(
    Joi.object({
      name: Joi.string().required(),
      value: offerValue,
    }),
  )
  .required()
  .unique((a, b) => a.name.toLowerCase() === b.name.toLowerCase())
  .custom((offers, helpers) => {
    // Check that all value arrays have the same length
    const lengths = offers.map((offer) => offer.value.length);
    const allSameLength = lengths.every((length) => length === lengths[0]);
    if (!allSameLength) {
      return helpers.message('Specify offer for every plan mentioned');
    }

    // Check that each value has the exact same plans
    const allPlans = offers.map((offer) => offer.value.map((v) => v.plan.toLowerCase()).sort());
    const firstPlanSet = JSON.stringify(allPlans[0]);
    const allSamePlans = allPlans.every((planSet) => JSON.stringify(planSet) === firstPlanSet);
    if (!allSamePlans) {
      return helpers.message('Specify offer for every plan mentioned');
    }

    return offers; // If everything is valid, return the offers array
  });

const validateRequirementSpecify = Joi.object({
  requirementId: Joi.string(), // To be provided when updating a requirement
  question: Joi.string().required(),
  answerTip: Joi.string(),
  type: Joi.string()
    .valid(...Object.values(businessEnums.serviceRequirementTypes))
    .required(),
  optional: Joi.boolean().default(false),
  options: Joi.array()
    .items(Joi.string())
    .min(2)
    .when('type', {
      is: businessEnums.serviceRequirementTypes.SELECT,
      then: Joi.array().min(2).required(),
    })
    .when('type', {
      is: businessEnums.serviceRequirementTypes.MULTI_SELECT,
      then: Joi.array().min(2).required(),
    }),
  plansIncluded: Joi.array()
    .items(Joi.string().valid(...Object.values(businessEnums.servicePlans)))
    .min(1)
    .required(),
});
// Add disclaimer response as part of the validation for requirements provide

const validateRequirementsSpecify = Joi.object({
  requirements: Joi.array()
    .items(validateRequirementSpecify)
    .unique((a, b) => a.question?.toLowerCase() === b.question?.toLowerCase(), { ignoreUndefined: true })
    .unique('requirementId', { ignoreUndefined: true }),
  deletedRequirements: Joi.array().items(Joi.string()).unique(),
}).custom((value, helpers) => {
  const { requirements, deletedRequirements } = value;

  // Check if any requirementId is in deletedRequirements
  const conflict = requirements?.some((req) => req.requirementId && deletedRequirements?.includes(req.requirementId));

  if (conflict) {
    return helpers.message('A requirement cannot be updated and deleted at the same time');
  }

  return value;
});

const validateRequirementProvide = Joi.object().keys({
  requirement: Joi.string().required(),
  text: Joi.string(),
  choices: Joi.array().items(Joi.string()),
});

const validateCompletedService = Joi.object({
  provider: Joi.object().required(),
  name: Joi.string().required(),
  description: Joi.string().required(),
  category: Joi.string().required(),
  tags: Joi.array().items(Joi.string().trim()).required(),
  faq: Joi.array()
    .items(
      Joi.object({
        _id: Joi.object().required(),
        question: Joi.string().required(),
        answer: Joi.string().required(),
      }),
    )
    .required(),
  offers: Joi.array().items(Joi.object()).required(),
  // plans: Joi.array().items(Joi.object()).required(),
  currency: Joi.string()
    .valid(...Object.values(currencies))
    .required(),
  media: Joi.array().items(Joi.object()).required(),
  // completedCreationStages: Joi.array()
  //   .items(Joi.string().valid(...Object.values(businessEnums.serviceStages)))
  //   .custom((value, helpers) => {
  //     const requiredStages = Object.values(businessEnums.serviceStages);
  //     const missingStages = requiredStages.filter((stage) => !value.includes(stage));
  //     if (missingStages.length > 0) {
  //       return helpers.message(`Missing stages: ${missingStages.join(', ')}`);
  //     }
  //     return value;
  //   })
  //   .required(),
}).unknown(true);

const updateService = {
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
  body: Joi.object().keys({
    name: Joi.string(),
    status: Joi.string().valid(...Object.values(businessEnums.serviceStatuses)), // Modify to restrict to only certain statuses. Also validate each status change
    newOffers: validateOffers.optional(),
    deletedOffers: Joi.array().items(Joi.string()),
    updatedOffers: Joi.array().items(
      Joi.object({
        id: Joi.string().required(), // Name of offer can't be changed
        value: offerValue,
      }),
    ),
  }),
};

const getSavedServices = {
  query: Joi.object().keys({
    sortBy: Joi.string()
      .valid('createdAt:asc', 'createdAt:desc', 'updatedAt:asc', 'updatedAt:desc')
      .default('createdAt:desc'),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
  }),
};

// service offers
const getServiceOffers = {
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
  query: Joi.object().keys({
    planType: Joi.string().valid(...Object.values(businessEnums.servicePlans)),
  }),
};

const verifyService = {
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
  body: Joi.object().keys({
    serviceVerification: Joi.string().required(),
    verificationStatus: Joi.string()
      .valid(
        ...[
          businessEnums.businessVerificationStatuses.APPROVED,
          businessEnums.businessVerificationStatuses.CHANGES_REQUESTED,
        ],
      )
      .required(),
    comment: Joi.string().required(),
  }),
};

const fetchUnverifiedServices = {
  query: Joi.object().keys({
    name: Joi.string(), // service name
    displayName: Joi.string(), // business name
    verificationStatus: Joi.string().valid(...Object.values(businessEnums.businessVerificationStatuses)),
    sortBy: Joi.string()
      .valid('createdAt:asc', 'createdAt:desc', 'updatedAt:asc', 'updatedAt:desc')
      .default('createdAt:desc'),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
  }),
};

module.exports = {
  createService,
  getService,
  getServices,
  updateService,
  getSavedServices,

  addReview,
  getReviews,
  updateReview,

  validateOverview,
  validateFAQ,
  validateGallery,
  validateOffers,
  validateRequirementsSpecify,
  validateCompletedService,
  validateRequirementProvide,
  // validateRequirementsProvide,

  getServiceOffers,

  verifyService,
  fetchUnverifiedServices,
};
