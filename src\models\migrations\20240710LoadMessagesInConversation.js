const Async = require('async');
const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const MessageConversation = require('../messageConversation.model');
const Message = require('../message.model');

const loadMessagesToConversations = async () => {
  const migrated = await GlobalVariable.findOne({ name: 'Load Messages in Conversations' });

  if (migrated) {
    return;
  }

  const allConversations = await MessageConversation.find();
  await Async.eachOfSeries(allConversations, async (conversation, index) => {
    const messages = await Message.find({ conversation: conversation._id }).sort({ createdAt: 1 });
    await MessageConversation.updateOne(
      { _id: conversation._id },
      { $addToSet: { messages: messages.map((message) => message._id) } },
    );

    logger.info(`(${index + 1}) Messages added to conversation ${conversation._id}`);
  });
  await GlobalVariable.create({ name: 'Load Messages in Conversations', value: 'true' });
  logger.info('Loaded Messages in Conversations');
};

module.exports = loadMessagesToConversations;
