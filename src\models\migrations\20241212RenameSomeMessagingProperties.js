/* eslint-disable no-param-reassign */
const Async = require('async');
const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const MessageConversation = require('../messageConversation.model');
const Message = require('../message.model');
const Order = require('../order.model');
const { createConversation } = require('../../services/message.service');

const varName = 'Modify messaging to include business 5';

const renameSomeMessagingProperties = async () => {
  const migrated = await GlobalVariable.findOne({ name: varName });

  if (migrated) {
    return;
  }

  // Delete all conversations with empty members and participants
  // const result = await MessageConversation.deleteMany({ members: { $size: 0 }, participants: { $size: 0 } });

  // console.log(result);
  // throw new Error('Migration failed');

  const orders = await Order.find();
  await Async.eachOfSeries(orders, async (order, index) => {
    const conversation = await createConversation({
      members: [{ user: order.client.toString() }, { business: order.provider.toString() }],
      directMessage: 'true',
    });
    order.conversation = conversation._id;
    await MessageConversation.findByIdAndUpdate(conversation._id, {
      // $addToSet: [{ members: { user: order.client.toString() } }, { members: { business: order.provider.toString() } }],
      // $addToSet: { members: [{ user: order.client.toString() }, { business: order.provider.toString() }] },
      $set: { members: [{ user: order.client }, { business: order.provider }] },
    });
    await order.save();

    logger.info(`Order ${index + 1} of ${orders.length} updated. Conversation ${conversation._id.toString()} created.`);
  });

  const conversations = await MessageConversation.find({
    $or: [{ members: { $size: 0 } }, { members: { $exists: false } }],
  });

  await Async.eachOfSeries(conversations, async (conversation, index) => {
    await MessageConversation.findByIdAndUpdate(conversation._id, {
      $set: {
        members: conversation.participants.map((participant) => ({
          user: participant,
        })),
      },
    });
    logger.info(`Conversation ${index + 1} updated.`);
  });

  logger.info('Conversations updated.');

  const messages = await Message.find();
  await Async.eachOfSeries(messages, async (message, index) => {
    await Message.findByIdAndUpdate(message._id, {
      $set: {
        sentBy: {
          user: message.sender,
        },
        isReadBy: message.readBy.map((readBy) => ({
          user: readBy,
        })),
        isReceivedBy: message.receivedBy.map((receivedBy) => ({
          user: receivedBy,
        })),
      },
    });

    logger.info(`Message ${index + 1} updated.`);
  });

  await GlobalVariable.create({ name: varName, value: 'true' });
  logger.info('Business added to messaging.');
};

module.exports = renameSomeMessagingProperties;
