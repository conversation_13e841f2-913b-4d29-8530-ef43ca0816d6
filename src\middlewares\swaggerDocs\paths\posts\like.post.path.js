module.exports = {
  post: {
    summary: 'Like a post',
    description: 'Like a post based on the provided post ID',
    tags: ['Posts'],
    parameters: [
      {
        in: 'path',
        name: 'id',
        schema: {
          type: 'string',
          example: '507f1f77bcf86cd799439011',
        },
        description: 'ID of the post to like',
        required: true,
      },
    ],
    responses: {
      200: {
        description: 'Post liked successfully',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Post liked successfully',
              data: {
                repostWithThought: [],
                tags: [],
                user: {
                  username: 'joh<PERSON><PERSON>',
                  photo: {
                    url: 'https://storagesite/users%2Fphoto/74570fc0.jpg',
                  },
                  firstName: '<PERSON>',
                  lastName: 'Doe',
                  tagLine: '',
                  middleName: '',
                  id: '651bb5bf4eb9240327eaaaaa',
                },
                text: 'Updated Visibility 2345',
                media: [
                  {
                    url: 'https://storagesite/users%2Fphoto/74570fc0.jpg',
                    _id: '65bb7f69f2b37ca660a3c05f',
                  },
                ],
                likes: ['65bb7f69f2b37ca660a3c05f', '65bb7f69f2b37ca660a3c05f', '65bb7f69f2b37ca660a3c05f'],
                comments: ['65bb7f69f2b37ca660a3c05f', '65bb7f69f2b37ca660a3c05f', '65bb7f69f2b37ca660a3c05f'],
                reposts: ['65bb7f69f2b37ca660a3c05f', '65bb7f69f2b37ca660a3c05f', '65bb7f69f2b37ca660a3c05f'],
                bookmarks: ['65bb7f69f2b37ca660a3c05f', '65bb7f69f2b37ca660a3c05f', '65bb7f69f2b37ca660a3c05f'],
                visibility: 'only me',
                createdAt: '2024-02-14T14:04:02.231Z',
                updatedAt: '2024-02-14T14:06:05.128Z',
                id: '65ccc85202c7a6b4db5ababab',
              },
            },
          },
        },
      },
    },
  },
};
