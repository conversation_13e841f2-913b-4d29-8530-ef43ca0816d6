const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const { reviewQuestions } = require('../config/constants').businessEnums;

const reviewSchema = new mongoose.Schema(
  {
    order: { type: mongoose.Schema.Types.ObjectId, ref: 'Order', required: true },
    service: { type: mongoose.Schema.Types.ObjectId, ref: 'Service', required: true },
    ...Object.values(reviewQuestions).reduce(
      (acc, reviewQuestion) => ({ ...acc, [reviewQuestion[0]]: { type: Number, min: 0, max: 5, required: true } }),
      {},
    ),

    text: { type: String, trim: true },
    rating: { type: Number, min: 0, max: 5, required: true },
  },
  { timestamps: true },
);

reviewSchema.index({ createdAt: 1, updatedAt: 1, rating: 1 });

reviewSchema.plugin(toJSON);
reviewSchema.plugin(paginate);

const Review = mongoose.model('Review', reviewSchema);
module.exports = Review;
