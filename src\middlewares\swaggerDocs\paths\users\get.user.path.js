const userSchema = require('../../schemas/users/user.schema');

module.exports = {
  get: {
    summary: 'Get User by ID',
    tags: ['Users'],
    description: 'Retrieve a user record by their ID.',
    parameters: [
      {
        name: 'id',
        in: 'path',
        required: true,
        description: 'User ID to retrieve',
        schema: {
          type: 'string',
          example: '507f1f77bcf86cd799439011',
        },
      },
    ],
    responses: {
      200: {
        description: 'User record retrieved successfully.',
        content: {
          'application/json': {
            schema: { ...userSchema },
          },
        },
      },
      400: {
        description: 'Bad Request. User record not found.',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  example: 'User record not found',
                },
                status: {
                  type: 'string',
                  example: 'FAILED',
                },
              },
            },
          },
        },
      },
    },
  },
};
