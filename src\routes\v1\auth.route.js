const express = require('express');

const router = express.Router();
const { authController } = require('../../controllers');
const loginLimiter = require('../../middlewares/loginLimiter');
const validate = require('../../middlewares/validate');
const { authValidation } = require('../../validations');
const { auth } = require('../../middlewares/auth');

router.post('/signup', validate(authValidation.register), authController.signup);
router.post('^/$|/login', loginLimiter, validate(authValidation.login), authController.login);
router.get('/refresh', validate(authValidation.refreshTokens), authController.refresh);
router.delete('/logout', authController.logout);
router.post(
  '/send-verification-email',
  validate(authValidation.sendVerificationEmail),
  authController.sendVerificationEmail,
);
router.post('/verify-email', validate(authValidation.verifyEmail), authController.verifyEmail);
router.post('/forgot-password', validate(authValidation.forgotPassword), authController.forgotPassword);
router.post('/reset-password', validate(authValidation.resetPassword), authController.resetPassword);
router.get('/username-email-avail', validate(authValidation.usernameEmailAvail), authController.usernameEmailAvail);

router.get('/oauth/google-url', authController.getGoogleOauthURL);
router.post('/oauth/google', validate(authValidation.oauth), authController.googleOauth);

router.get('/oauth/linkedin-url', authController.getLinkedInOauthURL);
router.post('/oauth/linkedin', validate(authValidation.oauth), authController.linkedInOauth);

router.get('/verify-access', validate(authValidation.verifyAccess), authController.verifyAccess);

router.post('/send-otp', validate(authValidation.sendOTP), authController.sendOTP);
router.post('/verify-otp', validate(authValidation.verifyOTP), authController.verifyOTP);

router.post('/create-id-verification', auth(), authController.createIdVerification);
module.exports = router;
