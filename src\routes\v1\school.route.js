const express = require('express');

const router = express.Router();
const { schoolValidation } = require('../../validations');
const { schoolController } = require('../../controllers');
const validate = require('../../middlewares/validate');
const { auth } = require('../../middlewares/auth');
const { uploadSchoolPhotos } = require('../../config/multer.config');

router.get('/', auth(), validate(schoolValidation.getSchools), schoolController.getSchools);
router.get('/enums', schoolController.getschoolsEnums);
router.use(auth('manageSchools'));

router.post('/', uploadSchoolPhotos, validate(schoolValidation.createSchool), schoolController.createSchool);
router.post('/:id/programs', validate(schoolValidation.addSchoolPrograms), schoolController.addSchoolPrograms);

router.get('/stats', schoolController.getSchoolStats);
router.get('/:id', schoolController.getSchoolById);
router.get('/programs/:programId', schoolController.getSchoolProgram);
router.patch('/:id', uploadSchoolPhotos, validate(schoolValidation.updateSchool), schoolController.updateSchool);
router.patch('/programs/:programId', validate(schoolValidation.updateSchoolProgram), schoolController.updateSchoolProgram);
router.delete('/:id', schoolController.deleteSchool);
router.delete('/programs/:programId', schoolController.deleteSchoolProgram);
module.exports = router;
