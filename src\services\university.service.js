const httpStatus = require('http-status');
const stopWord = require('stopword');
const { University, Country } = require('../models');
const ApiError = require('../utils/ApiError');
const { pick } = require('../utils/pick');
const { formatToCamelCase } = require('../utils/stringFormatting');

const getUniversityNames = async (filter) => {
  // const universities = await University.paginate(filter, options);
  const universities = await University.find(filter).select('name.value').sort('name.value');

  return universities;
};

const getUniversityById = async (req) => {
  const university = await University.findById(req.params.id).select('-savedBy -hideFrom');
  if (!university) {
    throw new ApiError(httpStatus.NOT_FOUND, 'University record not found');
  }

  university._doc.savedByMe = req.user.savedUniversities.includes(university._id);
  university._doc.appliedByMe = req.user.appliedUniversities.includes(university._id);
  const awardsOffered = university.completionsNumberOfAwardsConferred20212022.value.reduce((acc, program) => {
    program.awardLevels.forEach((awardLevel) => {
      if (awardLevel.count !== null) {
        acc.push(awardLevel.name);
      }
    });
    return acc;
  }, []);
  university._doc.awardsOffered2 = [...new Set(awardsOffered)];
  return university;
};

const getUniversityEnums = async () => {
  const uniqueValues = await University.aggregate([
    {
      $unwind: '$awardsOffered.value', // Unwind the array
    },
    {
      $unwind: '$type.value',
    },
    {
      $unwind: '$completionsNumberOfAwardsConferred20212022.value',
    },

    {
      $group: {
        _id: null,
        campusHousing: { $addToSet: '$campusHousing.value' },
        campusHousingKey: { $addToSet: '$campusHousing.key' },
        awardsOffered: { $addToSet: '$awardsOffered.value' },
        awardsOfferedKey: { $addToSet: '$awardsOffered.key' },
        type: { $addToSet: '$type.value' },
        typeKey: { $addToSet: '$type.key' },
        campusSetting: { $addToSet: '$campusSetting.value' },
        campusSettingKey: { $addToSet: '$campusSetting.key' },
        carnegieClassification: { $addToSet: '$carnegieClassification.value' },
        carnegieClassificationKey: { $addToSet: '$carnegieClassification.key' },
        programsOffered: { $addToSet: '$completionsNumberOfAwardsConferred20212022.value.name' },
        programsOfferedKey: { $addToSet: '$completionsNumberOfAwardsConferred20212022.key' },
        // Add more fields as needed
      },
    },
    {
      $project: {
        _id: 0,
        campusHousing: '$campusHousing',
        campusHousingKey: '$campusHousingKey',
        awardsOffered: '$awardsOffered',
        awardsOfferedKey: '$awardsOfferedKey',
        type: '$type',
        typeKey: '$typeKey',
        campusSetting: '$campusSetting',
        campusSettingKey: '$campusSettingKey',
        carnegieClassification: '$carnegieClassification',
        carnegieClassificationKey: '$carnegieClassificationKey',
        programsOffered: '$programsOffered',
        programsOfferedKey: '$programsOfferedKey',
        // Add more fields as needed
      },
    },
  ]);

  const enums = {};
  const keys = Object.keys(uniqueValues[0] || {});
  keys.forEach((item, index) => {
    if (index % 2 === 0) {
      enums[uniqueValues[0][keys[index + 1]]] = uniqueValues[0][keys[index]];
    }
  });

  return enums;
};

const wordsArrayToFilterArray = (searchTextArray, key) => {
  const resultArray = [];
  searchTextArray.forEach((searchText) => {
    resultArray.push({ [key]: { $regex: searchText, $options: 'i' } });
  });

  return resultArray;
};

const getUniversities = async (req) => {
  const filter = {};
  const optionKeys = ['sortBy', 'limit', 'page', 'filter'];
  const locationKeys = ['city', 'state', 'country'];
  filter.$or = [];
  filter.$and = [];

  Object.keys(req.query)
    .filter((key) => !optionKeys.includes(key) && !locationKeys.includes(key) && key !== 'searchText')
    .forEach((key) => {
      if (Array.isArray(req.query[key])) {
        req.query[key].forEach((value) => {
          filter.$or.push({ [`${key}.value`]: value });
        });
      } else {
        filter[formatToCamelCase(key)] = { key, value: req.query[key] };
      }
    });

  // properties that have value as array
  ['awardsOffered', 'type'].forEach((key) => {
    if (filter[key]) {
      filter[`${key}.value`] = { $elemMatch: { $eq: filter[key].value } };
      delete filter[key];
    }
  });

  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const select = 'addressBrief mission applyOnline name website';

  const counts = {};
  if (req.query.filter === 'my_saved') {
    counts.appliedUniversitiesCount = req.user.appliedUniversities.length;

    filter.savedBy = { $in: [req.user._id] };
  } else if (req.query.filter === 'applied') {
    counts.mySavedCount = req.user.savedUniversities.length;

    filter._id = { $in: req.user.appliedUniversities };
  } else if (req.query.filter === 'discover') {
    counts.mySavedCount = req.user.savedUniversities.length;
    counts.appliedUniversitiesCount = req.user.appliedUniversities.length;

    filter._id = { $nin: [...req.user.savedUniversities, ...req.user.appliedUniversities] };
  }

  if (req.query.searchText) {
    req.query.searchText = req.query.searchText.replace(/\([^)]*\)/g, ''); // Remove text in parentheses
    const searchText = stopWord.removeStopwords(req.query.searchText.trim().split(' ')); // Remove stopwords e.g 'of', 'the', 'a', 'an' etc
    const filterObject = [];
    const nonLiteralValues = [
      'type.value',
      'awardsOffered.value',
      'noncreditEducationOffered.value',
      'completionsNumberOfAwardsConferred20212022.value',
      'facultyInfoFall2022.value', // Not array but has value as object
    ].reduce((acc, item) => ({ ...acc, [item]: item }), {});

    Object.keys(University.schema.paths).forEach((key) => {
      if (key.includes('value')) {
        if (!(nonLiteralValues[key] || key.split('value')[1] !== '')) {
          // e.g select type.value but not type.value.name
          filterObject.push({
            $and: wordsArrayToFilterArray(searchText, key),
          });
        } else if (['type.value', 'awardsOffered.value'].includes(key)) {
          // type.value and awardsOffered.value are arrays of strings
          filterObject.push({
            $and: wordsArrayToFilterArray(searchText, '$elemMatch').map((item) => ({ [key]: item })),
          });
        } else if (key === 'facultyInfoFall2022.value') {
          // facultyInfoFall2022.value is an object
          filterObject.push({
            $and: wordsArrayToFilterArray(searchText, `${key}.value`),
          });
        } else if (key.includes('completionsNumberOfAwardsConferred')) {
          filterObject.push({
            $and: wordsArrayToFilterArray(searchText, 'name').map((item) => ({ [key]: { $elemMatch: item } })),
          });
          filterObject.push({
            $and: wordsArrayToFilterArray(searchText, 'category').map((item) => ({ [key]: { $elemMatch: item } })),
          });
          filterObject.push({
            $and: wordsArrayToFilterArray(searchText, 'awardLevels.name').map((item) => ({ [key]: { $elemMatch: item } })),
          });
        }
      }
    });
    if (filterObject.length > 0) {
      filter.$and.push({
        $or: filterObject,
      });
    }
  }

  const locationOptions = pick(req.query, locationKeys);
  if (locationOptions.city) {
    const cities = Array.isArray(locationOptions.city) ? locationOptions.city : [locationOptions.city];

    const citiesFilter = { $or: [] };
    cities.forEach((city) => {
      citiesFilter.$or.push({ [`address.value`]: { $regex: city, $options: 'i' } });
    });
    filter.$and.push(citiesFilter);
  } else if (locationOptions.state) {
    locationOptions.state = Array.isArray(locationOptions.state) ? locationOptions.state : [locationOptions.state];
    filter.$and.push({
      $or: locationOptions.state.map((state) => ({ [`address.value`]: { $regex: state, $options: 'i' } })),
    });
  } else if (locationOptions.country) {
    locationOptions.country = Array.isArray(locationOptions.country) ? locationOptions.country : [locationOptions.country];
    const countries = await Country.find({ name: { $in: locationOptions.country } }).populate('states');
    const states = countries.reduce((acc, country) => [...acc, ...country.states.map((state) => state.name)], []);
    const statesFilter = { $or: [] };
    states.forEach((state) => {
      statesFilter.$or.push({ [`address.value`]: { $regex: state, $options: 'i' } });
    });
    if (statesFilter.$or.length > 0) {
      filter.$and.push(statesFilter);
    }
  }

  if (filter.$or.length === 0) delete filter.$or;
  if (filter.$and.length === 0) delete filter.$and;
  const universities = await University.paginate(filter, { ...options, select });

  universities.appliedUniversitiesCount = universities.totalResults; // Will be overwritten if counts.appliedUniversitiesCount is set
  universities.mySavedCount = universities.totalResults; // will be overwritten if counts.mySavedCount is set

  // universities.results.forEach((university, index) => {
  //   universities.results[index]._doc.savedByMe = req.user.savedUniversities.includes(university._id);
  //   universities.results[index]._doc.appliedByMe = req.user.appliedUniversities.includes(university._id);
  // });

  return { ...universities, ...counts };
};

const saveUserUniversity = async (id, user) => {
  const university = await University.findById(id);
  let saved = true;
  if (!university) throw new ApiError(httpStatus.NOT_FOUND, 'University not found');
  if (user.savedUniversities.includes(id)) {
    user.savedUniversities.pull(id);
    university.savedBy.pull(user._id);
    saved = false;
  } else {
    user.savedUniversities.push(id);
    university.savedBy.push(user._id);
  }
  await university.save();
  await user.save();
  return { userId: user.id, savedUniversities: user.savedUniversities, saved };
};

const markAsApplied = async (id, user) => {
  const university = await University.findById(id);
  let applied = true;
  if (!university) throw new ApiError(httpStatus.NOT_FOUND, 'University not found');
  if (user.appliedUniversities.includes(id)) {
    user.appliedUniversities.pull(id);
    applied = false;
  } else {
    user.appliedUniversities.push(id);
  }
  await user.save();
  return { userId: user.id, appliedUniversities: user.appliedUniversities, applied };
};

const hideUniversityFromUser = async (id, user) => {
  const university = await University.findById(id);
  let hidden = true;

  if (!university) throw new ApiError(httpStatus.NOT_FOUND, 'University not found');
  if (user.hiddenUniversities.includes(id)) {
    user.hiddenUniversities.pull(id);
    university.hideFrom.pull(user._id);
    hidden = false;
  } else {
    user.hiddenUniversities.push(id);
    university.hideFrom.push(user._id);
  }

  await user.save();
  await university.save();

  return { userId: user.id, hiddenUniversities: user.hiddenUniversities, hidden };
};

module.exports = {
  getUniversities,
  saveUserUniversity,
  markAsApplied,
  getUniversityById,
  hideUniversityFromUser,
  getUniversityEnums,
  wordsArrayToFilterArray,
  getUniversityNames,
};
