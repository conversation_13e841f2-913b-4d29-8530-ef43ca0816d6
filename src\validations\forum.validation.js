const Joi = require('joi');
const { objectId } = require('./custom.validation');
const { fileSchema } = require('./file.validation');

const validCategories = [
  'All',
  'General Discussion',
  'Admissions',
  'Test Scores',
  'SAT',
  'ACT',
  'College Applications',
  'Scholarships',
  'Financial Aid',
  'Study Tips',
  'Student Life',
  'Internships',
  'Career Advice',
  'Graduate School',
];

const uniqueTags = (value, helpers) => {
  const uniqueValues = new Set(value);
  if (uniqueValues.size !== value.length) {
    return helpers.message('tags must be unique');
  }
  return value;
};

const createForumPost = {
  body: Joi.object().keys({
    text: Joi.string().required(),
    topic: Joi.string().required(),
    category: Joi.string().valid(...validCategories),
    tags: Joi.alternatives().try(Joi.string().trim(), Joi.array().items(Joi.string().trim()).custom(uniqueTags)),
  }),
  files: Joi.array().items(fileSchema()),
};

const getForumPosts = {
  query: Joi.object().keys({
    userId: Joi.string().custom(objectId),
    sortBy: Joi.string().valid('createdAt:asc', 'createdAt:desc', 'updatedAt:asc', 'updatedAt:desc'),
    tags: Joi.array().items(Joi.string().trim()),
    category: Joi.string().valid(...validCategories),
    limit: Joi.number().integer(),
    page: Joi.number().integer(),
    searchText: Joi.string(),
  }),
};

const getForumPost = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId),
  }),
};

const updateForumPost = {
  params: Joi.object().keys({
    id: Joi.required().custom(objectId),
  }),
  body: Joi.object().keys({
    text: Joi.string(),
    topic: Joi.string(),
    deletedUrls: Joi.string(),
    solved: Joi.boolean(),
    category: Joi.string().valid(...validCategories),
    tags: Joi.alternatives().try(Joi.string().trim(), Joi.array().items(Joi.string().trim()).custom(uniqueTags)),
  }),
  files: Joi.array().items(fileSchema()),
};

const deleteForumPost = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId),
  }),
};

const searchForumPosts = {
  query: Joi.object().keys({
    text: Joi.string().required(),
    topic: Joi.string(),
  }),
};

// forum reply
const createForumReply = {
  body: Joi.object().keys({
    text: Joi.string().required(),
  }),
  files: Joi.array().items(fileSchema()),
};

const getForumReplies = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required(),
  }),
  query: Joi.object().keys({
    userId: Joi.string().custom(objectId),
    sortBy: Joi.string().valid(
      'createdAt:asc',
      'createdAt:desc',
      'updatedAt:asc',
      'updatedAt:desc',
      'upvotes:asc',
      'upvotes:desc',
      'downvotes:asc',
      'downvotes:desc',
    ),
    limit: Joi.number().integer(),
    page: Joi.number().integer(),
  }),
};

const getForumReply = {
  params: Joi.object().keys({
    forumPostId: Joi.string().custom(objectId).required(),
    id: Joi.string().custom(objectId).required(), // ForumReply id
  }),
};

const updateForumReply = {
  params: Joi.object().keys({
    forumPostId: Joi.string().custom(objectId).required(),
    id: Joi.string().custom(objectId).required(), // ForumReply id
  }),
  body: Joi.object().keys({
    text: Joi.string(),
    deletedUrls: Joi.string(),
    accepted: Joi.boolean(),
  }),
  files: Joi.array().items(fileSchema()),
};

const voteReply = {
  params: Joi.object().keys({
    forumPostId: Joi.string().custom(objectId).required(),
    id: Joi.string().custom(objectId).required(), // ForumReply id
  }),
  body: Joi.object({
    upvote: Joi.boolean().valid(true),
    downvote: Joi.boolean().valid(true),
  })
    .xor('upvote', 'downvote')
    .messages({ 'object.xor': 'Either upvote or downvote can be specified, but not both' })
    .required(),
};

const deleteForumReply = {
  params: Joi.object().keys({
    forumPostId: Joi.string().custom(objectId).required(),
    id: Joi.string().custom(objectId).required(), // ForumReply id
  }),
};

module.exports = {
  createForumPost,
  getForumPosts,
  getForumPost,
  updateForumPost,
  deleteForumPost,
  searchForumPosts,
  createForumReply,
  getForumReplies,
  getForumReply,
  updateForumReply,
  voteReply,
  deleteForumReply,
};
