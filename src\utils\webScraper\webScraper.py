from selenium import webdriver
from selenium.webdriver import <PERSON><PERSON>hai<PERSON>
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select, WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.edge.service import Service as EdgeService
# from selenium.webdriver.opera.service import Service as OperaService
import json
import time
# ********** First Bank
edge_service = EdgeService('C:\\Users\\<USER>\\Downloads\\Programs\\edgedriver_win64\\msedgedriver.exe')

# Launch the browser and open a new blank page
browser = webdriver.Edge(service=edge_service)
browser.set_window_size(1200, 800)

# Navigate the page to NCES college navigator URL
browser.get('https://nces.ed.gov/collegenavigator')

universities = []

# Find the state select element
state_select = Select(browser.find_element(By.ID, 'ctl00_cphCollegeNavBody_ucSearchMain_ucMapMain_lstState'))
print('------------------------------------')
print(state_select.first_selected_option.get_attribute('value'))
print(len(state_select.options))
print('------------------------------------')

# Loop through the state options
for index in range(len(state_select.options)):
    state_select = Select(browser.find_element(By.ID, 'ctl00_cphCollegeNavBody_ucSearchMain_ucMapMain_lstState'))
    option = state_select.options[index]
    # print(option.get_attribute('value'))
    print(state_select.first_selected_option.get_attribute('value'))
    print(len(state_select.options))
    if option.get_attribute('value') != 'all':
        
        # Unselect last selected option
        state_select.deselect_all()        
        
        # Select the state option
        option.click()

        print('------------------------------------')
        print(state_select.first_selected_option.get_attribute('value'))
        print(option.get_attribute('value'))
        print('------------------------------------')

        # Click the show results button
        search_button = browser.find_element(By.ID, 'ctl00_cphCollegeNavBody_ucSearchMain_btnSearch')
        search_button.click()
        # WebDriverWait(browser, 30).until(EC.url_changes(browser.current_url))

        # Wait for the page to load
        # browser.implicitly_wait(10)
        # print('------------------------------------')
        # print('Waiting for page to load')
        # wait = WebDriverWait(browser, 20)  # Adjust the timeout as needed
        # new_page_element = wait.until(EC.presence_of_element_located((By.ID, "ctl00_cphCollegeNavBody_ucResultsMain_tblResults")))
        # print('Done Waiting')
        # print('------------------------------------')


        # Perform actions on the new page here
        university_elements = browser.find_elements(By.CSS_SELECTOR, '.resultsW, .resultsY')
        print('University Elements Count:', len(university_elements))

        for uni_index in range(len(university_elements)):
            university_elements = browser.find_elements(By.CSS_SELECTOR, '.resultsW, .resultsY')
            university_element = university_elements[uni_index]
            university = {}
            university_link = university_element.find_element(By.CSS_SELECTOR, 'td:nth-child(2) a')
            print('------------------------------------')
            print(university_link.text)
            print('------------------------------------')
            university_link.click()
            # WebDriverWait(browser, 30).until(EC.url_changes(browser.current_url))

            # Wait for the new page to load
            # browser.implicitly_wait(10)

            # Get the data from the page
            print('------------------------------------')
            university['name'] = browser.find_element(By.CSS_SELECTOR, '.collegedash div span.headerlg').text
            print(university)
            print('------------------------------------')
            universities.append(university)

            # Go back to the previous page
            browser.back()

        # Go back to the previous page
        browser.back()
        print('------------------------------------')
        print(universities)
        print('------------------------------------')
        # print(option.get_attribute('value'))
        # wait = WebDriverWait(browser, 20)  # Adjust the timeout as needed
        # new_page_element = wait.until(EC.presence_of_element_located((By.ID, "ctl00_cphCollegeNavBody_ucSearchMain_ucMapMain_lstState")))
        # state_select = Select(browser.find_element(By.ID, 'ctl00_cphCollegeNavBody_ucSearchMain_ucMapMain_lstState'))
        # wait = WebDriverWait(browser, 10)
        # option = wait.until(lambda driver: Select(driver.find_element(By.ID, 'ctl00_cphCollegeNavBody_ucSearchMain_ucMapMain_lstState')).first_selected_option.is_clickable())

        print('------------------------------------')
        # print(state_select.first_selected_option.get_attribute('value'))
        # print(len(state_select.options))
        print('------------------------------------')
        

# Close the browser
browser.quit()

# Save the data to a JSON file
with open('universities.json', 'w') as json_file:
    json.dump(universities, json_file)

print('Universities JSON data has been saved')
