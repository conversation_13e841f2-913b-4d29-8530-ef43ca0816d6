<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>

<body>
  <h1>Hello socket</h1>
  <h3>651bb5bf4eb9240327ea9d56 - <PERSON></h3>
  <button onclick="fetchData(followUrl)">Follow user!</button>
  <button onclick="fetchData(joinGroupUrl)">Join Group!</button>
  <button onclick="fetchData(likePostUrl)">Like Post!</button>
  <button onclick="fetchData(likeCommentUrl)">Like Comment!</button>

  <br><br><br>
  <h3>Message Feature</h3>

  <div>
    <input type="text" placeholder="Enter text">
    <button onclick="sendMessage()">Send Message</button>
  </div>
</body>



<script src="https://cdn.socket.io/4.7.2/socket.io.min.js"
  integrity="sha384-mZLF4UVrpi/QTWPA7BjNPEnkIfRFn4ZEO3Qt/HFklTJBj/gBOV8G3HcKn4NfQblz" crossorigin="anonymous"></script>
<script>
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOnsidXNlcklkIjoiNjUxYmI1YmY0ZWI5MjQwMzI3ZWE5ZDU2Iiwicm9sZXMiOlsic3R1ZGVudCJdfSwiaWF0IjoxNzAyMjg5NTMwLCJleHAiOjE5NzAyMjg5NTMwLCJ0eXBlIjoiYWNjZXNzIn0.aER7mAGCskr9Z89xCiPS28hiciMERs9xZ1zHwOcny8k';

  const url = 'http://localhost:4000';
  const socket = io(url, {
    auth: {
      token: `${token}`,
    },
  });

  socket.on('connect', () => {
    isSocketConnected = true;
    console.log('Connection established');
  });

  socket.on('notification', (data) => {
    console.log('connected');
    console.log('data: ', data);
    console.log(socket.user);
  });

  // const apiUrl = `${url}/v1/users/652f6fb971c38a9b93066b9d/follow`; // Replace with your API URL
  const followUrl = `${url}/v1/users/652f6fb971c38a9b93066b9d/follow`; // Replace with your API URL
  const authToken = token; // Replace with your actual authorization token
  const joinGroupUrl = `${url}/v1/groups/65699eb8ff316eff38c4264d/join`; // Replace with your API URL
  const likePostUrl = `${url}/v1/posts/652d4d71e8fb3c03691f91d8/like`; // Replace with your API URL
  const likeCommentUrl = `${url}/v1/comments/656a12e8227abfe720e59e1b/like`; // Replace with your API URL

  const fetchData = async (apiUrl) => {
    try {
      const response = await fetch(apiUrl, {
        method: 'POST', // Change the method according to your API endpoint
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}` // Include the authorization token in the headers
        }
      });

      if (!response.ok) {
        throw new Error('Network response was not ok.');
      }

      const data = await response.json();
      return 3;

    } catch (error) {
      console.error('Error:', error.message);
    }
  };

  const sendMessage = async () => {
    console.log("sent to WS");
    const payload = { text: "Hello", conversationId: '652f6fb971c38a9b93066b9d' };
    socket.emit('chatMessage', payload);
  };

</script>

</html>