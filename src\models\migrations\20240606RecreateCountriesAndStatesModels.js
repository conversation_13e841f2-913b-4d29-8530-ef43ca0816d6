const Async = require('async');
const fs = require('fs').promises;
const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const Country = require('../country.model');
const State = require('../state.model');
const City = require('../city.model');

const createStateModel = async () => {
  const migrated = await GlobalVariable.findOne({ name: 'Recreate countries and states' });

  if (migrated) {
    return;
  }

  // const countries = await Country.find({});
  const data = await fs.readFile('./src/utils/webScraper/countries+states+cities.json', 'utf8');
  const countries = JSON.parse(data);
  await State.deleteMany({});
  logger.info('State model deleted');
  await City.deleteMany({});
  logger.info('City model deleted');
  await Country.deleteMany({});
  logger.info('Country model deleted');

  await Async.eachSeries(countries, async (country) => {
    logger.info(`Reloading country: ${country.name}`);
    const states = [];

    await Async.eachSeries(country.states, async (state) => {
      const cities = [];
      await Async.eachSeries(state.cities, async (city) => {
        const createdCity = await City.create({
          name: city.name,
          latitude: city.latitude,
          longitude: city.longitude,
        });
        cities.push(createdCity._id);
      });
      const createdState = await State.create({
        name: state.name,
        stateCode: state.state_code,
        cities,
      });
      states.push(createdState._id);
    });

    await Country.create({
      name: country.name,
      iso3: country.iso3,
      iso2: country.iso2,
      phoneCode: country.phone_code,
      capital: country.capital,
      states,
    });
  });

  await GlobalVariable.create({ name: 'Recreate countries and states', value: 'true' });
  logger.info('Recreated countries and states');
};

module.exports = createStateModel;
