module.exports = {
  tags: ['Users'],
  summary: 'Get User Profile by ID',
  description: 'Retrieve a user profile record by their ID.',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: false,
      description: 'User ID to retrieve',
      schema: {
        type: 'string',
        example: '507f1f77bcf86cd799439011',
      },
    },
  ],
  responses: {
    200: {
      description: 'User profile record retrieved successfully',
      content: {
        'application/json': {
          example: {
            status: 'SUCCESS',
            message: 'User profile record retrieved successfully',
            data: {
              username: 'char<PERSON><PERSON><PERSON>',
              photo: {
                url: 'https://storagesite.com/users%2Fphoto/74570fc0-c0f4-11ee-92c2-61dcf2fafad1-866-536x354.jpg',
              },
              firstName: '<PERSON>',
              lastName: 'Darwin',
              commentLikes: [],
              postLikes: ['652d4d71e8fb3c03691faaa8', '65de64a55ce992d3d539aaae'],
              profile: {
                basicInformation: {
                  personalStatement: 'This is a personal statement 232.',
                },
                education: [],
                certification: [],
                testscore: [],
                experience: [
                  {
                    title: 'Software Developers',
                    companyName: 'Example Company',
                    start: '2021-01-01T00:00:00.000Z',
                    end: '2022-12-31T00:00:00.000Z',
                    current: false,
                    description: 'Worked on various software projects.',
                    id: '65df01d1aed9db0a9df4f010',
                  },
                  {
                    title: 'Software Developer Date',
                    companyName: 'Example Company',
                    start: '2021-01-01T00:00:00.000Z',
                    end: '2022-12-31T00:00:00.000Z',
                    current: false,
                    description: 'Worked on various software projects.',
                    id: '65df035fc66dcf2f4d332ce7',
                  },
                ],
                skills: ['JavaScript x2', 'Python', 'Data Analysis'],
                hobbies: ['Hiking', 'Photography', 'Gardening'],
                project: [
                  {
                    projectName: 'Sample Projectxxxxxxx',
                    description: 'This project aims to...',
                    start: '2021-01-01T00:00:00.000Z',
                    end: '2021-12-31T00:00:00.000Z',
                    projectLink: 'https://example.com/project',
                    id: '65df001caed9db0a9df4eff7',
                  },
                  {
                    projectName: 'Sample Project 2',
                    description: 'This project aims to...',
                    start: '2021-01-01T00:00:00.000Z',
                    end: '2021-12-31T00:00:00.000Z',
                    projectLink: 'https://example.com/project',
                    id: '65df0035aed9db0a9df4effd',
                  },
                ],
                award: [],
                volunteering: [
                  {
                    title: 'Volunteer Title 2',
                    companyName: 'Volunteer Organization',
                    start: '2022-01-01T00:00:00.000Z',
                    end: '2022-12-31T00:00:00.000Z',
                    current: false,
                    description: 'Volunteered for...',
                    id: '65df0265aed9db0a9df4f023',
                  },
                  {
                    title: 'Volunteer Title Date 2',
                    companyName: 'Volunteer Organization',
                    start: '2022-01-01T00:00:00.000Z',
                    end: '2022-12-31T00:00:00.000Z',
                    current: false,
                    description: 'Volunteered for...',
                    id: '65df0355c66dcf2f4d332ce1',
                  },
                ],
              },
              followers: ['652f6fb971c38a9b93066b9d', '65bb8ea12540b2c33e00ccde'],
              following: ['652f6fb971c38a9b93066b9d'],
              postBookmarks: ['652ccdfd888027a1d8e4e097'],
              reposts: [],
              banner: {
                url: 'https://storageunyked.blob.core.windows.net/users%2Fbanners/287ccbb0-b07a-11ee-a7b2-ad8c5eb45332-ad_ps.jpg',
              },
              tagLine: '',
              middleName: '',
              id: '651bb5bf4eb9240327ea9d56',
            },
          },
        },
      },
    },
  },
};
