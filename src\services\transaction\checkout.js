/* eslint-disable no-undef */
/* eslint-disable no-use-before-define */
// This is your test publishable API key.
const stripe = Stripe(
  'pk_test_51O4GjGJvpX1RuF9vVWXaPah2PMTSpvmHAhoYAn7ZdHmq3GVeTyNwgtPIJZLNnDgCnoGhVvGwj4Nswm7M0sF8yNaE00OVi7ps8D',
);

// The items the customer wants to buy

let elements;

initialize();
checkStatus();

document.querySelector('#payment-form').addEventListener('submit', handleSubmit);

// Fetches a payment intent and captures the client secret
async function initialize() {
  const response = await fetch('http://localhost:3500/v1/transactions/create-payment-intent', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization:
        'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOnsidXNlcklkIjoiNjVjNWQ0MTBjMDU3M2Y4ZTdkZDk1NDk5Iiwicm9sZXMiOlsic3R1ZGVudCIsImFkbWluIiwiZGV2ZWxvcGVyIl0sImJ1c2luZXNzSWQiOiI2NmM0OWIwNmQ0MDI2MDJhNGJlZjIwOWEiLCJwZW5kaW5nQnVzaW5lc3MiOiJ0cnVlIn0sImlhdCI6MTcyODEzMzc2NiwiZXhwIjoxNzQ2MTMzNzY2LCJ0eXBlIjoiYWNjZXNzIn0.9WEK4OSgXiBQ42D-86hwtH-niMvMkV3KqTIHz8milqI',
    },
    body: JSON.stringify({ serviceId: '66edad94b7386d200e1b0b6f', plan: 'basic' }),
  });
  const { clientSecret } = (await response.json()).data.clientSecret;

  const appearance = {
    theme: 'stripe',
  };
  elements = stripe.elements({ appearance, clientSecret });

  const paymentElementOptions = {
    layout: 'tabs',
  };

  const paymentElement = elements.create('payment', paymentElementOptions);
  paymentElement.mount('#payment-element');
}

async function handleSubmit(e) {
  e.preventDefault();
  setLoading(true);

  const { error } = await stripe.confirmPayment({
    elements,
    confirmParams: {
      // Make sure to change this to your payment completion page
      return_url: 'http://localhost:5501/src/services/transaction/checkout_completed.html',
    },
  });

  // This point will only be reached if there is an immediate error when
  // confirming the payment. Otherwise, your customer will be redirected to
  // your `return_url`. For some payment methods like iDEAL, your customer will
  // be redirected to an intermediate site first to authorize the payment, then
  // redirected to the `return_url`.
  if (error.type === 'card_error' || error.type === 'validation_error') {
    showMessage(error.message);
  } else {
    showMessage('An unexpected error occurred.');
  }

  setLoading(false);
}

// Fetches the payment intent status after payment submission
async function checkStatus() {
  const clientSecret = new URLSearchParams(window.location.search).get('payment_intent_client_secret');

  if (!clientSecret) {
    return;
  }

  const { paymentIntent } = await stripe.retrievePaymentIntent(clientSecret);

  switch (paymentIntent.status) {
    case 'succeeded':
      showMessage('Payment succeeded!');
      break;
    case 'processing':
      showMessage('Your payment is processing.');
      break;
    case 'requires_payment_method':
      showMessage('Your payment was not successful, please try again.');
      break;
    default:
      showMessage('Something went wrong.');
      break;
  }
}

// ------- UI helpers -------

function showMessage(messageText) {
  const messageContainer = document.querySelector('#payment-message');

  messageContainer.classList.remove('hidden');
  messageContainer.textContent = messageText;

  setTimeout(function () {
    messageContainer.classList.add('hidden');
    messageContainer.textContent = '';
  }, 4000);
}

// Show a spinner on payment submission
function setLoading(isLoading) {
  if (isLoading) {
    // Disable the button and show a spinner
    document.querySelector('#submit').disabled = true;
    document.querySelector('#spinner').classList.remove('hidden');
    document.querySelector('#button-text').classList.add('hidden');
  } else {
    document.querySelector('#submit').disabled = false;
    document.querySelector('#spinner').classList.add('hidden');
    document.querySelector('#button-text').classList.remove('hidden');
  }
}
