const express = require('express');

const router = express.Router();
const { auth } = require('../../middlewares/auth');
const { globalController } = require('../../controllers');
const validate = require('../../middlewares/validate');
const { globalValidation } = require('../../validations');

router.post('/contact-us', validate(globalValidation.contactUs), globalController.contactUs);

router.get('/user-analytics', auth('manageUsers'), validate(globalValidation.userAnalytics), globalController.userAnalytics);

router.get('/languages', validate(globalValidation.getLanguages), globalController.getLanguages);

router.use(auth());

router.get('/search', validate(globalValidation.search), globalController.search);

router.post('/report', validate(globalValidation.reportSchema), globalController.report);

router.get('/search-users', validate(globalValidation.searchUsers), globalController.searchUsers);

router.get('/generate-sas-url', validate(globalValidation.generateSasUrl), globalController.generateSasUrl);

// router.get('/get-recommendation-profile', auth('getBotRecommendationProfile'), globalController.getAllUsersProfiles);

module.exports = router;
