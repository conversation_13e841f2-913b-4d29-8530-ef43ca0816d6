const Joi = require('joi');

const addSubscriber = {
  body: Joi.object().keys({
    email: Joi.string().email().required(),
    subscribedFor: Joi.string().valid('counselor', 'newsletter').required(),
    name: Joi.string().when('subscribedFor', { is: 'counselor', then: Joi.required() }),
  }),
};

const getSubscribers = {
  query: Joi.object().keys({
    subscribedFor: Joi.string().valid('counselor', 'newsletter').required(),
    sortBy: Joi.string().valid('createdAt:asc', 'createdAt:desc', 'subscribedFor:asc', 'subscribedFor:desc'),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
  }),
};

const unsubscribe = {
  body: Joi.object().keys({
    email: Joi.string().email().required(),
    subscribedFor: Joi.string().valid('counselor', 'newsletter').required(),
  }),
};

module.exports = {
  addSubscriber,
  getSubscribers,
  unsubscribe,
};
