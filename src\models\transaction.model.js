const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');
const { toJSON, paginate } = require('./plugins');
const { currencies, businessEnums } = require('../config/constants');

const transactionSchema = new mongoose.Schema(
  {
    account: { type: mongoose.Schema.Types.ObjectId, ref: 'Account', required: true },

    checkoutSessionId: { type: String, trim: true, unique: true }, // For checkout
    transferId: { type: String, trim: true, unique: true }, // For transfers out

    amount: { type: mongoose.Schema.Types.Decimal128, required: true },
    comment: { type: String, trim: true, required: true },
    currency: { type: String, required: true, default: currencies.USD, enum: Object.values(currencies) },
    paymentProvider: { type: String, trim: true, required: true },
    stripeRefundId: { type: String },
    refundTransactionId: { type: mongoose.Schema.Types.ObjectId, ref: 'Transaction' },
    reference: { type: String, unique: true, default: uuidv4 },
    status: { type: String, required: true, enum: Object.values(businessEnums.transactionStatuses) },
    type: { type: String, required: true, enum: Object.values(businessEnums.transactionTypes) },
    transactionFee: { type: Number }, // Service charge for the transaction
  },
  {
    timestamps: true,
  },
);

transactionSchema.index({ status: 1, createdAt: 1, updatedAt: 1 });

transactionSchema.plugin(toJSON);
transactionSchema.plugin(paginate);

const Transaction = mongoose.model('Transaction', transactionSchema);

module.exports = Transaction;
