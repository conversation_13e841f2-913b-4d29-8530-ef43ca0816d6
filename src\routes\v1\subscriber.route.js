const express = require('express');

const router = express.Router();
const validate = require('../../middlewares/validate');
const { subscriberValidation } = require('../../validations');
const { subscriberController } = require('../../controllers');

// router.get('/', validate(subscriberValidation.getSubscribers), subscriberController.getSubscribers);
router.post('/', validate(subscriberValidation.addSubscriber), subscriberController.addSubscriber);
// router.patch('/unsubscribe', validate(subscriberValidation.unsubscribe), subscriberController.unsubscribe);

module.exports = router;
