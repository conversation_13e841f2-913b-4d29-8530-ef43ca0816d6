const postService = require('../post.service');
const logger = require('../../config/logger');
const { socketAuth } = require('../../middlewares/auth');
const messageService = require('../message.service');
const { globalController } = require('../../controllers');
const { scheduleSetBusinessOnlineStatus } = require('../../utils/jobs/jobSchedulers');

const socketLogger = (connectedUsers, user, action) => {
  const instancesCount = connectedUsers[user._id.toString()]?.length || 0;

  logger.info(
    `Socket ${action} for user: ${user.firstName || ''} ${user.lastName || ''} with id: ${
      user._id
    } (${instancesCount} instance${instancesCount > 1 ? 's' : ''}) `,
  );
  logger.info(`${Object.keys(connectedUsers).length} users connected by socket`);
};

class SocketConnection {
  constructor(io) {
    this.io = io;
    this.connectedUsers = {};
  }
}

const socketConnection = new SocketConnection();

const sockets = (io) => {
  io.use(socketAuth);
  socketConnection.io = io;

  io.on('connection', async (socket) => {
    const { user } = socket;
    const userId = user?._id.toString();
    if (!socketConnection.connectedUsers[userId]) {
      socketConnection.connectedUsers[userId] = [];
    }

    socketConnection.connectedUsers[userId].push(socket);
    socketConnection.connectedUsers[userId] = [...new Set(socketConnection.connectedUsers[userId])];

    socketLogger(socketConnection.connectedUsers, user, 'Connected');

    try {
      const messages = await messageService.getConversations(
        { query: { member: { user } }, options: { sortBy: 'updatedAt:desc', limit: 10, page: 1 } },
        true,
        false,
      );
      socket.emit('send-conversations', { message: messages });
    } catch (error) {
      logger.error('Error fetching conversations on socket connection');
      logger.error(error);
    }

    socket.on('chat-message', async (data) => messageService.createMessage(socket, data, socketConnection));
    socket.on('edit-message', async (data) => messageService.editMessage(socket, data, socketConnection));
    socket.on('delete-message', async (data) => messageService.deleteMessage(socket, data, socketConnection));
    socket.on('get-conversation', async (data) => messageService.getConversationWithSocket(socket, data, socketConnection));
    socket.on('mark-messages-as-read', async (data) => messageService.markAsRead(socket, data, socketConnection));
    socket.on('typing', async (data) => messageService.markTyping(socket, data));

    socket.on('/react', async (data) => postService.reactToPostBySocket(socket, data, socketConnection));
    socket.on('posts/repost', async (data) => postService.createRepostBySocket(socket, data, socketConnection));

    socket.on('user-meta-data', async (data) => globalController.getUserMetaData(socket, data, socketConnection));

    // eslint-disable-next-line global-require
    const { User } = require('../../models');
    socket.on('disconnect', async () => {
      if (socketConnection.connectedUsers[userId].length === 1) {
        delete socketConnection.connectedUsers[userId];

        // Mark user as offline
        await User.findByIdAndUpdate(userId, { online: false, lastSeen: Date.now() });
      } else {
        socketConnection.connectedUsers[userId] = socketConnection.connectedUsers[userId].filter(
          (instance) => instance.id !== socket.id,
        );
      }
      socketLogger(socketConnection.connectedUsers, user, 'Disconnected');
    });

    // User online status updates immediately, Business online status updates after 5 minutes
    await User.findByIdAndUpdate(userId, { online: true, lastSeen: Date.now() });
    if (socket.user.business) {
      await scheduleSetBusinessOnlineStatus({ businessId: socket.user.business });
    }
  });
};

module.exports = { sockets, socketConnection };
