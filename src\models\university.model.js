const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');

const universitySchema = new mongoose.Schema(
  {
    address: {
      key: { type: String, trim: true },
      value: { type: String, trim: true }, // TODO: unique: true
    },
    addressBrief: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },
    admissions: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },
    applyOnline: {
      key: { type: String },
      value: { type: String },
    },
    athleticGraduationRates: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },
    awardsOffered: {
      key: { type: String, trim: true },
      value: [{ type: String, trim: true }],
    },
    campusHousing: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },
    campusSetting: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },
    carnegieClassification: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },
    completionsNumberOfAwardsConferred20212022: {
      key: { type: String, trim: true },
      value: [
        {
          category: { type: String, trim: true },
          name: { type: String, trim: true },
          awardLevels: [
            {
              name: { type: String, trim: true },
              count: { type: Number, trim: true },
              offersDistantEducation: { type: Boolean, trim: true },
            },
          ],
        },
      ],
    },
    crawlId: { type: String, trim: true, required: true, unique: true },
    creditAccepted: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },
    disabilityServices: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },
    facultyInfoFall2022: {
      key: { type: String, trim: true },
      value: {
        key: { type: String, trim: true },
        value: { type: String, trim: true },
      },
    },
    federalAid: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },
    financialAid: {
      key: { type: String },
      value: { type: String },
    },
    forCreditInstructionProgramsOffered: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },
    ipedsId: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },
    mission: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },
    name: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },
    note: { type: String, trim: true },
    netPriceCalculator: {
      key: { type: String },
      value: { type: String },
    },
    noncreditEducationOffered: {
      key: { type: String, trim: true },
      value: [{ type: String, trim: true }],
    },
    opeId: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },
    otherCharacteristics: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },
    religiousAffiliation: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },
    relatedInstitutions: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },
    tel: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },
    type: {
      key: { type: String, trim: true },
      value: [{ type: String, trim: true }],
    },
    specialLearningOpportunities: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },
    studentPopulation: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },
    studentServices: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },
    studentToFacultyRatio: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },
    tuitionPoliciesForServicemembersAndVeterans: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },
    undergraduateStudentsEnrolledWhoAreFormallyRegisteredWithOfficeOfDisabilityServices: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },
    website: {
      key: { type: String, trim: true },
      value: { type: String, trim: true },
    },

    hideFrom: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
    savedBy: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
  },
  {
    timestamps: true,
  },
);

universitySchema.index({ createdAt: 1, 'name.value': 1 });

universitySchema.plugin(toJSON);
universitySchema.plugin(paginate);

universitySchema.pre('save', async function save(next) {
  try {
    if (this.type && this.type.value) {
      this.type.value = this.type.value[0]?.split(',').map((item) => item.trim());
    }
    if (this.awardsOffered) {
      this.awardsOffered.value = this.awardsOffered.value[0]?.split('\n').map((item) => item.trim());
    }
    if (this.mission) {
      this.mission.value = this.mission.value?.replace(/^[^a-zA-Z0-9]+|[^a-zA-Z0-9]+$/g, '');
    }
    if (this.noncreditEducationOffered && this.noncreditEducationOffered.value) {
      this.noncreditEducationOffered.value = this.noncreditEducationOffered.value[0]?.split('\n').map((item) => item.trim());
    }
    if (this.creditAccepted && this.creditAccepted.value) {
      this.creditAccepted.value = this.creditAccepted?.value[0]?.split('\n').map((item) => item.trim());
    }
    if (this.specialLearningOpportunities && this.specialLearningOpportunities.value) {
      this.specialLearningOpportunities.value = this.specialLearningOpportunities.value[0]
        ?.split('\n')
        .map((item) => item.trim());
    }
    return next();
  } catch (error) {
    return next(error);
  }
});

const University = mongoose.model('University', universitySchema);

module.exports = University;
