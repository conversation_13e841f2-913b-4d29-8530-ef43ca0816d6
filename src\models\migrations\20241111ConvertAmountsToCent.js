const Async = require('async');
const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const { businessEnums } = require('../../config/constants');
const ServiceOffer = require('../service.offer.model');

const varName = 'Convert amounts to cent';

const convertAmountsToCent = async () => {
  const migrated = await GlobalVariable.findOne({ name: varName });

  if (migrated) {
    return;
  }

  // Get all price offers
  const allPriceOffers = await ServiceOffer.find({ name: businessEnums.requiredOffers.PRICE[0] });

  // Convert all price offers to cent
  await Async.eachSeries(allPriceOffers, async (priceOffer) => {
    await ServiceOffer.updateOne(
      { _id: priceOffer._id },
      { value: priceOffer.value.map((v) => ({ ...v, offer: String(Number(v.offer) * 100) })) },
    );
  });

  await GlobalVariable.create({ name: varName, value: 'true' });
  logger.info('Amounts converted to cent');
};

module.exports = convertAmountsToCent;
