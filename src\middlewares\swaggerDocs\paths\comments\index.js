const createComment = require('./create.comment.path');
const getComments = require('./get.comments.path');
const getComment = require('./get.comment.path');
const updateComment = require('./update.comment.path');
const deleteComment = require('./delete.comment.path');
const likeComment = require('./like.comment.path');

module.exports = {
  '/comments/': { ...createComment, ...getComments },
  '/comments/{id}': { ...getComment, ...updateComment, ...deleteComment },
  '/comments/{commentId}/like': { ...likeComment },
};
