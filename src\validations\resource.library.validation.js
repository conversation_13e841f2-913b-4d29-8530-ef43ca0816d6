const Joi = require('joi');
const { resourceLibraryCategories } = require('../config/constants');
const { fileSchema } = require('./file.validation');
const { schoolsEnums } = require('../config/constants');

const fileMimeTypes = ['application/pdf'];
const imageMimeTypes = ['image/.+'];

const getResources = {
  query: Joi.object().keys({
    searchText: Joi.string(),
    // tags: Joi.alternatives().try(Joi.array().items(Joi.string()).unique(), Joi.string()),
    categories: Joi.alternatives().try(
      Joi.array()
        .items(Joi.string().valid(...Object.values(resourceLibraryCategories)))
        .unique(),
      Joi.string().valid(...Object.values(resourceLibraryCategories)),
    ),
    programType: Joi.string().valid(...Object.values(schoolsEnums.programTypes)),
    program: Joi.alternatives().try(Joi.array().items(Joi.string()).unique(), Joi.string()),
    sortBy: Joi.string()
      .valid('createdAt:asc', 'createdAt:desc', 'updatedAt:asc', 'updatedAt:desc', 'title:asc', 'title:desc')
      .default('createdAt:desc'),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
  }),
};

const getResource = {
  param: Joi.object().keys({
    id: Joi.string().required(),
  }),
};

const addResource = {
  body: Joi.object().keys({
    title: Joi.string().required(),
    description: Joi.string(),
    tags: Joi.alternatives().try(Joi.array().items(Joi.string()).unique(), Joi.string()),
    categories: Joi.alternatives().try(
      Joi.array()
        .items(Joi.string().valid(...Object.values(resourceLibraryCategories)))
        .unique(),
      Joi.string().valid(...Object.values(resourceLibraryCategories)),
    ),
    programTypes: Joi.alternatives().try(
      Joi.array()
        .items(Joi.string().valid(...Object.values(schoolsEnums.programTypes)))
        .unique(),
      Joi.string().valid(...Object.values(schoolsEnums.programTypes)),
    ),
    programs: Joi.alternatives().try(Joi.array().items(Joi.string()).unique(), Joi.string()),
  }),
  files: {
    file: Joi.array().items(fileSchema(10, fileMimeTypes)).required(),
    thumbnails: Joi.array().items(fileSchema(10, imageMimeTypes)),
  },
};

const updateResource = {
  param: Joi.object().keys({
    id: Joi.string().required(),
  }),
  body: Joi.object().keys({
    title: Joi.string(),
    description: Joi.string(),
    tags: Joi.alternatives().try(Joi.array().items(Joi.string()).unique(), Joi.string()),
    categories: Joi.alternatives().try(
      Joi.array()
        .items(Joi.string().valid(...Object.values(resourceLibraryCategories)))
        .unique(),
      Joi.string().valid(...Object.values(resourceLibraryCategories)),
    ),
    programTypes: Joi.alternatives().try(
      Joi.array()
        .items(Joi.string().valid(...Object.values(schoolsEnums.programTypes)))
        .unique(),
      Joi.string().valid(...Object.values(schoolsEnums.programTypes)),
    ),
    programs: Joi.alternatives().try(Joi.array().items(Joi.string()).unique(), Joi.string()),
  }),
  files: {
    file: Joi.array().items(fileSchema(10, fileMimeTypes)),
    thumbnails: Joi.array().items(fileSchema(10, imageMimeTypes)),
  },
};

const deleteResource = {
  param: Joi.object().keys({
    id: Joi.string().required(),
  }),
};

const resourceCounter = {
  param: Joi.object().keys({
    id: Joi.string().required(),
  }),
  query: Joi.object().keys({
    action: Joi.string().valid('view', 'download').default('view'),
  }),
};

const getResourcesAnalytics = {
  query: Joi.object().keys({
    sortBy: Joi.string()
      .valid('createdAt:asc', 'createdAt:desc', 'updatedAt:asc', 'updatedAt:desc', 'title:asc', 'title:desc')
      .default('createdAt:desc'),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
  }),
};

module.exports = {
  getResources,
  getResource,
  addResource,
  updateResource,
  deleteResource,
  resourceCounter,
  getResourcesAnalytics,
};
