const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const { businessEnums } = require('../config/constants');

const { ObjectId } = mongoose.SchemaTypes;

/*
Order is 
*/
const orderSchema = new mongoose.Schema(
  {
    acceptDisclaimer: { type: Boolean },
    client: { type: ObjectId, ref: 'User', required: true },
    cancelReason: { type: String },
    cancelledAt: { type: Date },
    conversation: { type: ObjectId, ref: 'MessageConversation' },
    fulfilledAt: { type: Date },
    deliverable: {
      files: [{ type: ObjectId, ref: 'File' }],
      comment: { type: String },
    },
    dueAt: { type: Date },
    fulfillComment: { type: String },
    orderNumber: { type: Number, unique: true },
    plan: { type: String, enum: Object.values(businessEnums.servicePlans), required: true },
    provider: { type: ObjectId, ref: 'Business', required: true },
    requirements: [{ type: ObjectId, ref: 'ServiceRequirementProvide' }],
    revisionCount: { type: Number, default: 0 },
    revisionsMaxCount: { type: Number, required: true },
    revisions: [{ type: ObjectId, ref: 'ServiceRevision' }],
    service: { type: ObjectId, ref: 'Service', required: true },
    startedAt: { type: Date },
    status: { type: String, enum: Object.values(businessEnums.orderStatuses), required: true },
    // supportingDocuments: [{ type: ObjectId, ref: 'File' }],
    text: { type: String },
    transaction: { type: ObjectId, ref: 'Transaction', required: true },
    extendedAt: { type: Date }, // date when the order was extended
    requestedExtension: { type: Boolean, default: false }, // flag to indicate if the order was extended
    referral: { type: ObjectId, ref: 'Referral' },
  },
  {
    timestamps: true,
  },
);

orderSchema.index({ orderNumber: 1, status: 1, createdAt: 1, updatedAt: 1 });

orderSchema.plugin(toJSON);
orderSchema.plugin(paginate);

orderSchema.pre('save', async function (next) {
  if (this.isNew) {
    const lastSequence = await mongoose.model('Counter').findOne({ id: 'orderNumber' });
    this.orderNumber = lastSequence ? lastSequence.seq + 1 : 1000;
    await mongoose.model('Counter').findOneAndUpdate({ id: 'orderNumber' }, { seq: this.orderNumber });
  }
  next();
});

const Order = mongoose.model('Order', orderSchema);

module.exports = Order;
