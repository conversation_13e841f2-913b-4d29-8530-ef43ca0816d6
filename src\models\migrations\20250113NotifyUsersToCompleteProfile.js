/* eslint-disable no-param-reassign */
const Async = require('async');
const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const User = require('../user.model');
const { registrationStatuses } = require('../../config/constants');
const { notifyUsersToCompleteProfile } = require('../../services/notification.service');

const varName = 'Notify users to complete profile';

const notifyUsersToCompleteProfileScript = async () => {
  const migrated = await GlobalVariable.findOne({ name: varName });
  if (migrated) {
    return;
  }

  const usersPromise = User.find({
    registrationStatus: { $in: [registrationStatuses.ACCOUNT_VERIFIED, registrationStatuses.SIGNUP_COMPLETED] },
  }).select('_id email');

  const [users] = await Promise.all([usersPromise]);

  await Async.eachOfSeries(users, async (user, index) => {
    await notifyUsersToCompleteProfile(user);
    await User.findByIdAndUpdate(user._id, { emailVerificationCount: 0 });
    logger.info(`User ${index + 1} of ${users.length} notified.`);
  });

  await GlobalVariable.create({ name: varName, value: 'true' });
  logger.info('Users notified to complete profile');
};

module.exports = notifyUsersToCompleteProfileScript;
