module.exports = {
  post: {
    summary: 'Join a group',
    tags: ['Groups'],
    parameters: [
      {
        in: 'path',
        name: 'id',
        schema: {
          type: 'string',
          example: '5f9d1e9a6f3f4b001f6d4c0b',
        },
        description: 'ID of the group to join',
      },
    ],
    responses: {
      201: {
        description: 'Operation successful',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Group joined successfully',
            },
          },
        },
      },
    },
  },
};
