const httpStatus = require('http-status');
const Async = require('async');
const stopword = require('stopword');
const { IssueReport, User } = require('../models');
const ApiError = require('../utils/ApiError');
const { processFileUploads } = require('./file.service');
const validateId = require('./shared/validateId');
const { issueReport: issueReportConstants } = require('../config/constants');
const { basicUserPopulate, getUserById } = require('./user.service');
const { generateUniqueId } = require('../utils/helperMethods');

const createIssueReport = async (req) => {
  const { body, files } = req;
  body.reporter = req.user._id;

  if (files) {
    const { attachment } = files;
    const file = await processFileUploads(attachment, 'issue-reports');
    body.attachment = file._id;
  }

  const issueReport = IssueReport.create(body);
  return issueReport;
};

const getIssueReportEnums = async (isPrivate) => {
  const issueUserSelect = `${basicUserPopulate.select} roles`;
  const developers = await User.find({ roles: { $in: ['developer'] } }).select(issueUserSelect);
  const admins = await User.find({ roles: { $in: ['admin', 'superAdmin'] } }).select(issueUserSelect);
  const users = { assignedTo: developers, resolvedBy: developers, closedBy: admins };
  return {
    types: issueReportConstants.types,
    priorities: issueReportConstants.priorities,
    statuses: issueReportConstants.statuses,
    possibleUsers: isPrivate ? users : undefined,
  };
};

const getIssueReports = async (filterParam, options, searchTextParam) => {
  const filter = filterParam;
  await Async.eachSeries(['reporter', 'assignedTo', 'resolvedBy'], async (field) => {
    if (filter[field]) {
      const id = validateId(filter[field], `User as ${field}`, false);
      if (!id) filter[field] = await generateUniqueId(IssueReport);
    }
  });
  const searchText = stopword.removeStopwords((searchTextParam || '').split(' '));

  if (filter.title) {
    filter.title = { $regex: filter.title, $options: 'i' };
  }
  filter.$or = [];

  if (searchText.length > 0) {
    const matchingUsers = (
      await User.find({
        $and: searchText.map((item) => ({
          $or: ['firstName', 'lastName', 'middleName', 'username'].map((field) => ({
            [field]: { $regex: item, $options: 'i' },
          })),
        })),
      })
    ).map((user) => String(user._id));

    filter.$or.push({
      $and: searchText.map((item) => ({
        $or: ['title', 'type', 'description', 'status', 'priority'].map((field) => ({
          [field]: { $regex: item, $options: 'i' },
        })),
      })),
    });

    if (matchingUsers.length > 0) {
      filter.$or.push({
        $or: [
          { reporter: { $in: matchingUsers } },
          { assignedTo: { $in: matchingUsers } },
          { resolvedBy: { $in: matchingUsers } },
          { closedBy: { $in: matchingUsers } },
        ],
      });
    }
  }

  if (filter.$or.length < 1) delete filter.$or;

  const issueReports = await IssueReport.paginate(filter, {
    ...options,
    populate: { path: 'reporter', ...basicUserPopulate },
  });
  return issueReports;
};

const getIssueReportById = async (id) => {
  validateId(id, 'Issue Report');

  const issueReport = await IssueReport.findById(id).populate([
    { path: 'reporter', ...basicUserPopulate },
    { path: 'assignedTo', ...basicUserPopulate },
    { path: 'resolvedBy', ...basicUserPopulate },
    { path: 'closedBy', ...basicUserPopulate },
  ]);
  if (!issueReport) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Issue Report not found');
  }
  return issueReport;
};

const updateIssueReport = async (id, body) => {
  validateId(id, 'Issue Report');

  const issueReport = await IssueReport.findById(id);
  if (!issueReport) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Issue Report not found');
  }

  const updateBody = body;
  const { statuses } = issueReportConstants;

  if (updateBody.status === statuses.RESOLVED) {
    validateId(body.resolvedBy, 'User as resolvedBy');

    const resolvedByUser = await getUserById(body.resolvedBy);
    if (!(resolvedByUser.roles.includes('admin') || resolvedByUser.roles.includes('developer'))) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        "You could't have resolved this report as you are not an admin or developer",
      );
    }

    updateBody.resolvedBy = body.resolvedBy;
    updateBody.resolvedAt = new Date();
  }
  if (updateBody.status === statuses.IN_PROGRESS) {
    validateId(body.assignedTo, 'User as assignedTo');

    const assignedToUser = await getUserById(body.assignedTo);
    if (!(assignedToUser.roles.includes('developer') || assignedToUser.roles.includes('admin'))) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Cannot assign to non-developer user');
    }

    updateBody.assignedTo = body.assignedTo;
  }
  if (updateBody.status === statuses.CLOSED) {
    validateId(body.closedBy, 'User as closedBy');

    const closedByUser = await getUserById(body.closedBy);
    if (!(closedByUser.roles.includes('admin') || closedByUser.roles.includes('developer'))) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'You cannot close this report as you are not an admin or developer');
    }

    updateBody.closedBy = body.closedBy;
    updateBody.closedAt = new Date();
  }

  Object.assign(issueReport, updateBody);
  await issueReport.save();
  return issueReport;
};

const getIssueReportStats = async () => {
  const stats = await IssueReport.aggregate([
    { $group: { _id: '$status', count: { $sum: 1 } } },
    { $project: { _id: 0, status: '$_id', count: 1 } },
    { $sort: { _id: 1 } },
  ]);

  const result = Object.values(issueReportConstants.statuses).reduce((acc, status) => {
    const stat = stats.find((s) => s.status === status);
    if (stat) {
      return { ...acc, [status]: stat.count };
    }
    return { ...acc, [status]: 0 };
  }, {});
  return result;
};

module.exports = {
  createIssueReport,
  getIssueReports,
  updateIssueReport,
  getIssueReportEnums,
  getIssueReportStats,
  getIssueReportById,
};
