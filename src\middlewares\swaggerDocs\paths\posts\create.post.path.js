module.exports = {
  post: {
    tags: ['Posts'],
    summary: 'Create a new post',
    description: 'Create a new post with file files',
    consumes: ['multipart/form-data'],
    parameters: [
      {
        in: 'query',
        name: 'group',
        description: 'If to post to group',
        required: false,
        default: false,
        schema: {
          type: 'boolean',
          example: true,
        },
      },
      {
        name: 'files',
        in: 'formData',
        description: 'The file files to upload',
        required: false,
        type: 'file',
      },
      {
        name: 'body',
        in: 'formData',
        description: 'The data to upload',
        required: true,
        schema: {
          type: 'object',
          properties: {
            text: {
              type: 'string',
              description: 'Text content of the post',
              example: 'This is a new post.',
            },
            group: {
              type: 'string',
              description: 'The group ID to post to',
              example: '507f1f77bcf86cd799439011',
            },
            visibility: {
              type: 'string',
              description: 'The visibility of the post',
              example: 'only_me',
              default: 'public',
            },
            tags: {
              type: 'array',
              description: 'The tags for the post',
              items: {
                type: 'string',
                example: 'tag1',
              },
            },
          },
        },
      },
    ],
    responses: {
      201: {
        description: 'Post created successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: {
                  type: 'string',
                  example: 'SUCCESS',
                },
                message: {
                  type: 'string',
                  example: 'Post created successfully',
                },
              },
            },
          },
        },
      },
      500: {
        description: 'Internal server error',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error500',
            },
          },
        },
      },
    },
  },
};
