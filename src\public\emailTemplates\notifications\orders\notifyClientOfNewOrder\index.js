// const config = require('../../../../../config/config');
// const { capitalizeFirstChar } = require('../../../../../utils/stringFormatting');

const year = new Date().getFullYear();

module.exports = (sender, recipient, details) => {
  // details -> {receiverType: 'client', orderId}
  // const recipientFirstName = capitalizeFirstChar(recipient?.firstName || '');
  // const recipientLastName = capitalizeFirstChar(recipient?.lastName || '');

  // const resourceUrl = `${config?.client?.baseUrl}/orders/${details?.orderId}`;

  const html = `<!DOCTYPE html
  PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html" charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@300;400;500;600;700;800&display=swap"
    rel="stylesheet">
  <title>Invoice</title>
</head>

<body style="margin: 0;font-family: 'Plus Jakarta Sans', sans-serif;background: white;">
  <center class="wrapper" style="position: relative; height: 100%; table-layout: fixed;">
    <div class="webkit" style="width: 80%;box-shadow: 0px 10px 20px rgba(207, 203, 222, 0.42);border-radius: 10px;">
      <table class="outer"
        style="line-height: 1.65rem;border-spacing: 0;margin: 0 auto;width: 100%;color: #4a4a4a;border-radius: 10px;"
        align="center">
        <!-- Header -->
        <tr>
          <td style="text-align: center; border-bottom: 2px solid rgb(243 244 246);">
            <center style="background: #f6f9fc;border-radius: 10px 10px 0 0;">
              <a href="https://yourwebsite.com"
                style="text-decoration:none;font-weight: bolder;padding: 15px;background: #f6f9fc;">
                <img src="https://res.cloudinary.com/emmii/image/upload/v1696345178/unyked/sv1zvadt6si3uwkevzki.png"
                  width="100px" alt="Your Logo" style="border: 0;padding: 15px;">
              </a>
            </center>
          </td>
        </tr>

        <!-- Message Content -->
        <tr>
          <td style="padding:0 10%;background: #f6f9fc;">
            <table style="width: 100%;border-spacing: 0;">
              <tr>
                <td style="padding: 0;">
                  <h3>
                    <b>Invoice</b>
                  </h3>
                </td>
              </tr>

              <!-- Order Details Section -->
              <tr>
                <td style="padding-top: 20px;">
                  <table style="width: 100%;border-spacing: 0;margin: 20px 0;">
                    <tr>
                      <td style="text-align: left; padding-top: 20px; border-bottom: 1px solid #0b0000;">Transaction Amount:</td>
                      <td style="text-align: right; padding-top: 20px; border-bottom: 1px solid #000000;">${details.transactionAmount}</td>
                    </tr>
                    
                    <tr>
                      <td style="text-align: left; padding-top: 20px; border-bottom: 1px solid #0b0000;">Destination:</td>
                      <td style="text-align: right; padding-top: 20px; border-bottom: 1px solid #0b0000;">${details.destination}</td>
                    </tr>
                    
                    <tr>
                      <td style="text-align: left; padding-top: 20px; border-bottom: 1px solid #0b0000;">Transaction Type:</td>
                      <td style="text-align: right; padding-top: 20px; border-bottom: 1px solid #0b0000;">
                        <button style="color: rgb(255, 0, 0); background-color: rgb(255, 236, 236); border: 1px solid red; border-radius: 3px; padding: 5px; margin-bottom: 5px;">Debit</button>
                      </td>
                    </tr>

                    <tr>
                      <td style="text-align: left; padding-top: 20px; border-bottom: 1px solid #0b0000;">Transaction Status:</td>
                      <td style="text-align: right; padding-top: 20px; border-bottom: 1px solid #0b0000;">
                        <button style="color: rgb(1, 180, 1); background-color: rgb(186, 231, 193); border: 1px solid rgb(1, 180, 1); border-radius: 3px; padding: 5px; margin-bottom: 5px;">Completed</button>
                      </td>
                    </tr>

                    <tr>
                      <td style="text-align: left; padding-top: 20px; border-bottom: 1px solid #0b0000;">Service Fee:</td>
                      <td style="text-align: right; padding-top: 20px; border-bottom: 1px solid #0b0000;">${details.serviceFee}</td>
                    </tr>
                  </table>
                </td>
              </tr>
              
            </table>
          </td>
        </tr>

        <!-- Footer -->
        <tr>
          <td style="padding-top:20px; background-color: #f6f9fc;">
            <center>
              <img src="https://res.cloudinary.com/unyked/image/upload/v1732116425/payslip-bg_apjmxi.png" alt="" style="width: 100%; height: 150px;">
            </center>
          </td>
        </tr>

        <tr>
          <td style="height: 10px;background-color: #d7e3ff;padding: 0;border-radius: 0 0 10px 10px;">
            <p style="font-size: smaller;padding: 30px 10%;">&copy;${year} UnykEd Inc.</p>
          </td>
        </tr>
      </table>
    </div>

  </center>
</body>

</html>
`;
  return html;
};
