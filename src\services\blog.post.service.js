const httpStatus = require('http-status');
const Joi = require('joi');
const Async = require('async');
const stopword = require('stopword');
const { BlogPost, File } = require('../models');
const ApiError = require('../utils/ApiError');
const { azureContainers, blogStatuses, blogCategories } = require('../config/constants');
const { objectId } = require('../validations/custom.validation');
const validateId = require('./shared/validateId');
const { removeUndefinedKeys, pick } = require('../utils/pick');
const { processFileUpload } = require('./file.service');
const { checkOptionalAuth } = require('./auth.service');
const { wordsArrayToFilterArray } = require('./university.service');

const deleteFileByUrls = async (urls, blogPostParam) => {
  const blogPost = blogPostParam;
  const deletedFileIds = [];
  await Async.eachOfSeries(urls || [], async (url) => {
    const file = await File.findOne({ url });
    if (file) {
      deletedFileIds.push(file._id);
      blogPost.media = blogPost.media.filter((id) => id.toString() !== file._id.toString());
      await file.remove();
    }
  });
  return deletedFileIds;
};

const processFile = async (req) => {
  const { coverImage, mediaFiles } = req.files;
  let { fileIds } = req.body;
  if (coverImage) {
    req.body.coverImage = await processFileUpload(coverImage[0], azureContainers.blogs);
  }
  if (mediaFiles) {
    if (typeof fileIds === 'string') {
      fileIds = fileIds.split(',');
    }
    if (mediaFiles.length !== fileIds?.length) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Corresponding fileIds are required');
    }

    const imgTagRegex = /<img alt="Blog Post Image\*\*[^>]+>/g;
    const imgTags = req.body.text.match(imgTagRegex);

    if (imgTags?.length !== mediaFiles?.length) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Corresponding file tags are required');
    }

    const media = [];
    await Async.eachOfSeries(mediaFiles, async (mediaFile, index) => {
      const { url: imageUrl, id: imageId } = await processFileUpload(mediaFile, azureContainers.blogs, true);
      const imgTag = imgTags.filter((tag) => tag.includes(`alt="${fileIds[index]}"`));
      if (imgTag.length > 1) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'File ids must be unique');
      }
      if (imgTag.length === 0) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Corresponding file tags are required');
      }
      req.body.text = req.body.text.replace(
        imgTag[0],
        imgTag[0]
          .replace(/src="[^"]+"/, `src="${imageUrl}"`)
          .replace(/\salt="Blog Post Image\*\*[^>]+\s/g, ' alt="Blog Post Image" '),
      );
      media.push(imageId);
    });
    if (media.length > 0) req.body.media = media;
  }
};

const createBlogPost = async (req) => {
  if (req.body.status === !blogStatuses.SCHEDULED && req.body.scheduledAt) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Scheduled date is only allowed for scheduled posts');
  }
  await processFile(req);
  req.body.author = req.user._id;
  if (typeof req.body.tags === 'string') {
    req.body.tags = req.body.tags.split(',');
  }
  if (req.body.status === 'published') {
    req.body.publishedAt = new Date();
  }
  let uniqueTitle = req.body.title
    .replaceAll(/[^a-zA-Z\s]+/g, '')
    .replaceAll(' ', '-')
    .toLowerCase();
  const existingPost = await BlogPost.findOne({ uniqueTitle });
  uniqueTitle = existingPost ? `${uniqueTitle}-${Date.now()}` : uniqueTitle;
  req.body.uniqueTitle = uniqueTitle;

  const blogPost = await BlogPost.create(req.body);
  return blogPost;
};

const getBlogPosts = async (req) => {
  const filter = pick(req.query, ['title', 'tags', 'user', 'category', 'title', 'uniqueTitle']);

  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  options.populate = [
    {
      path: 'coverImage',
      select: 'url',
    },
  ];
  options.select = 'category coverImage publishedAt status text title uniqueTitle createdAt updatedAt';
  if (req.query.searchText) {
    const searchText = stopword.removeStopwords(req.query.searchText.split(' '));

    if (searchText.length > 0) {
      const $or = [];
      ['title', 'subtitle', 'text'].forEach((field) => {
        const filterArray = wordsArrayToFilterArray(searchText, field);
        $or.push({ $and: filterArray });
      });
      filter.$or = filter.$or ? [...filter.$or, ...$or] : $or;
    }
  }

  await checkOptionalAuth(req);

  filter.status = req.user?.roles.includes('admin') ? req.query.status : 'published';
  removeUndefinedKeys(filter);

  if (filter.user) {
    const { error } = Joi.string().required().custom(objectId).validate(filter.user);
    if (error) delete filter.user;
  }
  const blogPosts = await BlogPost.paginate(filter, options);
  return blogPosts;
};

const getBlogPostByUniqueTitle = async (req) => {
  const { uniqueTitle } = req.params;
  await checkOptionalAuth(req);
  const userIsAdmin = req.user?.roles.includes('admin');

  const blogPost = await BlogPost.findOne({ uniqueTitle }).populate([
    {
      path: 'coverImage',
      select: 'url',
    },
  ]);
  if (!blogPost) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Blog not found');
  }
  if (blogPost.status !== blogStatuses.PUBLISHED && !userIsAdmin) {
    throw new ApiError(httpStatus.FORBIDDEN, "You're not allowed to view this post");
  }

  return blogPost;
};

const upvote = async (blogPostParam, undo) => {
  const blogPost = blogPostParam;
  if (undo === 'true') {
    blogPost.upvotes = blogPost.upvotes > 0 ? blogPost.upvotes - 1 : 0;
  } else {
    blogPost.upvotes += 1;
  }
  return blogPost;
};

const downvote = async (blogPostParam, undo) => {
  const blogPost = blogPostParam;
  if (undo === 'true') {
    blogPost.downvotes = blogPost.downvotes > 0 ? blogPost.downvotes - 1 : 0;
  } else {
    blogPost.downvotes += 1;
  }
  return blogPost;
};

const react = async (id, reaction, existingReaction) => {
  // Because unregistered users can react, existingReaction tells if the user has previously reacted as saved in their local storage
  validateId(id, 'Blog');

  let blogPost = await BlogPost.findById(id);
  if (!blogPost) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Blog not found');
  }

  if (!existingReaction) {
    // No previous upvote or downvote
    blogPost = reaction === 'upvote' ? await upvote(blogPost) : await downvote(blogPost);
  } else {
    if (reaction === 'upvote') {
      if (existingReaction === 'upvote') {
        blogPost = await upvote(blogPost, 'true');
      } else if (existingReaction === 'downvote') {
        blogPost = await downvote(blogPost, 'true');
        blogPost = await upvote(blogPost);
      }
    }
    if (reaction === 'downvote') {
      if (existingReaction === 'downvote') {
        blogPost = await downvote(blogPost, 'true');
      } else if (existingReaction === 'upvote') {
        blogPost = await upvote(blogPost, 'true');
        blogPost = await downvote(blogPost);
      }
    }
  }

  await blogPost.save();
  return blogPost;
};

const updateBlogPost = async (req) => {
  const { coverImage, file, body: updateBody } = req;
  const postId = req.params.id;

  validateId(postId, 'Blog');

  const blogPost = await BlogPost.findById(postId);

  await processFile(req);
  await deleteFileByUrls(updateBody.deletedUrls, blogPost);

  if (!blogPost) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Blog not found');
  }

  if (coverImage) {
    if (blogPost.coverImage) {
      await File.findByIdAndDelete(blogPost.coverImage);
    }
    updateBody.coverImage = await processFileUpload(coverImage[0], azureContainers.blogs);
  }
  if (updateBody.title) {
    const existingPost = await BlogPost.findOne({
      uniqueTitle: updateBody.title.replaceAll(' ', '-').toLowerCase(),
    });
    const uniqueTitle = existingPost ? `${updateBody.title}-${Date.now()}` : updateBody.title;
    updateBody.uniqueTitle = uniqueTitle.replaceAll(' ', '-').toLowerCase();
  }

  if (file) {
    // TODO
    // Handle deleting old and uploading new file
  }

  Object.assign(blogPost, updateBody);
  await blogPost.save();
  return blogPost;
};

const updateBlogPostStatus = async (req) => {
  const { status } = req.body;
  const postId = req.params.id;

  validateId(postId, 'Blog');

  const blogPost = await BlogPost.findById(postId);
  if (!blogPost) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Blog not found');
  }

  // Handle restore (reverse of delete)
  if (blogPost.status === blogStatuses.DELETED) {
    if (status) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Status is not allowed for restoring a deleted post');
    }

    blogPost.deletedAt = undefined;
    blogPost.status = blogPost.statusOnDelete || blogStatuses.DRAFT;
    blogPost.statusOnDelete = undefined;
  } else {
    if (!status) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Status is required');
    }
    blogPost.status = status;
    if (status === blogStatuses.PUBLISHED) {
      blogPost.publishedAt = new Date();
    }
  }

  await blogPost.save();
};

const deleteBlogPost = async (id) => {
  validateId(id, 'Blog');

  const post = await BlogPost.findById(id);
  if (!post) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Blog not found');
  }
  post.statusOnDelete = post.status;
  post.status = blogStatuses.DELETED;
  post.deletedAt = new Date();
  await post.save();
};

const getCategories = async () => {
  const categories = blogCategories;
  return categories;
};

const getStats = async () => {
  let result = await BlogPost.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
      },
    },
    {
      $project: {
        _id: 0, // Remove _id field
        status: '$_id', // Rename _id to status
        count: 1,
      },
    },
  ]);

  result = result.reduce((acc, entry) => ({ ...acc, [entry.status]: entry.count }), {});
  return result;
};

module.exports = {
  createBlogPost,
  getBlogPosts,
  getBlogPostByUniqueTitle,
  react,
  deleteBlogPost,
  getCategories,
  updateBlogPost,
  updateBlogPostStatus,
  getStats,
};
