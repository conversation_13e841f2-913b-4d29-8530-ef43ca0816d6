module.exports = {
  get: {
    tags: ['Countries'],
    summary: 'Get countries',
    description: 'Get list of countries',
    responses: {
      200: {
        description: 'Countries retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                data: {
                  type: 'object',
                  properties: {
                    result: {
                      type: 'array',
                      items: {
                        type: 'string',
                        example: 'United States',
                      },
                    },
                    count: {
                      type: 'integer',
                      description: 'Total number of countries',
                      example: 195,
                    },
                  },
                },
                message: {
                  type: 'string',
                  example: 'Countries retrieved successfully',
                },
                status: {
                  type: 'string',
                  example: 'SUCCESS',
                },
              },
            },
            example: {
              data: {
                result: ['United States', 'Canada', 'United Kingdom'],
                count: 3,
              },
              message: 'Countries retrieved successfully',
              status: 'SUCCESS',
            },
          },
        },
      },
    },
  },
};
