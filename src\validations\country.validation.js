const Joi = require('joi');

const getCities = {
  query: Joi.object().keys({
    state: Joi.alternatives().try(Joi.string(), Joi.array().items(Joi.string())),
    country: Joi.alternatives().try(Joi.string(), Joi.array().items(Joi.string())),
    searchText: Joi.string(),
    sortBy: Joi.string().valid('name:asc', 'name:desc').default('name:asc'),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer(),
  }),
};

const getStates = {
  query: Joi.object().keys({
    country: Joi.alternatives().try(Joi.string(), Joi.array().items(Joi.string())),
  }),
};

module.exports = {
  getCities,
  getStates,
};
