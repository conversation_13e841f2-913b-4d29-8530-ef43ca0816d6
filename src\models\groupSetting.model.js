const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const { groupSettingsEnums } = require('../config/constants');

const groupSettingSchema = new mongoose.Schema(
  {
    group: { type: mongoose.SchemaTypes.ObjectId, ref: 'Group', unique: true },
    users: [
      {
        user: { type: mongoose.SchemaTypes.ObjectId, ref: 'User' },
        settings: {
          posts: { type: String, default: groupSettingsEnums.ALL, enums: Object.values(groupSettingsEnums) },
          messages: { type: String, default: groupSettingsEnums.ALL, enums: Object.values(groupSettingsEnums) },
        },
      },
    ],
  },
  {
    timestamps: true,
  },
);

groupSettingSchema.index({ group: 1, createdAt: 1, updatedAt: 1 });

groupSettingSchema.plugin(toJSON);
groupSettingSchema.plugin(paginate);

const GroupSetting = mongoose.model('GroupSetting', groupSettingSchema);

module.exports = GroupSetting;
