const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const Business = require('../business.model');
const { businessEnums } = require('../../config/constants');

const migrationName = 'set existing business verification status';

const setExistsingBusinessVerificationStatus = async () => {
  const migrated = await GlobalVariable.findOne({ name: migrationName });
  if (migrated) {
    return;
  }

  const setOnboardedBusinessVerificationStatusPromise = Business.updateMany(
    { onboardingStage: businessEnums.onboardingStages.COMPLETED },
    { $set: { verificationStatus: businessEnums.businessVerificationStatuses.APPROVED } },
  );

  const setUnOnboardedBusinessVerificationStatusPromise = Business.updateMany(
    { onboardingStage: { $ne: businessEnums.onboardingStages.COMPLETED } },
    { $set: { verificationStatus: businessEnums.businessVerificationStatuses.AWAITING_APPROVAL } },
  );

  await Promise.all([setOnboardedBusinessVerificationStatusPromise, setUnOnboardedBusinessVerificationStatusPromise]);

  await GlobalVariable.create({ name: migrationName, value: 'true' });
  logger.info('businesses verification status');
};

module.exports = setExistsingBusinessVerificationStatus;
