/* eslint-disable global-require */
const logger = require('../../config/logger');
const { Message, Post, Scholarship, Setting, User } = require('../../models');
const { schoolsEnums, registrationStatuses } = require('../../config/constants');
const jobEnums = require('./jobEnums');

const defineJobs = async (agenda) => {
  agenda.define(jobEnums.orderJobs.MARK_ORDER_AS_COMPLETED, async (job) => {
    // mark orders which the fulfilledAt has been more than 7 days ago
    const { orderId } = job.attrs.data;
    const { orderService } = require('../../services'); // Import here to avoid circular dependencies
    await orderService.scheduleCompleteOrder(orderId);
  });

  agenda.define(jobEnums.orderJobs.MARK_REVISION_AS_COMPLETED, async (job) => {
    // pass revisions to the job to check whethr the number of revisions is still the same as when it was created
    const { revisionId, prevRevisionsCount } = job.attrs.data;
    const { serviceRevisionService } = require('../../services');
    await serviceRevisionService.scheduleRevisionCompletion(revisionId, prevRevisionsCount);
  });

  agenda.define(jobEnums.orderJobs.CREATE_MESSAGE_EMAIL, async (job) => {
    const { sender, recipient, details } = job.attrs.data;
    const message = await Message.findById(details.messageId);
    if (!message.isReadBy.includes(recipient._id.toString())) {
      const { emailService } = require('../../services'); // Import here to avoid circular dependencies
      await emailService.sendMessageNotificationEmail(sender, recipient, details);
    }
  });

  agenda.define(jobEnums.emailJobs.CREATE_FOLLOWER_EMAIL, async (job) => {
    const { sender, recipient } = job.attrs.data;
    const user = await User.findById(recipient._id);
    if (user.following.includes(sender._id)) {
      logger.info('Sending email follow notification');
      const { emailService } = require('../../services'); // Import here to avoid circular dependencies
      await emailService.sendFollowNotificationEmail(sender, recipient);
    }
  });

  agenda.define(jobEnums.emailJobs.CREATE_REPOST_EMAIL, async (job) => {
    const { sender, details, recipient } = job.attrs.data;
    const { resourceId } = details;
    const resource = await Post.findById(resourceId);
    if (resource.reposts.includes(sender._id)) {
      const { emailService } = require('../../services'); // Import here to avoid circular dependencies
      emailService.sendRepostNotificationEmail(sender, recipient, details);
    }
  });

  agenda.define(jobEnums.scholarshipJobs.MODIFY_SCHOLARSHIP_STATUS, { priority: jobEnums.priority.HIGH }, async () => {
    const now = new Date();
    const todayStart = new Date(now);
    todayStart.setHours(0, 0, 0, 0);

    const todayEnd = new Date(now);
    todayEnd.setHours(23, 59, 59, 999);

    // open scholarships where startDate is today
    await Scholarship.updateMany(
      {
        startDate: { $gte: todayStart, $lte: todayEnd },
        status: { $ne: schoolsEnums.scholarshipStatuses.CLOSED },
      },
      { status: schoolsEnums.scholarshipStatuses.OPEN },
    );

    // close scholarships past deadline
    await Scholarship.updateMany(
      {
        deadline: { $lt: todayStart },
        status: { $ne: schoolsEnums.scholarshipStatuses.CLOSED },
      },
      { status: schoolsEnums.scholarshipStatuses.CLOSED },
    );
  });

  agenda.define(
    jobEnums.scholarshipJobs.SEND_NEW_SCHOLARSHIP_NOTIFICATION,
    { priority: jobEnums.priority.HIGH },
    async () => {
      // send scholarships added between 12:00am yesterday and today at 12:00am
      const now = new Date();
      const yesterdayStart = new Date(now);
      yesterdayStart.setDate(yesterdayStart.getDate() - 1);
      yesterdayStart.setHours(0, 0, 0, 0);

      const todayEnd = new Date(now);
      todayEnd.setHours(23, 59, 59, 999);

      const scholarships = await Scholarship.find({
        createdAt: { $gte: yesterdayStart, $lte: todayEnd },
        status: schoolsEnums.scholarshipStatuses.OPEN,
      });

      if (scholarships.length === 0) {
        logger.info('No new scholarships to send');
        return;
      }

      const enabledUsers = await Setting.aggregate([
        { $match: { receiveScholarshipNewsletter: true } },
        {
          $lookup: {
            from: 'users',
            localField: 'user',
            foreignField: '_id',
            as: 'userDetails',
          },
        },
        { $unwind: '$userDetails' },
        { $match: { 'userDetails.registrationStatus': registrationStatuses.ONBOARDING_COMPLETE } },
        {
          $project: {
            _id: 0,
            email: '$userDetails.email',
            firstName: '$userDetails.firstName',
            lastName: '$userDetails.lastName',
          },
        },
      ]);

      if (enabledUsers.length === 0) {
        logger.info('No users with enabled notifications to receive scholarship newsletter');
        return;
      }

      // console.log(enabledUsers);

      const { emailService } = require('../../services'); // Import here to avoid circular dependencies
      try {
        const emailPromises = enabledUsers.map((user) => emailService.sendNewScholarshipEmail(user, { scholarships }));
        await Promise.all(emailPromises);
        logger.info('Scholarship newsletter sent to users');
      } catch (err) {
        logger.error(`Error sending scholarship newsletter: ${err.message}`);
      }
    },
  );

  // new job
  // agenda.define(
  //   jobEnums.scholarshipJobs.SEND_SCHOLARSHIP_EXPIRATION_NOTIFICATION,
  //   { priority: jobEnums.priority.HIGH },
  //   async () => {
  //     // get scholarships [ids] *with a deadline* that are to expire in 3 days
  //     const now = new Date();
  //     const threeDaysFromNow = new Date(now);
  //     threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);
  //     threeDaysFromNow.setHours(0, 0, 0, 0);

  //     const scholarships = await Scholarship.find({
  //       durationType: schoolsEnums.durationTypes.DURATION_SPECIFIED,
  //       deadline: { $gte: now, $lte: threeDaysFromNow },
  //       status: schoolsEnums.scholarshipStatuses.OPEN,
  //     });

  //     if (scholarships.length === 0) {
  //       logger.info('No scholarships to send expiration notification');
  //       return;
  //     }

  //     // check which user have the scholarship in their scholarship-bookmarks & want to receive notifications
  //     const enabledUsers = await Setting.aggregate([
  //       { $match: { receiveScholarshipNewsletter: true } },
  //       {
  //         $lookup: {
  //           from: 'users',
  //           localField: 'user',
  //           foreignField: '_id',
  //           as: 'userDetails',
  //         },
  //       },
  //       { $unwind: '$userDetails' },
  //       {
  //         $match: {
  //           'userDetails.registrationStatus': registrationStatuses.ONBOARDING_COMPLETE,
  //           'userDetails.scholarships': { $in: scholarships.map((s) => s._id) },
  //         },
  //       },
  //       {
  //         $project: {
  //           _id: 0,
  //           email: '$userDetails.email',
  //           firstName: '$userDetails.firstName',
  //           lastName: '$userDetails.lastName',
  //           bookmarkedScholarships: {
  //             $filter: {
  //               input: '$userDetails.scholarships',
  //               as: 'sch',
  //               cond: { $in: ['$$sch', scholarships.map((s) => s._id)] },
  //             },
  //           },
  //         },
  //       },
  //     ]);

  //     if (enabledUsers.length === 0) {
  //       logger.info('No users with enabled notifications to receive scholarship expiration notification');
  //       return;
  //     }

  //     // send an email to these users
  //     const { emailService } = require('../../services'); // Import here to avoid circular dependencies
  //     try {
  //       const emailPromises = enabledUsers
  //         .map((user) => {
  //           const userScholarships = scholarships.filter((s) => user.bookmarkedScholarships.some((id) => id.equals(s._id)));
  //           if (userScholarships.length > 0) {
  //             return emailService.sendScholarshipExpirationEmail(user, { scholarships: userScholarships });
  //           }
  //           return null;
  //         })
  //         .filter(Boolean); // remove nulls

  //       await Promise.all(emailPromises);
  //       logger.info('Scholarship expiration notification sent to users');
  //     } catch (err) {
  //       logger.error(`Error sending scholarship expiration notification: ${err.message}`);
  //     }
  //   },
  // );

  // start agenda and call static jobs (i.e. jobs that have constant names and not dynamic)
  await agenda.start();
  logger.info('Agenda has started!');

  await agenda.every('0 */6 * * *', jobEnums.scholarshipJobs.MODIFY_SCHOLARSHIP_STATUS); // Schedule to run every 6 hours
  await agenda.every('0 7 * * *', jobEnums.scholarshipJobs.SEND_NEW_SCHOLARSHIP_NOTIFICATION); // Schedule to run every day at 7am
  // await agenda.every('0 7 * * *', jobEnums.scholarshipJobs.SEND_SCHOLARSHIP_EXPIRATION_NOTIFICATION); // Schedule to run every day at 7am

  // await agenda.every('*/1 * * * *', jobEnums.scholarshipJobs.SEND_NEW_SCHOLARSHIP_NOTIFICATION); // Schedule to run every 5 minutes
};

module.exports = defineJobs;
