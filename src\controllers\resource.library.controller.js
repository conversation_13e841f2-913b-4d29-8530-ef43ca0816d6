const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { resourceLibraryService } = require('../services');
const { pick, removeUndefinedKeys } = require('../utils/pick');
const { resourceLibraryCategories, schoolsEnums } = require('../config/constants');
const { ResourceLibrary } = require('../models');

const getResourceLibraryEnums = catchAsync(async (req, res) => {
  const data = { categories: resourceLibraryCategories, programTypes: Object.values(schoolsEnums.programTypes) };

  const programs = (
    await ResourceLibrary.aggregate([{ $unwind: '$programs' }, { $group: { _id: '$programs' } }, { $sort: { _id: 1 } }])
  ).map((program) => program._id);

  if (!programs.find((program) => program.toLowerCase() === 'all programs')) {
    programs.unshift('All Programs');
  }

  return res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', message: 'Resource Library enums fetched successfully', data: { ...data, programs } });
});

const addResource = catchAsync(async (req, res) => {
  await resourceLibraryService.addResource(req.body, req.files);
  return res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Resource added successfully' });
});

const getResource = catchAsync(async (req, res) => {
  const isAdmin = !!req.user && req.user.roles.includes('admin');
  const resource = await resourceLibraryService.getResource(req.params.id, isAdmin);
  return res.status(httpStatus.OK).json({ status: 'SUCCESS', data: resource, message: 'Resource fetched successfully' });
});

const getResources = catchAsync(async (req, res) => {
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const filter = pick(req.query, ['categories', 'searchText', 'programType', 'program']);
  removeUndefinedKeys(filter);

  const isAdmin = !!req.user && req.user.roles.includes('admin');

  const resources = await resourceLibraryService.getResources(filter, options, isAdmin);
  return res.status(httpStatus.OK).json({ status: 'SUCCESS', data: resources, message: 'Resources fetched successfully' });
});

const updateResource = catchAsync(async (req, res) => {
  await resourceLibraryService.updateResource(req.params.id, req.body, req.files);
  return res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Resource updated successfully' });
});

const deleteResource = catchAsync(async (req, res) => {
  await resourceLibraryService.deleteResource(req.params.id);
  return res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Resource deleted successfully' });
});

const saveDownloads = catchAsync(async (req, res) => {
  await resourceLibraryService.saveDownloads(req.params.id, req.user, req.query.action);
  return res.status(httpStatus.OK).json({ status: 'SUCCESS', message: `Saved to ${req.query.action}s successfully` });
});

const getResourcesAnalytics = catchAsync(async (req, res) => {
  const options = pick(req.query, ['sortBy', 'limit', 'page']);

  const analytics = await resourceLibraryService.getResourcesAnalytics(options);
  return res.status(httpStatus.OK).json({ status: 'SUCCESS', data: analytics, message: 'Analytics fetched successfully' });
});

const getResourceCategoriesData = catchAsync(async (req, res) => {
  const categoriesData = await resourceLibraryService.getResourceCategoriesData();
  return res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data: categoriesData, message: 'Categories data fetched successfully' });
});

module.exports = {
  getResourceLibraryEnums,
  addResource,
  getResource,
  getResources,
  updateResource,
  deleteResource,
  saveDownloads,
  getResourcesAnalytics,
  getResourceCategoriesData,
};
