const express = require('express');

const router = express.Router();
const userValidator = require('../../validations/user.validation');
const validate = require('../../middlewares/validate');
const { uploadProfilePhotos, uploadNamedFiles } = require('../../config/multer.config');
const { userController } = require('../../controllers');
const { auth, canManageResource } = require('../../middlewares/auth');

const uploadPhotos = uploadNamedFiles({ photo: 1, banner: 1 });

router.get('/deleted-users', auth('manageDeletedUsers'), userController.getDeletedUsers);

router.use(auth());

// General User-related endpoints
// router.get('/', userController.getAllUsers);

router.get('/profile/:id?', validate(userValidator.getUser), userController.getUserProfile);

router.get('/meta-data', userController.getUserMetaData);

router.get('/follow-suggestions', validate(userValidator.getFollowSuggestions), userController.getFollowSuggestions);
router.get('/:id', validate(userValidator.getUser), canManageResource('User'), userController.getUserById);

router.patch(
  '/:id/change-password',
  canManageResource('User'),
  validate(userValidator.updateUserPassword),
  userController.changeUserPassword,
);

router.patch('/onboard', uploadPhotos, validate(userValidator.onboardStudent), userController.onboardStudent);

router.post('/:id', canManageResource('User'), validate(userValidator.deleteUser), userController.deleteUser);

// Profile-related endpoints
router.patch(
  '/:id',
  canManageResource('User'),
  uploadProfilePhotos,
  validate(userValidator.updateUser),
  userController.updateUser,
);

// Follow/Unfollow endpoints
router.get('/:id/follow', validate(userValidator.followsSchema), userController.getFollowsInfo);

router.post('/:id/follow', validate(userValidator.followUnfollowSchema), userController.followUser);

router.get('/:id/follows-count', validate(userValidator.deleteUser), userController.countFollowership);

// mutuals
router.get('/follows/mutuals', userController.getMutuals);
router.patch('/block/:id', validate(userValidator.deleteUser), userController.blockUser);

module.exports = router;
