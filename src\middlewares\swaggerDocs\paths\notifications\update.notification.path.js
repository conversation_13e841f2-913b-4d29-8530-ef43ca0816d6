module.exports = {
  patch: {
    summary: 'Update notification status by ID',
    tags: ['Notifications'],
    description: 'Update notification status by ID',
    parameters: [
      {
        name: 'id',
        in: 'path',
        description: 'ID of the notification. This takes either an ObjectID, "all-read" or "all-unread"',
        required: true,
        schema: {
          type: 'ObjectId',
        },
      },
    ],
    requestBody: {
      description: 'Notification update data',
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              status: { type: 'boolean', example: true },
            },
            required: ['status'],
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Notification updated successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string', example: 'SUCCESS' },
                message: { type: 'string', example: 'All notifications marked as unread' },
              },
            },
          },
        },
      },
    },
  },
};
