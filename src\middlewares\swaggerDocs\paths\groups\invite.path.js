module.exports = {
  post: {
    summary: 'Invite users to a group',
    tags: ['Groups'],
    parameters: [
      {
        in: 'path',
        name: 'id',
        schema: {
          type: 'string',
          example: '5f9d1e9a6f3f4b001f6d4c0b',
        },
        description: 'ID of the group to invite users to',
      },
    ],
    requestBody: {
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              mutuals: {
                type: 'array',
                items: {
                  type: 'string',
                  example: '5f9d1e9a6f3f4b001f6d4c0b',
                },
              },
              inviteUrl: {
                type: 'string',
                example: 'https://example.com/invite',
              },
            },
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Operation successful',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Invite sent successfully',
            },
          },
        },
      },
    },
  },
};
