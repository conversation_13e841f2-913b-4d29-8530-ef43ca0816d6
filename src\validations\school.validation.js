const Joi = require('joi');
const { schoolsEnums, currencies } = require('../config/constants');
const { fileSchema } = require('./file.validation');

const schoolProgramSchemaObject = {
  name: Joi.string().required(),
  description: Joi.string().required(),
  programType: Joi.string().valid(...Object.values(schoolsEnums.programTypes)),
  tuition: Joi.string().required(),
  applicationFee: Joi.number().min(0).default(0),
  durationMonths: Joi.number().min(1).required(),
  applicationPeriod: Joi.array()
    .items(
      Joi.object().keys({
        season: Joi.string()
          .valid(...Object.values(schoolsEnums.applicationSeasons))
          .required(),
        duration: Joi.array().items(Joi.date()).max(2).required(),
        _id: Joi.string(),
      }),
    )
    .unique((a, b) => a.season === b.season)
    .min(1),
  applicationRequirements: Joi.string().required(),
  url: Joi.string(),
  applicationUrl: Joi.string().required(),
  funding: Joi.array().items(
    Joi.object().keys({
      category: Joi.string()
        .valid(...Object.values(schoolsEnums.fundingCategories))
        .required(),
      fundingType: Joi.string()
        .valid(...Object.values(schoolsEnums.fundingTypes))
        .required(),
      description: Joi.string().required(),
      _id: Joi.string(),
    }),
  ),
};

const addSchoolProgram = Joi.object().keys(schoolProgramSchemaObject);

const addSchoolPrograms = Joi.object().keys({
  params: Joi.object().keys({
    id: Joi.string(),
  }),
  body: addSchoolProgram,
});

const schoolSchemaBodyObject = {
  name: Joi.string().required(),
  abbreviation: Joi.string(),
  about: Joi.string().required(),
  country: Joi.string().required(),
  state: Joi.string().required(),
  city: Joi.string().required(),
  website: Joi.string().required(),
  currency: Joi.string()
    .valid(...Object.values(currencies))
    .default(currencies.USD),
  minimumTuition: Joi.number().min(0).required(),
  maximumTuition: Joi.number().min(0).required(),
  programs: Joi.string(),
};

const createSchool = {
  body: Joi.object().keys(schoolSchemaBodyObject),
  files: Joi.object().keys({
    logo: Joi.array().items(fileSchema(1)).max(1).required(),
    banner: Joi.array().items(fileSchema(1)).max(1).required(),
  }),
};

const getSchools = {
  query: Joi.object().keys({
    name: Joi.string(),
    country: Joi.alternatives().try(Joi.array().items(Joi.string()), Joi.string()),
    state: Joi.alternatives().try(Joi.array().items(Joi.string()), Joi.string()),
    city: Joi.alternatives().try(Joi.array().items(Joi.string()), Joi.string()),
    programType: Joi.string().valid(...Object.values(schoolsEnums.programTypes)),
    searchText: Joi.string(),
    brief: Joi.string().valid('true', 'false').default('true'),
    sortBy: Joi.string().valid('name:asc', 'name:desc').default('name:asc'),
    limit: Joi.number().integer(),
    page: Joi.number().integer(),
  }),
};

const updateSchool = {
  params: Joi.object().keys({
    id: Joi.string(),
  }),
  body: Joi.object().keys({
    name: Joi.string(),
    abbreviation: Joi.string(),
    about: Joi.string(),
    country: Joi.string(),
    state: Joi.string(),
    city: Joi.string(),
    website: Joi.string(),
    currency: Joi.string().valid(...Object.values(currencies)),
    minimumTuition: Joi.number().min(0),
    maximumTuition: Joi.number().min(0),
    programs: Joi.string(),
  }),
  files: Joi.object().keys({
    logo: Joi.array().items(fileSchema(1)).max(1),
    banner: Joi.array().items(fileSchema(1)).max(1),
  }),
};

const updateSchoolProgram = {
  params: Joi.object().keys({
    programId: Joi.string(),
  }),
  body: Joi.object().keys({
    name: Joi.string(),
    description: Joi.string(),
    programType: Joi.string().valid(...Object.values(schoolsEnums.programTypes)),
    tuition: Joi.string(),
    applicationFee: Joi.number().min(0).default(0),
    durationMonths: Joi.number().min(1),
    applicationPeriod: Joi.array()
      .items(
        Joi.object().keys({
          season: Joi.string().valid(...Object.values(schoolsEnums.applicationSeasons)),
          duration: Joi.array().items(Joi.date()).max(2),
        }),
      )
      .unique((a, b) => a.season === b.season),
    applicationRequirements: Joi.string(),
    url: Joi.string(),
    applicationUrl: Joi.string(),
    funding: Joi.array().items(
      Joi.object().keys({
        category: Joi.string().valid(...Object.values(schoolsEnums.fundingCategories)),
        fundingType: Joi.string().valid(...Object.values(schoolsEnums.fundingTypes)),
        description: Joi.string(),
      }),
    ),
  }),
};

module.exports = {
  createSchool,
  getSchools,
  updateSchool,
  addSchoolProgram,
  addSchoolPrograms,
  schoolProgramSchemaObject,
  schoolSchemaBodyObject,
  updateSchoolProgram,
};
