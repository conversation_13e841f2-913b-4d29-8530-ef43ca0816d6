module.exports = {
  get: {
    tags: ['Countries'],
    summary: 'Get cities',
    description: 'Get a list of cities',
    parameters: [
      {
        in: 'query',
        name: 'state',
        description: 'state of a country',
        schema: { type: 'string', example: 'Texas' },
      },
      {
        in: 'query',
        name: 'country',
        description: 'Country to fetch state from',
        schema: { type: 'string', example: 'United States' },
      },
      {
        in: 'query',
        name: 'searchText',
        description: '',
        schema: { type: 'string', example: '' },
      },
      {
        in: 'query',
        name: 'sortBy',
        description: 'sort in ascending or descending order',
        schema: { type: 'string', example: 'name:desc' },
      },
      {
        in: 'query',
        name: 'limit',
        description: 'Number of records to fetch',
        schema: { type: 'integer', example: '10' },
      },
      {
        in: 'query',
        name: 'page',
        description: 'Current page',
        schema: { type: 'integer', example: '2' },
      },
    ],
    responses: {
      200: {
        description: 'Cities retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                data: {
                  type: 'object',
                  properties: {
                    docs: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          name: { type: 'string', example: 'Los Angeles' },
                          _id: { type: 'string', example: '61e56a452bda0400236e7a8b' },
                        },
                      },
                    },
                    page: { type: 'integer', example: 1 },
                    limit: { type: 'integer', example: 10 },
                    totalPages: { type: 'integer', example: 1 },
                    totalResults: { type: 'integer', example: 1 },
                  },
                },
                message: { type: 'string', example: 'Cities retrieved successfully' },
                status: { type: 'string', example: 'SUCCESS' },
              },
            },
          },
        },
      },
    },
  },
};
