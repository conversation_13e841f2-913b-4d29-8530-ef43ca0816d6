const DataUri = require('datauri/parser');
const multer = require('multer');
const path = require('path');

const dataUri = (file) => {
  const dUri = new DataUri();
  if (!file) {
    throw new Error('File is required');
  }
  if (!file.originalname) {
    throw new Error('File must have an originalname property');
  }
  return dUri.format(`.${path.extname(file.originalname).toString()}`, file.buffer);
};

const multerOptions = { storage: multer.memoryStorage() };

const uploadUnnamedFiles = (maxLength = 12) => multer(multerOptions).array('files', maxLength);

/** Sample fieldsObject
 {
  supportingDocuments: 12,
  deliverables: 12,
}
 */
const uploadNamedFiles = (fieldsObject) =>
  multer(multerOptions).fields([...Object.keys(fieldsObject).map((name) => ({ name, maxCount: fieldsObject[name] }))]);

const uploadMultiple = multer(multerOptions).array('files', 12);

const uploadSingle = multer(multerOptions).single('file');

const uploadResourceLibraryFiles = multer(multerOptions).fields([
  { name: 'file', maxCount: 1 },
  { name: 'thumbnails', maxCount: 10 },
]);

const uploadGroupPhotos = multer(multerOptions).fields([
  { name: 'profilePhoto', maxCount: 1 },
  { name: 'coverPhoto', maxCount: 1 },
]);

const uploadBlogFiles = multer(multerOptions).fields([
  { name: 'coverImage', maxCount: 1 },
  { name: 'mediaFiles', maxCount: 12 },
]);

const uploadProfilePhotos = multer(multerOptions).fields([
  { name: 'photo', maxCount: 1 },
  { name: 'banner', maxCount: 1 },
]);

const uploadSchoolPhotos = multer(multerOptions).fields([
  { name: 'logo', maxCount: 1 },
  { name: 'banner', maxCount: 1 },
]);

const uploadBusinessPhotos = multer(multerOptions).fields([
  { name: 'profilePhoto', maxCount: 1 },
  { name: 'coverPhoto', maxCount: 1 },
]);

const uploadJobApplicationFiles = multer(multerOptions).fields([
  { name: 'resume', maxCount: 1 },
  { name: 'coverLetter', maxCount: 1 },
]);

const uploadIssueReportFiles = multer(multerOptions).fields([{ name: 'attachment', maxCount: 12 }]);

module.exports = {
  uploadMultiple,
  uploadSingle,
  uploadGroupPhotos,
  dataUri,
  uploadProfilePhotos,
  uploadBlogFiles,
  uploadSchoolPhotos,
  uploadJobApplicationFiles,
  uploadIssueReportFiles,
  uploadBusinessPhotos,
  uploadResourceLibraryFiles,

  uploadUnnamedFiles,
  uploadNamedFiles,
};
