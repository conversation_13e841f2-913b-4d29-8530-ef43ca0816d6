const httpStatus = require('http-status');
const Async = require('async');
const { default: mongoose } = require('mongoose');
const stopword = require('stopword');
const logger = require('../config/logger');
const ApiError = require('../utils/ApiError');
const {
  Service,
  Review,
  File,
  ServiceOffer,
  Order,
  User,
  Business,
  Referral,
  ServiceRequirementSpecify,
} = require('../models');
const { wordsArrayToFilterArray } = require('./university.service');
const emailService = require('./email.service');
const fileService = require('./file.service');
const authService = require('./auth.service');
const { basicUserPopulate } = require('./user.service');
// const { orderPopulatePaths } = require('./order.service');
const { businessEnums, azureContainers } = require('../config/constants');
const validateId = require('./shared/validateId');
const { serviceValidation } = require('../validations');
const { validateObjectData } = require('../utils/helperMethods');

// service
const createService = async (serviceData) => {
  const newService = await Service.create(serviceData);
  return newService;
};

const servicePopulate = (requestingUser, orders) => {
  const populateParam = [
    { path: 'offers' },
    { path: 'media', select: '_id url filename' },
    {
      path: 'provider',
      select: 'username displayName profilePhoto _id online',
      populate: { path: 'profilePhoto', select: 'url _id' },
    },
    { path: 'requirements' },
    {
      path: 'reviews',
      populate: {
        path: 'order',
        populate: { path: 'client', ...basicUserPopulate },
        select: 'client', // Don't send the whole order
      },
    },
    {
      path: 'orders',
      match: { _id: { $in: orders } }, // Populate orders in the array where requesting user is client or provider contact person
      select: '_id dueAt orderNumber status',
    },
  ];

  if (requestingUser) {
    // if requestingUser is null (for public route), skip this block
    populateParam.push({ path: 'bookmarks', match: { _id: requestingUser?._id }, select: '_id' });
  }

  return populateParam;
};

const getService = async (serviceId, referralCode, requestingUser) => {
  validateId(serviceId, 'Service');
  const service = await Service.findById(serviceId).populate(servicePopulate(requestingUser));
  if (!service) {
    logger.error('Service is invalid');
    throw new ApiError(httpStatus.NOT_FOUND, 'Service is invalid');
  }

  // ensure that the service is verified, only business owner and admin can fetch unverified service
  const { verificationStatus, provider } = service;
  const { business, roles } = requestingUser;

  const isBusinessOwner = business?.toString() === provider?._id.toString();
  const isVerified = verificationStatus === businessEnums.businessVerificationStatuses.APPROVED;
  const isAdmin = roles?.some((role) => ['admin', 'superAdmin'].includes(role));

  // Allow only business owners or admins to fetch unverified services
  if (!isVerified && !(isBusinessOwner || isAdmin)) {
    throw new ApiError(httpStatus.FORBIDDEN, 'Service is under review and cannot be viewed at this time');
  }

  if (referralCode) {
    if (requestingUser) {
      const referral = await Referral.findOne({
        referredUser: requestingUser._id,
        service: service._id,
        referralCode,
        order: { $exists: false },
      });

      if (!referral) {
        await authService.generateReferralRecord(requestingUser, service.provider._id, service._id);
      }
    }
  }

  return service;
};

const getServices = async (user, filterParam, options, offerFilterParam) => {
  let filter = filterParam;
  const offerFilter = offerFilterParam || {};
  filter.$and = [];
  filter.$or = [];

  ['category', 'planType'].forEach((key) => {
    if (filter[key]) {
      filter[key] = Array.isArray(filter[key]) ? filter[key] : [filter[key]];
    }
    if (offerFilter[key]) {
      offerFilter[key] = Array.isArray(offerFilter[key]) ? offerFilter[key] : [offerFilter[key]];
    }
  });

  if (filter.provider) {
    if (!validateId(filter.provider, 'Business', false)) {
      const provider = await Business.findOne({ username: filter.provider });
      if (provider) filter.provider = provider._id;
      else delete filter.provider;
    }
  }

  if (filter.searchText) {
    const filterObject = [];
    const searchText = stopword.removeStopwords(filter.searchText.split(' '));
    ['name', 'tags', 'category'].forEach((key) => {
      filterObject.push({ $and: wordsArrayToFilterArray(searchText, key) });
    });

    delete filter.searchText;
    filter.$or.push(...filterObject);
  }
  const reviews = await Review.find({ rating: { $gte: filter.minimumRating, $lte: filter.maximumRating } });
  const reviewsOr = [];
  reviewsOr.push({ reviews: { $in: reviews.map((review) => review._id) } });
  if (filter.maximumRating === 5) {
    reviewsOr.push({ reviews: { $size: 0 } }); // Default rating is 5. If a service has no reviews, it's assumed to be 5
  }
  filter.$and.push({ $or: reviewsOr });
  delete filter.minimumRating;
  delete filter.maximumRating;

  if (filter.category) {
    filter.$and.push({ category: { $regex: filter.category.join('|'), $options: 'i' } });
    delete filter.category;
  }

  if (!offerFilter.planType) {
    offerFilter.planType = Object.values(businessEnums.servicePlans);
  }

  const queryMapForRanges = {};

  let serviceOffers;
  if (offerFilter.maximumPrice) {
    // If maximumPrice is provided, minimumPrice, maximumPrice, minimumCompletionDays and maximumCompletionDays must also be provided (validated in Joi)
    queryMapForRanges[businessEnums.requiredOffers.PRICE[0]] = [offerFilter.minimumPrice, offerFilter.maximumPrice];
    queryMapForRanges[businessEnums.requiredOffers.COMPLETION_DAYS[0]] = [
      offerFilter.minimumCompletionDays,
      offerFilter.maximumCompletionDays,
    ];

    serviceOffers = await ServiceOffer.aggregate([
      {
        $group: {
          _id: '$service', // Group by the `service` field
          offers: {
            $push: {
              name: '$name',
              value: '$value',
            },
          },
        },
      },
      {
        $project: {
          service: '$_id', // Bring `service` back from the _id
          offers: {
            $map: {
              input: '$offers',
              as: 'offer',
              in: {
                name: '$$offer.name',
                value: {
                  $map: {
                    input: '$$offer.value',
                    as: 'v',
                    in: {
                      plan: '$$v.plan',
                      // Convert offer to integer, handle errors for non-numeric values
                      offer: {
                        $convert: {
                          input: '$$v.offer',
                          to: 'int', // convert to integer
                          onError: null, // If conversion fails (e.g., "true", "false"), set to null
                          onNull: null, // If value is null, set to null
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },

      /* *
      Sample data after group stage (the structure when querying at the match stage)
      [
        {
          _id: new ObjectId("66e8b1d3525371431fe6bcd8"),
          service: new ObjectId("66e8b1d3525371431fe6bcd8"),
          offers: [
            { name: 'Completion Days', value: [ [Object], [Object] ] },
            { name: 'Price', value: [ [Object], [Object] ] },
            { name: 'CV exam', value: [ [Object], [Object] ] },
            { name: 'Maximum Revisions', value: [ [Object], [Object] ] }
          ]
        },
      ]
      * */
      {
        $match: {
          offers: {
            $all: [
              ...Object.entries(queryMapForRanges).map(([key, value]) => ({
                $elemMatch: {
                  name: key,
                  value: {
                    $elemMatch: {
                      offer: {
                        $gte: Number(value[0]),
                        $lte: Number(value[1]),
                      },
                    },
                  },
                },
              })),
              {
                $elemMatch: {
                  value: { $elemMatch: { plan: { $regex: offerFilter.planType.join('|'), $options: 'i' } } },
                },
              },
            ],
          },
        },
      },
    ]);
  }

  if (serviceOffers) {
    filter = { ...filter, _id: { $in: serviceOffers.map((offer) => offer.service) } };
  }

  if (filter.$and.length === 0) delete filter.$and;
  if (filter.$or.length === 0) delete filter.$or;

  if (filter._id?.$in && filter._id.$in.length === 0) {
    delete filter._id;
  }

  if (filter.status === 'all') {
    filter.status = { $in: [...Object.values(businessEnums.serviceStatuses)] };
  }

  let isBusinessOwner = false;
  if (filter.provider) {
    if (!validateId(filter.provider, 'Business', false)) {
      const provider = await Business.findById(filter.provider);
      if (provider) {
        filter.provider = provider._id;
        isBusinessOwner = user?.business?.toString() === provider._id.toString();
      } else {
        delete filter.provider;
      }
    } else {
      isBusinessOwner = user?.business?.toString() === filter.provider;
    }
  }

  // Restrict verification status based on ownership
  if (isBusinessOwner) {
    filter.verificationStatus = { $in: Object.values(businessEnums.businessVerificationStatuses) };
  } else {
    filter.verificationStatus = businessEnums.businessVerificationStatuses.APPROVED;
  }

  filter.createdAt = { $gte: new Date(filter.createdAtStartDate), $lte: new Date(filter.createdAtEndDate) };
  if (process.env.STRIPE_LIVE_TEST === 'true') {
    filter.createdAt = { ...filter.createdAt, $gt: new Date('2024-11-19T16:00:00.000+00:00') };
  }
  delete filter.createdAtStartDate;
  delete filter.createdAtEndDate;

  // const orders = (await Order.find({ provider: user?.business })).map((order) => order._id);
  const populateFields = [
    ...servicePopulate(user).filter((populate) => populate.path !== 'reviews'),
    { path: 'reviews', select: 'rating' },
  ];

  const services = await Service.paginate(filter, {
    ...options,
    populate: populateFields,
    select: 'name provider media offers category bookmarks reviews status createdAt updatedAt -orders -requirements',
  });
  return services;
};

const validateRequiredOffers = (data, forUpdate = false) => {
  // If it's for update, data is assumed to have been validated
  const value = !forUpdate ? validateObjectData(data, serviceValidation.validateOffers) : data;

  const requiredOffers = Object.values(businessEnums.requiredOffers);

  const missingOffers = requiredOffers.filter(
    (offer) =>
      !value.some((deliverable) => {
        // Run function to confirm that value can be converted to the required type
        const offerFound = deliverable.name === offer[0];
        if (offerFound) {
          deliverable.value
            .map((v) => v.offer)
            .forEach((offerValue, idx) => {
              if (!offer[1](offerValue)) {
                throw new ApiError(
                  httpStatus.BAD_REQUEST,
                  `Invalid value for ${deliverable.value[idx].plan} ${deliverable.name}`,
                );
              }
            });
        }
        return offerFound;
      }),
  );

  if (!forUpdate && missingOffers && missingOffers.length > 0) {
    throw new ApiError(httpStatus.BAD_REQUEST, `Validation error: ${missingOffers.map((v) => v[0]).join(', ')} required`);
  }
  return value;
};

// updateService is always expected to be called by the authorized user. There is no check against the contactPerson
const updateService = async (businessId, serviceId, updateDataParam) => {
  const updateData = updateDataParam;
  validateId(serviceId, 'Service');
  let service = await Service.findOne({ _id: serviceId, provider: businessId });
  if (!service) {
    logger.error('Service is invalid');
    throw new ApiError(httpStatus.NOT_FOUND, 'Service is invalid');
  }

  if (service.status === businessEnums.serviceStatuses.IN_REVIEW) {
    logger.error('Service is under review and cannot be updated');
    throw new ApiError(httpStatus.FORBIDDEN, 'Service is under review and cannot be updated');
  }

  updateData.$addToSet = {};
  updateData.$pull = {};

  const session = await mongoose.startSession();
  session.startTransaction();

  /**
   * To reverse changes when transation is aborted, session option needs to be passed to queries that are part of the transaction
   * Otherwise, the queries will be saved to the database immediately
   * However, using the same session for multiple queries is not allowed by CosmosDB
   *
   * So, at the moment, the session above does not have any effect as it's not passed to any query
   *
   * TODO: Find a way to centralize session handling where a session is created for each model and passed to the queries
   * At the end of the transaction each session is committed or aborted and also ended
   */

  try {
    if (updateData.newOffers) {
      const validatedOffers = validateRequiredOffers(updateData.newOffers, true);

      const newOfferNames = validatedOffers.map((offer) => offer.name);
      const existingOffers = await ServiceOffer.find({
        service: serviceId,
        name: { $regex: newOfferNames.join('|'), $options: 'i' },
      });
      if (existingOffers.length > 0) {
        const existingOfferNames = existingOffers.map((offer) => offer.name);
        throw new ApiError(httpStatus.BAD_REQUEST, `Validation error: ${existingOfferNames.join(', ')} already exists`);
      }

      const newOffers = await Promise.all(
        validatedOffers.map(async (offer) => {
          const newOffer = await ServiceOffer.create({ ...offer, service: serviceId });
          return newOffer._id;
        }),
      );
      delete updateData.newOffers;
      updateData.$addToSet.offers = newOffers;
    }

    if (updateData.deletedOffers) {
      updateData.deletedOffers.map((offerId) => validateId(offerId, 'ServiceOffer'));

      const offerNames = await ServiceOffer.find({ _id: { $in: updateData.deletedOffers }, provider: businessId });
      if (offerNames.length !== updateData.deletedOffers.length) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'One or more offers are invalid');
      }

      // check that required offers are not deleted
      const requiredOffers = Object.values(businessEnums.requiredOffers).map((v) => v[0]);
      if (offerNames.some((offer) => requiredOffers.includes(offer.name))) {
        throw new ApiError(httpStatus.BAD_REQUEST, `Validation error: ${requiredOffers.join(', ')} cannot be deleted`);
      }

      // Delete offers and remove them from service.offers array
      await ServiceOffer.deleteMany({ _id: { $in: updateData.deletedOffers } });
      service = await Service.findByIdAndUpdate(
        serviceId,
        { $pull: { offers: { $in: updateData.deletedOffers } } },
        { new: true },
      );
      // await Service.findByIdAndUpdate(serviceId, { $pull: { offers: { $in: updateData.deletedOffers } } });
      delete updateData.deletedOffers;
      updateData.$pull.offers = { $in: updateData.deletedOffers };
    }
    if (updateData.updatedOffers) {
      const existingOffers = await ServiceOffer.find({
        _id: { $in: updateData.updatedOffers.map((offer) => offer.id) },
        service: serviceId,
      });
      if (existingOffers.length !== updateData.updatedOffers.length) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'One or more offers are invalid');
      }
      const validatedOffers = validateRequiredOffers(
        updateData.updatedOffers.map((offer) => ({ value: offer.value })),
        true,
      );

      await Promise.all(
        validatedOffers.map(async (offer) => {
          const updatedOffer = await ServiceOffer.findByIdAndUpdate(
            offer.id,
            { $set: { value: offer.value } },
            { new: true },
          );
          return updatedOffer._id;
        }),
      );
      delete updateData.updatedOffers;
    }

    service = await Service.findOneAndUpdate({ _id: serviceId, provider: businessId }, updateData, {
      new: true,
      runValidators: true,
      status: businessEnums.serviceStatuses.IN_REVIEW,
      verificationStatus: businessEnums.businessVerificationStatuses.NOT_STARTED,
    });

    // notify super admin to assign verifier
    const superAdmin = await User.findOne({ roles: { $in: ['superAdmin'] } });
    await emailService.sendNotifySuperAdminToVerifyService(superAdmin, { service });

    if (!service) {
      logger.error('Service is invalid');
      throw new ApiError(httpStatus.NOT_FOUND, 'Service is invalid');
    }

    await session.commitTransaction();
    return service;
  } catch (error) {
    await session.abortTransaction();

    if (error instanceof ApiError) {
      throw error;
    } else {
      logger.error(error.message);
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  } finally {
    session.endSession();
  }
};

const manageSavedServices = async (user, serviceId) => {
  validateId(serviceId, 'Service');
  let status = false;

  const serviceExists = await Service.findById(serviceId);
  if (!serviceExists) {
    logger.error('Service is invalid');
    throw new ApiError(httpStatus.NOT_FOUND, 'Service is invalid');
  }

  if (user.savedServices.includes(serviceId)) {
    await User.updateOne({ _id: user._id }, { $pull: { savedServices: serviceId } });
    await Service.updateOne({ _id: serviceId }, { $pull: { bookmarks: user._id } });
  } else {
    await User.updateOne({ _id: user._id }, { $addToSet: { savedServices: serviceId } });
    await Service.updateOne({ _id: serviceId }, { $addToSet: { bookmarks: user._id } });
    status = true;
  }
  return status;
};

const getSavedServices = async (user, options) => {
  const services = await Service.paginate(
    { _id: { $in: user.savedServices } },
    { ...options, populate: servicePopulate(user) },
  );
  return services;
};

// Reviews

const getReviews = async (filterParam, options) => {
  const filter = filterParam;

  filter.$and = [];

  if (filter.provider) {
    let provider;
    if (!validateId(filter.provider, 'Business', false)) {
      provider = await Business.findOne({ username: filter.provider });
    } else {
      provider = await Business.findById(filter.provider);
    }
    let services = [];
    if (provider) {
      services = await Service.find({ provider: provider._id });
    }
    filter.$and.push({ service: services.length ? { $in: services.map((service) => service._id) } : null });
    delete filter.provider;
  }
  if (filter.service) {
    validateId(filter.service, 'Service');
  }

  filter.rating = { $gte: filter.minRating, $lte: filter.maxRating };
  delete filter.minRating;
  delete filter.maxRating;

  const reviews = await Review.paginate(filter, {
    ...options,
    populate: [
      {
        path: 'order',
        select: 'client plan name transaction',
        populate: [
          { path: 'client', ...basicUserPopulate },
          { path: 'transaction', select: 'amount currency' },
          { path: 'service', select: 'name media', populate: { path: 'media', select: 'url' } },
        ],
      },
    ],
  });

  reviews.results.forEach((review, index) => {
    reviews.results[index]._doc.order._doc.transaction._doc.amount = parseFloat(review.order.transaction.amount);
  });

  return reviews;
};

const addReview = async (user, data) => {
  const reviewData = data;
  const { order, reviews } = reviewData;
  delete reviewData.reviews;

  validateId(order, 'Order');

  const validOrder = await Order.findOne({
    _id: order,
    status: { $in: [businessEnums.orderStatuses.COMPLETED, businessEnums.orderStatuses.REPORTED] },
  });
  if (!validOrder) {
    logger.error('Order is invalid');
    throw new ApiError(httpStatus.BAD_REQUEST, 'Order is invalid');
  }

  if (validOrder.client.toString() !== user._id.toString()) {
    logger.error('You are not authorized to review this order');
    throw new ApiError(httpStatus.FORBIDDEN, 'You are not authorized to review this order');
  }

  const reviewExists = await Review.findOne({ order }).populate('order');
  if (reviewExists) {
    logger.error('You have already reviewed this order');
    throw new ApiError(httpStatus.BAD_REQUEST, 'You have already reviewed this order');
  }

  const ratings = Object.values(reviews);
  const totalRatings = ratings.reduce((sum, value) => sum + value, 0);
  reviewData.rating = (totalRatings / ratings.length).toFixed(1);

  const review = await Review.create({ ...reviewData, ...reviews, service: validOrder.service });
  await Service.findOneAndUpdate({ _id: validOrder.service }, { $addToSet: { reviews: review._id } });
  return review;
};

const getReview = async (reviewId) => {
  validateId(reviewId, 'Review');
  const review = await Review.findById(reviewId).populate({
    path: 'order',
    select: 'client service plan',
    populate: [{ path: 'client', ...basicUserPopulate }, { path: 'service' }],
  });
  if (!review) {
    logger.error('Review is invalid');
    throw new ApiError(httpStatus.NOT_FOUND, 'Review is invalid');
  }
  return review;
};

const updateReview = async (reviewId, userId, updateData) => {
  validateId(reviewId, 'Review');
  const updatedReview = await Review.findOneAndUpdate({ _id: reviewId, user: userId }, updateData, {
    new: true,
    runValidators: true,
  });
  if (!updatedReview) {
    logger.error('Review is invalid');
    throw new ApiError(httpStatus.NOT_FOUND, 'Review is invalid');
  }
  return updatedReview;
};

const deleteReview = async (reviewId, userId) => {
  validateId(reviewId, 'Review');
  const review = await Review.findOneAndDelete({ _id: reviewId, user: userId });
  if (!review) {
    logger.error('Review is invalid');
    throw new ApiError(httpStatus.NOT_FOUND, 'Review is invalid');
  }
};
// update

const CreateServiceHandler = {
  addNewService: async (req) => {
    const { stage, completedCreationStages } = req.query;
    if (req.query.completedCreationStages) {
      req.query.completedCreationStages = Array.isArray(completedCreationStages)
        ? completedCreationStages
        : [completedCreationStages];
    }

    const handlers = {
      [businessEnums.serviceStages.OVERVIEW]: CreateServiceHandler._handleOverviewStage,
      [businessEnums.serviceStages.FAQ]: CreateServiceHandler._handleFAQStage,
      [businessEnums.serviceStages.GALLERY]: CreateServiceHandler._handleGalleryStage,
      [businessEnums.serviceStages.OFFERS]: CreateServiceHandler._handleOffersStage,
      [businessEnums.serviceStages.REQUIREMENTS]: CreateServiceHandler._handleRequirementsStage,
      [businessEnums.serviceStages.SUBMIT]: CreateServiceHandler._handleSubmitStage,
    };

    if (!handlers[stage]) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Unknown stage');
    }

    let service;
    if (req.query.stage !== businessEnums.serviceStages.OVERVIEW) {
      if (!req.query.serviceId) throw new ApiError(httpStatus.BAD_REQUEST, 'Service ID required');
      validateId(req.query.serviceId, 'Service');

      service = await Service.findOne({ _id: req.query.serviceId });
      if (!service) throw new ApiError(httpStatus.NOT_FOUND, 'Service not found');
      if (service.provider.toString() !== req.user?.business?.toString()) {
        throw new ApiError(httpStatus.FORBIDDEN, "Forbidden: You're not allowed to perform this operation");
      }
    }

    service = await handlers[stage](req, service);
    return service;
  },

  _handleOverviewStage: async (req) => {
    let service;

    // validate caller func with req.query with Joi. i.e. serviceId must be provided if it is update
    const { error, value } = serviceValidation.validateOverview.validate(req.body);
    if (error) throw new ApiError(httpStatus.BAD_REQUEST, `Validation error: ${error.message}`);

    if (req.query.update === 'true') {
      validateId(req.query.serviceId, 'Service');
      const updatedService = await Service.findByIdAndUpdate(
        req.query?.serviceId,
        { $set: value, $addToSet: { completedCreationStages: businessEnums.serviceStages.OVERVIEW } },
        { new: true },
      );
      if (!updatedService) {
        logger.error('Error updating service: Service is invalid');
        throw new ApiError(httpStatus.NOT_FOUND, 'Service not found');
      }
    } else {
      const existingService = await Service.find({
        name: { $regex: value.name.trim(), $options: 'i' },
        provider: req.user.business,
      });
      if (existingService.length > 0) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Service with this name already exists');
      }

      service = await Service.create({
        ...value,
        provider: req.user.business,
        status: businessEnums.serviceStatuses.DRAFT,
        completedCreationStages: [businessEnums.serviceStages.OVERVIEW],
      });
      await Business.updateOne({ _id: req.user.business }, { $addToSet: { services: service._id } });
    }

    return service;
  },

  _handleFAQStage: async (req) => {
    const { serviceId } = req.query;

    const value = validateObjectData(req.body, serviceValidation.validateFAQ);

    const updatedService = await Service.findByIdAndUpdate(
      serviceId,
      { $set: value, $addToSet: { completedCreationStages: businessEnums.serviceStages.FAQ } },
      { new: true },
    );
    return updatedService;
  },

  _handleGalleryStage: async (req, service) => {
    // ensure serviceId and validate
    const { serviceId } = req.query;

    const value = validateObjectData(
      { files: req.files, deletedUrls: req.body.deletedUrls },
      serviceValidation.validateGallery,
    );

    if (value.deletedUrls) {
      value.deletedUrls = Array.isArray(value.deletedUrls) ? value.deletedUrls : [value.deletedUrls];
      const mediaPromises = value.deletedUrls.map(async (url) => {
        // eslint-disable-next-line no-useless-escape
        const urlExtract = url.match(/^(https?:\/\/[^\/\s]+\/[^\?\s]*)/)[0]?.replace(/\/$/, '');
        if (!urlExtract) {
          throw new ApiError(httpStatus.BAD_REQUEST, 'One or more Invalid URLs.');
        }
        const media = await File.findOne({ url: urlExtract });
        if (media) {
          await media.remove();
          service.media.pull(media._id);
        }
      });
      await Promise.all(mediaPromises);
      await service.save();
    }

    const $addToSet = {};
    if (value.files) {
      value.media = await fileService.processFileUploads(value.files, azureContainers.businessServices);
      $addToSet.media = value.media;
    }

    const updatedService = await Service.findByIdAndUpdate(
      serviceId,
      { $addToSet: { ...$addToSet, completedCreationStages: businessEnums.serviceStages.GALLERY } },
      { new: true },
    );
    return updatedService;
  },

  _handleOffersStage: async (req, service) => {
    const { serviceId } = req.query;

    const validatedOffers = validateRequiredOffers(req.body);

    if (service?.offers?.length > 0) {
      await ServiceOffer.deleteMany({ _id: { $in: service.offers } });
    }

    const offers = await Promise.all(
      validatedOffers.map(async (offer) => {
        const newOffer = await ServiceOffer.create({ ...offer, service: serviceId });
        return newOffer._id;
      }),
    );
    const updatedService = await Service.findByIdAndUpdate(
      serviceId,
      { $set: { offers }, $addToSet: { completedCreationStages: businessEnums.serviceStages.OFFERS } },
      { new: true },
    );
    return updatedService;
  },

  _handleRequirementsStage: async (req, service) => {
    const { serviceId } = req.query;

    const value = validateObjectData(req.body, serviceValidation.validateRequirementsSpecify);
    [
      ...(value.deletedRequirements || []),
      ...(value.requirements || []).reduce((acc, r) => (r.requirementId ? [...acc, r.requirementId] : acc), []),
    ].forEach((id) => {
      validateId(id, 'Requirement');
    });

    if (value.deletedRequirements?.length) {
      const deleted = await ServiceRequirementSpecify.deleteMany({
        _id: { $in: value.deletedRequirements },
        service: serviceId,
      });
      if (deleted.deletedCount !== value.deletedRequirements.length) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'One or more requirements to be deleted are invalid');
      }
      service.requirements.pull(...value.deletedRequirements);
    }

    const toUpdate = [];
    const toCreate = [];
    value.requirements.forEach((requirement) => {
      if (requirement.requirementId) {
        toUpdate.push(requirement);
      } else {
        toCreate.push({ ...requirement, service: serviceId });
      }
    });

    if (toCreate.length) {
      const createdRequirements = await ServiceRequirementSpecify.insertMany(toCreate);

      service.requirements.push(...createdRequirements.map((r) => r._id));
    }

    await Async.eachSeries(toUpdate, async (requirement) => {
      const existingQuestion = await ServiceRequirementSpecify.findOne({
        question: { $regex: requirement.question, $options: 'i' },
        service: serviceId,
        _id: { $ne: requirement.requirementId },
      });
      if (existingQuestion) {
        throw new ApiError(httpStatus.BAD_REQUEST, `Question "${requirement.question}" already exists for this service`);
      }
      await ServiceRequirementSpecify.findOneAndUpdate(
        { _id: requirement.requirementId },
        {
          $set: requirement,
        },
        { new: true },
      );
    });

    const updatedService = await Service.findByIdAndUpdate(
      serviceId,
      {
        $addToSet: { completedCreationStages: businessEnums.serviceStages.REQUIREMENTS },
        $set: { requirements: service.requirements },
      },
      { new: true },
    );
    return updatedService;
  },

  _handleSubmitStage: async (req) => {
    const { serviceId } = req.query;
    const service = await Service.findById(serviceId).lean();
    if (!service) throw new ApiError(httpStatus.NOT_FOUND, 'Service not found');

    const { error } = serviceValidation.validateCompletedService.validate(service);
    if (error) {
      logger.error(`Error validating service: ${error.message}`);
      throw new ApiError(httpStatus.BAD_REQUEST, `Validation error: ${error.message}`);
    }

    const updatedService = await Service.findByIdAndUpdate(
      serviceId,
      {
        $set: {
          status: businessEnums.serviceStatuses.IN_REVIEW,
          verificationStatus: businessEnums.businessVerificationStatuses.NOT_STARTED,
        },
        $addToSet: { completedCreationStages: businessEnums.serviceStages.SUBMIT },
      },
      { new: true },
    );
    // notify super admin to assign verifier
    const superAdmin = await User.findOne({ roles: { $in: ['superAdmin'] } });
    await emailService.sendNotifySuperAdminToVerifyService(superAdmin, { updatedService });

    return updatedService;
  },
};

// const manageArchivedServices = async (user, serviceId) => {
//   validateId(serviceId, 'Service');

//   const service = await Service.findOneAndUpdate(
//     { _id: serviceId, provider: user.business },
//     [
//       {
//         $set: {
//           status: {
//             $cond: {
//               if: { $eq: ['$status', businessEnums.serviceStatuses.UNPUBLISHED] },
//               then: businessEnums.serviceStatuses.PUBLISHED,
//               else: businessEnums.serviceStatuses.UNPUBLISHED,
//             },
//           },
//         },
//       },
//     ],
//     { new: true },
//   );

//   if (!service) {
//     logger.error('Service is invalid');
//     throw new ApiError(httpStatus.NOT_FOUND, 'Service is invalid');
//   }

//   const status = service.status === businessEnums.serviceStatuses.UNPUBLISHED;
//   return status;
// };

const deleteService = async (user, serviceId) => {
  validateId(serviceId, 'Service');

  const service = await Service.findOne({ _id: serviceId, provider: user.business });
  if (!service) {
    logger.error('Service not found');
    throw new ApiError(httpStatus.NOT_FOUND, 'Service not found');
  }

  const pendingServiceOrders = await Order.find({
    service: serviceId,
    status: { $ne: businessEnums.orderStatuses.COMPLETED },
  });
  if (pendingServiceOrders.length > 0) {
    logger.error('Service has pending orders');
    throw new ApiError(httpStatus.BAD_REQUEST, 'Service has pending orders');
  }

  const deletePromise = Service.deleteOne({ _id: serviceId, provider: user.business });
  const updateUsersPromise = User.updateMany({ savedServices: serviceId }, { $pull: { savedServices: serviceId } });
  const updateBusinessPromise = Business.updateOne({ _id: service.provider }, { $pull: { services: serviceId } });
  const offersDeletePromise = ServiceOffer.deleteMany({ service: serviceId });
  const reviewsDeletePromise = Review.deleteMany({ service: serviceId });

  await Promise.all([deletePromise, updateUsersPromise, updateBusinessPromise, offersDeletePromise, reviewsDeletePromise]);
};

// service offers
const ServiceOfferHandler = {
  getServiceOffers: async (serviceId, planType) => {
    validateId(serviceId, 'Service');

    const serviceExists = await Service.findById(serviceId)
      .select('provider name category media')
      .populate([
        {
          path: 'provider',
          select: 'username displayName profilePhoto',
        },
        {
          path: 'media',
          select: 'url',
        },
        {
          path: 'requirements',
          select: '-createdAt -updatedAt -service',
        },
      ]);
    if (!serviceExists) {
      logger.error('Service is invalid');
      throw new ApiError(httpStatus.NOT_FOUND, 'Service is invalid');
    }

    const filter = { service: serviceId };
    if (planType) filter.value = { $elemMatch: { plan: planType } }; // refactor
    const serviceOffers = await ServiceOffer.find(filter).select({
      name: 1,
      value: { $elemMatch: { plan: planType } },
    });

    if (!serviceOffers) {
      logger.error(`Service Offers do not exist for the ${planType} plan of this service`);
      throw new ApiError(httpStatus.NOT_FOUND, `Service Offers do not exist for the ${planType} plan of this service`);
    }

    serviceOffers.forEach((offer) => {
      if (!offer.value) {
        // eslint-disable-next-line no-param-reassign
        offer.value = [];
      }
    });

    return { serviceOffers, service: serviceExists };
  },
};

const getServiceRequirements = async (serviceId) => {
  validateId(serviceId, 'Service');

  const service = await Service.findById(serviceId).populate('requirements').select('requirements');
  if (!service) {
    logger.error('Service is invalid');
    throw new ApiError(httpStatus.NOT_FOUND, 'Service is invalid');
  }

  return service.requirements;
};

const getRecentlyViewedServices = async (user) => {
  const filter = {
    _id: { $in: user.recentlyViewedServices },
    verificationStatus: businessEnums.businessVerificationStatuses.APPROVED,
  };
  const services = await Service.find(filter).populate(servicePopulate(user));
  return services;
};

const addToRecentlyViewedServices = async (serviceId, user) => {
  validateId(serviceId, 'Service');
  const MAX_RECENTLY_VIEWED = 2;

  const serviceExists = await Service.findById(serviceId);
  if (!serviceExists) throw new ApiError(httpStatus.NOT_FOUND, 'Service record not found');

  // Remove the service if it already exists
  await User.findByIdAndUpdate(user._id, { $pull: { recentlyViewedServices: serviceId } });

  // Add the service front of the array, and limit to MAX_RECENTLY_VIEWED
  await User.findByIdAndUpdate(user._id, {
    $push: { recentlyViewedServices: { $each: [serviceId], $position: 0, $slice: MAX_RECENTLY_VIEWED } },
  });
};

// const generateServiceRequirementsJoi = async (serviceId, plan) => {
//   validateId(serviceId, 'Service');

//   const serviceRequirements = await ServiceRequirementSpecify.find({ service: serviceId, plansIncluded: { $in: [plan] } });
//   if (!serviceRequirements.length) {
//     logger.error('Service Requirements do not exist');
//     throw new ApiError(httpStatus.NOT_FOUND, 'Service has no requirements or does not exist');
//   }

//   const requirements = [
//     {
//       question: 'What is your name?',
//       answerTip: 'Enter your name',
//       type: 'text',
//       optional: false,
//     },
//     {
//       question: 'What is your age?',
//       answerTip: 'Enter your age',
//       type: 'number',
//       optional: false,
//     },
//   ];

//   const requirementsSchema = serviceRequirements.map((requirement) => {
//     const schema = {};
//     schema.requirement = requirement._id;
//     return schema;
//   });
// };

module.exports = {
  createService,
  getService,
  getServices,
  updateService,
  manageSavedServices,
  getSavedServices,
  servicePopulate,
  // manageArchivedServices,
  deleteService,

  getServiceRequirements,
  // generateServiceRequirementsJoi,

  getReviews,
  addReview,
  getReview,
  updateReview,
  deleteReview,
  ...CreateServiceHandler,

  ...ServiceOfferHandler,
  getRecentlyViewedServices,
  addToRecentlyViewedServices,
};
