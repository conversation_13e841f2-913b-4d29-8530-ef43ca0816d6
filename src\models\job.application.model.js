const mongoose = require('mongoose');
const validator = require('validator');
const { toJSON, paginate } = require('./plugins');
const { careerJobsEnums } = require('../config/constants');

const jobApplicationSchema = mongoose.Schema(
  {
    careerJob: { type: mongoose.Schema.Types.ObjectId, ref: 'CareerJobs' },
    firstName: { type: String, required: true, trim: true },
    lastName: { type: String, required: true, trim: true },
    email: {
      type: String,
      required: true,
      trim: true,
      lowercase: true,
      validate(value) {
        if (!validator.isEmail(value)) {
          throw new Error('Invalid email');
        }
      },
    },
    status: {
      type: String,
      enum: Object.values(careerJobsEnums.jobApplicationStatus),
      default: careerJobsEnums.jobApplicationStatus.IN_REVIEW,
    },
    socialLinks: [
      {
        name: { type: String, required: true, trim: true, enum: Object.values(careerJobsEnums.socialLinks) },
        url: { type: String, required: true, trim: true },
        description: { type: String, trim: true },
      },
    ],
    resume: { type: mongoose.Schema.Types.ObjectId, ref: 'File' },
    coverLetter: { type: mongoose.Schema.Types.ObjectId, ref: 'File' },
    // resume: {
    //   filename: { type: String, required: true },
    //   url: { type: String, required: true },
    // },
    // coverLetter: {
    //   filename: { type: String },
    //   url: { type: String },
    // },
  },
  {
    timestamps: true,
  },
);

jobApplicationSchema.index({
  careerJob: 1,
  status: 1,
  createdAt: 1,
  firstName: 1,
  lastName: 1,
  email: 1,
});

jobApplicationSchema.plugin(toJSON);
jobApplicationSchema.plugin(paginate);

const JobApplication = mongoose.model('JobApplication', jobApplicationSchema);
module.exports = JobApplication;
