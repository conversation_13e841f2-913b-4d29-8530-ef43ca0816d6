{"name": "unyked_backend_server", "version": "1.0.0", "description": "UnykEd Backend Service", "main": "src/index.js", "scripts": {"start": "pm2 start ecosystem.config.json --no-daemon", "dev": "cross-env NODE_ENV=development nodemon src/index.js", "test": "jest -i --colors --verbose --detect<PERSON><PERSON><PERSON>andles", "test:watch": "jest -i --watchAll", "coverage": "jest -i --coverage", "coverage:coveralls": "jest -i --coverage --coverageReporters=text-lcov | coveralls", "lint": "eslint .", "lint:fix": "eslint . --fix", "prettier": "prettier --check **/*.js", "prettier:fix": "prettier --write **/*.js", "docker:prod": "docker-compose -f docker-compose.yml -f docker-compose.prod.yml up", "docker:dev": "docker-compose -f docker-compose.yml -f docker-compose.dev.yml up", "docker:test": "docker-compose -f docker-compose.yml -f docker-compose.test.yml up", "prepare": "husky install"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@api/personaidentities": "file:.api/apis/personaidentities", "@azure/identity": "^4.0.0", "@azure/storage-blob": "^12.17.0", "agenda": "^5.0.0", "async": "^3.2.4", "axios": "^1.7.7", "bcryptjs": "^2.4.3", "brcypt": "^1.0.1", "cities.json": "^1.1.26", "cloudinary": "^2.5.1", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "countries-list": "^3.0.6", "cross-env": "^7.0.3", "crypto": "^1.0.1", "datauri": "^4.1.0", "date-fns": "^2.29.1", "dotenv": "^16.3.1", "express": "^4.18.1", "express-async-errors": "^3.1.1", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.0.2", "fluent-ffmpeg": "^2.1.3", "googleapis": "^131.0.0", "helmet": "^7.0.0", "html-to-text": "^9.0.5", "http-status": "^1.7.0", "immer": "^10.0.2", "into-stream": "^8.0.1", "joi": "^17.10.2", "jsonwebtoken": "^9.0.2", "mime-types": "^3.0.1", "moment": "^2.29.4", "mongoose": "^6.5.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nanoid": "^5.0.8", "node-cron": "^3.0.3", "nodemailer": "^6.9.5", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "pm2": "^5.3.0", "querystring": "^0.2.1", "selenium-webdriver": "^4.15.0", "short-unique-id": "^5.2.0", "socket.io": "^4.7.2", "stopword": "^2.0.8", "stripe": "^16.7.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "twilio": "^5.2.2", "uuid": "^9.0.1", "validator": "^13.11.0", "winston": "^3.10.0", "xss-clean": "^0.1.4"}, "devDependencies": {"coveralls": "^3.1.1", "eslint": "^8.50.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jest": "^27.4.2", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-security": "^1.7.1", "faker": "^5.5.3", "husky": "^8.0.3", "jest": "^29.7.0", "lint-staged": "^14.0.1", "node-mocks-http": "^1.13.0", "nodemon": "^3.0.1", "prettier": "^3.0.3", "supertest": "^6.3.3"}}