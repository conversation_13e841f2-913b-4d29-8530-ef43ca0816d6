const express = require('express');

const router = express.Router();
const validate = require('../../middlewares/validate');
const { profileValidation: ProfileValidation } = require('../../validations');
const { auth, canManageResource } = require('../../middlewares/auth');
const { profileController: ProfileController } = require('../../controllers');

router.use(auth());

router.post('/', validate(ProfileValidation.createProfile), ProfileController.createProfile);
// router.get('/', ProfileController.getProfiles);
router.get('/', ProfileController.getProfile);
// router.get('/:userId', validate(ProfileValidation.getUserInformation), ProfileController.getProfileById);
router.patch('/', validate(ProfileValidation.updateBasicInformation), ProfileController.updateBasicInformation);

//  router.post('/education', validate(ProfileValidation.addEducation), ProfileController.addEducationInfo);
// router.patch('/education/:id', validate(ProfileValidation.updateEducation), ProfileController.updateEducationInfo);

router.post('/resource', validate(ProfileValidation.requiredQueryPart), ProfileController.addResourceInfo);
router.patch(
  '/resource/:id',
  canManageResource('User'),
  validate(ProfileValidation.requiredQuery),
  ProfileController.updateResourceInfo,
);
router.get(
  '/resource/:id',
  canManageResource('User'),
  validate(ProfileValidation.requiredQuery),
  ProfileController.getResourceInfo,
);

router.delete(
  '/resource/:id',
  canManageResource('User'),
  validate(ProfileValidation.deleteResource),
  ProfileController.deleteResourceInfo,
);

module.exports = router;
