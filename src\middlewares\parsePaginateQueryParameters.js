const parsePaginateQueryParameters = (req, res, next) => {
  const { userId, sortBy, limit, page, populate } = req.query;

  const filter = {};
  if (userId) {
    filter.userId = userId;
  }

  const options = {};
  if (sortBy) {
    options.sortBy = sortBy;
  }
  if (limit) {
    options.limit = parseInt(limit, 10);
  }
  if (page) {
    options.page = parseInt(page, 10);
  }

  if (populate) {
    options.populate = populate;
  }
  req.filter = filter;
  req.options = options;
  next();
};

module.exports = parsePaginateQueryParameters;
