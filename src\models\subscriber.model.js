// models/Subscriber.js
const mongoose = require('mongoose');
const validator = require('validator');
const { toJSON, paginate } = require('./plugins');

const subscriberSchema = new mongoose.Schema(
  {
    email: {
      type: String,
      required: true,
      trim: true,
      lowercase: true,
      validate(value) {
        if (!validator.isEmail(value)) {
          throw new Error('Invalid email');
        }
      },
    },
    name: { type: String, trim: true },
    subscribedFor: { type: String, enum: ['counselor', 'newsletter'], required: true },
    subscribed: { type: Boolean, default: true },
  },
  {
    timestamps: true,
  },
);

subscriberSchema.plugin(toJSON);
subscriberSchema.plugin(paginate);

const Subscriber = mongoose.model('Subscriber', subscriberSchema);

module.exports = Subscriber;
