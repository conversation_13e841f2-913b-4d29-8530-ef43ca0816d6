const express = require('express');

const router = express.Router();
const httpStatus = require('http-status');
const Joi = require('joi');
const validate = require('../../middlewares/validate');
const catchAsync = require('../../utils/catchAsync');
const { IntendingUser } = require('../../models');

const validation = {
  body: Joi.object().keys({
    email: Joi.string().required().email(),
  }),
};

const controller = catchAsync(async (req, res) => {
  await IntendingUser.create(req.body);
  res.status(httpStatus.CREATED).json({
    status: 'SUCCESS',
    message: `Your email has been recorded. We'll notify you about our product launch soon.`,
  });
});

router.get('/', validate(validation), controller);

module.exports = router;
