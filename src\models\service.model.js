const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const { businessEnums, currencies } = require('../config/constants');

const serviceSchema = new mongoose.Schema(
  {
    provider: { type: mongoose.Schema.Types.ObjectId, ref: 'Business', required: true },
    name: { type: String, required: true, trim: true },
    description: { type: String, trim: true },
    category: { type: String, trim: true, enum: Object.values(businessEnums.serviceCategories) },
    tags: [{ type: String, trim: true }],
    faq: [
      {
        question: { type: String, trim: true, required: true },
        answer: { type: String, trim: true, required: true },
      },
    ],
    offers: [{ type: mongoose.Schema.Types.ObjectId, ref: 'ServiceOffer' }],
    orders: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Order' }],
    currency: { type: String, enum: Object.values(currencies), default: currencies.USD },
    media: [{ type: mongoose.Schema.Types.ObjectId, ref: 'File' }],
    status: {
      type: String,
      enum: Object.values(businessEnums.serviceStatuses),
      default: businessEnums.serviceStatuses.DRAFT,
    },
    completedCreationStages: [{ type: String, enum: Object.values(businessEnums.serviceStages) }],
    reviews: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Review' }],
    bookmarks: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],

    requirements: [{ type: mongoose.Schema.Types.ObjectId, ref: 'ServiceRequirementSpecify' }],
    disclaimer: {
      type: String,
      trim: true,
      default: 'I agree that the information provided is accurate and clear. Any changes at this point may cost extra.',
    },
    verificationStatus: {
      type: String,
      trim: true,
      enum: Object.values(businessEnums.businessVerificationStatuses),
      default: businessEnums.businessVerificationStatuses.NOT_STARTED,
    },
    verifications: [{ type: mongoose.Schema.Types.ObjectId, ref: 'BusinessVerification' }],
  },
  {
    timestamps: true,
  },
);

serviceSchema.index({ name: 1, categories: 1, tags: 1, createdAt: 1, updatedAt: 1 });

serviceSchema.plugin(toJSON);
serviceSchema.plugin(paginate);

const Service = mongoose.model('Service', serviceSchema);

module.exports = Service;
