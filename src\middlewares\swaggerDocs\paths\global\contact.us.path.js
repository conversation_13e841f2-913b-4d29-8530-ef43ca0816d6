module.exports = {
  post: {
    summary: 'Send a message to the admin',
    tags: ['Global'],
    requestBody: {
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              name: {
                type: 'string',
                example: '<PERSON>',
              },
              email: {
                type: 'string',
                example: 'joh<PERSON><PERSON>@email.com',
              },
              message: {
                type: 'string',
                example: 'Hello, I would like to ask a question...',
              },
            },
            required: ['name', 'email', 'message'],
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Message sent successfully',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Message successfully sent',
            },
          },
        },
      },
    },
  },
};
