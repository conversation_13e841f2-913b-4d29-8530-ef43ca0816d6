const { visibilityTypes, themeTypes } = require('../../../../config/constants');

module.exports = {
  patch: {
    tags: ['Settings'],
    summary: 'Update user settings',
    requestBody: {
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              theme: { type: 'string', enum: Object.values(themeTypes) },
              dateOfBirth: { type: 'string', enum: Object.values(visibilityTypes) },
              skills: { type: 'string', enum: Object.values(visibilityTypes) },
              hobbies: { type: 'string', enum: Object.values(visibilityTypes) },
              email: { type: 'string', enum: Object.values(visibilityTypes) },
              nationality: { type: 'string', enum: Object.values(visibilityTypes) },
              phone: { type: 'string', enum: Object.values(visibilityTypes) },
              countryOfResidence: { type: 'string', enum: Object.values(visibilityTypes) },
              targetedCountries: { type: 'string', enum: Object.values(visibilityTypes) },
              preferredFieldsOfStudy: {
                type: 'array',
                items: { type: 'string', enum: Object.values(visibilityTypes) },
              },
              preferredSchoolOfStudy: { type: 'string', enum: Object.values(visibilityTypes) },
              preferredCountryOfStudy: { type: 'string', enum: Object.values(visibilityTypes) },
              preferredDegreeOfStudy: { type: 'string', enum: Object.values(visibilityTypes) },
              interestedInScholarship: { type: 'string', enum: Object.values(visibilityTypes) },
              basicInformation: { type: 'string', enum: Object.values(visibilityTypes) },
              education: { type: 'string', enum: Object.values(visibilityTypes) },
              certification: { type: 'string', enum: Object.values(visibilityTypes) },
              testscore: { type: 'string', enum: Object.values(visibilityTypes) },
              experience: { type: 'string', enum: Object.values(visibilityTypes) },
              volunteering: { type: 'string', enum: Object.values(visibilityTypes) },
              project: { type: 'string', enum: Object.values(visibilityTypes) },
              award: { type: 'string', enum: Object.values(visibilityTypes) },
            },
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Settings updated successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: `Your preference setting is saved` },
                status: { type: 'string', example: 'SUCCESS' },
              },
            },
          },
        },
      },
    },
  },
};
