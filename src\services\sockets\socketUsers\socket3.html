<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>

<body>
  <h1>Hello socket</h1>
  <h3>652f6fb971c38a9b93066b9d - <PERSON></h3>
  <button onclick="fetchData()">Click me!</button>
  <button onclick="fetchData(joinGroupUrl)">Join Group!</button>
  <button onclick="fetchData(likePostUrl)">Like Post!</button>
  <button onclick="fetchData(likeCommentUrl)">Like Comment!</button>
</body>

<script src="https://cdn.socket.io/4.7.2/socket.io.min.js"
  integrity="sha384-mZLF4UVrpi/QTWPA7BjNPEnkIfRFn4ZEO3Qt/HFklTJBj/gBOV8G3HcKn4NfQblz" crossorigin="anonymous"></script>
<script>
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOnsidXNlcklkIjoiNjUyZjZmYjk3MWMzOGE5YjkzMDY2YjlkIiwicm9sZXMiOlsic3R1ZGVudCJdfSwiaWF0IjoxNzAyMjg5NTg0LCJleHAiOjE5NzAyMjg5NTg0LCJ0eXBlIjoiYWNjZXNzIn0.RfcwiX-K4C3jtTKXbr7Fm_BICxOft2Q733LBNplW6f4';

  const url = 'http://localhost:4000';
  const socket = io(url, {
    auth: {
      token: `${token}`,
    },
  });

  socket.on('connect', () => {
    isSocketConnected = true;
    console.log('Connection established');
  });

  socket.on('notification', (data) => {
    console.log('connected');
    console.log('data: ', data);
    console.log(socket.user);
  });

  const joinGroupUrl = `${url}/v1/groups/65699eb8ff316eff38c4264d/join`; // Replace with your API URL
  const likePostUrl = `${url}/v1/posts/652d4d71e8fb3c03691f91d8/like`; // Replace with your API URL
  const likeCommentUrl = `${url}/v1/comments/656a12e8227abfe720e59e1b/like`; // Replace with your API URL
  const authToken = token;

  const fetchData = async (apiUrl) => {
    try {
      const response = await fetch(apiUrl, {
        method: 'POST', // Change the method according to your API endpoint
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}` // Include the authorization token in the headers
        }
      });

      if (!response.ok) {
        throw new Error('Network response was not ok.');
      }

      const data = await response.json();
      return 3;

    } catch (error) {
      console.error('Error:', error.message);
    }
  };


</script>

</html>