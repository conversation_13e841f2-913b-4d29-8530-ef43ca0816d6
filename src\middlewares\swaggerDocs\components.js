const errors = require('./schemas/errors/errors.schemas');
const activityLogs = require('./schemas/activityLogs/activityLogs.schema');
const comments = require('./schemas/comments/comment.schema');
const forumPosts = require('./schemas/forums/forum.post.schema');
const posts = require('./schemas/posts/post.schema');
const settings = require('./schemas/profile/settings.schema');
const users = require('./schemas/users/user.schema');

const award = require('./schemas/profile/award.schema');
const certification = require('./schemas/profile/certification.schema');
const education = require('./schemas/profile/education.schema');
const experience = require('./schemas/profile/experience.schema');
const profile = require('./schemas/profile/profile.schema');
const project = require('./schemas/profile/project.schema');
const testScore = require('./schemas/profile/testScore.schema');
const volunteering = require('./schemas/profile/volunteering.schema');

module.exports = {
  components: {
    schemas: {
      ActivityLogs: { ...activityLogs },
      Comment: { ...comments },
      User: { ...users },
      Post: { ...posts },
      ForumPosts: { ...forumPosts },
      Settings: { ...settings },

      Award: { ...award },
      Certification: { ...certification },
      Education: { ...education },
      Experience: { ...experience },
      Profile: { ...profile },
      Project: { ...project },
      TestScore: { ...testScore },
      Volunteering: { ...volunteering },
      ...errors,
    },
  },
};
