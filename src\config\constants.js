const { deepFreeze } = require('../utils/helperMethods');

const clientUserTypes = { BUSINESS: 'business', STUDENT: 'student' };

const genderTypes = {
  MALE: 'male',
  FEMALE: 'female',
  OTHER: 'other',
};

const blogStatuses = {
  PUBLISHED: 'published',
  DRAFT: 'draft',
  UNPUBLISHED: 'unpublished',
  DELETED: 'deleted',
  SCHEDULED: 'scheduled',
};

const reactionTypes = {
  LIKE: 'like',
  LOVE: 'love',
  INSIGHTFUL: 'insightful',
  HELPFUL: 'helpful',
  FUNNY: 'funny',
  APPLAUD: 'applaud',
};

const blogCategories = [
  'Admission',
  'Study Tips',
  'Career Development',
  'Student Life',
  'Internships',
  'College Applications',
  'Academic Resources',
  'Scholarships',
];

const fileTypes = {
  TEXT: 'text',
  IMAGE: 'image',
  VIDEO: 'video',
};

const registrationStatuses = {
  SIGNUP_COMPLETED: 'signup_completed',
  ACCOUNT_VERIFIED: 'account_verified',
  ONBOARDING_COMPLETE: 'onboarding_completed',
};

// faux commit
const resourceLibraryCategories = [
  'Statement of Purpose',
  'Academic CV and Resume',
  'Research Proposal',
  'Cold Emails',
  'Recommendation Letters',
];

const resourceLibraryCategoriesDescriptions = {
  'Statement of Purpose': 'A personal essay required for graduate school applications.',
  'Academic CV and Resume': 'A detailed document highlighting academic achievements and experiences.',
  'Research Proposal': 'A document outlining a proposed research project.',
  'Cold Emails': 'Unsolicited emails sent to potential collaborators or institutions.',
  'Recommendation Letters': 'Letters written by others to endorse your qualifications.',
};

const visibilityTypes = {
  PRIVATE: 'private',
  PUBLIC: 'public',
  UNIVERSITIES: 'universities',
};

const postVisibilityTypes = Object.freeze({
  PUBLIC: 'public',
  ONLY_ME: 'only_me',
  FOLLOWERS: 'followers',
  UNIVERSITIES: 'universities',
});

const themeTypes = {
  LIGHT: 'light',
  DARK: 'dark',
  DEVICE: 'device',
};

const incomingEmailAddresses = {
  CONTACT_US: '<EMAIL>',
  REPORT: '<EMAIL>',
};

const reactionsActionMap = {
  LIKE: 'liked',
  LOVE: 'loved',
  INSIGHTFUL: 'found insightful,',
  HELPFUL: 'found helpful,',
  FUNNY: 'found funny,',
  APPLAUD: 'applauded',
};

const activityMap = {
  // posts and comments
  ...Object.entries(reactionTypes).reduce((acc, [key, type]) => {
    acc[`POST_${type.toUpperCase()}`] = { prefix: 'P', text: reactionsActionMap[key], resourceName: 'post' };
    acc[`COMMENT_${type.toUpperCase()}`] = { prefix: 'C', text: reactionsActionMap[key], resourceName: 'comment' };
    return acc;
  }, {}),
  POST_COMMENT: { prefix: 'P', text: 'commented on', resourceName: 'post' },
  COMMENT_COMMENT: { prefix: 'C', text: 'commented on', resourceName: 'comment' },
  // Post bookmarks
  BOOKMARK_POST: { prefix: 'P', text: 'bookmarked', resourceName: 'post' },
  UNBOOKMARK_POST: { prefix: 'P', text: 'unbookmarked', resourceName: 'post' },
  // Reposts
  REPOST: { prefix: 'P', text: 'reposted', resourceName: 'post' },
  // forum reply
  UPVOTE_REPLY: { prefix: 'FR', text: 'upvoted', resourceName: 'forum reply' },
  DOWNVOTE_REPLY: { prefix: 'FR', text: 'downvoted', resourceName: 'forum reply' },
  FORUM_POST_LIKE: { prefix: 'FP', text: 'downvoted', resourceName: 'forum post' },

  // group membership
  CANCEL_REQUEST: { prefix: 'G', text: 'cancelled your request to join', resourceName: 'group' },
  JOIN_REQUEST: { prefix: 'G', text: 'requested to join', resourceName: 'group' },
  JOIN_SUCCESS: { prefix: 'G', text: 'joined', resourceName: 'group' },
};

const userDefaultSettings = () => {
  const settings = {};
  settings.theme = themeTypes.LIGHT;
  ['dateOfBirth', 'email', 'phone'].forEach((value) => {
    // 'skills', 'hobbies',
    settings[value] = visibilityTypes.PRIVATE;
  });

  const visibileToPublic = [
    'skills',
    'hobbies',
    'countryOfResidence',
    'basicInformation',
    'education',
    'certification',
    'testscore',
    'experience',
    'volunteering',
    'project',
    'award',
    'nationality',
    'stateOfResidence',
    'gender',
  ];
  visibileToPublic.forEach((value) => {
    settings[value] = visibilityTypes.PUBLIC;
  });

  const visibileToUniversities = [
    'targetedCountries',
    'preferredFieldsOfStudy',
    'preferredSchoolsOfStudy',
    'preferredCountryOfStudy',
    'preferredDegreeOfStudy',
    'interestedInScholarship',
  ];
  visibileToUniversities.forEach((value) => {
    settings[value] = visibilityTypes.UNIVERSITIES;
  });

  return settings;
};

const azureContainers = {
  blogs: 'blogs',
  businessProfilePhotos: 'business/profilePhotos',
  businessCoverPhotos: 'business/coverPhotos',
  businessOrders: 'business/orders',
  businessServices: 'business/services',
  forums: 'forums',
  groups: 'groups',
  groupsCoverImg: 'groups/cover_img',
  groupsProfileImg: 'groups/profile_img',
  issues: 'issues',
  jobResumes: 'career-jobs/resumes',
  jobCoverLetters: 'career-jobs/cover_letters',
  messages: 'messages',
  posts: 'posts',
  postComments: 'posts/comments',
  postsThumbnail: 'posts/thumbnail',
  resourceLibrary: 'resource-library',
  schools: 'schools',
  scholarships: 'scholarships',
  userProfilePhotos: 'users/photo',
  userProfileBanners: 'users/banners',
};

const notificationSettingModelFields = Object.freeze({
  FOLLOW: 'followNotification',
  UNFOLLOW: 'unfollowNotification',
  JOIN_GROUP_REQUEST: 'joinGroupRequestNotification',
  ACCEPT_GROUP_REQUEST: 'acceptGroupRequestNotification',
  REJECT_GROUP_REQUEST: 'rejectGroupRequestNotification',
  LIKE: 'likeNotification',
  REACTION: 'reactionNotification',
  COMMENT: 'commentNotification',
  REPOST: 'repostNotification',
  COMMENT_OF_COMMENT: 'commentOfCommentNotification',
  MESSAGE: 'messageNotification',
  GROUP_MESSAGE: 'groupNotification.settings',
});

const groupSettingsEnums = Object.freeze({
  ADMIN: 'admin',
  ALL: 'all',
  OFF: 'off',
});

const allowedAuthRoutesWithoutOnboarding = [
  /^\/v1\/auth\/refresh$/,
  /^\/v1\/users\/onboard$/,
  /^\/v1\/business\/onboard$/,
  /^\/v1\/business\/[^/]+$/,
];

const schoolsEnums = {
  applicationSeasons: {
    SUMMER: 'Summer',
    WINTER: 'Winter',
    SPRING: 'Spring',
    FALL: 'Fall',
    ALL: 'All',
  },
  fundingCategories: {
    SCHOLARSHIP: 'Scholarship',
    ASSISTANTSHIP: 'Assistantship',
    FELLOWSHIP: 'Fellowship',
    WORK_STUDY: 'Work Study',
    LOAN: 'Loan',
    GRANT: 'Grant',
  },
  // scholarshipTypes: {
  //   FULL_SCHOLARSHIP: 'Full Scholarship',
  //   PARTIAL_SCHOLARSHIP: 'Partial Scholarship',
  //   FELLOWSHIP: 'Fellowship',
  //   ASSISTANTSHIP: 'Assistantship',
  // },
  fundingTypes: {
    FULL_FUNDING: 'Full Scholarship',
    PARTIAL_FUNDING: 'Partial Scholarship',
    ASSISTANTSHIP: 'Assistantship',
    FELLOWSHIP: 'Fellowship',
  },
  programTypes: {
    BACHELOR: 'Bachelor',
    MASTER: 'Master',
    PHD: 'PhD',
    DIPLOMA: 'Diploma',
    SHORT_COURSE: 'Short Course',
  },
  scholarshipStatuses: {
    OPEN: 'open',
    CLOSED: 'closed',
  },
  durationTypes: {
    DEADLINE_VARIES: 'Deadline Varies',
    ALWAYS_OPEN: 'Always Open',
    DURATION_SPECIFIED: 'Duration Specified',
  },
};

const careerJobsEnums = {
  careerJobCategories: {
    SOFTWARE_DEVELOPMENT: 'Software Development',
    DATA_SCIENCE: 'Data Science',
    PRODUCT_MANAGEMENT: 'Product Management',
    DESIGN: 'Design',
    MARKETING: 'Marketing',
    SALES: 'Sales',
    HR: 'Human Resources',
    FINANCE: 'Finance',
    OPERATIONS: 'Operations',
    CONTENT_WRITING: 'Content Writing',
  },

  careerJobType: {
    FULL_TIME: 'Full Time',
    PART_TIME: 'Part Time',
    INTERNSHIP: 'Internship',
    VOLUNTEERING: 'Volunteering',
  },

  careerJobMode: {
    HYBRID: 'Hybrid',
    REMOTE: 'Remote',
    ON_SITE: 'On Site',
  },

  careerJobLocations: ['California, USA'],

  careerJobPublishStatus: {
    PUBLISHED: 'published',
    UNPUBLISHED: 'unpublished',
    ALL_JOBS: 'all-jobs',
  },

  jobApplicationStatus: {
    IN_REVIEW: 'in-review',
    INTERVIEW: 'interview',
    REJECTED: 'rejected',
    HIRED: 'hired',
  },

  socialLinks: {
    GITHUB: 'gitHub',
    LINKEDIN: 'linkedIn',
    FIGMA: 'figma',
    TWITTER: 'twitter',
    PORTFOLIO: 'portfolio',
    OTHER: 'other',
  },
};

const currencies = {
  USD: 'USD',
  EUR: 'EUR',
  GBP: 'GBP',
  INR: 'INR',
  NGN: 'NGN',
  KES: 'KES',
  GHS: 'GHS',
  ZAR: 'ZAR',
};

const issueReport = {
  statuses: {
    OPEN: 'open',
    IN_PROGRESS: 'in_progress',
    RESOLVED: 'resolved',
    CLOSED: 'closed',
  },
  types: {
    BUG: 'bug',
    FEATURE: 'feature',
    OTHER: 'other',
    SECURITY: 'security',
    PERFORMANCE: 'performance',
    USABILITY: 'usability',
    COMPATIBILITY: 'compatibility',
  },
  priorities: {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    URGENT: 'urgent',
  },
  statusDescriptions: {
    OPEN: 'Open and not assigned',
    IN_PROGRESS: 'In Progress and assigned',
    RESOLVED: 'Resolved and closed',
    CLOSED: 'Closed but not resolved',
  },
};

const businessEnums = {
  onboardingStages: {
    BUSINESS_INFO: 'business_info',
    PROFESSIONAL_INFO: 'professional_info',
    ADDITIONAL_INFO: 'additional_info',
    ACCOUNT_SECURITY: 'account_security',
    COMPLETED: 'completed',
  },
  transactionStatuses: {
    REQUIRES_PAYMENT_METHOD: 'requires_payment_method',
    REQUIRES_ACTION: 'requires_action',
    PROCESSING: 'processing',
    REQUIRES_CAPTURE: 'requires_capture',
    CANCELED: 'canceled',
    SUCCEEDED: 'succeeded', // Payment Intent is successful, order can be fulfilled
    REQUIRES_CONFIRMATION: 'requires_confirmation',

    // Stripe Checkout Session
    OPEN: 'open',
    EXPIRED: 'expired',
    COMPLETE: 'complete', // When checkout session has been successfully completed, order can be fulfilled

    REFUNDED: 'refunded',
  },
  transactionTypes: {
    DEBIT: 'debit', // Withdrawal from account
    CREDIT: 'credit', // Deposit to account
    CREDIT_DEBIT: 'credit_debit', // Deposit to account and withdrawal from account
    REFUND: 'refund', // Refund to customer
  },
  transactionComments: {
    ORDER_PLACEMENT: 'Order placement was successful',
    ORDER_REFUND: 'Order refunded to client as per policy',
    WITHDRAWAL: 'Withdrawal from your UnykEd account',
  },
  reviewQuestions: {
    QUALITY_OF_SERVICE: ['qualityOfService', 'How would you rate the quality of the service provided?'],
    TIMELINESS: ['timeliness', 'How punctual was the service provider in delivering the service?'],
    COMMUNICATION: ['communication', 'How would you rate the communication with the service provider?'],
    // SATISFACTION: ['satisfaction', 'Overall, how satisfied are you with the service you received?'],
    // RECOMMENDATION: ['recommendation', 'Would you recommend this service to others?'],
  },
  orderStatuses: {
    // STARTED: 'started',
    AWAITING_REQUIREMENTS: 'awaiting_requirements',
    REVISION_REQUESTED: 'revision_requested',
    IN_PROGRESS: 'in_progress',
    AWAITING_CLIENT_APPROVAL: 'awaiting_client_approval',
    COMPLETED: 'completed',
    // SATISFIED: 'satisfied',
    DISPUTED: 'disputed',
    CANCELLED: 'cancelled',
  },
  orderActivityActions: {
    ORDER_PLACED: 'order_placed',
    ORDER_IN_PROGRESS: 'order_in_progress',
    ORDER_DELIVERED: 'order_delivered',
    ORDER_COMPLETED: 'order_completed',
    ORDER_CANCELLED: 'order_cancelled',
    ORDER_REQUIREMENTS_SUBMITTED: 'order_requirements_submitted',
    REVISION_REQUESTED: 'revision_requested',
    REVISION_DELIVERED: 'revision_delivered',
    REVISION_COMPLETED: 'revision_completed',
    REVISION_REJECTED: 'revision_rejected',
    REVISION_CANCELLED: 'revision_cancelled',
    DISPUTE_REPORTED: 'dispute_reported',
  },
  orderDisputeStatuses: {
    OPEN: 'open',
    RESOLVED: 'resolved',
    CLOSED: 'closed',
  },
  orderDisputeResolutions: {
    REFUND: 'refund',
    CANCELLATION: 'cancellation',
    REVISION: 'revision',
  },
  orderActivityActors: { CLIENT: 'client', PROVIDER: 'provider' },
  servicePlans: {
    BASIC: 'basic',
    STANDARD: 'standard',
    PREMIUM: 'premium',
  },
  premiumSubscriptionPlans: {
    PREMIUM: { name: 'premium', priceMonthly: 2000, currency: 'USD' }, // Price is in cents and for monthly. Yearly price can be derived by multiplying by 12
  },
  premiumSubscriptionPeriods: {
    MONTHLY: ['monthly', 1],
    YEARLY: ['yearly', 12], // [name, multiplier]
  },
  serviceStatuses: {
    DRAFT: 'draft',
    PUBLISHED: 'published',
    UNPUBLISHED: 'unpublished',
    SUSPENDED: 'suspended',
    IN_REVIEW: 'in_review',
    CHANGES_REQUESTED: 'changes_requested',
  },
  languageLevels: {
    ELEMENTARY: 'elementary',
    CONVERSATIONAL: 'conversational',
    PROFICIENT: 'proficient',
    NATIVE: 'native',
  },
  serviceStages: {
    OVERVIEW: 'overview',
    OFFERS: 'offers',
    REQUIREMENTS: 'requirements',
    FAQ: 'faq',
    GALLERY: 'gallery',
    SUBMIT: 'submit',
  },
  requiredOffers: {
    PRICE: ['Price', (n) => !Number.isNaN(parseInt(n, 10)) && parseInt(n, 10) > 0], // Name of the offer and validation function
    MAXIMUM_REVISIONS: ['Maximum Revisions', (n) => !Number.isNaN(parseInt(n, 10)) && parseInt(n, 10) >= 0],
    COMPLETION_DAYS: ['Completion Days', (n) => !Number.isNaN(Number(n)) && Number(n) > 0],
  },
  serviceCategories: {
    ACADEMIC_CV_REVIEW: 'Academic CV Review',
    APPLICATION_REVIEW: 'Application Review',
    APPLICATION_STRATEGY_AND_PLANNING: 'Application Strategy and Planning',
    FINANCIAL_AID_AND_SCHOLARSHIP_APPLICATIONS: 'Financial Aid and Scholarship Applications',
    INTERVIEW_PREPARATION: 'Interview Preparation',
    LETTER_OF_RECOMMENDATION_REVIEW: 'Letter of Recommendation Review',

    PERSONAL_STATEMENT_REVIEW: 'Personal Statement Review',
    RESUME_REVIEW: 'Resume Review',
    SCHOOL_SELECTION_AND_RESEARCH: 'School Selection and Research',
    STANDARDIZED_TEST_PREPARATION: 'Standardized Test Preparation',
    STATEMENT_OF_PURPOSE_REVIEW: 'Statement of Purpose Review',
    TRANSCRIPT_EVALUATION: 'Transcript Evaluation',
  },
  serviceRequirementTypes: {
    TEXT: 'text',
    FILE: 'file',
    SELECT: 'select',
    MULTI_SELECT: 'multi_select',
    // DATE: 'date',
    // NUMBER: 'number',
  },
  referralBonusTypes: {
    POINTS: 'points',
    AMOUNT: 'amount',
  },
  businessVerificationStatuses: {
    APPROVED: 'approved',
    IN_PROGRESS: 'in_progress', // Reviewer has been assigned
    CHANGES_REQUESTED: 'changes_requested', // Business verification was approved but changes were requested
    // AWAITING_APPROVAL: 'awaiting_approval', // Reviewer has been assigned
    NOT_STARTED: 'not_started', // [formally n/a] When the business has not started the verification process
  },
  counselorVerificationDocumentTypes: {
    PROFESSIONAL_CERTIFICATION: 'Professional Certification', // Licenses like LPC, NCC, or state-issued certifications.
    EDUCATION_CREDENTIALS: 'Education Credentials', // Degrees, diplomas, or transcripts from accredited institutions.
    PROFESSIONAL_MEMBERSHIP: 'Professional Membership', // Memberships in organizations like ACA, NBCC, etc.
    WORK_EXPERIENCE_DOCUMENTS: 'Work Experience Documents', // Resume, CV, or letters of recommendation.
    REFERENCES: 'References', // Contact information for past clients, supervisors, or employers.
    ONLINE_PROFILE_OR_PORTFOLIO: 'Online Profile or Portfolio', // Links to websites or LinkedIn profiles.
    BACKGROUND_CHECK: 'Background Check', // Criminal record clearance or similar checks.
    CODE_OF_ETHICS: 'Code of Ethics', // Ethical standards and privacy policies.
    LIABILITY_INSURANCE_PROOF: 'Liability Insurance Proof', // Evidence of professional liability insurance.
    SERVICE_AGREEMENT_TEMPLATE: 'Service Agreement Template', // Sample counseling agreements or contracts.
    OTHER: 'Other', // Any other relevant documents.
  },
};

const messageEnums = {
  createdForValues: {
    USER_USER: 'user-user',
    USER_BUSINESS: 'user-business',
    ORDER: 'order',
  },
};

const OTPEnums = {
  resendOTPTimes: [0, 1, 2, 5], // in minutes
  maxDate: 8640000000000000,
};

const returnData = {
  clientUserTypes,
  genderTypes,
  blogStatuses,
  reactionTypes,
  fileTypes,
  registrationStatuses,
  themeTypes,
  activityMap,
  reactionsActionMap,
  userDefaultSettings,
  visibilityTypes,
  azureContainers,
  incomingEmailAddresses,
  postVisibilityTypes,
  notificationSettingModelFields,
  groupSettingsEnums,
  blogCategories,
  allowedAuthRoutesWithoutOnboarding,
  schoolsEnums,
  careerJobsEnums,
  currencies,
  issueReport,
  businessEnums,
  OTPEnums,
  messageEnums,
  resourceLibraryCategories,
  resourceLibraryCategoriesDescriptions,
};

deepFreeze(returnData);

module.exports = returnData;
