module.exports = {
  post: {
    summary: 'Create a new forum post',
    tags: ['Forum Posts'],
    consumes: ['multipart/form-data'],
    parameters: [
      {
        in: 'formData',
        name: 'files',
        type: 'file',
        description: 'File files',
      },
      {
        in: 'formData',
        name: 'body',
        required: true,
        description: 'Comment data',
        schema: {
          type: 'object',
          properties: {
            text: {
              type: 'string',
              description: 'Forum post text',
              example: 'We have a new post to search for the best college',
            },
            topic: {
              type: 'string',
              description: 'Forum post topic',
              example: 'Find a new college',
            },
            tags: { type: 'array', description: 'Forum post tags', example: ['New'] },
            category: {
              type: 'string',
              description: 'Forum post category',
              enum: [
                'All',
                'General Discussion',
                'Admissions',
                'Test Scores',
                'SAT',
                'ACT',
                'College Applications',
                'Scholarships',
                'Financial Aid',
                'Study Tips',
                'Student Life',
                'Internships',
                'Career Advice',
                'Graduate School',
              ],
              example: 'General Discussion',
            },
            files: { type: 'file', description: 'File files' },
          },
          required: ['text', 'topic'],
        },
      },
    ],
    responses: {
      201: {
        description: 'Forum post created successfully',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Forum post created successfully',
            },
          },
        },
      },
      500: {
        description: 'Error creating forum post',
        content: {
          'application/json': {
            example: {
              status: 'FAILED',
              message: 'Error creating forum post',
            },
          },
        },
      },
    },
  },
};
