const mongoose = require('mongoose');
const httpStatus = require('http-status');
const Async = require('async');
const { toJSON, paginate } = require('./plugins');
const { businessEnums } = require('../config/constants');
const ApiError = require('../utils/ApiError');

const serviceRequirementSpecifySchema = new mongoose.Schema(
  {
    service: { type: mongoose.Schema.Types.ObjectId, ref: 'Service', required: true },
    plansIncluded: [
      {
        type: String,
        enum: Object.values(businessEnums.servicePlans),
        default: Object.values(businessEnums.servicePlans),
        required: true,
      },
    ],
    question: { type: String, trim: true, required: true },
    answerTip: { type: String, trim: true },
    type: { type: String, required: true, enum: Object.values(businessEnums.serviceRequirementTypes) },
    optional: { type: Boolean, default: false },
    options: [{ type: String, trim: true }],
  },
  {
    timestamps: true,
  },
);

const verifyDuplicateQuestion = async function (next, docs) {
  // eslint-disable-next-line no-param-reassign
  docs = Array.isArray(docs) ? docs : [docs];

  const serviceRequirementSpecify = mongoose.model('ServiceRequirementSpecify');
  try {
    await Async.eachSeries(docs, async (doc) => {
      if (doc.question) {
        const question = await serviceRequirementSpecify.findOne({
          question: { $regex: doc.question, $options: 'i' },
          service: doc.service,
          _id: { $ne: doc._id },
        });
        if (question) {
          throw new ApiError(httpStatus.BAD_REQUEST, `Question "${doc.question}" already exists for this service`);
        }
      }
    });
    next();
  } catch (error) {
    next(error);
  }
};

// serviceRequirementSpecifySchema.pre('save', verifyDuplicateQuestion);
serviceRequirementSpecifySchema.pre('insertMany', verifyDuplicateQuestion);
// serviceRequirementSpecifySchema.pre(['updateOne', 'updateMany'], verifyDuplicateQuestion);
// serviceRequirementSpecifySchema.pre('findOneAndUpdate', async function (next) {
//   const update = this.getUpdate();
//   const { _id } = this.getQuery();
//   console.log('Checking')
//   await verifyDuplicateQuestion(next, { ...update, _id });
//   console.log('Done Checking')
// });

serviceRequirementSpecifySchema.plugin(toJSON);
serviceRequirementSpecifySchema.plugin(paginate);

const ServiceRequirementSpecify = mongoose.model('ServiceRequirementSpecify', serviceRequirementSpecifySchema);

module.exports = ServiceRequirementSpecify;
