const Async = require('async');
const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const Order = require('../order.model');
const ServiceOffer = require('../service.offer.model');
const { businessEnums } = require('../../config/constants');

const varName = 'Add revisionsMaxCount to existing orders';

const addRevisionsMaxCountToExistingOrders = async () => {
  const migrated = await GlobalVariable.findOne({ name: varName });

  if (migrated) {
    return;
  }

  const orders = await Order.find();

  const { requiredOffers } = businessEnums;

  await Async.eachSeries(orders, async (order) => {
    const offers = (
      await ServiceOffer.find({
        service: order.service,
        $or: [{ name: [requiredOffers.COMPLETION_DAYS[0]] }, { name: requiredOffers.MAXIMUM_REVISIONS[0] }],
      }).select({
        value: { $elemMatch: { plan: order.plan } },
        name: 1,
      })
    ).reduce((acc, offer) => ({ ...acc, [offer.name]: offer.value[0].offer }), {});

    // eslint-disable-next-line no-param-reassign
    order.revisionsMaxCount = Number(offers[requiredOffers.MAXIMUM_REVISIONS[0]]);
    await order.save();

    logger.info(`Add revisionsMaxCount to order #${order.orderNumber}`);
  });

  await GlobalVariable.create({ name: varName, value: 'true' });
  logger.info('Added revisionsMacCount to existing orders');
};

module.exports = addRevisionsMaxCountToExistingOrders;
