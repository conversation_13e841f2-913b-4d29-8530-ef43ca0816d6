const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const { schoolsEnums } = require('../config/constants');

const scholarshipSchema = new mongoose.Schema(
  {
    name: { type: String, trim: true, required: true, unique: true },
    uniqueName: { type: String, trim: true, unique: true },
    // scholarshipType: { type: String, trim: true, enum: Object.values(schoolsEnums.scholarshipTypes) },
    fundingType: { type: String, trim: true, enum: Object.values(schoolsEnums.fundingTypes) },
    description: { type: String, trim: true },
    hostCountry: { type: String, trim: true },
    eligibleCountries: { type: [String] },
    startDate: { type: Date },
    deadline: { type: Date },
    deadlineVaries: { type: Boolean, default: false },
    durationType: { type: String, enum: Object.values(schoolsEnums.durationTypes) },
    linkToApply: { type: String, trim: true },
    status: {
      type: String,
      default: schoolsEnums.scholarshipStatuses.OPEN,
      enum: Object.values(schoolsEnums.scholarshipStatuses),
    },
    programTypes: [{ type: String, enum: Object.values(schoolsEnums.programTypes) }],
    programs: [{ type: String }],
    logo: { type: mongoose.Schema.Types.ObjectId, ref: 'File' },
  },
  { timestamps: true },
);

scholarshipSchema.index({
  fundingType: 'text',
  programType: 'text',
  status: 'text',
});

scholarshipSchema.index({
  name: 1,
  deadline: 1,
  createdAt: 1,
  updatedAt: 1,
});

scholarshipSchema.plugin(toJSON);
scholarshipSchema.plugin(paginate);

const Scholarship = mongoose.model('Scholarship', scholarshipSchema);

module.exports = Scholarship;
