module.exports = {
  get: {
    tags: ['Users'],
    summary: 'Get followership counts for a user',
    parameters: [
      {
        name: 'id',
        in: 'path',
        description: 'ID of the user to retrieve followership counts',
        required: true,
        schema: {
          type: 'string',
          format: 'objectid',
        },
      },
    ],
    responses: {
      200: {
        description: 'Followership counts retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                data: {
                  type: 'object',
                  properties: {
                    followers: { type: 'integer', example: 5 },
                    following: { type: 'integer', example: 5 },
                  },
                },
                status: { type: 'string' },
              },
            },
          },
        },
      },
    },
  },
};
