const addResourceInfoSwaggerPath = {
  post: {
    tags: ['Profile'],
    summary: 'Add Resource Info to Profile',
    description: 'Adds resource information to a profile',
    parameters: [
      {
        name: 'resourceName',
        in: 'query',
        description: 'Name of the resource to add (e.g., Education, Certification, Project, etc.)',
        required: true,
        schema: {
          type: 'string',
          enum: ['Education', 'Certification', 'Project', 'Award', 'Volunteering', 'Experience', 'TestScore'],
        },
      },
    ],
    requestBody: {
      required: true,
      content: {
        'application/json': {
          schema: {
            oneOf: [
              { $ref: '#/components/schemas/Award' },
              { $ref: '#/components/schemas/Certification' },
              { $ref: '#/components/schemas/Education' },
              { $ref: '#/components/schemas/TestScore' },
              { $ref: '#/components/schemas/Experience' },
              { $ref: '#/components/schemas/Volunteering' },
              { $ref: '#/components/schemas/Project' },
            ],
          },
        },
      },
    },
    responses: {
      201: {
        description: 'Success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: {
                  type: 'string',
                },
                message: {
                  type: 'string',
                },
              },
            },
          },
        },
      },
    },
  },
};

module.exports = addResourceInfoSwaggerPath;
