module.exports = {
  get: {
    summary: 'Get group membership requests',
    description: 'Retrieve membership requests for a group. Only administrators can perform this action.',
    tags: ['Groups'],
    parameters: [
      {
        in: 'path',
        name: 'id',
        required: true,
        description: 'ID of the group',
        schema: {
          type: 'string',
          example: '507f1f77bcf86cd799439011',
        },
      },
      {
        in: 'query',
        name: 'sortBy',
        description: 'Sort members by a specific field',
        schema: {
          type: 'string',
          example: 'createdAt:desc',
        },
      },
      {
        in: 'query',
        name: 'limit',
        description: 'Limit the number of members per page',
        schema: {
          type: 'integer',
          example: 10,
        },
      },
      {
        in: 'query',
        name: 'page',
        description: 'Page number for pagination',
        schema: {
          type: 'integer',
          example: 2,
        },
      },
    ],
    responses: {
      200: {
        description: 'Membership requests retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string' },
                data: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      firstName: { type: 'string', example: 'Viek' },
                      lastName: { type: 'string', example: 'Urr' },
                      middleName: { type: 'string', example: 'Sr.' },
                      username: { type: 'string', example: 'viv33' },
                      email: { type: 'string', example: '<EMAIL>' },
                      id: { type: 'string', example: '507f1f77bcf86cd799439011' },
                      photo: {
                        type: 'object',
                        properties: { url: { type: 'string', example: 'google.com/image.jpg' } }, // Corrected property definition
                      },
                    },
                  },
                },
                message: { type: 'string', example: 'Group membership requests retrieved successfully' },
              },
            },
          },
        },
      },
      403: {
        description: 'Only administrators can perform this action',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string', example: 'FAILED' },
                message: { type: 'string', example: 'Only administrators can perform this action' },
              },
            },
          },
        },
      },
      404: {
        description: 'Group not found',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string', example: 'FAILED' },
                message: { type: 'string', example: 'Group not found' },
              },
            },
          },
        },
      },
    },
  },
};
