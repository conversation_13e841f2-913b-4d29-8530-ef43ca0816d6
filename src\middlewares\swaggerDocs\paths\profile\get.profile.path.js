module.exports = {
  get: {
    summary: 'Get user profile',
    tags: ['Profile'],
    responses: {
      200: {
        description: 'Profile retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string', description: 'Status of the operation', example: 'SUCCESS' },
                data: {
                  type: 'object',
                  $ref: '#/components/schemas/Profile',
                },
                message: { type: 'string', description: 'Success message', example: 'Profile retrieved successfully' },
              },
            },
          },
        },
      },
      404: {
        description: 'Not Found',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string', description: 'Status of the operation', example: 'NOT_FOUND' },
                message: {
                  type: 'string',
                  description: 'Error message for profile not found',
                  example: 'Profile not found',
                },
              },
            },
          },
        },
      },
    },
  },
};
