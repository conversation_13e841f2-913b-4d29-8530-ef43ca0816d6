const objectId = (value, helpers) => {
  if (!value?.match(/^[0-9a-fA-F]{24}$/)) {
    if (process.env.ENVIRON === 'development') {
      return helpers?.message('"{{#label}}" must be a valid mongo id');
    }
    return helpers?.message('An error occurred');
  }
  return value;
};

const password = (value, helpers) => {
  if (value.length < 8) {
    return helpers.message('password must be at least 8 characters');
  }
  if (!value.match(/\d/) || !value.match(/[a-zA-Z]/)) {
    return helpers.message('password must contain at least 1 letter and 1 number');
  }
  return value;
};

module.exports = {
  objectId,
  password,
};
