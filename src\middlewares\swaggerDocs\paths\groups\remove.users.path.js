module.exports = {
  patch: {
    summary: 'Remove user(s) from a group',
    tags: ['Groups'],
    parameters: [
      {
        in: 'path',
        name: 'id',
        description: 'ID of the group to handle membership request for',
        required: true,
        schema: {
          type: 'string',
          example: '507f1f77bcf86cd799439011',
        },
      },
    ],
    requestBody: {
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              users: {
                type: 'array',
                items: {
                  type: 'string',
                  example: '5f9d1e9a6f3f4b001f6d4c0b',
                },
              },
            },
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Operation successful',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Operation successful',
            },
          },
        },
      },
    },
  },
};
