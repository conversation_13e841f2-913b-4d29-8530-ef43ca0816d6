const mongoose = require('mongoose');
const httpStatus = require('http-status');
const multer = require('multer');
const config = require('../config/config');
const logger = require('../config/logger');
const ApiError = require('../utils/ApiError');
// const { logEvents } = require('./logger');

const errorConverter = (err, req, res, next) => {
  let error = err;
  if (!(error instanceof ApiError)) {
    const statusCode =
      error.statusCode || error instanceof mongoose.Error || error instanceof multer.MulterError
        ? httpStatus.BAD_REQUEST
        : httpStatus.INTERNAL_SERVER_ERROR;
    const message = error.message || httpStatus[statusCode];
    const status = error.status || 'FAILED';
    error = new ApiError(statusCode, message, false, err.stack, status);
  }
  next(error);
};

// eslint-disable-next-line no-unused-vars
const errorHandler = (err, req, res, next) => {
  let { statusCode, message } = err;
  if (config.env === 'development' && !process.env.ENVIRON === 'development' && !err.isOperational) {
    statusCode = httpStatus.INTERNAL_SERVER_ERROR;
    message = httpStatus[httpStatus.INTERNAL_SERVER_ERROR];
  }

  res.locals.errorMessage = err.message;

  const response = {
    // code: statusCode,
    message,
    status: err.status,
    ...(config.env === 'development' && { stack: err.stack }),
  };

  if (
    statusCode === httpStatus.INTERNAL_SERVER_ERROR ||
    config.env === 'development' ||
    process.env.ENVIRON === 'development'
  ) {
    logger.error(err);
  }

  // logEvents(`${err.name}: ${err.message}\t${req.method}\t${req.url}\t${req.headers.origin}`, 'errLog.log');
  res.status(statusCode).send(response);
};

module.exports = {
  errorConverter,
  errorHandler,
};
