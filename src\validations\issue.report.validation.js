const Joi = require('joi');
const { issueReport } = require('../config/constants');
const { fileSchema } = require('./file.validation');

const createIssueReport = {
  body: Joi.object().keys({
    title: Joi.string().required(),
    description: Joi.string().required(),
  }),
  files: Joi.object().keys({
    attachment: Joi.array().items(fileSchema()),
  }),
};

const getIssueReports = {
  query: Joi.object().keys({
    title: Joi.string(),
    type: Joi.string().valid(...Object.values(issueReport.types)),
    priority: Joi.string().valid(...Object.values(issueReport.priorities)),
    status: Joi.string().valid(...Object.values(issueReport.statuses)),
    reporter: Joi.string(),
    assignedTo: Joi.string(),
    resolvedBy: Joi.string(),
    searchText: Joi.string(),
    sortBy: Joi.string()
      .valid('createdAt:asc', 'createdAt:desc', 'updatedAt:asc', 'updatedAt:desc', 'resolvedAt:asc', 'resolvedAt:desc')
      .default('createdAt:desc'),
    limit: Joi.number().integer(),
    page: Joi.number().integer(),
  }),
};

const updateIssueReport = {
  body: Joi.object().keys({
    type: Joi.string().valid(...Object.values(issueReport.types)),
    priority: Joi.string().valid(...Object.values(issueReport.priorities)),
    status: Joi.string().valid(...Object.values(issueReport.statuses)),
    resolvedBy: Joi.string().when('status', {
      is: issueReport.statuses.RESOLVED,
      then: Joi.string().required(),
    }),
    assignedTo: Joi.string().when('status', {
      is: issueReport.statuses.IN_PROGRESS,
      then: Joi.string().required(),
    }),
    closedBy: Joi.string().when('status', {
      is: issueReport.statuses.CLOSED,
      then: Joi.string().required(),
    }),
  }),
};

module.exports = {
  createIssueReport,
  getIssueReports,
  updateIssueReport,
};
