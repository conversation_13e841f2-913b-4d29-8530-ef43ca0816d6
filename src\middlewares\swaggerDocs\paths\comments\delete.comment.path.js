module.exports = {
  delete: {
    tags: ['Comments'],
    summary: 'Delete a comment by ID',
    description: 'Deletes a comment by its unique identifier.',
    parameters: [
      {
        in: 'path',
        name: 'id',
        required: true,
        description: 'ID of the comment to delete',
        schema: { type: 'string', example: '659fd7434857f088ed7d3bc5' },
      },
    ],
    responses: {
      200: {
        description: 'Comment Deleted successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Comment deleted successfully' },
                status: { type: 'string', example: 'SUCCESS' },
              },
            },
          },
        },
      },
    },
  },
};
