/* eslint-disable no-param-reassign */
const Async = require('async');
const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const Business = require('../business.model');
const Order = require('../order.model');
const Service = require('../service.model');

const varName = 'Make Orders in Business Unique';

const makeOrdersInBusinessUnique = async () => {
  const migrated = await GlobalVariable.findOne({ name: varName });

  if (migrated) {
    return;
  }

  await Business.updateMany({}, { $set: { orders: [] } });

  logger.info('Reset business.orders array');
  const orders = await Order.find().populate('service');

  await Async.eachSeries(orders, async (order) => {
    await Business.findOneAndUpdate({ _id: order.service.provider }, { $addToSet: { orders: order._id } });
    await Service.findOneAndUpdate({ _id: order.service }, { $addToSet: { orders: order._id } });

    await Order.updateOne({ _id: order._id }, { $set: { provider: order.service.provider } });
  });

  await GlobalVariable.create({ name: varName, value: 'true' });
  logger.info('Make Orders in Business Unique completed');
};

module.exports = makeOrdersInBusinessUnique;
