const testScoreSchemaSwagger = {
  type: 'object',
  properties: {
    id: { type: 'string', example: '6009c0eee65f6dce28fb3e50' },
    testName: {
      type: 'string',
      description: 'Name of the test score award',
      example: 'Outstanding Performance Award',
    },
    issuingBody: {
      type: 'string',
      description: 'Organization issuing the test score',
      example: 'ABC Educational Institute',
    },
    date: {
      type: 'string',
      description: 'Date when the test score was received',
      example: '2023-11-25',
    },
    testScore: {
      type: 'string',
      description: 'score of the test taken',
      example: '7.5',
    },
  },
};

module.exports = testScoreSchemaSwagger;
