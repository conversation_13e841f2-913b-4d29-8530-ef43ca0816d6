const updateBasicInformationSwaggerPath = {
  patch: {
    tags: ['Profile'],
    summary: 'Update Basic Information of a Profile',
    requestBody: {
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              basicInformation: {
                type: 'object',
                properties: {
                  personalStatement: {
                    type: 'string',
                  },
                },
              },
              skills: {
                type: 'array',
                items: {
                  type: 'string',
                },
                uniqueItems: true,
              },
              hobbies: {
                type: 'array',
                items: {
                  type: 'string',
                },
                uniqueItems: true,
              },
            },
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: {
                  type: 'string',
                },
                message: {
                  type: 'string',
                },
              },
            },
          },
        },
      },
    },
  },
};

module.exports = updateBasicInformationSwaggerPath;
