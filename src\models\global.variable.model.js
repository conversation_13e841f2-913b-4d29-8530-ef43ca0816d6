const mongoose = require('mongoose');
const { toJSON } = require('./plugins');

const globalVariableSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      unique: true,
    },
    value: {
      type: String,
      default: 'true',
    },
  },
  {
    timestamps: true,
  },
);

// add plugin that converts mongoose to json
globalVariableSchema.plugin(toJSON);

const GlobalVariable = mongoose.model('GlobalVariable', globalVariableSchema);

module.exports = GlobalVariable;
