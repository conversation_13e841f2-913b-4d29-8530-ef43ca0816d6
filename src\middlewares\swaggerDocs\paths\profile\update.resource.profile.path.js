const updateResourceInfoSwaggerPath = {
  patch: {
    tags: ['Profile'],
    summary: 'Update Resource Info in Profile',
    description: 'Updates resource information in a profile',
    parameters: [
      {
        name: 'id',
        in: 'path',
        description: 'ID of the resource to update',
        required: true,
        type: 'string',
        format: 'objectId',
      },
      {
        name: 'resourceName',
        in: 'query',
        description: 'Name of the resource to update (e.g., Education, Certification, Project, etc.)',
        required: true,
        schema: {
          type: 'string',
          enum: ['Education', 'Certification', 'Project', 'Award', 'Volunteering', 'Experience', 'TestScore'],
        },
      },
    ],
    requestBody: {
      required: true,
      content: {
        'application/json': {
          schema: {
            oneOf: [
              { $ref: '#/components/schemas/Award' },
              { $ref: '#/components/schemas/Certification' },
              { $ref: '#/components/schemas/Education' },
              { $ref: '#/components/schemas/TestScore' },
              { $ref: '#/components/schemas/Experience' },
              { $ref: '#/components/schemas/Volunteering' },
              { $ref: '#/components/schemas/Project' },
            ],
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: {
                  type: 'string',
                },
                message: {
                  type: 'string',
                },
              },
            },
          },
        },
      },
    },
  },
};

module.exports = updateResourceInfoSwaggerPath;
