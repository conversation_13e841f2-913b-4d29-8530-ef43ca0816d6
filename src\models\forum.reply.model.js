const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');

const forumReplySchema = new mongoose.Schema(
  {
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    forumPost: { type: mongoose.SchemaTypes.ObjectId, ref: 'ForumPost', required: true },
    text: { type: String, required: true, index: true },
    media: [{ type: mongoose.Schema.Types.ObjectId, ref: 'File' }],
    upvotes: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
    downvotes: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
    accepted: { type: Boolean, default: false },
  },
  { timestamps: true },
);

forumReplySchema.index({ createdAt: 1, updatedAt: 1, upvotes: 1, downvotes: 1 });

forumReplySchema.plugin(toJSON);
forumReplySchema.plugin(paginate);

const ForumReply = mongoose.model('ForumReply', forumReplySchema);
module.exports = ForumReply;
