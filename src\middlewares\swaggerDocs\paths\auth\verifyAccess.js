module.exports = {
  get: {
    summary: 'Verify Access Tokens',
    description: 'Verify the validity of access and refresh tokens',
    tags: ['Authentication'],
    parameters: [
      {
        in: 'query',
        name: 'accessToken',
        required: true,
        schema: {
          type: 'string',
          example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        },
        description: 'Access token to be verified',
      },
      {
        in: 'query',
        name: 'refreshToken',
        required: true,
        schema: {
          type: 'string',
          example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        },
        description: 'Refresh token to be verified',
      },
    ],
    responses: {
      200: {
        description: 'Successful operation',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string', example: 'SUCCESS' },
                message: { type: 'string', example: 'Token verification successful' },
                data: {
                  type: 'object',
                  properties: {
                    access: { type: 'string', example: 'valid' },
                    refresh: { type: 'string', example: 'valid' },
                  },
                },
              },
            },
          },
        },
      },
      403: {
        description: 'Invalid token',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string', example: 'FAILED' },
                message: { type: 'string', example: 'Invalid token' },
                data: {
                  type: 'object',
                  properties: {
                    access: { type: 'string', example: 'invalid' },
                    refresh: { type: 'string', example: 'invalid' },
                  },
                },
              },
            },
          },
        },
      },
    },
  },
};
