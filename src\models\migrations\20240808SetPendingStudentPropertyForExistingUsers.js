/* eslint-disable no-param-reassign */
const Async = require('async');
const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const { User } = require('..');
const { registrationStatuses } = require('../../config/constants');

const setPendingStudentProperty = async () => {
  const migrated = await GlobalVariable.findOne({ name: 'Set pendingStudent property for existing users' });

  if (migrated) {
    return;
  }

  const allUsers = await User.find();

  await Async.eachSeries(allUsers, async (user) => {
    if (!user.business) {
      if (
        user.registrationStatus === registrationStatuses.SIGNUP_COMPLETED ||
        user.registrationStatus === registrationStatuses.ACCOUNT_VERIFIED
      ) {
        user.pendingStudent = 'true';
      } else {
        user.pendingStudent = 'false';
      }
    }
    await user.save();
  });

  await GlobalVariable.create({ name: 'Set pendingStudent property for existing users', value: 'true' });
  logger.info('pendingStudent property set for existing users');
};

module.exports = setPendingStudentProperty;
