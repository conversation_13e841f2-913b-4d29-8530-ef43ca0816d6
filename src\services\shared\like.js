const httpStatus = require('http-status');
const ApiError = require('../../utils/ApiError');
const activityLogService = require('../activity.log.service');
const notificationService = require('../notification.service');
const { User } = require('../../models');

const getActionType = (resourceName) => {
  switch (resourceName) {
    case 'comment':
      return 'COMMENT_LIKE';
    case 'post':
      return 'POST_LIKE';
    default:
      throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid activity type');
  }
};

const likeAnItem = async (resourceModel, resourceId, user) => {
  const resource = await resourceModel.findById(resourceId).populate([
    {
      path: 'user',
      select: 'firstName lastName middleName username photo _id tagLine',
      populate: { path: 'photo', select: 'url -_id' },
    },
  ]);
  if (!resource) {
    throw new ApiError(httpStatus.NOT_FOUND, `${resourceModel.modelName} not found`);
  }

  const liked = resource.likes.includes(user.id);
  if (liked) {
    resource.likes.pull(user.id);
    user.postLikes.pull(resource.id);
    await resource.save();
  } else {
    resource.likes.push(user.id); // If there's call to save, this line and the next calling updateOne should not be combined
    await resourceModel.updateOne(
      { _id: resourceId },
      { $set: { likes: [...new Set([...resource.likes, user.id].map((like) => like.toString()))] } },
    );
    if (resourceModel.modelName === 'Post') {
      user.postLikes.push(resource.id); // If there's call to save, this line and the next calling updateOne should not be combined
      await User.updateOne(
        { _id: user.id },
        { $set: { postLikes: [...new Set([...user.postLikes, resource.id].map((like) => like.toString()))] } },
      );
    }

    const actionType = getActionType(resourceModel.modelName.toLowerCase());
    await activityLogService.logActivity(user._id, actionType, resource._id);

    const details = { resourceId: resource.id, resourceName: resourceModel.modelName };
    const recipient = await User.findById(resource.user._id);
    await notificationService.createLikeNotification(user, recipient, details);
  }

  // await resource.save();
  await user.save();
  return resource;
};

module.exports = likeAnItem;
