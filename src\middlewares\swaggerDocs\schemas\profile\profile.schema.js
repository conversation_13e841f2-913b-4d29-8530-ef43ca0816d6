const profileSwaggerSchema = {
  type: 'object',
  properties: {
    id: { type: 'string', example: '6009c0eee65f6dce28fb3e50' },
    skills: {
      type: 'array',
      items: {
        type: 'string',
        example: 'JavaScript',
      },
      description: 'List of skills',
    },
    hobbies: {
      type: 'array',
      items: {
        type: 'string',
        example: 'Hiking',
      },
      description: 'List of hobbies',
    },
    award: {
      type: 'array',
      items: { $ref: '#/components/schemas/Award' },
    },
    certification: {
      type: 'array',
      items: { $ref: '#/components/schemas/Certification' },
    },
    education: {
      type: 'array',
      items: { $ref: '#/components/schemas/Education' },
    },
    testScore: {
      type: 'array',
      items: { $ref: '#/components/schemas/TestScore' },
    },
    experience: {
      type: 'array',
      items: { $ref: '#/components/schemas/Experience' },
    },
    volunteering: {
      type: 'array',
      items: { $ref: '#/components/schemas/Volunteering' },
    },
    project: {
      type: 'array',
      items: { $ref: '#/components/schemas/Project' },
    },
  },
};

module.exports = profileSwaggerSchema;
