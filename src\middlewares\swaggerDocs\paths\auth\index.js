const login = require('./login');
const logout = require('./logout');
const refresh = require('./refresh');
const signup = require('./signup');
const emailVerification = require('./emailVerification');
const resetPassword = require('./resetPassword');
const usernameEmailAvailability = require('./usernameEmailAvailability');
const oauth = require('./oauth');
const verifyAccess = require('./verifyAccess');

module.exports = {
  '/auth/login': { ...login },
  '/auth/logout': { ...logout },
  '/auth/refresh': { ...refresh },
  '/auth/signup': { ...signup },
  '/auth/username-email-avail': { ...usernameEmailAvailability },
  '/verify-access': { ...verifyAccess },
  ...emailVerification,
  ...resetPassword,
  ...oauth,
};
