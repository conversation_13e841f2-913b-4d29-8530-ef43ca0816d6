module.exports = {
  patch: {
    summary: 'Change User Profile Photo by ID',
    tags: ['Users'],
    description: 'Change the profile photo of a user by their ID.',
    parameters: [
      {
        name: 'id',
        in: 'path',
        required: true,
        description: 'User ID to change profile photo',
        schema: {
          type: 'string',
          example: '507f1f77bcf86cd799439011',
        },
      },
    ],
    requestBody: {
      required: true,
      content: {
        'multipart/form-data': {
          schema: {
            type: 'object',
            properties: {
              file: {
                type: 'string',
                format: 'binary',
              },
            },
          },
        },
      },
    },
    responses: {
      200: {
        description: 'User profile photo changed successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  example: 'User profile photo changed',
                },
                status: {
                  type: 'string',
                  example: 'SUCCESS',
                },
              },
            },
          },
        },
      },
      400: {
        description: 'Bad Request. Invalid profile photo change request',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  example: 'Invalid profile photo change request',
                },
                status: {
                  type: 'string',
                  example: 'FAILED',
                },
              },
            },
          },
        },
      },
    },
  },
};
