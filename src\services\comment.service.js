const httpStatus = require('http-status');
const Async = require('async');
const { Comment, Post, File, User, Reaction } = require('../models');
const ApiError = require('../utils/ApiError');
const { uploadAndSaveMultipleBlobs, deleteManyFiles } = require('./azure.file.service');
const NotificationService = require('./notification.service');
const ActivityLogService = require('./activity.log.service');
const { azureContainers } = require('../config/constants');

const getActionType = (resourceName) => {
  switch (resourceName) {
    case 'comment':
      return 'COMMENT_COMMENT';
    case 'post':
      return 'POST_COMMENT';
    default:
      throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid activity type');
  }
};

const createComment = async (req) => {
  const [data, files] = [{ ...req.body, user: req.user._id }, req.files];
  const commentBody = { ...data, post: data.postId, parentComment: data.parentCommentId };

  let parentComment;
  if (commentBody.parentComment) {
    parentComment = await Comment.findById(commentBody.parentComment).populate('user');
    if (!parentComment) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Parent comment not found');
    }
    if (parentComment.post.toString() !== commentBody.post.toString()) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Parent comment mismatch');
    }
  }

  const post = await Post.findById(commentBody.postId);
  const postAuthor = await User.findById(post.user);
  if (!post) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Post not found');
  }
  if (files && files.length > 0) {
    // Upload new file to cloudinary
    commentBody.media = await uploadAndSaveMultipleBlobs(files, azureContainers.postComments);
  }

  const comment = await Comment.create(commentBody);
  const recipient = await User.findById(post.user);
  if (parentComment) {
    parentComment.replies.push(comment._id);
    await parentComment.save();
    const details = {
      commentId: comment._id,
      secondRecipient: parentComment.user,
      resourceName: 'Comment',
      postId: post._id,
      post,
      parentCommentId: parentComment._id,
    };
    await NotificationService.createCommentNotification(req.user, recipient, details);
    const actionType = getActionType(Comment.modelName.toLowerCase());
    await ActivityLogService.logActivity(req.user._id, actionType, comment.post);
  } else {
    post.comments.push(comment._id);
    const details = { commentId: comment._id, resourceName: 'Post', postId: post._id };
    await NotificationService.createCommentNotification(req.user, postAuthor, details);
    const actionType = getActionType(Post.modelName.toLowerCase());
    await ActivityLogService.logActivity(req.user._id, actionType, post._id);
  }
  post.allCommentsCount += 1;
  await post.save();

  const details = { resourceName: 'comment', resourceId: comment._id };
  const mentions = req.body.text.match(/@(\w+)/g);
  if (mentions?.length) {
    await Promise.all(
      mentions.map(async (mention) => {
        const username = mention.slice(1);
        const mentionedUser = await User.findOne({ username });
        if (mentionedUser) {
          await NotificationService.createMentionNotification(req.user, mentionedUser, details);
        }
      }),
    );
  }
  return comment;
};

const queryComments = async (filter, options) => {
  // eslint-disable-next-line global-require
  const { basicUserPopulate } = require('./user.service');
  const comments = await Comment.paginate(filter, {
    ...options,
    populate: [
      {
        path: 'user',
        ...basicUserPopulate,
      },
      { path: 'media', select: 'url -_id' },
      {
        path: 'reactions',
        select: 'user type',
      },
    ],
  });
  // Rename createdAt to createdOn. createdAt is auto removed by toJSON plugin
  comments.results.forEach((comment, index) => {
    comments.results[index]._doc.createdOn = comment.createdAt;
    comments.results[index]._doc.updatedOn = comment.updatedAt;
  });
  return comments;
};

const getCommentById = async (commentId) => {
  const comment = await Comment.findById(commentId).populate([
    'media',
    {
      path: 'reactions',
      select: 'user type',
    },
    {
      path: 'user',
      select: 'firstName lastName middleName username photo _id tagLine',
      populate: { path: 'photo', select: 'url -_id' },
    },
  ]);
  if (!comment) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Comment not found');
  }
  comment.createdOn = comment.createdAt;
  comment.updatedOn = comment.updatedAt;
  return comment;
};

const updateCommentById = async (req) => {
  const { files, resourceRecord, body: updateBody } = req;
  const comment = resourceRecord || (await Post.findById(req.params.id)); // resourceRecord is from canManageResource middleware

  if (!comment) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Comment record not found');
  }
  if (updateBody.deletedUrls) {
    await Async.eachOfSeries(updateBody.deletedUrls.split(','), async (url) => {
      // eslint-disable-next-line no-useless-escape
      const urlExtract = url.match(/^(https?:\/\/[^\/\s]+\/[^\?\s]*)/)[0]?.replace(/\/$/, '');
      const file = await File.findOne({ url: urlExtract });
      if (file) {
        comment.media = comment.media.filter((fileId) => fileId.toString() !== file._id.toString());
        file.remove(); // No need to wait for this to finish. Success is not important
      }
    });
  }

  try {
    if (files && files.length > 0) {
      const fileIds = await uploadAndSaveMultipleBlobs(files, azureContainers.postComments);
      updateBody.media = [...comment.media, ...fileIds];
    }

    Object.assign(comment, updateBody);
    await comment.save();
    return comment;
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error updating comment');
  }
};

const deleteCommentById = async (commentId, resourceRecord) => {
  const comment = resourceRecord || (await Comment.findById(commentId));

  if (!comment) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Comment not found');
  }

  const post = await Post.findById(comment.post);
  post.comments.pull(comment._id);
  post.allCommentsCount -= 1;

  if (comment.media && comment.media.length > 0) {
    await deleteManyFiles(comment.media);
  }

  if (comment.parentComment) {
    const parentComment = await Comment.findById(comment.parentComment);
    parentComment.replies.pull(comment._id);
    await parentComment.save();
  }

  await Reaction.deleteMany({ comment: comment._id });
  await Promise.all(
    comment.replies.map(async (reply) => {
      await Comment.findByIdAndRemove(reply);
      post.allCommentsCount -= 1;
    }),
  );

  await post.save();
  await comment.remove();
  return comment;
};

module.exports = {
  createComment,
  queryComments,
  getCommentById,
  updateCommentById,
  deleteCommentById,
};
