// const httpStatus = require('http-status');
// const catchAsync = require('../utils/catchAsync');
// const { businessService } = require('../services');

// const createBusiness = catchAsync(async (req, res) => {
//   const business = await businessService.createBusiness(req.user, req.body, req.files);
//   res.status(httpStatus.CREATED).json({ status: 'SUCCESS', data: business, message: 'Business created successfully' });
// });

// module.exports = {
//   createBusiness,
// };
