const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { serviceService, serviceVerificationService } = require('../services');
const { pick, removeUndefinedKeys } = require('../utils/pick');

const createService = catchAsync(async (req, res) => {
  if (!req.user.business) {
    res.status(httpStatus.UNAUTHORIZED).json({ status: 'FAILED', message: 'Only business owner can perform action' });
  }

  const service = await serviceService.createService({ ...req.body, provider: req.user.business });
  res.status(httpStatus.CREATED).json({ status: 'SUCCESS', data: service, message: 'Service created successfully' });
});

const getService = catchAsync(async (req, res) => {
  const { referralCode } = req.query;
  const service = await serviceService.getService(req.params.id, referralCode, req.user);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: service, message: 'Service retrieved successfully' });
});

const getServiceExternal = catchAsync(async (req, res) => {
  // public service detail page
  const service = await serviceService.getService(req.params.id);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: service, message: 'Service retrieved successfully' });
});

const getServices = catchAsync(async (req, res) => {
  const filter = pick(req.query, [
    'searchText',
    'status',
    'provider',
    'createdAtStartDate',
    'createdAtEndDate',
    'minimumRating',
    'maximumRating',
    'category', // Array
  ]);
  const offerFilter = pick(req.query, [
    'minimumPrice',
    'maximumPrice',
    'minimumCompletionDays',
    'maximumCompletionDays',
    'planType', // Array
  ]);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);

  const services = await serviceService.getServices(req?.user, filter, options, offerFilter);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: services, message: 'Services retrieved successfully' });
});

const updateService = catchAsync(async (req, res) => {
  const service = await serviceService.updateService(req.user.business, req.params.id, req.body);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: service, message: 'Service updated successfully' });
});

const manageSavedServices = catchAsync(async (req, res) => {
  const status = await serviceService.manageSavedServices(req.user, req.params.id);
  const message = status ? 'Service saved successfully' : 'Service removed successfully';
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message });
});

const getSavedServices = catchAsync(async (req, res) => {
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const services = await serviceService.getSavedServices(req.user, options);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: services, message: 'Saved services retrieved successfully' });
});

const deleteService = catchAsync(async (req, res) => {
  await serviceService.deleteService(req.user, req.params.id);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Service deleted successfully' });
});

// Reviews

const getReviews = catchAsync(async (req, res) => {
  const filter = pick(req.query, ['service', 'minRating', 'maxRating', 'provider']);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  removeUndefinedKeys(filter);

  const reviews = await serviceService.getReviews(filter, options);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: reviews, message: 'Reviews retrieved successfully' });
});

const addReview = catchAsync(async (req, res) => {
  const review = await serviceService.addReview(req.user, req.body);
  res.status(httpStatus.CREATED).json({ status: 'SUCCESS', data: review, message: 'Review added successfully' });
});

const getReview = catchAsync(async (req, res) => {
  const review = await serviceService.getReview(req.params.id);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: review, message: 'Review retrieved successfully' });
});

const updateReview = catchAsync(async (req, res) => {
  const review = await serviceService.updateReview(req.params.id, req.user.id, req.body);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: review, message: 'Review updated successfully' });
});

const deleteReview = catchAsync(async (req, res) => {
  await serviceService.deleteReview(req.params.id, req.user.id);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Review deleted successfully' });
});

const addNewService = catchAsync(async (req, res) => {
  const service = await serviceService.addNewService(req);
  res.status(httpStatus.CREATED).json({ status: 'SUCCESS', data: service, message: 'Service created successfully' });
});

// service offers
const getServiceOffers = catchAsync(async (req, res) => {
  const data = await serviceService.getServiceOffers(req.params?.id, req.query?.planType);
  res.status(httpStatus.CREATED).json({ status: 'SUCCESS', data, message: 'Service offers retrieved successfully' });
});

// const fetchUnverifiedServices = catchAsync(async (req, res) => {
//   const options = pick(req.query, ['sortBy', 'limit', 'page']);
//   const filter = pick(req.query, ['verificationStatus', 'displayName', 'name']);
//   const services = await serviceVerificationService.fetchUnverifiedServices(filter, options);
//   res.status(httpStatus.OK).json({ status: 'SUCCESS', data: services, message: 'Services retrieved successfully' });
// });

const fetchServiceVerifications = catchAsync(async (req, res) => {
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const filter = pick(req.query, ['verificationStatus', 'displayName', 'serviceName']);
  const serviceVerifications = await serviceVerificationService.fetchServiceVerifications(filter, options);
  res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data: serviceVerifications, message: 'Service verifications retrieved successfully' });
});

const fetchServiceVerificationInfo = catchAsync(async (req, res) => {
  const serviceVerificationInfo = await serviceVerificationService.fetchServiceVerificationInfo(req.params.id);
  res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data: serviceVerificationInfo, message: 'Service verification retrieved successfully' });
});

const assignServiceVerifier = catchAsync(async (req, res) => {
  const { verifier, parentVerification } = req.body;
  await serviceVerificationService.assignServiceVerifier(req.params.id, req.user.id, verifier, parentVerification);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Verifier assigned successfully' });
});

const verifyService = catchAsync(async (req, res) => {
  await serviceVerificationService.verifyService(req.params.id, req.user.id, req.body);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Service verified successfully' });
});

const fetchParentVerification = catchAsync(async (req, res) => {
  const parentVerification = await serviceVerificationService.fetchParentVerification(req.params.id);
  res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data: parentVerification, message: 'Service verification retrieved successfully' });
});

const getRecentlyViewedServices = catchAsync(async (req, res) => {
  const data = await serviceService.getRecentlyViewedServices(req.user);
  return res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data, message: 'Recenlty viewed services retrieved successfully' });
});

const addToRecentlyViewedServices = catchAsync(async (req, res) => {
  await serviceService.addToRecentlyViewedServices(req.params.id, req.user);
  return res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Service added to recenlty viewed services' });
});

module.exports = {
  createService,
  getService,
  getServices,
  updateService,
  manageSavedServices,
  getSavedServices,
  deleteService,
  getServiceExternal,

  getReviews,
  addReview,
  getReview,
  updateReview,
  deleteReview,

  addNewService,

  getServiceOffers,

  fetchServiceVerifications,
  fetchServiceVerificationInfo,
  assignServiceVerifier,
  verifyService,
  fetchParentVerification,
  // fetchUnverifiedServices,
  getRecentlyViewedServices,
  addToRecentlyViewedServices,
};
