module.exports = {
  patch: {
    summary: 'Update a group',
    tags: ['Groups'],
    parameters: [
      {
        in: 'formData',
        name: 'coverPhoto',
        type: 'string',
        format: 'base64',
        required: false,
        description: 'Group Cover Photo',
      },
      {
        in: 'formData',
        name: 'profilePhoto',
        type: 'string',
        format: 'base64',
        required: false,
        description: 'Group Profile Photo',
      },
      {
        in: 'formData',
        name: 'body',
        required: true,
        description: 'Group data',
        schema: {
          type: 'object',
          properties: {
            name: { type: 'string', description: 'Name of the group', example: 'IELTS group' },
            description: {
              type: 'string',
              description: 'Description of the group',
              example: 'This is a group for IELTS students',
            },
            rules: { type: 'string', description: 'group rules', example: 'No spamming. No Insults' },
            type: {
              type: 'string',
              enum: ['public', 'private'],
              default: 'public',
              description: 'Type of the group',
              example: 'public',
            },
            enableInvitation: {
              type: 'boolean',
              default: false,
              description: 'Enable invitation for the group',
              example: false,
            },
          },
        },
      },
    ],
    responses: {
      200: {
        description: 'Group updated successfully',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Group updated successfully',
            },
          },
        },
      },
      400: {
        description: 'Bad request',
        content: {
          'application/json': {
            example: {
              status: 'ERROR',
              message: 'Invalid request data',
            },
          },
        },
      },
      404: {
        description: 'Group not found',
        content: {
          'application/json': {
            example: {
              status: 'ERROR',
              message: 'Group record not found',
            },
          },
        },
      },
    },
  },
};
