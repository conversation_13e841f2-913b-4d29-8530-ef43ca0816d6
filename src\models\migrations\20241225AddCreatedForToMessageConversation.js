const Async = require('async');
const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const MessageConversation = require('../messageConversation.model');
const Message = require('../message.model');
const { messageEnums } = require('../../config/constants');

const varName = 'Add createdFor to Message Conversation';

const addCreatedForToMessageConversation = async () => {
  const migrated = await GlobalVariable.findOne({ name: varName });
  if (migrated) {
    return;
  }

  const conversations = await MessageConversation.find();
  await Async.eachOfSeries(conversations, async (conversation) => {
    const createdFor = [];
    const orderMessages = await Message.find({ conversation: conversation._id, order: { $exists: true } });
    if (orderMessages.length) {
      createdFor.push(messageEnums.createdForValues.ORDER);
    }

    const businessMember = conversation.members.filter((m) => m.business);
    const userMember = conversation.members.filter((m) => m.user);
    if (businessMember.length === 1 && userMember.length === 1) {
      createdFor.push(messageEnums.createdForValues.USER_BUSINESS);
    } else if (userMember.length === 2 || userMember.length === conversation.members.length) {
      createdFor.push(messageEnums.createdForValues.USER_USER);
    }

    await MessageConversation.findByIdAndUpdate(conversation._id, { $addToSet: { createdFor } });
  });

  await GlobalVariable.create({ name: varName, value: 'true' });
  logger.info('CreatedFor added to MessageConversation');
};

module.exports = addCreatedForToMessageConversation;
