const Joi = require('joi');
const { careerJobsEnums, currencies } = require('../config/constants');
const { documentFileSchema } = require('./file.validation');

const addCareerJob = {
  body: {
    title: Joi.string().required(),
    location: Joi.string()
      .valid(...careerJobsEnums.careerJobLocations)
      .required(),
    description: Joi.string().required(),
    applicationEndDate: Joi.date().min(Date.now()),
    proposedAnnualSalary: Joi.string().allow(null),
    currency: Joi.string().valid(...Object.values(currencies)),
    jobType: Joi.string()
      .valid(...Object.values(careerJobsEnums.careerJobType))
      .required(),
    jobCategories: Joi.alternatives()
      .try(
        Joi.string().valid(...Object.values(careerJobsEnums.careerJobCategories)),
        Joi.array().items(Joi.string().valid(...Object.values(careerJobsEnums.careerJobCategories))),
      )
      .required(),
    jobMode: Joi.string()
      .valid(...Object.values(careerJobsEnums.careerJobMode))
      .default(careerJobsEnums.careerJobMode.REMOTE),
    publishStatus: Joi.string()
      .valid(careerJobsEnums.careerJobPublishStatus.PUBLISHED, careerJobsEnums.careerJobPublishStatus.UNPUBLISHED)
      .default(careerJobsEnums.careerJobPublishStatus.PUBLISHED),
  },
};

const getCareerJobs = {
  query: {
    searchText: Joi.string(),
    jobType: Joi.string().valid(...Object.values(careerJobsEnums.careerJobType)),
    jobCategory: Joi.alternatives().try(
      Joi.string().valid(...Object.values(careerJobsEnums.careerJobCategories)),
      Joi.array().items(Joi.string().valid(...Object.values(careerJobsEnums.careerJobCategories))),
    ),
    jobMode: Joi.string().valid(...Object.values(careerJobsEnums.careerJobMode)),
    publishStatus: Joi.string()
      .valid(...Object.values(careerJobsEnums.careerJobPublishStatus))
      .default(careerJobsEnums.careerJobPublishStatus.PUBLISHED),
    title: Joi.string(),
    sortBy: Joi.string()
      .valid(
        'createdAt:asc',
        'createdAt:desc',
        'updatedAt:asc',
        'updatedAt:desc',
        'title:asc',
        'title:desc',
        'currency:asc',
        'currency:desc',
      )
      .default('createdAt:desc'),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
  },
};

const getCareerJob = {
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
};

const getCareerJobsExt = {
  query: {
    jobType: Joi.string().valid(...Object.values(careerJobsEnums.careerJobType)),
    jobCategory: Joi.alternatives().try(
      Joi.string().valid(...Object.values(careerJobsEnums.careerJobCategories)),
      Joi.array().items(Joi.string().valid(...Object.values(careerJobsEnums.careerJobCategories))),
    ),
    jobMode: Joi.string().valid(...Object.values(careerJobsEnums.careerJobMode)),
    title: Joi.string(),
    sortBy: Joi.string()
      .valid(
        'createdAt:asc',
        'createdAt:desc',
        'updatedAt:asc',
        'updatedAt:desc',
        'title:asc',
        'title:desc',
        'currency:asc',
        'currency:desc',
      )
      .default('createdAt:desc'),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
  },
};

const updateCareerJob = {
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
  body: {
    title: Joi.string(),
    location: Joi.string().valid(...careerJobsEnums.careerJobLocations),
    description: Joi.string(),
    applicationEndDate: Joi.date(),
    currency: Joi.string().valid(...Object.values(currencies)),
    jobType: Joi.string().valid(...Object.values(careerJobsEnums.careerJobType)),
    jobCategories: Joi.alternatives().try(
      Joi.string().valid(...Object.values(careerJobsEnums.careerJobCategories)),
      Joi.array().items(Joi.string().valid(...Object.values(careerJobsEnums.careerJobCategories))),
    ),
    jobMode: Joi.string().valid(...Object.values(careerJobsEnums.careerJobMode)),
    publishStatus: Joi.string().valid(
      careerJobsEnums.careerJobPublishStatus.PUBLISHED,
      careerJobsEnums.careerJobPublishStatus.UNPUBLISHED,
    ),
    proposedAnnualSalary: Joi.number().allow(null),
  },
};

const createJobApplication = {
  body: Joi.object().keys({
    careerJob: Joi.string().required(),
    firstName: Joi.string().required(),
    lastName: Joi.string().required(),
    email: Joi.string().email(),
    socialLinks: Joi.string().required(),
  }),
  files: Joi.object().keys({
    resume: Joi.array().items(documentFileSchema(5)).max(1).required(),
    coverLetter: Joi.array().items(documentFileSchema(5)).max(1),
  }),
};

const validateJobSocialLinks = Joi.array()
  .items(
    Joi.object().keys({
      name: Joi.string()
        .valid(...Object.values(careerJobsEnums.socialLinks))
        .required(),
      url: Joi.string().required(),
      description: Joi.string().when('name', {
        is: careerJobsEnums.socialLinks.OTHER,
        then: Joi.required(),
        otherwise: Joi.optional(),
      }),
    }),
  )
  .unique('name');

const getJobApplications = {
  query: Joi.object().keys({
    careerJob: Joi.string(),
    status: Joi.string().valid(...Object.values(careerJobsEnums.jobApplicationStatus)),
    name: Joi.string(),
    sortBy: Joi.string()
      .valid('createdAt:asc', 'createdAt:desc', 'updatedAt:asc', 'updatedAt:desc')
      .default('createdAt:desc'),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
  }),
};

const updateJobApplication = {
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
  body: Joi.object().keys({
    status: Joi.string().valid(...Object.values(careerJobsEnums.jobApplicationStatus)),
  }),
};

module.exports = {
  addCareerJob,
  getCareerJobs,
  getCareerJob,
  getCareerJobsExt,
  updateCareerJob,

  createJobApplication,
  validateJobSocialLinks,
  getJobApplications,
  updateJobApplication,
};
