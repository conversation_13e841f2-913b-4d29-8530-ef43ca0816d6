const httpStatus = require('http-status');
const { premiumSubscriberService } = require('../services');
const catchAsync = require('../utils/catchAsync');
const { pick } = require('../utils/pick');

const createSubscriber = catchAsync(async (req, res) => {
  const data = await premiumSubscriberService.createSubscriber(req.user, req.body);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data, message: 'Subscriber created successfully' });
});

const getSubscription = catchAsync(async (req, res) => {
  const data = await premiumSubscriberService.getSubscriptionById(req.params.subscriptionId, req.user);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data, message: 'Subscriber retrieved successfully' });
});

const getSessionStatus = catchAsync(async (req, res) => {
  const data = await premiumSubscriberService.getSessionStatus(req.query.sessionId, req.user);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data, message: 'Session status retrieved successfully' });
});

const getSubscriptions = catchAsync(async (req, res) => {
  const filter = pick(req.query, ['active']);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  filter.user = req.user._id.toString();

  const data = await premiumSubscriberService.getSubscriptions(filter, options);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data, message: 'Subscriptions retrieved successfully' });
});

const getLatestSubscription = catchAsync(async (req, res) => {
  const subscription = await premiumSubscriberService.getLatestSubscription(req.user);
  res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data: subscription, message: 'Latest subscription retrieved successfully' });
});

const getSubscriptionsByAdmin = catchAsync(async (req, res) => {
  const filter = pick(req.query, ['user', 'subscriber', 'active']);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);

  const data = await premiumSubscriberService.getSubscriptions(filter, options);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data, message: 'Subscriptions retrieved successfully' });
});

const updateSubscription = catchAsync(async (req, res) => {
  const response = await premiumSubscriberService.updateSubscription(req.user, req.body);
  res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data: response.data, message: `Subscription ${response.action} successfully` });
});

module.exports = {
  createSubscriber,
  getSubscription,
  getLatestSubscription,
  getSessionStatus,
  getSubscriptions,
  getSubscriptionsByAdmin,
  updateSubscription,
};
