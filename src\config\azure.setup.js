const { BlobServiceClient } = require('@azure/storage-blob');
const logger = require('./logger');

const blobServiceClient = BlobServiceClient.fromConnectionString(process.env.AZURE_STORAGE_CONNECTION_STRING);

const parseAzureBlobUrl = (blobUrl) => {
  const urlParts = new URL(blobUrl);
  const pathSegments = urlParts.pathname.split('/');

  const containerEncoded = pathSegments[1];
  const containerName = decodeURIComponent(containerEncoded);
  const blobName = pathSegments.slice(2).join('/');

  return { containerName, blobName };
};

const deleteFileByBlobName = async (containerName, blobName) => {
  const containerClient = blobServiceClient.getContainerClient(containerName);
  const blobBlockClient = containerClient.getBlobClient(blobName);

  await blobBlockClient.deleteIfExists({ deleteSnapshots: 'include' });
  if (blobName.includes('.m3u8')) {
    // Example container name for HLS: 'posts/1741617315461-unyked-VIDEO-mp4'
    const newContainerClient = blobServiceClient.getContainerClient(containerName.split('/')[0]);
    // eslint-disable-next-line no-restricted-syntax
    for await (const blob of newContainerClient.listBlobsFlat({ prefix: containerName.split('/')[1] })) {
      if (!blob.name.includes('.m3u8')) {
        logger.info(`Deleting blob: ${blob.name}`);
        await newContainerClient.deleteBlob(blob.name);
      }
    }
  }
};

module.exports = { blobServiceClient, parseAzureBlobUrl, deleteFileByBlobName };
