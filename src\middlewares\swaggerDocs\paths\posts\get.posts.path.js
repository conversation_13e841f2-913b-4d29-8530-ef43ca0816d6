module.exports = {
  get: {
    tags: ['Posts'],
    summary: 'Get a list of posts',
    description: 'Get a list of posts based on filter criteria',
    parameters: [
      {
        in: 'query',
        name: 'userId',
        schema: {
          type: 'string',
          example: '507f1f77bcf86cd799439011',
        },
        description: 'User ID to filter posts (optional)',
      },
      {
        in: 'query',
        name: 'parentPostId',
        schema: {
          type: 'string',
          example: '507f1f77bcf86cd799439011',
        },
      },
      {
        in: 'query',
        name: 'sortBy',
        schema: {
          type: 'string',
          example: 'createdAt',
        },
        description: 'Sort criteria for posts (optional)',
      },
      {
        in: 'query',
        name: 'limit',
        schema: {
          type: 'integer',
          example: '10',
        },
        description: 'Maximum number of posts to return (optional)',
      },
      {
        in: 'query',
        name: 'page',
        schema: {
          type: 'integer',
          example: '2',
        },
        description: 'Page number for paginated results (optional)',
      },
      {
        in: 'query',
        name: 'group',
        schema: {
          type: 'string',
          example: '507f1f77bcf86cd799439011',
          default: null,
        },
        description: 'Group ID to filter posts (optional)',
      },
      {
        in: 'query',
        name: 'tab',
        schema: {
          type: 'string',
          example: 'all',
          enums: ['all', 'bookmarks', 'reposts', 'likes'],
        },
      },
    ],
    responses: {
      200: {
        description: 'List of posts retrieved successfully',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              data: {
                results: [
                  {
                    repostWithThought: [],
                    visibility: 'public',
                    tags: [],
                    user: {
                      username: 'johndoe',
                      photo: {
                        url: 'https://storagedevunyked.blob.core.windows.net/users%2Fphoto/74570fc0-c0f4-11ee-92c2-61dcf2fafad1-866-536x354.jpg',
                      },
                      firstName: 'George O',
                      lastName: 'Darwin',
                      tagLine: '',
                      middleName: '',
                      id: '651bb5bf4eb9240327ea9d56',
                    },
                    text: 'Updated Deleted 2345',
                    media: [
                      {
                        url: 'https://storagesite/posts/d274bae0-afb9-11ee-b432-b75166286bdd-fit_image_2.jpg',
                      },
                      {
                        url: 'https://storagesite/posts/5f0805c0-b069-11ee-9007-d51a1bac1b32-fit_image_2.jpg',
                      },
                    ],
                    likes: [],
                    comments: [],
                    reposts: [],
                    createdAt: '2024-01-10T13:01:19.489Z',
                    updatedAt: '2024-01-11T10:08:31.384Z',
                    createdOn: '2024-01-10T13:01:19.489Z',
                    updatedOn: '2024-01-11T10:08:31.384Z',
                    bookmarkedByMe: false,
                    repostedByMe: false,
                    likedByMe: false,
                    id: '659e951fd21ab2365ee4bcd1',
                  },
                  {
                    repostWithThought: [],
                    visibility: 'public',
                    tags: [],
                    user: {
                      username: 'johndoe',
                      photo: {
                        url: 'https://storagedevunyked.blob.core.windows.net/users%2Fphoto/74570fc0-c0f4-11ee-92c2-61dcf2fafad1-866-536x354.jpg',
                      },
                      firstName: 'George O',
                      lastName: 'Darwin',
                      tagLine: '',
                      middleName: '',
                      id: '651bb5bf4eb9240327ea9d56',
                    },
                    text: 'Updated Deleted 2345',
                    media: [
                      {
                        url: 'https://storagesite/posts/a75d8840-b06e-11ee-847c-ed8610d6147e-fit_image_2.jpg',
                      },
                      {
                        url: 'https://storagesite/posts/a75e4b90-b06e-11ee-847c-ed8610d6147e-mega_wing.jpg',
                      },
                    ],
                    likes: [],
                    comments: ['659fd7434857f088ed7d3bc5'],
                    reposts: [],
                    createdAt: '2024-01-11T10:31:33.512Z',
                    updatedAt: '2024-01-11T11:58:53.803Z',
                    createdOn: '2024-01-11T10:31:33.512Z',
                    updatedOn: '2024-01-11T11:58:53.803Z',
                    bookmarkedByMe: false,
                    repostedByMe: false,
                    likedByMe: false,
                    id: '659fc3858b69db379530fef3',
                  },
                ],
                page: 1,
                limit: 2,
                totalPages: 21,
                totalResults: 42,
                myBookmarksCount: 0,
                myRepostsCount: 0,
                myLikesCount: 1,
              },
              message: 'Posts retrieved successfully',
            },
          },
        },
      },
      500: {
        description: 'Internal server error',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error500',
            },
          },
        },
      },
    },
  },
};
