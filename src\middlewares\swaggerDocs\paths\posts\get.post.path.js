module.exports = {
  get: {
    tags: ['Posts'],
    summary: 'Get a post',
    description: 'Get a post based on Post Id',
    parameters: [
      {
        in: 'query',
        name: 'id',
        schema: {
          type: 'string',
          example: '507f1f77bcf86cd799439011',
        },
        description: 'Post ID to filter posts (optional)',
      },
    ],
    responses: {
      200: {
        description: 'Post retrieved successfully',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              data: {
                repostWithThought: [],
                tags: [],
                user: {
                  username: 'joh<PERSON><PERSON>',
                  photo: {
                    url: 'https://storagesite/users%2Fphoto/74570fc0.jpg',
                  },
                  firstName: 'John <PERSON>',
                  lastName: 'Doe',
                  tagLine: '',
                  middleName: '',
                  id: '651bb5bf4eb9240327eaaaaa',
                },
                text: 'Updated Visibility 2345',
                media: [
                  {
                    url: 'https://storagesite/users%2Fphoto/74570fc0.jpg',
                    _id: '65bb7f69f2b37ca660a3c05f',
                  },
                ],
                likes: ['65bb7f69f2b37ca660a3c05f', '65bb7f69f2b37ca660a3c05f', '65bb7f69f2b37ca660a3c05f'],
                comments: ['65bb7f69f2b37ca660a3c05f', '65bb7f69f2b37ca660a3c05f', '65bb7f69f2b37ca660a3c05f'],
                reposts: ['65bb7f69f2b37ca660a3c05f', '65bb7f69f2b37ca660a3c05f', '65bb7f69f2b37ca660a3c05f'],
                bookmarks: ['65bb7f69f2b37ca660a3c05f', '65bb7f69f2b37ca660a3c05f', '65bb7f69f2b37ca660a3c05f'],
                visibility: 'only me',
                createdAt: '2024-02-14T14:04:02.231Z',
                updatedAt: '2024-02-14T14:06:05.128Z',
                id: '65ccc85202c7a6b4db5ababab',
              },
              message: 'Post retrieved successfully',
            },
          },
        },
      },
    },
    404: {
      description: 'Record not found',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/Error404',
          },
        },
      },
    },
    500: {
      description: 'Internal server error',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/Error500',
          },
        },
      },
    },
  },
};
