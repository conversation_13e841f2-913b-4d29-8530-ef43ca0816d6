const httpStatus = require('http-status');
const Async = require('async');
const mongoose = require('mongoose');
const mime = require('mime-types');
const { Message, MessageConversation, User, Business, Order } = require('../models');
const { generateNotificationMessage, notificationTypes } = require('./shared/notification.handler');
const ApiError = require('../utils/ApiError');
const { messageValidation } = require('../validations');
const { pick } = require('../utils/pick');
const logger = require('../config/logger');
const validateId = require('./shared/validateId');
// const { basicUserPopulate } = require('./user.service');
const { emit } = require('./sockets/socket.shared');
const { basicBusinessPopulate } = require('./business.service');
const { scheduleCreateMessageEmail } = require('../utils/jobs/jobSchedulers');
const { processPostFiles } = require('./post.service');
const { azureContainers } = require('../config/constants');
const { createdForValues } = require('../config/constants').messageEnums;

const { ObjectId } = mongoose.Types;

const matchMembers = (member) => ({
  $elemMatch: { [member.user ? 'user' : 'business']: ObjectId(member.user || member.business) },
});
const getConversationsQuery = (member) => ({ query: { member }, options: { sortBy: 'updatedAt:desc', limit: 10, page: 1 } });

const matchMember = (member) => ({ $or: [{ user: ObjectId(member.user) }, { business: ObjectId(member.business) }] });

const createMemberObject = (member) => ({ [member.user ? 'user' : 'business']: ObjectId(member.user || member.business) });

const memberIncluded = (member, members) => {
  const id = member.user || member.business;
  return members.some((m) => (m.user ? String(m.user) === String(id) : String(m.business) === String(id)));
};

const populateMember = (title = 'members', select = undefined) => [
  {
    // eslint-disable-next-line global-require
    ...require('./user.service').basicUserPopulate,
    path: `${title}.user`,
    select: select || 'firstName lastName online',
    populate: title === 'members' ? { path: 'photo', select: 'url' } : undefined,
  },
  {
    ...basicBusinessPopulate,
    path: `${title}.business`,
    select: select || 'displayName online',
    populate: title === 'members' ? { path: 'profilePhoto', select: 'url' } : undefined,
  },
];

const populateMessage = () => [
  ...populateMember('sentBy'),
  ...populateMember('isReadBy', '_id'),
  ...populateMember('isReceivedBy', '_id'),
  {
    path: 'files',
    select: 'url thumbnail metadata',
  },
];

/**
 * Receiver of socket emit is either the user or contact person of a business
 *
 */
const getReceiverId = async (memberParam, returnMember) => {
  const member = memberParam;
  if (member.user?._id || member.user?.id) {
    member.user = member.user._id || member.user.id;
  }
  if (member.business?._id) {
    member.business = member.business._id;
  }
  let business;
  if (member.business) {
    business = await Business.findById(member.business);
    if (!business) {
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Something went wrong while fetching conversations');
    }
  } else if (!member.user) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Something went wrong while fetching conversations');
  }
  const receiverId = String(member.user || business.contactPerson);

  if (returnMember) {
    return { receiverId, member };
  }
  return receiverId;
};

const verifyRequester = (requesterParam, userObject) => {
  const requester = Array.isArray(requesterParam) ? requesterParam : [requesterParam];

  const found = requester.some((r) => {
    if (String(r.user) && String(r.user) === String(userObject?._id)) {
      return true;
    }
    if (String(r.business) && String(r.business) === String(userObject.business)) {
      return true;
    }
    return false;
  });
  if (!found) {
    // socket.emit('error', { message: 'You are not allowed to perform this action' });
    throw new ApiError(httpStatus.FORBIDDEN, 'You are not allowed to perform this action');
  }
};

const countUnreadConversations = async (member) => {
  const myConversations = (await MessageConversation.find({ members: matchMembers(member) })).map((conversation) =>
    conversation._id.toString(),
  );
  const unreadMessages = await Message.find({
    conversation: { $in: myConversations },
    $nor: [{ isReadBy: matchMembers(member) }],
    // isReadBy: { $not: matchMembers(member) },
  });

  const unreadConversationsCount = [...new Set(unreadMessages.map((message) => message.conversation.toString()))];
  return unreadConversationsCount.length;
};

const markMessagesAsRead = async (messageId, member) => {
  const messageIds = Array.isArray(messageId) ? messageId : [messageId];
  messageIds.forEach((id) => validateId(id, 'Message(s)'));

  const messages = await Message.find({ _id: { $in: messageIds }, $nor: [{ isReadBy: matchMembers(member) }] }).populate(
    'conversation',
  );

  const memberOfConversations = messages.every((message) => memberIncluded(member, message.conversation.members));

  if (!memberOfConversations) {
    throw new ApiError(httpStatus.FORBIDDEN, "You're not allowed to mark one or more of these messages as read");
  }

  await Promise.all(
    messages.map(async (message) => {
      // await Message.updateOne({ _id: message._id }, { $addToSet: { readBy: userId } });
      await Message.updateOne({ _id: message._id }, { $addToSet: { isReadBy: createMemberObject(member) } });
      await emit(
        { messageId: message._id, conversationId: message.conversation?._id, member },
        await getReceiverId(member),
        'message-read',
      );
    }),
  );
};

const markMessagesAsReceived = async (messageId, member) => {
  // eslint-disable-next-line no-console
  console.log('---------------------------------------------');
  // eslint-disable-next-line no-console
  console.log('Marking Messages as Received', messageId, member);
  const messageIds = Array.isArray(messageId) ? messageId : [messageId];
  const members = Array.isArray(member) ? member : [member];

  const messages = await Message.find({
    _id: { $in: messageIds },
    $nor: [{ isReceivedBy: members.map((m) => matchMembers(m)) }],
    // isReceivedBy: { $not: { $all: members.map((m) => matchMembers(m)) } },
  }).populate('conversation');
  const conversations = [...new Set(messages.map((message) => message.conversation._id))];
  if (conversations.length > 1) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'All messages must be in the same conversation');
  }

  // eslint-disable-next-line no-console
  console.log('Messages to be marked as received', messages);
  if (messages.length === 0) {
    return;
  }

  members.forEach((m) => {
    if (!memberIncluded(m, messages[0].conversation.members)) {
      // if (!messages[0].conversation.participants.includes(id)) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'You are not a participant in this conversation');
    }
  });

  await Async.eachOfSeries(messages, async (message) => {
    await Async.eachOfSeries(members, async (receiver) => {
      if (!memberIncluded(receiver, message.isReceivedBy)) {
        // if (!message.receivedBy.includes(id)) {
        await Message.updateOne(
          { _id: message._id },
          {
            $addToSet: { isReceivedBy: createMemberObject(receiver) },
          },
        );
        message.isReceivedBy.push(member); // Not saved to DB

        if (message.isReceivedBy.length === message.conversation.members.length) {
          // if (message.receivedBy.length === message.conversation.participants.length) {
          await emit(
            { messageId: message._id, conversationId: message.conversation?._id },
            await getReceiverId(message.sentBy),
            'message-received',
          );
        }
      }
    });
  });
};

const updateUserConversationMessagesReadStatus = async (conversationId, member, markRead = true) => {
  // const userConversations = (await MessageConversation.find({ participants: { $in: [userId] } })).map((c) =>
  const userConversations = (
    await MessageConversation.find({
      members: matchMembers(member),
    })
  ).map((c) => c._id.toString());
  if (!userConversations.includes(String(conversationId))) {
    throw new ApiError(httpStatus.FORBIDDEN, "You're not allowed to mark messages in this conversation as read");
  }
  // const filter = { conversation: conversationId, sender: { $ne: userId } };
  const filter = { conversation: conversationId, $nor: [{ sentBy: matchMember(member) }] };

  // Mark messages in conversation as received and read for user
  const unreceivedMessages = await Message.find({ ...filter, $nor: [{ isReceivedBy: matchMembers(member) }] });
  if (markRead) {
    const unreadMessages = await Message.find({ ...filter, $nor: [{ isReadBy: matchMembers(member) }] });
    if (unreadMessages.length) {
      // eslint-disable-next-line no-console
      console.log('---------------------------------------------');
      // eslint-disable-next-line no-console
      console.log('Messages to be marked as read', unreadMessages);
      await markMessagesAsRead(
        unreadMessages.map((message) => message._id.toString()),
        member,
      );
    }
  }
  if (unreceivedMessages.length) {
    // eslint-disable-next-line no-console
    console.log('---------------------------------------------');
    // eslint-disable-next-line no-console
    console.log('Messages to be marked as received: member', member);
    // eslint-disable-next-line no-console
    console.log('Messages to be marked as received: messages', unreceivedMessages);
    await markMessagesAsReceived(
      unreceivedMessages.map((message) => message._id),
      member,
    );
  }
};

const getConversations = async ({ query, options }, updateMessagesReceivedStatus = true, emitSocket = true) => {
  let members = query.member;
  members = Array.isArray(members) ? members : [members];
  // eslint-disable-next-line no-param-reassign
  delete query.member;
  const optionsParam = {
    ...options,
    populate: [
      ...populateMember(),
      {
        path: 'messages',
        populate: populateMessage(),
        options: { sort: { createdAt: -1 }, limit: 10 },
        select: 'sentBy conversation order text createdAt updatedAt',
      },
    ],
    select: 'createdAt updatedAt createdFor',
  };

  let conversations;

  await Async.eachOfSeries(members, async (member) => {
    // conversations = await MessageConversation.find({ participants: { $in: [userId] } })
    const receiverId = await getReceiverId(member);
    conversations = await MessageConversation.paginate({ ...query, members: matchMembers(member) }, optionsParam);
    if (emitSocket) {
      await emit(conversations, receiverId, 'send-conversations');
    }
    if (!updateMessagesReceivedStatus) {
      return conversations;
    }
    await Async.eachOfSeries(conversations.results, async (conversation) => {
      await updateUserConversationMessagesReadStatus(conversation._id, member, false);
    });
  });

  if (members.length === 1) {
    return conversations;
  }
};

const markAsRead = async (socket, clientData, socketConnection) => {
  try {
    const { value: data, error } = messageValidation.markAsRead.validate(clientData);
    if (error) {
      socket.emit('error', { message: error.details[0].message });
      logger.error(`${error.details[0].message}`);
    }
    const { member } = data;
    verifyRequester(member, socket.user);

    await markMessagesAsRead(data.messageIds, data.member, socketConnection);
  } catch (error) {
    logger.error(error);
  }
};

const createMessageNotification = async (sender, recipientId, details, socketConnection) => {
  let type;
  let message;
  if (details.conversation.directMessage) {
    type = notificationTypes.MESSAGE;
    message = await generateNotificationMessage(sender, type, details);
  } else {
    // Notification message for a non-direct message conversation
    type = notificationTypes.GROUP_MESSAGE;
    message = await generateNotificationMessage(sender, type, details);
  }
  // const notification = new Notification({ recipient: recipientId, message, type, actor: sender._id });
  // await notification.save();
  await emit(message, recipientId, undefined, socketConnection);

  const recipient = await User.findById(recipientId);
  await scheduleCreateMessageEmail({ sender, recipient, details });
};

const getConversation = async (conversationId, member, options, updateMessagesReadStatus = true) => {
  // const filter = { conversation: conversationId, participants: { $in: userId } };
  const filter = { conversation: conversationId, members: matchMembers(member) };

  // eslint-disable-next-line no-param-reassign
  options = {
    ...options,
    populate: [...populateMessage(), { path: 'files', select: 'url' }],
    select: 'sentBy conversation order text isReadBy isReceivedBy edited createdAt updatedAt',
  };

  if (updateMessagesReadStatus) {
    await updateUserConversationMessagesReadStatus(conversationId, member);
  }

  const messages = await Message.paginate(filter, options);

  const conversation = await MessageConversation.findOne({ _id: conversationId, members: matchMembers(member) }).populate(
    populateMember(),
  );

  const unreadConversationsCount = await countUnreadConversations(member);
  messages.members = conversation.members;

  const receiverId = await getReceiverId(member);
  await emit({ unreadConversationsCount }, receiverId, 'unread-conversations-count');
  return messages;
};

const getOnlineMembers = async (conversation, connectedUsers) => {
  const allMembersReceiverIds = await Promise.all(conversation.members.map(async (member) => getReceiverId(member, true)));
  const onlineMembers = allMembersReceiverIds.filter(
    (member) => connectedUsers[member.receiverId] !== undefined && connectedUsers[member.receiverId].length > 0,
  );

  return {
    onlineMembers: onlineMembers.map((m) => m.member),
    allMembersReceiverIds: allMembersReceiverIds.map((m) => m.receiverId),
  };
};

const createMessage = async (socket, clientData, socketConnection) => {
  try {
    const { value: data, error } = messageValidation.createMessage.validate(clientData);
    if (error) {
      socket.emit('error', { message: error.details[0].message });
      throw new Error(`${error.details[0].message}`);
    }
    const { sentBy } = data;
    verifyRequester(sentBy, socket.user);

    if (data.order) {
      // verify that order exists and that the user and business are the members of the conversation
      const order = await Order.findById(data.order);
      if (!order) {
        socket.emit('error', { message: 'Order not found' });
        throw new Error('Order not found');
      }
      if (!(sentBy.user === order.client.toString() || sentBy.business === order.provider.toString())) {
        socket.emit('error', { message: 'You are not allowed to message in this order' });
        throw new Error('You are not allowed to message in this order');
      }
    }

    if (data.filenames) {
      // Save file and get URL
      data.buffers = Array.isArray(data.buffers) ? data.buffers : [data.buffers];
      data.filenames = Array.isArray(data.filenames) ? data.filenames : [data.filenames];

      const files = data.filenames.map((filename, index) => ({
        originalname: filename,
        buffer: data.buffers[index],
        size: data.buffers[index].length,
        mimetype: mime.lookup(filename) || 'application/octet-stream',
      }));
      data.files = await processPostFiles(files, azureContainers.messages);

      delete data.buffers;
      delete data.filenames;
    }

    const { conversationId, clientMessageId } = data;
    delete data.clientMessageId;
    // const senderId = sender?._id;
    const conversation = await MessageConversation.findOne({
      _id: conversationId,
      members: matchMembers(sentBy),
      // participants: { $in: [senderId] },
    }).populate(populateMember());

    if (!conversation) {
      socket.emit('error', { status: 404, message: 'Conversation not found' });
      throw new Error('Conversation not found');
    }
    if (!data.text && (!data.files || data.files.length === 0)) {
      socket.emit('error', { message: 'Message must have content' });
      throw new Error('Message must have content');
    }

    // const participantsBlockedProfiles = conversation.participants.reduce(
    //   (acc, user) => [...acc, ...user.blockedUsers.map((id) => id.toString())],
    //   [],
    // );

    const membersBlockedProfiles = conversation.members.reduce(
      (acc, member) => [...acc, ...(member.user?.blockedUsers || []).map((id) => id.toString())], // Will apply to users only. Business cannot block users
      [],
    );
    if (sentBy.user && membersBlockedProfiles.includes(sentBy.user)) {
      socket.emit('error', { message: 'You are blocked by one or more participants' });
      throw new Error('You are blocked by one or more participants');
    }

    const messageBody = {
      ...data,
      conversation: conversationId,
      isReadBy: [sentBy],
      isReceivedBy: [sentBy],
    };

    const message = await Message.create(messageBody);
    if (messageBody.order && !conversation.createdFor.includes(createdForValues.USER_BUSINESS)) {
      // This condition handles
      conversation.createdFor.push(createdForValues.USER_USER);
    }
    conversation.messages.push(message._id);
    await conversation.save();
    // eslint-disable-next-line no-console
    console.log('------------------------------------------------');
    // eslint-disable-next-line no-console
    console.log('Before Message Sent Socket', await getReceiverId(sentBy));
    await emit({ message: 'Message Sent', conversationId, clientMessageId }, await getReceiverId(sentBy), 'message-sent');
    // eslint-disable-next-line no-console
    console.log('------------------------------------------------');
    // eslint-disable-next-line no-console
    console.log('After Message Sent Socket', await getReceiverId(sentBy));

    // const otherParticipants = conversation.participants.filter(
    //   (participant) => participant._id.toString() !== senderId.toString(),
    // );
    const otherMembers = conversation.members.filter(
      (member) => (member.user || member.business).toString() !== (sentBy.user || sentBy.business),
    );

    if (otherMembers.length) {
      await Promise.all(
        otherMembers.map(async (otherMember) => {
          // TODO: Handle user to business message notification
          if (otherMember.user && sentBy.user) {
            // Handle user to user message notification
            await createMessageNotification(
              sentBy.user,
              otherMember.user,
              { messageId: message._id, conversation, otherMembers },
              socketConnection,
            );
          }

          // notify the receiver of the message
          // check if the recipientId is in the readBy
          // if no, call the scheduler with sender, recipient & conversationId
          const unreadConversationsCount = await countUnreadConversations(otherMember);
          const receiver = await getReceiverId(otherMember);
          await emit({ unreadConversationsCount }, receiver, 'unread-conversations-count');
        }),
      );
    }

    // eslint-disable-next-line no-console
    console.log('---------------------------------------------');
    // eslint-disable-next-line no-console
    console.log('Getting Sender Conversation', sentBy);
    const updatedConversation = await getConversation(
      conversationId,
      sentBy, // Messages including current message will be marked as read for sender
      { sortBy: 'createdAt:asc', limit: 1000 },
      false,
    );
    // const membersReceiverIds = await Promise.all(conversation.members.map(async (member) => getReceiverId(member)));
    const { connectedUsers } = socketConnection;
    const { onlineMembers, allMembersReceiverIds } = await getOnlineMembers(conversation, connectedUsers);
    // eslint-disable-next-line no-console
    console.log('---------------------------------------------');
    // eslint-disable-next-line no-console
    console.log('Online Members', onlineMembers);
    // eslint-disable-next-line no-console
    console.log('Emitting Send Conversation in create message');
    // eslint-disable-next-line no-console
    console.log('All members receiver ids', allMembersReceiverIds);
    allMembersReceiverIds.reverse();
    await emit(
      updatedConversation,
      allMembersReceiverIds,
      // conversation.participants.map((c) => c._id),
      'send-conversation',
    );

    // const onlineParticipants = conversation.participants
    //   .filter((participant) => connectedUsers[participant._id] !== undefined && connectedUsers[participant._id].length > 0)
    //   .map((participant) => participant._id);
    // const onlineMembers = membersReceiverIds.filter(
    //   (member) => connectedUsers[member] !== undefined && connectedUsers[member].length > 0,
    // );

    await getConversations(getConversationsQuery(onlineMembers));
  } catch (error) {
    logger.error(error);
  }
};

const getDistinctMembers = (members) => {
  const distinctMembers = { user: [], business: [] };

  members.forEach((member) => {
    if (member.user) {
      distinctMembers.user.push(member.user);
    } else if (member.business) {
      distinctMembers.business.push(member.business);
    }
  });
  const result = [
    ...[...new Set(distinctMembers.user)].map((m) => ({ user: m })),
    ...[...new Set(distinctMembers.business)].map((m) => ({ business: m })),
  ];
  return result;
};

const createConversation = async (data, initiator, createdFor) => {
  /**
   * Create conversation between user and user(s) or user and business.
   *
   * - If conversation already exists between participants and its a direct message, the conversation is returned
   * - If conversation already exists between participants and its a group message, the conversation between participants which is a group is returned
   *
   * Return created conversation
   * */
  const { directMessage, members: m } = data;
  // directMessage = directMessage === 'true';

  const members = getDistinctMembers(m);
  verifyRequester(members, initiator);

  // initiator is the user who is creating the conversation
  // confirm that all specified members exist
  await Promise.all(
    members.map(async (member) => {
      const modelName = member.user ? 'User' : 'Business';
      const memberId = member.user || member.business; // member can be either a user or a business not both (validated in Joi schema)
      validateId(memberId, modelName);
      // eslint-disable-next-line global-require
      const memberData = await require('../models')[modelName].findById(memberId);
      if (!memberData) {
        throw new ApiError(httpStatus.NOT_FOUND, 'One or more users not found');
      }
      if ((memberData.blockedUsers || []).includes(initiator?._id)) {
        throw new ApiError(httpStatus.FORBIDDEN, 'You cannot message one or more of the participants at this time');
      }
    }),
  );

  // Check if conversation already exists between members
  let existingConversation = await MessageConversation.findOne({
    members: {
      $all: members.map((member) => matchMembers(member)),
      $size: members.length,
    },
    // participants: {
    //   $all: participants.map((participant) => mongoose.Types.ObjectId(participant)),
    //   $size: participants.length,
    // },
    directMessage: directMessage === 'true',
  });

  if (existingConversation) {
    existingConversation = await MessageConversation.findByIdAndUpdate(existingConversation._id, {
      $addToSet: { createdFor },
    });
    return existingConversation._id;
  }

  const conversation = await MessageConversation.create({
    members,
    directMessage: directMessage === 'true',
    createdFor: [createdFor],
  });
  await getConversations(getConversationsQuery(members));
  return conversation._id;
};

const getConversationWithSocket = async (socket, clientData, socketConnection) => {
  try {
    const { value: data, error } = messageValidation.getConversationWithSocket.validate(clientData);
    if (error) {
      socket.emit('error', { message: error.details[0].message });
      throw new Error(`${error.details[0].message}`);
    }
    const options = pick(data, ['sortBy', 'limit', 'page']);
    // const userId = socket.user?._id;
    verifyRequester(data.member, socket.user);
    const messages = await getConversation(data.conversationId, data.member, options);

    await emit(messages, await getReceiverId(data.member), 'send-conversation', socketConnection);
  } catch (error) {
    logger.error(error);
  }
};

const markTyping = async (socket, clientData) => {
  try {
    const { value: data, error } = messageValidation.markTyping.validate(clientData);
    if (error) {
      socket.emit('error', { message: error.details[0].message });
      // eslint-disable-next-line no-console
      console.log(clientData);
      throw new Error(`${error.details[0].message}`);
    }
    const { conversationId, isTyping } = data;
    // const userId = socket.user?._id;
    verifyRequester(data.member, socket.user);
    // const conversation = await MessageConversation.findOne({ _id: conversationId, participants: { $in: [userId] } });
    const conversation = await MessageConversation.findOne({ _id: conversationId, members: matchMembers(data.member) });
    if (!conversation) {
      socket.emit('error', { status: 404, message: 'Conversation not found' });
      throw new Error('Conversation not found');
    }
    // const otherParticipants = conversation.participants.filter(
    //   (participant) => participant.toString() !== userId.toString(),
    // );
    const senderReceiverId = await getReceiverId(data.member);
    const membersReceiverIds = await Promise.all(conversation.members.map(async (member) => getReceiverId(member)));
    const otherMembers = membersReceiverIds.filter((member) => member !== senderReceiverId);
    const member = {};
    if (data.member.user) {
      member.user = { id: data.member.user, firstName: socket.user.firstName, lastName: socket.user.lastName };
    } else if (data.member.business) {
      member.business = {
        id: data.member.business,
        name: await Business.findById(data.member.business).select('name').lean().name,
      };
    }

    if (otherMembers.length) {
      await emit({ conversationId, isTyping: isTyping === 'true', member }, otherMembers, 'typing');
    }
  } catch (error) {
    logger.error(error);
  }
};

const editMessage = async (socket, clientData, socketConnection) => {
  try {
    const { value: data, error } = messageValidation.editMessage.validate(clientData);
    if (error) {
      socket.emit('error', { message: error.details[0].message });
      throw new Error(`${error.details[0].message}`);
    }
    // const messageEditor = data.sentBy;
    const { messageId, text } = data;
    verifyRequester(data.sentBy, socket.user);

    const message = await Message.findById(messageId);

    if (!message) {
      socket.emit('error', { message: 'Message not found' });
      throw new Error('Message not found');
    }
    if ((message.sentBy.user || message.sentBy.business) === (data.sentBy.user || data.sentBy.business)) {
      // if (String(message.sender) !== String(messageEditor._id)) {
      socket.emit('error', { message: 'You are not the sender of this message' });
      throw new Error('You are not the sender of this message');
    }

    const conversation = await MessageConversation.findOne({
      _id: message.conversation,
      members: matchMembers(data.sentBy),
      // participants: { $in: [messageEditor._id] },
    });
    if (!conversation) {
      socket.emit('error', { message: "Sorry, you can't edit this message" });
      throw new Error('An error occurred while editing message. User not in conversation');
    }

    message.text = text;
    message.edited = true;
    await message.save();

    const updatedConversation = await getConversation(String(conversation._id), data.sentBy, {
      sortBy: 'createdAt:asc',
      limit: 1000,
    });

    const { connectedUsers } = socketConnection;
    const { allMembersReceiverIds, onlineMembers } = await getOnlineMembers(conversation, connectedUsers);

    await emit(updatedConversation, allMembersReceiverIds, 'conversation-modified');

    await markMessagesAsReceived(message._id, onlineMembers);

    await getConversations(getConversationsQuery(onlineMembers));
  } catch (error) {
    logger.error(`Error updating message: ${error}`);
  }
};

const deleteMessage = async (socket, clientData, socketConnection) => {
  try {
    const { value: data, error } = messageValidation.deleteMessageViaSocket.validate(clientData);
    if (error) {
      socket.emit('error', { message: error.details[0].message });
      throw new Error(`${error.details[0].message}`);
    }

    verifyRequester(data.sentBy, socket.user);
    // const messageDeleter = socket.user;
    const { messageId } = data;

    const message = await Message.findById(messageId);

    if (!message) {
      socket.emit('error', { message: 'Message not found' });
      throw new Error('Message not found');
    }
    // if (String(message.sender) !== String(messageDeleter._id)) {
    if ((message.sentBy.user || message.sentBy.business) === (data.sentBy.user || data.sentBy.business)) {
      socket.emit('error', { message: 'You are not the sender of this message' });
      throw new Error('You are not the sender of this message');
    }

    const conversation = await MessageConversation.findOne({
      _id: message.conversation,
      // participants: { $in: [messageDeleter._id] },
      members: matchMembers(data.sentBy),
    });
    if (!conversation) {
      socket.emit('error', { message: "Sorry, can't delete this message" });
      throw new Error('An error occurred while deleting message. User not in conversation');
    }

    conversation.messages.pull(message._id);
    await conversation.save();
    await message.remove();

    const updatedConversation = await getConversation(String(conversation._id), data.sentBy, {
      sortBy: 'createdAt:asc',
      limit: 1000,
    });
    const { connectedUsers } = socketConnection;
    const { allMembersReceiverIds, onlineMembers } = await getOnlineMembers(conversation, connectedUsers);
    await emit(updatedConversation, allMembersReceiverIds, 'conversation-modified');

    // const onlineParticipants = conversation.participants.filter(
    //   (participant) => connectedUsers[participant] !== undefined && connectedUsers[participant].length > 0,
    // );

    await markMessagesAsReceived(message._id, onlineMembers);
    await getConversations(getConversationsQuery(onlineMembers));
  } catch (error) {
    logger.error(`Error deleting message: ${error}`);

    // if (process.env.ENVIRON === 'development') {
    //   throw error;
    // }
  }
};

module.exports = {
  createMessage,
  createConversation,
  getConversations,
  getConversation,
  createMessageNotification,
  getConversationWithSocket,
  markMessagesAsRead,
  markAsRead,
  markTyping,
  editMessage,
  deleteMessage,
  countUnreadConversations,
  verifyRequester,
  getDistinctMembers,
};
