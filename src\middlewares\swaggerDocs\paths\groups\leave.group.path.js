module.exports = {
  post: {
    summary: 'Leave Group',
    description: 'Leave a group as a member',
    tags: ['Groups'],
    parameters: [
      {
        name: 'id',
        in: 'path',
        required: true,
        description: 'ID of the group',
        schema: {
          type: 'string',
        },
      },
      {
        name: 'userId',
        in: 'path',
        required: true,
        description: 'ID of the user leaving the group',
        schema: {
          type: 'string',
        },
      },
    ],
    requestBody: {
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              newAdminId: {
                type: 'string',
                description: 'New admin ID to replace the exiting admin if an admin is leaving the group',
                example: '5f9d1e9a6f3f4b001f6d4c0b',
              },
            },
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Exit group action successful',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Exit group action successful',
            },
          },
        },
      },
      400: {
        description: 'Bad Request',
        content: {
          'application/json': {
            example: {
              status: 'FAILED',
            },
          },
        },
      },
      403: {
        description: 'Forbidden - Ad<PERSON> cannot leave the group',
        content: {
          'application/json': {
            example: {
              status: 'FAILED',
              message: 'Forbidden - <PERSON><PERSON> cannot leave the group',
            },
          },
        },
      },
      404: {
        description: 'Group not found',
        content: {
          'application/json': {
            example: {
              status: 'FAILED',
              message: 'Group not found',
            },
          },
        },
      },
    },
  },
};
