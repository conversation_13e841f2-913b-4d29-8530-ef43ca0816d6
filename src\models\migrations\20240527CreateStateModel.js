const Async = require('async');
const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const Country = require('../country.model');
const State = require('../state.model');

const createStateModel = async () => {
  const migrated = await GlobalVariable.findOne({ name: 'created state model' });

  if (migrated) {
    return;
  }

  const countries = await Country.find({});
  await State.deleteMany({});

  await Async.eachOfSeries(countries, async (country) => {
    const states = await State.insertMany(
      country.states.map((state) => ({
        name: state.name,
        stateCode: state.stateCode,
        cities: state.cities,
      })),
    );

    await Country.findByIdAndUpdate(country._id, { states: states.map((state) => state._id) });
    logger.info(`State model created for country ${country.name}, states count: ${states.length}`);
  });

  await GlobalVariable.create({ name: 'created state model', value: 'true' });
  logger.info('State model created');
};

module.exports = createStateModel;
