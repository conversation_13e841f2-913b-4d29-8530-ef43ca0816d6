const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { botRecordService } = require('../services');
const { pick } = require('../utils/pick');

const createBotRecord = catchAsync(async (req, res) => {
  const botRecord = await botRecordService.createBotRecord({ ...req.body, user: req.user._id });
  res.status(httpStatus.CREATED).json({ status: 'SUCCESS', data: botRecord, message: 'Bot record created successfully' });
});

const getBotRecords = catchAsync(async (req, res) => {
  const filter = { user: req.user._id };
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const botRecords = await botRecordService.getBotRecords(filter, options);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: botRecords });
});

module.exports = {
  createBotRecord,
  getBotRecords,
};
