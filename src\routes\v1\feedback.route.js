const express = require('express');

const router = express.Router();
const validate = require('../../middlewares/validate');
const { auth } = require('../../middlewares/auth');
const { feedbackController } = require('../../controllers');
const { feedbackValidation } = require('../../validations');

// router.use(auth());

router.post('/', auth(), validate(feedbackValidation.createFeedback), feedbackController.createFeedback);

router.get('/:id', auth('manageFeedbacks'), validate(feedbackValidation.getFeedback), feedbackController.getFeedbackById);

router.get('/', auth('manageFeedbacks'), validate(feedbackValidation.getFeedbacks), feedbackController.getFeedbacks);

module.exports = router;
