<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>
<body>
  <h1>Hello socket</h1>
</body>

<script src="https://cdn.socket.io/4.7.2/socket.io.min.js" integrity="sha384-mZLF4UVrpi/QTWPA7BjNPEnkIfRFn4ZEO3Qt/HFklTJBj/gBOV8G3HcKn4NfQblz" crossorigin="anonymous"></script>
<script>
  const token =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOnsidXNlcklkIjoiNjUxYmI1YmY0ZWI5MjQwMzI3ZWE5ZDU2Iiwicm9sZXMiOlsic3R1ZGVudCJdfSwiaWF0IjoxNzAyMDM1NTMxLCJleHAiOjE3MDIwNDI3MzEsInR5cGUiOiJhY2Nlc3MifQ.1_RfH85J-lCeZseC4_r5CinRjxxzynz-PJ9X6u3RKsM';

  const socket = io('http://localhost:4000', {
      auth: {
        token: `${token}`,
      },
    });

  socket.on('connect', () => {
    isSocketConnected = true;
    console.log('Connection established');
  });

  socket.on('notification', () => {
    console.log('connected');
    console.log(socket.user);
  });

</script>
</html>