const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');

const groupSchema = new mongoose.Schema(
  {
    name: { type: String, required: true, trim: true },
    posts: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Post' }],
    description: { type: String },
    admin: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }, // owner (admin)
    coAdmins: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }], // co-admins
    coverPhoto: { type: mongoose.Schema.Types.ObjectId, ref: 'File' },
    profilePhoto: { type: mongoose.Schema.Types.ObjectId, ref: 'File' },
    rules: [String],
    type: { type: String, enum: ['public', 'private'], default: 'public' },
    enableInvitation: { type: Boolean, default: false },
    requests: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
    members: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
    catchups: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
  },
  {
    timestamps: true,
  },
);

groupSchema.index({ name: 1, createdAt: 1, updatedAt: 1 });

groupSchema.plugin(toJSON);
groupSchema.plugin(paginate);

const Group = mongoose.model('Group', groupSchema);

module.exports = Group;
