const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { PremiumSubscriber, PremiumSubscription, User } = require('../models');
const { stripe } = require('../config/config');
const { businessEnums } = require('../config/constants');
const { capitalizeFirstChar } = require('../utils/stringFormatting');
const config = require('../config/config');
const logger = require('../config/logger');
const validateId = require('./shared/validateId');
const emailService = require('./email.service');
const { pick } = require('../utils/pick');
const { scheduleNotifyPremiumSubscriptionExpiration } = require('../utils/jobs/jobSchedulers');

const createCheckoutSession = async (user, body, subscriber) => {
  // const { amount, currency, service } = await initializePayment(user, body);

  const plan = Object.values(businessEnums.premiumSubscriptionPlans).find((p) => p.name === body.plan);
  const period = Object.values(businessEnums.premiumSubscriptionPeriods).find((p) => p[0] === body.period);
  const price = plan.priceMonthly * period[1] * parseInt(body.periodCount, 10);

  if (!plan) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Plan not found');
  }

  let { stripeCustomerId } = user;
  // Create Stripe Customer if it doesn't exist
  if (!stripeCustomerId) {
    const customer = await stripe.client.customers.create({
      email: user.email,
      name: `${user.firstName} ${user.lastName}`,
      metadata: {
        userId: user._id.toString(),
      },
    });
    await User.findByIdAndUpdate(user._id, { stripeCustomerId: customer.id });
    stripeCustomerId = customer.id;
  }

  const productData = {
    name: `UnykEd ${capitalizeFirstChar(body.plan)}`,
    images: ['https://res.cloudinary.com/unyked/image/upload/v1738580406/website_one-on-one_vsqosd.png'],
  };

  const session = await stripe.client.checkout.sessions.create({
    payment_method_types: ['card'],
    ui_mode: 'embedded', // 'embedded' || hosted
    customer: stripeCustomerId,
    line_items: [
      {
        price_data: {
          currency: plan.currency,
          product_data: productData,
          unit_amount: price,
        },
        quantity: 1,
      },
    ],
    mode: 'payment',

    // success_url: `${config.client.baseUrl}/payment/success?session_id={CHECKOUT_SESSION_ID}`, // comment
    // cancel_url: `${config.client.baseUrl}/payment/cancel`, // comment
    return_url: `${config.client.baseUrl}/premium/checkout/return?session_id={CHECKOUT_SESSION_ID}&data=${JSON.stringify(
      body,
    )}`, // uncomment

    // automatic_tax: { enabled: true },
    metadata: {
      userId: user._id.toString(),
      subscriber,
      price,
      ...body,
    },
  });

  // return session;

  // return { sessionId: session.id, url: session.url }; // comment
  return { sessionId: session.id, clientSecret: session.client_secret }; // uncomment
};

const createSubscriber = async (user, body) => {
  const existingSubscriber = await PremiumSubscriber.findOne({ user: user._id });

  // TODO: On subscription renewal, check if the existing subscriber has an active subscription
  if (existingSubscriber) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Subscriber already exists. Renew subscription instead.');
  }

  // Create a new subscriber
  // const subscriber = await PremiumSubscriber.create({ user: user._id, plan: body.plan });
  // Create a checkout session
  try {
    return await createCheckoutSession(user, body);
  } catch (error) {
    logger.error(error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error creating checkout session');
  }
};

const createSubscription = async (checkoutSession, transaction) => {
  const existingSubscription = await PremiumSubscription.findOne({ transaction: transaction._id });

  try {
    if (!existingSubscription) {
      const { period, periodCount: periodCountParam, price, userId, plan } = checkoutSession.metadata;
      const periodCount = Number(periodCountParam);

      const today = new Date();
      const expiresAt = new Date(today);
      if (period === 'monthly') {
        // Calculate a date that is one month to todays date
        expiresAt.setMonth(expiresAt.getMonth() + periodCount);
      } else if (period === 'yearly') {
        // Calculate a date that is one year to todays date
        expiresAt.setFullYear(expiresAt.getFullYear() + periodCount);
      }

      let { subscriber } = checkoutSession.metadata;
      const renewal = !!subscriber;
      if (!subscriber) {
        subscriber = await PremiumSubscriber.create({ user: userId, plan });
        await User.findByIdAndUpdate(checkoutSession.metadata.userId, { premiumSubscriber: subscriber });
      } else {
        subscriber = await PremiumSubscriber.findById(subscriber);
      }
      if (!subscriber) {
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Subscriber not found');
      }
      if (subscriber.cancelled) {
        await PremiumSubscriber.findByIdAndUpdate(subscriber._id, { cancelled: false, cancelledAt: null });
      }

      let subscriptionData = {
        subscriber,
        price,
        period,
        startDate: today,
        expiresAt,
        transaction: transaction._id,
      };
      const existingActiveSubscription = await PremiumSubscription.findOne({ subscriber, active: true });
      if (existingActiveSubscription) {
        await PremiumSubscription.findByIdAndUpdate(existingActiveSubscription._id, { active: false, renewedAt: today });

        subscriptionData = {
          ...subscriptionData,
          renewingParent: existingActiveSubscription._id,
          expiresAt: new Date(expiresAt.getTime() + existingActiveSubscription.expiresAt.getTime() - today.getTime()),
        };
      }

      const subscription = await PremiumSubscription.create(subscriptionData);
      if (existingActiveSubscription) {
        existingActiveSubscription.renewingChild = subscription._id;
        await existingActiveSubscription.save();
      }
      if (!renewal) {
        await emailService.sendPremiumWelcomeEmail(await User.findById(checkoutSession.metadata.userId));
      } else {
        // Send email for renewal;
      }

      // call scheduler
      await scheduleNotifyPremiumSubscriptionExpiration({
        subscriptionId: subscription._id.toString(),
        userId,
        expiresAt,
      });

      return subscription._id;
    }
  } catch (error) {
    logger.error(error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error creating subscription');
  }
};

const getSessionStatus = async (sessionId, requestingUser) => {
  // eslint-disable-next-line global-require
  const data = await require('./transaction.service').getSessionStatus(
    sessionId,
    requestingUser._id.toString(),
    'create-premium-subscription',
  );
  return data;
};

const getSubscriptions = async (filterParam, optionsParam) => {
  const filter = { ...filterParam };
  const options = { ...optionsParam };

  if (filter.user) {
    let subscriber;
    if (validateId(filter.user)) {
      filter.user = filter.user.toString();
      subscriber = await PremiumSubscriber.findOne({ user: filter.user });

      if (subscriber) {
        if (filter.subscriber && filter.subscriber !== subscriber._id.toString()) {
          delete filter.subscriber;
        } else {
          filter.subscriber = subscriber._id;
        }
      }
    } else {
      // Use an arbitrary object id that doesn't exist
      filter.subscriber = '000000000000000000000000';
    }
    delete filter.user;
  }

  if (filter.subscriber) {
    if (!validateId(filter.subscriber)) {
      filter.subscriber = '000000000000000000000000';
    }
  }

  if (filter.active) {
    filter.expiresAt = filter.active === 'true' ? { $gte: new Date() } : { $lt: new Date() };
    filter.active = filter.active === 'true';
    // delete filter.active;
  }

  options.populate = [{ path: 'subscriber', select: 'plan', populate: { path: 'user', select: 'firstName lastName' } }];

  const subscriptions = await PremiumSubscription.paginate(filter, options);
  return subscriptions;
};

const getActiveSubscription = async (userId) => {
  const subscriber = await PremiumSubscriber.findOne({ user: userId });

  if (!subscriber) {
    return null;
  }

  const subscription = await PremiumSubscription.findOne({
    subscriber: subscriber._id,
    // expiresAt: { $gte: new Date() },
    active: true,
  });

  return subscription;
};

const getSubscriptionById = async (id, user) => {
  if (!validateId(id)) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Subscription not found');
  }

  let subscriber;
  if (user.premiumSubscriber) {
    subscriber = await PremiumSubscriber.findById(user.premiumSubscriber);
  }

  const subscription = await PremiumSubscription.findById(id).populate([
    { path: 'subscriber', select: 'plan', populate: { path: 'user', select: 'firstName lastName' } },
  ]);
  if (!subscription) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Subscription not found');
  }

  const isInternalUser = user.roles.some((role) => role === 'admin' || role === 'super-admin');
  if (!isInternalUser && (!subscription || subscriber._id.toString() !== subscription.subscriber.toString())) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You are not allowed to access this subscription');
  }
  return subscription;
};

const getLatestSubscription = async (user) => {
  if (!user.premiumSubscriber) {
    return null;
  }
  return PremiumSubscription.findOne({ subscriber: user.premiumSubscriber })
    .sort({ createdAt: -1 })
    .populate({ path: 'subscriber', select: 'plan' });
};

const updateSubscription = async (user, body) => {
  const subscriberId = user.premiumSubscriber?.toString();
  if (!validateId(subscriberId, undefined, false)) {
    throw new ApiError(httpStatus.BAD_REQUEST, "You're not subscribed");
  }

  let activeSubscription = await PremiumSubscription.find({
    subscriber: subscriberId,
    // expiresAt: { $gte: new Date() },
    active: true,
  });
  if (activeSubscription.length > 1) {
    logger.error('Multiple active subscriptions found');
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Multiple active subscriptions found');
  }
  [activeSubscription] = activeSubscription;

  const today = new Date();
  let response;

  if (body.type === 'cancel') {
    /**
     * On cancellation, the subscriber is labelled as cancelled and the active subscription's autoRenew property is marked as false
     *
     * On renewing a subscripion, if the subscriber is marked as cancelled, it's changed to not cancelled
     */
    if (!activeSubscription) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'You do not have an active subscription');
    }
    await PremiumSubscriber.findByIdAndUpdate(subscriberId, { cancelledAt: today, cancelled: true });
    await PremiumSubscription.findByIdAndUpdate(activeSubscription._id, { autoRenew: false, autoRenewCancelledAt: today });

    response = { action: 'cancelled' };
  }
  if (body.type === 'renew') {
    const checkoutBody = pick(body, ['plan', 'period', 'periodCount']);
    response = { action: 'renewed', data: await createCheckoutSession(user, checkoutBody, subscriberId) };
  }

  return response;
};

module.exports = {
  createSubscriber,
  createSubscription,
  getSessionStatus,
  getSubscriptions,
  getActiveSubscription,
  getSubscriptionById,
  getLatestSubscription,
  updateSubscription,
};
