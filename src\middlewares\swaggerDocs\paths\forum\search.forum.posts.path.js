module.exports = {
  get: {
    tags: ['Forum Posts'],
    summary: 'Search forum posts',
    parameters: [
      {
        in: 'query',
        name: 'text',
        schema: {
          type: 'string',
          example: 'search text',
        },
        required: true,
        description: 'Text to search in forum posts',
      },
      {
        in: 'query',
        name: 'topic',
        schema: {
          type: 'string',
          example: 'topic name',
        },
        description: 'Optional topic to filter forum posts',
      },
    ],
    responses: {
      200: {
        description: 'Forum posts retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: {
                  type: 'string',
                  example: 'SUCCESS',
                },
                data: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string', example: '60db5f3a68f2387bd8a8e536' },
                      topic: { type: 'string', example: 'Topic Name' },
                      text: { type: 'string', example: 'Forum post content' },
                      user: {
                        type: 'object',
                        properties: {
                          username: { type: 'string', example: 'username1' },
                          photo: {
                            type: 'object',
                            properties: {
                              url: {
                                type: 'string',
                                format: 'url',
                                example:
                                  'https://www.storage.com/users%2Fphoto/74570fc0-h8i0-777-92c2-61dcf2fafad1-866-536x354.jpg',
                              },
                            },
                          },
                          firstName: { type: 'string', example: 'George O' },
                          lastName: { type: 'string', example: 'Darwin' },
                          tagLine: { type: 'string', example: '' },
                          middleName: { type: 'string', example: '' },
                          id: { type: 'string', example: '651bb5bf4eb9240327ea5554' },
                        },
                      },
                      media: {
                        type: 'array',
                        items: {
                          type: 'object',
                          properties: {
                            url: { type: 'string', format: 'uri', example: 'www.image.com' },
                          },
                        },
                      },
                      upvotes: {
                        type: 'array',
                        items: {
                          type: 'string',
                          example: '60db5f3a68f2387bd8a8e536',
                        },
                      },
                      downvotes: {
                        type: 'array',
                        items: {
                          type: 'string',
                          example: '60db5f3a68f2387bd8a8e536',
                        },
                      },
                      accepted: { type: 'boolean', example: true },
                    },
                  },
                },
                message: {
                  type: 'string',
                  example: 'Forum posts retrieved successfully',
                },
              },
            },
          },
        },
      },
    },
  },
};
