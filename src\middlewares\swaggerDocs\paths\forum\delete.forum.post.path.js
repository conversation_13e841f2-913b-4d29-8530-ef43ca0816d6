module.exports = {
  delete: {
    summary: 'Delete a forum post by ID',
    tags: ['Forum Posts'],
    parameters: [
      {
        name: 'id',
        in: 'path',
        description: 'ID of the forum post',
        required: true,
        type: 'string',
        format: 'objectId',
      },
    ],
    responses: {
      200: {
        description: 'Forum post deleted successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string', example: 'SUCCESS' },
                message: { type: 'string', example: 'Forum post with ID 12345 deleted' },
              },
              required: ['status', 'message'],
            },
          },
        },
      },
      404: {
        description: 'Forum post not found',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string', example: 'FAILED' },
                message: { type: 'string', example: 'Forum post not found' },
              },
              required: ['status', 'message'],
            },
          },
        },
      },
    },
  },
};
