const components = require('./components');
const allPaths = require('./paths');

const info = {
  openapi: '3.0.0',
  info: {
    version: '1.0.0',
    title: 'UnykEd API',
    description: "This is an API for UnykEd's frontend application",
    contact: {
      name: 'UnykEd Support',
      email: '<EMAIL>',
      url: 'http://unyked.com/support',
    },
    license: {
      name: 'Apache 2.0',
      url: 'http://www.apache.org/licenses/LICENSE-2.0.html',
    },
    servers: [
      {
        url: 'http://localhost:4000/v1',
        description: 'This is the local server',
      },
      {
        url: 'https://theunykedserver.onrender.com',
        description: 'This is the hosted server',
      },
    ],
  },
};

const prefixKeys = (object, prefix) =>
  Object.keys(allPaths).reduce((acc, path) => {
    acc[`${prefix}${path}`] = object[path];
    return acc;
  }, {});

const paths = {
  paths: prefixKeys(allPaths, '/v1'),
};

module.exports = {
  ...info,
  ...paths,
  ...components,
};
