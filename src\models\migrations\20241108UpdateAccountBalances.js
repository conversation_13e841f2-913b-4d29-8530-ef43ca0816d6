const Async = require('async');
const logger = require('../../config/logger');
const Transaction = require('../transaction.model');
const GlobalVariable = require('../global.variable.model');
const { businessEnums } = require('../../config/constants');
const Account = require('../account.model');
const Order = require('../order.model');
const Business = require('../business.model');

const varName = 'Update Account Balances';

const updateAccountBalances = async () => {
  const migrated = await GlobalVariable.findOne({ name: varName });

  if (migrated) {
    return;
  }

  const accounts = await Account.find();

  await Async.eachSeries(accounts, async (account) => {
    const allTimeDeposit = await Transaction.find({
      account: account._id,
      $or: [{ status: businessEnums.transactionStatuses.COMPLETE }, { status: businessEnums.transactionStatuses.SUCCEEDED }],
    });

    await Account.updateOne(
      { _id: account._id },
      { allTimeDeposit: allTimeDeposit.reduce((acc, curr) => acc + parseFloat(curr.amount), 0) },
    );
  });

  const businesses = await Business.find();
  await Async.eachSeries(businesses, async (business) => {
    const completedOrders = await Order.find({
      provider: business._id,
      status: businessEnums.orderStatuses.COMPLETED,
    }).populate('transaction');

    await Account.updateOne(
      { business: business._id },
      { withdrawableBalance: completedOrders.reduce((acc, order) => acc + parseFloat(order.transaction.amount), 0) },
    );

    // Assumption: No withdrawal has been made
    const allOrders = await Order.find({
      provider: business._id,
    }).populate('transaction');
    await Account.updateOne(
      { business: business._id },
      { totalBalance: allOrders.reduce((acc, order) => acc + parseFloat(order.transaction.amount), 0) },
    );
  });

  await GlobalVariable.create({ name: varName, value: 'true' });
  logger.info('Account balances updated');
};

module.exports = updateAccountBalances;
