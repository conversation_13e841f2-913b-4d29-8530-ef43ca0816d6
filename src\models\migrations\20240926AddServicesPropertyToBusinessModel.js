/* eslint-disable no-param-reassign */
const Async = require('async');
const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const Service = require('../service.model');
const Business = require('../business.model');

const addServicesPropertyToBusiness = async () => {
  const migrated = await GlobalVariable.findOne({ name: 'Add Services property to business' });

  if (migrated) {
    return;
  }

  const services = await Service.find();

  await Async.eachSeries(services, async (service) => {
    const business = await Business.findById(service.provider);

    await Business.updateOne({ _id: business._id }, { $addToSet: { services: service._id } });
  });

  await GlobalVariable.create({ name: 'Add Services property to business', value: 'true' });
  logger.info('Adding Services property to business completed');
};

module.exports = addServicesPropertyToBusiness;
