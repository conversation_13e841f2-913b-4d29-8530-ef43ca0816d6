module.exports = {
  get: {
    summary: 'Get replies for a forum post',
    tags: ['Forum Replies'],
    parameters: [
      {
        name: 'id',
        in: 'path',
        description: 'ID of the forum post',
        example: '6549f740956baabd787bbd58',
        required: true,
        type: 'string',
        format: 'objectId',
      },
      {
        in: 'query',
        name: 'userId',
        schema: {
          type: 'string',
          format: 'objectId',
          example: '6549f740956baabd787bbd58',
        },
        description: 'ID of the user',
      },
      {
        in: 'query',
        name: 'sortBy',
        schema: {
          type: 'string',
          example: 'createdAt:desc',
        },
        description: 'Sort order for the posts',
      },
      {
        in: 'query',
        name: 'limit',
        schema: {
          type: 'integer',
          format: 'int32',
        },
        description: 'Number of posts to return',
      },
      {
        in: 'query',
        name: 'page',
        schema: {
          type: 'integer',
          format: 'int32',
        },
        description: 'Page number',
      },
    ],
    responses: {
      200: {
        description: 'Replies retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string', example: 'SUCCESS' },
                message: { type: 'string', example: 'Replies retrieved successfully' },
                data: {
                  type: 'object',
                  properties: {
                    results: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          user: {
                            type: 'object',
                            properties: {
                              firstName: { type: 'string', example: 'John' },
                              lastName: { type: 'string', example: 'Doe' },
                              middleName: { type: 'string', example: 'M' },
                              username: { type: 'string', example: 'johndoe' },
                              photo: {
                                type: ['object', 'null'],
                                properties: {
                                  url: { type: 'string', example: 'https://example.com/photo.jpg' },
                                },
                                example: null,
                              },
                              id: { type: 'string', example: '12345' },
                            },
                          },
                          text: { type: 'string', example: 'Lorem ipsum' },
                          media: {
                            type: 'array',
                            items: {
                              type: 'object',
                              properties: {
                                url: { type: 'string', example: 'https://example.com/file.jpg' },
                              },
                              required: ['url'],
                            },
                            example: [{ url: 'https://false-url.com/file.jpg' }],
                          },
                          forumPost: { type: 'string', example: '6549f740956baabd787bbd58' },
                          upvotes: { type: 'array', items: { type: 'string', example: '6549f740956baabd787bbd58' } },
                          downvotes: { type: 'array', items: { type: 'string', example: '6549f740956baabd787bbd58' } },
                          accepted: { type: 'boolean', example: false },
                        },
                      },
                    },
                    page: { type: 'integer', example: 1 },
                    limit: { type: 'integer', example: 10 },
                    totalPages: { type: 'integer', example: 1 },
                    totalResults: { type: 'integer', example: 2 },
                  },
                },
              },
            },
          },
        },
      },
    },
  },
};
