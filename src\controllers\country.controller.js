const httpStatus = require('http-status');
const { countryService } = require('../services');
const catchAsync = require('../utils/catchAsync');

const getCities = catchAsync(async (req, res) => {
  const cities = await countryService.getCities(req);
  res.status(httpStatus.OK).json({ data: cities, message: 'Cities retrieved successfully', status: 'SUCCESS' });
});

const getStates = catchAsync(async (req, res) => {
  const states = await countryService.getStates(req.query.country);
  res.status(httpStatus.OK).json({ data: states, message: 'States retrieved successfully', status: 'SUCCESS' });
});

const getCountries = catchAsync(async (req, res) => {
  const countries = await countryService.getCountries();
  res.status(httpStatus.OK).json({ data: countries, message: 'Countries retrieved successfully', status: 'SUCCESS' });
});

module.exports = {
  getStates,
  getCountries,
  getCities,
};
