module.exports = {
  get: {
    summary: 'Get a specific forum post reply',
    tags: ['Forum Replies'],
    parameters: [
      {
        name: 'forumPostId',
        in: 'path',
        description: 'ID of the forum post',
        example: '6549f740956baabd787bbd58',
        required: true,
        type: 'string',
        format: 'objectId',
      },
      {
        name: 'id',
        in: 'path',
        description: 'ID of the forum post reply',
        example: '6549f740956baabd787bbd58',
        required: true,
        type: 'string',
        format: 'objectId',
      },
    ],
    responses: {
      200: {
        description: 'Reply retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string', example: 'SUCCESS' },
                message: { type: 'string', example: 'Reply retrieved successfully' },
                data: {
                  type: 'object',
                  properties: {
                    user: {
                      type: 'object',
                      properties: {
                        firstName: { type: 'string', example: 'John' },
                        lastName: { type: 'string', example: 'Doe' },
                        middleName: { type: 'string', example: 'M' },
                        username: { type: 'string', example: 'johndoe' },
                        photo: {
                          type: 'object',
                          properties: {
                            url: { type: 'string', example: 'https://example.com/photo.jpg' },
                          },
                        },
                        id: { type: 'string', example: '12345' },
                      },
                    },
                    text: { type: 'string', example: 'Reply text' },
                    media: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          url: { type: 'string', example: 'https://example.com/file.jpg' },
                        },
                      },
                    },
                    forumPost: { type: 'string', example: '6549f740956baabd787bbd58' },
                    upvotes: { type: 'array', items: { type: 'string', example: '6549f740956baabd787bbd58' } },
                    downvotes: { type: 'array', items: { type: 'string', example: '6549f740956baabd787bbd58' } },
                    accepted: { type: 'boolean', example: false },
                    id: { type: 'string', example: '6549f783956baabd787bbd5e' },
                  },
                },
              },
            },
          },
        },
      },
      404: {
        description: 'Reply not found',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string', example: 'FAILED' },
                message: { type: 'string', example: 'Reply record not found' },
              },
            },
          },
        },
      },
    },
  },
};
