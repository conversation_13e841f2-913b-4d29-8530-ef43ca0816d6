const express = require('express');
const authRoute = require('./auth.route');
const activityLogRoute = require('./activity.log.route');
const businessRoute = require('./business.route');
const blogPostRoute = require('./blog.post.route');
const botRecordRoute = require('./bot.record.route');
const careerRoute = require('./career.jobs.route');
const commentRoute = require('./comment.route');
const countryRoute = require('./country.route');
const feedbackRoute = require('./feedback.route');
const forumRoute = require('./forum.route');
const groupRoute = require('./group.route');
const globalRoute = require('./global.route');
const messageRoute = require('./message.route');
const subscriberRoute = require('./subscriber.route');
const notificationRoute = require('./notification.route');
const orderRoute = require('./order.route');
const postRoute = require('./post.route');
const profileRoute = require('./profile.route');
const userRoute = require('./user.route');
const resourceLibraryRoute = require('./resource.library.route');
const rootRoute = require('./root');
const scholarshipRoute = require('./scholarship.route');
const settingRoute = require('./setting.route');
const serviceRoute = require('./service.route');
const universityRoute = require('./university.route');
const schoolRoute = require('./school.route');
const issueRoute = require('./issue.report.route');
const transactionRoute = require('./transaction.route');
const premiumSubscriberRoute = require('./premium.subscriber.route');

const { interceptResponseBody } = require('../../middlewares/responseInterceptor');

const router = express.Router();

const pathsWithRoutesRequiringRawBody = ['/transactions'];
let jsonMiddlewareDefined = false;

const defaultRoutes = [
  // Add paths here that have one or more routes which require raw body. In each route mind the order of express.raw() and express.json()
  { path: '/transactions', route: transactionRoute },
  { path: '/premium-subscriptions', route: premiumSubscriberRoute },

  // This singlular route should always be inbetween the routes that require raw body and the routes that do not require raw body
  // express.json() middleware is used in the forEach below in a way to ensure it is defined before the routes that need it
  { path: '/', route: rootRoute },

  // Add routes that do not require raw body here
  { path: '/activity', route: activityLogRoute },
  { path: '/auth', route: authRoute },
  { path: '/business', route: businessRoute },
  { path: '/blogs', route: blogPostRoute },
  { path: '/bot-records', route: botRecordRoute },
  { path: '/careers', route: careerRoute },
  { path: '/comments', route: commentRoute },
  { path: '/countries', route: countryRoute },
  { path: '/feedback', route: feedbackRoute },
  { path: '/forums', route: forumRoute },
  { path: '/groups', route: groupRoute },
  { path: '/messages', route: messageRoute },
  { path: '/subscribers', route: subscriberRoute },
  { path: '/notifications', route: notificationRoute },
  { path: '/orders', route: orderRoute },
  { path: '/posts', route: postRoute },
  { path: '/profile', route: profileRoute },
  { path: '/resource-library', route: resourceLibraryRoute },
  { path: '/scholarships', route: scholarshipRoute },
  { path: '/settings', route: settingRoute },
  { path: '/universities', route: universityRoute },
  { path: '/users', route: userRoute },
  { path: '/global', route: globalRoute },
  { path: '/schools', route: schoolRoute },
  { path: '/services', route: serviceRoute },
  { path: '/issues', route: issueRoute },
  { path: '/orders', route: orderRoute },
];

defaultRoutes.forEach((route) => {
  if (!jsonMiddlewareDefined && !pathsWithRoutesRequiringRawBody.includes(route.path)) {
    router.use(express.json());
    router.use(interceptResponseBody);
    jsonMiddlewareDefined = true;
  }
  router.use(route.path, route.route);
});

module.exports = router;
