/* eslint-disable no-param-reassign */
const Joi = require('joi');
const httpStatus = require('http-status');
const { pick } = require('../utils/pick');
const ApiError = require('../utils/ApiError');
const logger = require('../config/logger');

const isPromise = (value) => typeof value === 'object' && value !== null && value.constructor === Promise;

const validate = (schema) => async (req, res, next) => {
  if (isPromise(schema)) {
    // eslint-disable-next-line no-param-reassign
    schema = await schema;
  }

  if (schema && !schema.query) {
    schema.query = Joi.object();
  }

  try {
    schema.query = schema.query.append({
      returnUrl: Joi.string(),
    });
  } catch (error) {
    if (error.message === 'schema.query.append is not a function') {
      schema.query.returnUrl = Joi.string();
    }
    logger.error(`Error in appending schema.query.returnUrl. URL visited: ${req.originalUrl}`);
    logger.error(error);
  }

  const validSchema = pick(schema, ['params', 'query', 'body', 'file', 'files']);
  const object = pick(req, Object.keys(validSchema));
  const { value, error } = Joi.compile(validSchema)
    .prefs({ errors: { label: 'key' }, abortEarly: false })
    .validate(object);

  if (error) {
    const errorMessage = error.details.map((details) => details.message.replace(/"/g, '')).join(', ');
    return next(new ApiError(httpStatus.BAD_REQUEST, errorMessage));
  }
  Object.assign(req, value);
  return next();
};

module.exports = validate;
