/**
 * Create an object composed of the picked object properties
 * @param {Object} object
 * @param {string[]} keys
 * @returns {Object}
 */
const pick = (object, keys) => {
  return keys.reduce((obj, key) => {
    if (object && Object.prototype.hasOwnProperty.call(object, key)) {
      // eslint-disable-next-line no-param-reassign
      obj[key] = object[key];
    }
    return obj;
  }, {});
};

// Remove keys from an object

const removeKeysFromObject = (obj, keysToRemove) => {
  const keysToRemoveArray = Array.isArray(keysToRemove) ? keysToRemove : [keysToRemove];
  const newObj = { ...obj };
  keysToRemoveArray.forEach((key) => {
    if (Object.prototype.hasOwnProperty.call(newObj, key)) {
      delete newObj[key];
    }
  });
  return newObj;
};

const removeUndefinedKeys = (obj) => {
  Object.keys(obj).forEach((key) => {
    if (obj[key] === undefined) {
      // eslint-disable-next-line no-param-reassign
      delete obj[key];
    }
  });
  return obj;
};

module.exports = { pick, removeKeysFromObject, removeUndefinedKeys };
