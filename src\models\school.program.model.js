const mongoose = require('mongoose');
const { toJSON, paginate, privacy } = require('./plugins');
const { schoolsEnums } = require('../config/constants');

const schoolProgramSchema = new mongoose.Schema(
  {
    name: { type: String, required: true, trim: true },
    description: { type: String, trim: true },
    school: { type: mongoose.Schema.Types.ObjectId, ref: 'School', required: true },
    programTypes: [{ type: mongoose.Schema.Types.ObjectId, ref: 'SchoolProgramType' }],
    programType: { type: String, trim: true, enum: Object.values(schoolsEnums.programTypes) },
    tuition: { type: String, trim: true },
    applicationFee: { type: Number },
    durationMonths: { type: Number },
    applicationPeriod: [
      {
        season: { type: String, trim: true, enum: Object.values(schoolsEnums.applicationSeasons), required: true },
        duration: {
          type: [Date],
          trim: true,
          required: true,
          validate: {
            validator: (value) => value.length === 2,
            message: 'Duration must have exactly 2 dates: start and end.',
          },
        },
      },
    ],
    applicationRequirements: { type: String, trim: true },
    url: { type: String, trim: true },
    applicationUrl: { type: String, trim: true },
    funding: [
      {
        category: { type: String, required: true, trim: true },
        fundingType: { type: String, required: true, trim: true },
        description: { type: String, trim: true },
      },
    ],
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  },
  {
    timestamps: true,
  },
);

schoolProgramSchema.plugin(toJSON);
schoolProgramSchema.plugin(paginate);
schoolProgramSchema.plugin(privacy);

const SchoolProgram = mongoose.model('SchoolProgram', schoolProgramSchema);

module.exports = SchoolProgram;
