module.exports = {
  get: {
    tags: ['Countries'],
    summary: 'Get states',
    description: 'Get a list of states in a country',
    parameters: [
      {
        in: 'query',
        name: 'country',
        description: 'Country to fetch states from',
        schema: { type: 'string', example: 'United States' },
      },
    ],
    responses: {
      200: {
        description: 'States retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                data: {
                  type: 'object',
                  properties: {
                    docs: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          name: { type: 'string', example: 'Los Angeles' },
                          _id: { type: 'string', example: '61e56a452bda0400236e7a8b' },
                        },
                      },
                    },
                    count: { type: 'integer', example: 1 },
                  },
                },
                message: { type: 'string', example: 'Cities retrieved successfully' },
                status: { type: 'string', example: 'SUCCESS' },
              },
            },
          },
        },
      },
    },
  },
};
