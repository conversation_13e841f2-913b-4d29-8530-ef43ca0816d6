const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');

const botRecordSchema = mongoose.Schema(
  {
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    userQuestion: { type: String },
    botResponse: { type: String },
  },
  {
    timestamps: true,
  },
);

botRecordSchema.index({ name: 1, createdAt: 1, updatedAt: 1 });

botRecordSchema.plugin(toJSON);
botRecordSchema.plugin(paginate);

const BotRecord = mongoose.model('BotRecord', botRecordSchema);

module.exports = BotRecord;
