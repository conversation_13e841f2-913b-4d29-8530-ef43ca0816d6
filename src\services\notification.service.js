const httpStatus = require('http-status');
const Notification = require('../models/notification.model');
// const UserService = require('./user.service');
const ApiError = require('../utils/ApiError');
const { generateNotificationMessage, notificationTypes } = require('./shared/notification.handler');
const { GroupSetting, User } = require('../models');
const { checkNotificationSetting, emit } = require('./sockets/socket.shared');
const { notificationSettingModelFields, groupSettingsEnums } = require('../config/constants');
const EmailService = require('./email.service');
const { scheduleCreateReaction, scheduleCreateFollower, scheduleRepost } = require('../utils/jobs/jobSchedulers');

const createFollowNotification = async (sender, recipient) => {
  // sender --> follower. recipient --> followed
  // recipient is the object of the user receiving the notification
  const type = notificationTypes.FOLLOW;
  if (await checkNotificationSetting(recipient._id, notificationSettingModelFields.FOLLOW)) {
    const message = await generateNotificationMessage(sender, type);
    const notification = new Notification({ recipient: recipient._id, message, type, actor: sender._id });
    await notification.save();
    await emit(message, recipient);
    await scheduleCreateFollower({ sender, recipient });
  }
};

const createJoinGroupNotification = async (sender, admins, details) => {
  // send notifications to admin and coAdmins
  // sender --> user requesting to join. details --> {groupId, groupName}
  const type = notificationTypes.JOIN_GROUP_REQUEST;
  const adminPromises = admins.map(async (admin) => {
    if (await checkNotificationSetting(admin, notificationSettingModelFields.JOIN_GROUP_REQUEST)) {
      const adminInfo = await User.findById(admin);
      const message = await generateNotificationMessage(sender, type, details);
      const notification = new Notification({ recipient: admin, message, type, actor: sender._id });
      await notification.save();
      await emit(message, admin);
      await EmailService.sendJoinGroupRequestEmail(sender, adminInfo, details);
    }
  });
  await Promise.all(adminPromises);
};

const createAcceptRejectGroupRequestNotification = async (recipient, details, accept = false) => {
  // send notifications to a user that they've been accepted into a group
  // recipient is the id of the user receiving the notification
  // details --> {groupId, actor, groupName, profilePhoto}
  const settingNotifType = accept
    ? notificationSettingModelFields.ACCEPT_GROUP_REQUEST
    : notificationSettingModelFields.REJECT_GROUP_REQUEST;

  if (await checkNotificationSetting(recipient, settingNotifType)) {
    const type = accept ? notificationTypes.ACCEPT_GROUP_REQUEST : notificationTypes.REJECT_GROUP_REQUEST;
    const message = await generateNotificationMessage(null, type, details);
    const notification = new Notification({ recipient, message, type, actor: details.actor });
    await notification.save();
    await emit(message, recipient);
    await EmailService.sendAcceptRejectGroupRequestEmail(recipient, { ...details, accept });
  }
};

const createNewAdminNotification = async (sender, recipient, details, isRandom = false) => {
  // recipient is the id of the user receiving the notification
  const settingNotifType = isRandom
    ? notificationSettingModelFields.NEW_RANDOM_ADMIN
    : notificationSettingModelFields.NEW_ADMIN;

  if (await checkNotificationSetting(recipient, settingNotifType)) {
    const type = isRandom ? notificationTypes.NEW_RANDOM_ADMIN : notificationTypes.NEW_ADMIN;
    const message = await generateNotificationMessage(sender, type, details);
    const notification = new Notification({ recipient, message, type, actor: sender._id });
    await notification.save();
    await emit(message, recipient._id);
    await EmailService.sendCreateNewAdminEmail(sender, recipient, details, isRandom);
  }
};

const createNewCoAdminNotification = async (sender, recipient, details) => {
  // recipient is the record and not id of the user receiving the notification
  const type = notificationTypes.NEW_CO_ADMIN;
  const message = await generateNotificationMessage(sender, type, details);
  const notification = new Notification({ recipient, message, type, actor: sender._id });
  await notification.save();
  await emit(message, recipient._id);
  await EmailService.sendNewCoAdminEmail(sender, recipient, details);
};

const createGroupNotification = async (sender, groupId, groupPostId, isAdmin, type, groupName) => {
  // when there is a new event (post || message), send a notification to all group members whose notification setting for that particular event is set to either admin or all
  // from the type, check if it's a post or message then use the groupSetting.post or groupSetting.message
  // send to users if e.g. groupSetting.posts is set to All or if it is set to ADMIN and isAdmin is true
  // this means that users can set to get notification form either every member or only from the admin
  const { users: usersSettings } = await GroupSetting.findOne({ group: groupId });
  await Promise.all(
    usersSettings.map(async (userSetting) => {
      let shouldNotify = false;

      if (type === notificationTypes.GROUP_POST) {
        shouldNotify =
          userSetting.settings.posts === groupSettingsEnums.ALL ||
          (userSetting.settings.posts === groupSettingsEnums.ADMIN && isAdmin);
      } else if (type === notificationTypes.GROUP_MESSAGE) {
        shouldNotify =
          userSetting.settings.messages === groupSettingsEnums.ALL ||
          (userSetting.settings.messages === groupSettingsEnums.ADMIN && isAdmin);
      }

      if (shouldNotify && sender._id.toString() !== userSetting.user.toString()) {
        const recipient = await User.findById(userSetting.user);
        const details = { groupId, groupPostId, groupName };
        const message = await generateNotificationMessage(sender, type, details);
        const notification = new Notification({ recipient, message, type, actor: sender._id, group: groupId });
        await notification.save();
        await emit(message, recipient, 'group-notification');
        await EmailService.sendGroupNotificationEmail(sender, recipient, details, type);
      }
    }),
  );
};

const createReactionNotification = async (sender, recipient, details) => {
  const type = notificationTypes.REACTION;

  if (
    sender._id.toString() !== recipient._id.toString() &&
    (await checkNotificationSetting(recipient._id, notificationSettingModelFields.REACTION))
  ) {
    const message = await generateNotificationMessage(sender, type, details);
    const notification = new Notification({ recipient, message, type, actor: sender._id });

    await notification.save();
    await emit(message, recipient._id);
    await scheduleCreateReaction({ sender, recipient, details });
  }
};

const createRepostNotification = async (sender, recipient, details) => {
  // const details = { resourceId: post._id, resourceName: Post.modelName };
  if (
    sender._id.toString() !== recipient._id.toString() &&
    (await checkNotificationSetting(recipient._id, notificationSettingModelFields.REPOST))
  ) {
    const type = notificationTypes.REPOST;
    const message = await generateNotificationMessage(sender, type, details);
    const notification = new Notification({ recipient, message, type, actor: sender._id });

    await notification.save();
    await emit(message, recipient._id);
    await scheduleRepost({ sender, recipient, details });
  }
};

const createCommentNotification = async (sender, recipient, details) => {
  // sender --> user object of the person commenting
  // recipient --> user object of the person receiving the notification (author of post or comment being commented on)
  // details.secondRecipient --> user object of the person who made the parent comment in case of a second level comment
  if (
    sender._id.toString() !== recipient._id.toString() &&
    (await checkNotificationSetting(recipient._id, notificationSettingModelFields.COMMENT))
  ) {
    // Don't send notification to the sender
    const type = notificationTypes.COMMENT;
    const message = await generateNotificationMessage(sender, type, details);
    const notification = new Notification({ recipient, message, type, actor: sender._id });
    await notification.save();
    await emit(message, recipient._id);
    await EmailService.sendCommentNotificationEmail(sender, recipient, details, type);
  }
  if (
    details.secondRecipient &&
    sender._id.toString() !== details.secondRecipient._id.toString() &&
    sender._id.toString() !== details.post.user.toString() &&
    (await checkNotificationSetting(details.secondRecipient._id, notificationSettingModelFields.COMMENT_OF_COMMENT))
  ) {
    const type = notificationTypes.COMMENT_OF_COMMENT;
    const secondRecipientsMessage = await generateNotificationMessage(sender, type, details);
    const secondRecipientNotification = new Notification({
      recipient: details.secondRecipient._id,
      message: secondRecipientsMessage,
      type,
      actor: sender._id,
    });
    await secondRecipientNotification.save();
    await emit(secondRecipientsMessage, details.secondRecipient._id);
    await EmailService.sendCommentNotificationEmail(sender, recipient, details, type);
  }
};

const createGroupInviteNotification = async (sender, recipient, inviteUrl, group) => {
  const type = notificationTypes.GROUP_INVITE;
  const message = await generateNotificationMessage(sender, type, { inviteUrl });
  const notification = new Notification({ recipient, message, type });
  await notification.save();
  await emit(message, recipient._id);
  await EmailService.sendGroupInviteNotificationEmail(sender, recipient, inviteUrl, group);
};

const getNotifications = async (filter, options) => {
  const notifications = await Notification.paginate(filter, {
    ...options,
    populate: {
      path: 'actor',
      select: 'firstName lastName username _id photo tagLine',
      populate: { path: 'photo', select: 'url' },
    },
  });
  return notifications;
};

const toggleRead = async (req) => {
  const notificationId = req.params.id;
  let notification;
  try {
    notification = await Notification.findById(notificationId);
  } catch (error) {
    if (error.name !== 'CastError') {
      throw new ApiError(httpStatus.BAD_REQUEST, error.message);
    }
  }
  if (notification) {
    if (notification.recipient.toString() !== req.user._id.toString()) {
      throw new ApiError(httpStatus.FORBIDDEN, "You don't have permission to perform this action");
    }
    if (!notification) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Notification record not found');
    }
    Object.assign(notification, { read: !notification.read });
    await notification.save();
    return `Notification read status updated as ${notification.read ? 'read' : 'unread'}`;
  }
  if (notificationId === 'all-read') {
    await Notification.updateMany({ recipient: req.user._id }, { read: true });
    return 'All notifications marked as read';
  }
  if (notificationId === 'all-unread') {
    await Notification.updateMany({ recipient: req.user._id }, { read: false });
    return 'All notifications marked as unread';
  }
  throw new ApiError(httpStatus.NOT_FOUND, 'Notification record not found');
};

const createMentionNotification = async (sender, recipient, details) => {
  const type = notificationTypes.MENTION;
  const message = await generateNotificationMessage(sender, type, details);
  const notification = new Notification({ recipient, message, type, actor: sender._id });
  await notification.save();
  await emit(message, recipient._id);
  await EmailService.sendMentionNotificationEmail(sender, recipient, details);
};

const createServiceNotification = async (sender, recipient, type, details) => {
  // details -> {receiverType: client || provider, orderId}
  const message = await generateNotificationMessage(sender, type, details);
  const notification = new Notification({ recipient, message, type, actor: sender._id });
  await notification.save();
  await emit(message, recipient._id);

  if (type === notificationTypes.REVISION_REQUESTED) {
    await EmailService.sendRevisionRequestEmail(sender, recipient, details, type);
  } else if (type === notificationTypes.REVISION_FULFILLED) {
    await EmailService.sendRevisionFulfilledEmail(sender, recipient, details);
  } else if (type === notificationTypes.REVISION_COMPLETED) {
    await EmailService.sendRevisionCompletedEmail(sender, recipient, details);
  } else if (type === notificationTypes.REVISION_CANCELLED) {
    await EmailService.sendRevisionCancelledEmail(sender, recipient, details);
  } else if (type === notificationTypes.ORDER_REQUIREMENTS_SUBMITTED) {
    await EmailService.sendOrderRequirementsSubmittedEmail(sender, recipient, details);
  } else if (type === notificationTypes.ORDER_DELIVERED) {
    await EmailService.sendOrderDeliveredEmail(sender, recipient, details);
  } else if (type === notificationTypes.ORDER_COMPLETED) {
    await EmailService.sendOrderCompletedEmail(sender, recipient, details);
  } else if (type === notificationTypes.ORDER_CANCELLED) {
    await EmailService.sendOrderCancelledEmail(sender, recipient, details);
  } else if (type === notificationTypes.DISPUTE_REPORTED) {
    await EmailService.sendDisputeOpenedEmail(sender, recipient, details);
  } else if (type === notificationTypes.EXTENSION_REQUESTED) {
    await EmailService.sendExtensionRequestedEmail(sender, recipient, details);
  } else if (type === notificationTypes.EXTENSION_APPROVED) {
    await EmailService.sendExtensionApprovedEmail(sender, recipient, details);
  } else if (type === notificationTypes.EXTENSION_DENIED) {
    await EmailService.sendExtensionDeniedEmail(sender, recipient, details);
  }
};

const createNewOrderNotification = async (sender, recipient, type, details) => {
  // details -> {receiverType: client || provider, orderId, amount, serviceFee, transactionType, serviceName}
  let message;
  if (details.receiverType === 'client') {
    message = await generateNotificationMessage(sender, notificationTypes.NEW_ORDER_CLIENT, details);
  } else if (details.receiverType === 'provider') {
    message = await generateNotificationMessage(sender, notificationTypes.NEW_ORDER_PROVIDER, details);
  }

  const notification = new Notification({ recipient, message, type, actor: sender._id });
  await notification.save();
  await emit(message, recipient._id);

  if (details.receiverType === 'client') {
    await EmailService.sendNewOrderPlacedEmailToClient(sender, recipient, details, notificationTypes.NEW_ORDER_CLIENT);
  } else if (details.receiverType === 'provider') {
    await EmailService.sendNewOrderPlacedEmailToProvider(sender, recipient, details, notificationTypes.NEW_ORDER_PROVIDER);
  }
};

const notifyUsersToCompleteProfile = async (recipient) => {
  // recipient -> {id, email}
  const message = await generateNotificationMessage(null, notificationTypes.COMPLETE_PROFILE);
  const notification = new Notification({ recipient: recipient._id, message, type: notificationTypes.COMPLETE_PROFILE });
  await notification.save();
  await EmailService.sendEmailToCompleteProfile(recipient);
};

module.exports = {
  checkNotificationSetting,
  createFollowNotification,
  createJoinGroupNotification,
  createAcceptRejectGroupRequestNotification,
  createNewAdminNotification,
  createNewCoAdminNotification,
  createGroupNotification,
  createReactionNotification,
  createRepostNotification,
  createCommentNotification,
  createGroupInviteNotification,
  getNotifications,
  toggleRead,
  createMentionNotification,
  createServiceNotification,
  createNewOrderNotification,
  notifyUsersToCompleteProfile,
};
