/* eslint-disable prettier/prettier */
const config = require('../../../../config/config');
const { truncateText, formatDate } = require('../../../../utils/helperMethods');

module.exports = (details) => {
  // details -> { scholarships }
  const { scholarships } = details;

  const viewAllUrl = `${config.client.baseUrl}/resource-hub/scholarships`;

  const html = `
      <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
      <html xmlns="http://www.w3.org/1999/xhtml" lang="en">

      <head>
        <meta http-equiv="Content-Type" content="text/html" charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
        <title>Your Bookmarked Scholarships Deadline Approaching</title>
      </head>

      <body style="margin: 0;font-family: 'Plus Jakarta Sans', sans-serif;background: #f6f9fc; border-radius: 4px">
        <center class="wrapper" style="position: relative; height: 100%; table-layout: fixed;">
          <div class="webkit" style="border-radius: 8px;">
            <table class="outer" style="line-height: 1.65rem;border-spacing: 0;margin: 0 auto;width: 100%;color: #4a4a4a;border-radius: 8px;" align="center">
              <tr>
                <td style="text-align: center; border-bottom: 2px solid rgb(243 244 246);">
                  <center>
                    <a href="${config.client.baseUrl}" style="text-decoration:none;font-weight: bolder;padding: 15px;background: #f6f9fc;">
                      <img src="https://res.cloudinary.com/emmii/image/upload/v1696345178/unyked/sv1zvadt6si3uwkevzki.png" width="100px" alt="" style="border: 0;padding: 15px;">
                    </a>
                  </center>
                </td>
              </tr>

              <tr>
                <td style="padding:0; background: #f6f9fc;">
                  <table style="width: 100%;border-spacing: 0;">
                    <tr>
                      <td style="padding: 0;">
                        <center>
                          <h1 style="margin-top: 30px; font-size: 24px; color: #2a2a2a;">⏰ Deadline Approaching!</h1>
                          <p style="font-size: 16px; color: #4a4a4a; margin-bottom: 30px;">You've bookmarked scholarships
                            closing soon. Don't miss out!</p>
                        </center>

                        <!-- Scholarship Items -->
                        <div style="padding: 0 0.675rem">
                          ${scholarships
                            .map(
                              (s) => `
                              <a href="${viewAllUrl}/${s._id}" style="text-decoration: none; color: inherit;">
                                <div style="background: white; border-radius: 8px; padding: 14px; margin-bottom: 12px; border-left: 4px solid #3772ff;">
                                  <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <h2 style="margin: 0; font-size: 1rem; color: #3772ff;">${s.name}</h2>
                                  </div>
                                  <div style="margin: 0 0 4px 0; font-weight: 600; font-size: 0.8rem; color: #4a4a4a; display: flex; align-items: center;">
                                    <div style="margin-right: 4px; display: flex; justify-content: center; align-items: center;">
                                      <img src="https://storagedevunyked.blob.core.windows.net/assets/email-icons/icons8-location-trimmed.png" style="width: 8px; object-fit: contain;" />
                                    </div>
                                    <p style="margin: 0; padding: 0;">${s.hostCountry}</p>
                                  </div>
                                  <p style="margin: 8px 0; color: #666; font-size: 0.875rem;">${truncateText(s.description)}</p>
                                  <div style="color: #3772ff; font-size: 0.75rem; border-radius: 12px; font-weight: 600;">
                                      ${formatDate(s.deadline)}
                                  </div>
                                </div>
                              </a>
                              `,
                            )
                            .join('')}
                        </div>

                        <!-- Call to Action -->
                        <center style="margin: 30px 0;">
                          <a href="${viewAllUrl}" target="_blank" style="padding: 12px 24px; background: #3772ff; border: none; font-weight: bold; color: white; border-radius: 5px; font-size: 1rem; text-align: center; text-decoration: none;">View Bookmarked Scholarships</a>
                        </center>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>

              <tr>
                <td style="padding: 20px 0.675rem; background: #f6f9fc; text-align: center;">
                  <p style="font-size: 0.875rem; color: #666; margin: 0 0 10px;">Need help with your application?</p>
                  <a href="${config.client.baseUrl}/resources" style="font-size: 0.875rem; color: #3772ff; text-decoration: none; font-weight: 600;">Check our application guide</a>
                </td>
              </tr>

              <tr>
                <td style="height: 10px;background-color: #d7e3ff;padding: 0;border-radius: 0 0 10px 10px;">
                  <p style="font-size: smaller;padding: 30px 0.675rem;">&copy;2025 UnykEd Inc.</p>
                </td>
              </tr>
            </table>
          </div>
        </center>
      </body>
    </html>
  `;
  return html;
};
