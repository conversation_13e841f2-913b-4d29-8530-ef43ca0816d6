const mongoose = require('mongoose');
const { toJSON, paginate, privacy } = require('../plugins');

const profileSchema = new mongoose.Schema({
  business: { type: mongoose.Schema.Types.ObjectId, ref: 'Business' },
  basicInformation: { personalStatement: String },
  education: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Education' }],
  certification: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Certification' }],
  testscore: [{ type: mongoose.Schema.Types.ObjectId, ref: 'TestScore' }],

  experience: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Experience' }],
  volunteering: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Volunteering' }],

  occupation: { type: String, trim: true },
  skills: [{ type: String }],
  hobbies: [{ type: String }],

  project: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Project' }],
  award: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Award' }],
});

profileSchema.plugin(toJSON);
profileSchema.plugin(paginate);
profileSchema.plugin(privacy);

const Profile = mongoose.model('Profile', profileSchema);

module.exports = Profile;
