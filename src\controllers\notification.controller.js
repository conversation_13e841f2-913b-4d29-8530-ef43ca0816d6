const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const NotificationService = require('../services/notification.service');
const { pick, removeUndefinedKeys } = require('../utils/pick');
const validateId = require('../services/shared/validateId');

const getNotifications = catchAsync(async (req, res) => {
  const filter = pick({ ...req.query, recipient: req.user._id }, ['recipient', 'group']);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  removeUndefinedKeys(filter);

  if (!validateId(filter.recipient, undefined, false)) {
    filter.group = undefined;
  }

  const notifications = await NotificationService.getNotifications(filter, options);
  res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data: notifications, message: 'Group Notifications retrieved successfully' });
});

const getUnreadCount = catchAsync(async (req, res) => {
  const filter = { recipient: req.user._id, read: false };
  if (req.query.group) filter.group = req.query.group;

  const { totalResults } = await NotificationService.getNotifications(filter, {});
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: { unreadCount: totalResults } });
});

const toggleRead = catchAsync(async (req, res) => {
  const message = await NotificationService.toggleRead(req);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message });
});

// const updateNotification = catchAsync(async (req, res) => {
//   await NotificationService.updateNotification(req?.params?.id, req.body);
//   res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Notification updated successfully' });
// });

module.exports = {
  getNotifications,
  getUnreadCount,
  toggleRead,
};
