const httpStatus = require('http-status');
const ApiError = require('../../../utils/ApiError');
const { Group } = require('../../../models');

const canRemoveFromGroup = () => async (req, res, next) => {
  let group;
  try {
    group = await Group.findById(req.params.id);
  } catch (err) {
    return next(new ApiError(httpStatus.NOT_FOUND, `Group not found`));
  }

  if (!group) {
    return next(new ApiError(httpStatus.NOT_FOUND, `Group not found`));
  }

  const groupMembers = [...group.members, ...group.coAdmins, group.admin].map((member) => String(member));

  if ((req?.body?.newAdmin && !groupMembers.includes(req?.body?.newAdmin)) || !groupMembers.includes(req?.params?.userId)) {
    return next(new ApiError(httpStatus.BAD_REQUEST, 'User is not a group member'));
  }

  // Admin or Co-Admin can remove a member
  // Admin can remove a Co-Admin
  // Co-Admin cannot remove an Admin
  // Member can only remove themselves
  if (
    !(
      String(group.admin) === String(req.user._id) || // Admin can remove anyone
      (group.coAdmins.includes(req.user._id) && group.admin.toString() !== req.params.userId) || // Co-Admin can remove a member but not an Admin
      req.params.userId === req.user._id.toString()
    )
  ) {
    return next(new ApiError(httpStatus.FORBIDDEN, 'Forbidden: You are not allowed to perform this operation'));
  }

  req.resourceRecord = group;
  next();
};

module.exports = canRemoveFromGroup;
