const mongoose = require('mongoose');
const { toJSON, paginate } = require('../plugins');

const testScoreSchema = new mongoose.Schema({
  testName: { type: String, trim: true, required: true },
  issuingBody: { type: String, trim: true, required: true },
  date: { type: String, trim: true, required: true },
  testScore: { type: String, trim: true, required: true },
});

testScoreSchema.plugin(toJSON);
testScoreSchema.plugin(paginate);

const TestScore = mongoose.model('TestScore', testScoreSchema);
module.exports = TestScore;
