module.exports = {
  post: {
    summary: 'Create a conversation',
    tags: ['Messages'],
    requestBody: {
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              participants: {
                type: 'array',
                items: {
                  type: 'string',
                  example: '5f4d5d2e7c213e2c6c9d3e7d',
                },
              },
              directMessage: {
                type: 'boolean',
                default: true,
                example: true,
                required: false,
              },
            },
            required: ['participants'],
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Conversation created successfully',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Conversation created successfully',
              data: {
                participants: ['651bb5bf4eb9240327ea9d56', '655b09725da6363f72713b48'],
                directMessage: true,
                createdAt: '2024-03-20T15:31:05.170Z',
                updatedAt: '2024-03-20T15:31:05.170Z',
                id: '65fb0139e898c0d58b7453b1',
              },
            },
          },
        },
      },
    },
  },
};
