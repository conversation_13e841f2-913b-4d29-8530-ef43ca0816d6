const Country = require('../../country.model');

const getStripeSupportedCountries = async () => {
  const supportedIsoList = [
    'AU', // Australia
    'AT', // Austria
    'BE', // Belgium
    'BR', // Brazil
    'BG', // Bulgaria
    'CA', // Canada
    'HR', // Croatia
    'CY', // Cyprus
    'CZ', // Czech Republic
    'DK', // Denmark
    'EE', // Estonia
    'FI', // Finland
    'FR', // France
    'DE', // Germany
    'GH', // Ghana
    'GI', // Gibraltar
    'GR', // Greece
    'HK', // Hong Kong
    'HU', // Hungary
    'IN', // India
    'ID', // Indonesia
    'IE', // Ireland
    'IT', // Italy
    'JP', // Japan
    'KE', // Kenya
    'LV', // Latvia
    'LI', // Liechtenstein
    'LT', // Lithuania
    'LU', // Luxembourg
    'MY', // Malaysia
    'MT', // Malta
    'MX', // Mexico
    'NL', // Netherlands
    'NZ', // New Zealand
    'NG', // Nigeria
    'NO', // Norway
    'PL', // Poland
    'PT', // Portugal
    'RO', // Romania
    'SG', // Singapore
    'SK', // Slovakia
    'SI', // Slovenia
    'ZA', // South Africa
    'ES', // Spain
    'SE', // Sweden
    'CH', // Switzerland
    'TH', // Thailand
    'AE', // United Arab Emirates
    'GB', // United Kingdom
    'US', // United States
  ];

  const countries = await Country.find({ iso2: { $in: supportedIsoList } }).select('name');

  return countries; // .map((country) => String(country._id));
};
module.exports = getStripeSupportedCountries;
