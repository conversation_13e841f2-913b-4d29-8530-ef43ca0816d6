const httpStatus = require('http-status');
const { Feedback } = require('../models');
const ApiError = require('../utils/ApiError');
const validateId = require('./shared/validateId');

const createFeedback = async (user, feedbackBody) => {
  const feedback = await Feedback.create({ ...feedbackBody, user: user._id });
  if (!feedback) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Feedback creation failed');
  }
  return feedback;
};

const getFeedbackById = async (feedbackId) => {
  validateId(feedbackId, 'Feedback');
  const feedback = await Feedback.findById(feedbackId).populate([
    { path: 'user', select: 'firstName lastName email photo', populate: { path: 'photo' } },
  ]);
  if (!feedback) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Feedback not found');
  }

  return feedback;
};

const getFeedbacks = async (filterParam, options) => {
  const filter = { ...filterParam };
  const feedbacks = await Feedback.paginate(filter, {
    ...options,
    populate: [{ path: 'user', select: 'firstName lastName email photo', populate: { path: 'photo' } }],
  });
  return feedbacks;
};

module.exports = {
  createFeedback,
  getFeedbackById,
  getFeedbacks,
};
