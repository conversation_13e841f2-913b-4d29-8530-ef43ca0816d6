const Async = require('async');
const GlobalVariable = require('../global.variable.model');
const GroupSetting = require('../groupSetting.model');
const Group = require('../group.model');
const logger = require('../../config/logger');

const createGroupSettingForGroupsAndMembers = async () => {
  const renamed = await GlobalVariable.findOne({ name: 'create group setting' });
  if (renamed) {
    return;
  }

  await Async.eachSeries(await Group.find({}), async (groupParam) => {
    const group = groupParam;
    const members = [group.admin, ...group.members, ...group.coAdmins];
    await GroupSetting.findOneAndDelete({ group: group._id });

    const groupSetting = await GroupSetting.create({ group: group._id });

    const settings = { groupPostNotification: false, groupMessageNotification: false };
    members.forEach((member) => {
      groupSetting.users.push({ user: member, settings });
    });
    await groupSetting.save();
  });

  await GlobalVariable.create({ name: 'create group setting', value: 'true' });
  logger.info('Group setting created for groups and members');
};

module.exports = createGroupSettingForGroupsAndMembers;
