const httpStatus = require('http-status');
const { groupService: GroupService } = require('../services');
const catchAsync = require('../utils/catchAsync');
const { pick } = require('../utils/pick');

const createGroup = catchAsync(async (req, res) => {
  await GroupService.createGroup({ ...req.body, admin: req?.user?._id }, req.files); // No need to list admin as member
  res.status(httpStatus.CREATED).json({ status: 'SUCCESS', message: 'Group created successfully' });
});

const joinGroup = catchAsync(async (req, res) => {
  const status = await GroupService.joinGroup(req.params.id, req.user);
  const message = status === 'PENDING' ? 'Group membership request sent successfully' : 'Group joined successfully';
  res.status(httpStatus.CREATED).json({ status, message });
});

const getUserGroups = catchAsync(async (req, res) => {
  const groups = await GroupService.getUserGroups(req);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: groups, message: 'Groups retrieved successfully' });
});

const searchGroup = catchAsync(async (req, res) => {
  const groups = await GroupService.searchGroup(req);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: groups, message: 'Groups retrieved successfully' });
});

const getGroupById = catchAsync(async (req, res) => {
  const group = req.user
    ? await GroupService.getGroupById(req?.params?.id, req.user?._id, req.userIsMember)
    : await GroupService.getGroupByIdForPublic(req.params?.id);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: group, message: 'Group retrieved successfully' });
});

const updateGroup = catchAsync(async (req, res) => {
  await GroupService.updateGroupById(req);
  res.status(httpStatus.OK).json({ message: `Group updated successfully`, status: 'SUCCESS' });
});

const deleteGroup = catchAsync(async (req, res) => {
  await GroupService.deleteGroup(req?.params?.id);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Group deleted successfully' });
});

const getGroupMembers = catchAsync(async (req, res) => {
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  options.skip = (options.page - 1) * options.limit;
  const members = await GroupService.getGroupMembers(req, options);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: members, message: 'Group members retrieved successfully' });
});

const getGroupRequests = catchAsync(async (req, res) => {
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  options.skip = (options.page - 1) * options.limit;
  const requests = await GroupService.getGroupRequests(req, options);
  res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data: requests, message: 'Group membership requests retrieved successfully' });
});

const handleMembershipRequest = catchAsync(async (req, res) => {
  const members = await GroupService.handleMembershipRequest(req);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: members, message: 'Operation successful' });
});

const removeUsers = catchAsync(async (req, res) => {
  const { id: groupId } = req.params;
  await GroupService.removeUsers(groupId, req.body.users);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Operation successful' });
});

const handleCoAdmins = catchAsync(async (req, res) => {
  const { id: groupId } = req.params;
  const { users, action } = req.body;
  await GroupService.handleCoAdmins(groupId, users, action, req.user);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Operation successful' });
});

const leaveGroup = catchAsync(async (req, res) => {
  await GroupService.leaveGroup(req);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Exit group action successful' });
});

const getUserGroupSettings = catchAsync(async (req, res) => {
  const userId = req.user._id;
  const { id: groupId } = req.params;
  const userGroupSetting = await GroupService.getUserGroupSettings(userId, groupId);
  res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', message: 'Group setting successfully retrieved', data: userGroupSetting });
});

const updateGroupSettings = catchAsync(async (req, res) => {
  const userId = req.user._id;
  const { id: groupId } = req.params;
  await GroupService.updateGroupSettings(userId, groupId, req.body);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Group setting successfully updated' });
});

const sendInvite = catchAsync(async (req, res) => {
  await GroupService.sendInvite(req.params.id, req.user, req.body);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Invite sent successfully' });
});

module.exports = {
  createGroup,
  joinGroup,
  getUserGroups,
  getGroupById,
  updateGroup,
  searchGroup,
  deleteGroup,
  getGroupMembers,
  getGroupRequests,
  handleMembershipRequest,
  leaveGroup,
  removeUsers,
  handleCoAdmins,
  getUserGroupSettings,
  updateGroupSettings,
  sendInvite,
};
