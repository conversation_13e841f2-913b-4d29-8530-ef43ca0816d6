const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const User = require('../user.model');

const migrationName = `Set 'scholarships' field in Users' record`;

const setScholarshipsFieldInUsersRecords = async () => {
  const migrated = await GlobalVariable.findOne({ name: migrationName });
  if (migrated) return;

  await User.updateMany({}, { $set: { scholarships: [] } });

  await GlobalVariable.create({ name: migrationName, value: 'true' });
  logger.info('User model scholarships');
};

module.exports = setScholarshipsFieldInUsersRecords;
