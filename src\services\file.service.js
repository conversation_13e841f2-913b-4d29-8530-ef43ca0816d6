const httpStatus = require('http-status');
const axios = require('axios');
const cloudinary = require('cloudinary').v2;
const Async = require('async');
const { BlobSASPermissions, generateBlobSASQueryParameters } = require('@azure/storage-blob');
const { blobServiceClient } = require('../config/azure.setup');
const { cloudinaryConfig } = require('../config/config');
const ApiError = require('../utils/ApiError');
const { File } = require('../models');
const { uploadAzureBlob, saveFile } = require('./azure.file.service');
const logger = require('../config/logger');

cloudinary.config(cloudinaryConfig);

const deleteFileById = async (fileId) => {
  const file = await File.findById(fileId);
  if (!file) {
    throw new ApiError(httpStatus.NOT_FOUND, 'File record not found');
  }
  // Model has pre remove hook to delete from cloudinary
  await file.remove();
  return file;
};

const deleteManyFiles = async (fileIds) => {
  if (fileIds?.length) {
    const mongoDeletions = fileIds.map(async (fileId) => {
      const file = await File.findOne({ _id: fileId });
      if (file) await file.remove();
    });
    await Promise.all([...mongoDeletions]);
  }
};

const processFileUpload = async (file, containerName, returnUrl = false) => {
  const { filename, url } = await uploadAzureBlob(file, containerName);
  const savedFile = await saveFile(url, filename, containerName);
  return returnUrl ? { url, id: savedFile._id } : savedFile._id;
};

const processFileUploads = async (files, containerName) => {
  const fileIds = [];
  await Async.eachOfSeries(files, async (file) => {
    const fileId = await processFileUpload(file, containerName);
    fileIds.push(fileId);
  });
  return fileIds;
};

const generateSasUrlHelper = async (fileId) => {
  // fileId is either file object or file id string
  const file =
    // eslint-disable-next-line no-nested-ternary
    typeof fileId !== 'string'
      ? !fileId.containerName && fileId.url && !fileId.url.includes('res.cloudinary.com')
        ? await File.findById(fileId)
        : fileId
      : fileId;
  // const file = typeof fileId === 'string' ? await File.findById(fileId) : fileId;

  if (!file.containerName || file.containerName.includes('posts')) {
    // Return url if file is not stored in azure
    // or if it is stored in posts container
    return file.url;
  }

  const containerClient = blobServiceClient.getContainerClient(file.containerName);
  const blobClient = containerClient.getBlobClient(file.filename);

  const expiryTime = new Date();
  expiryTime.setMinutes(expiryTime.getMinutes() + 15);

  const sasQueryParameters = generateBlobSASQueryParameters(
    {
      containerName: file.containerName,
      blobName: file.filename,
      permissions: BlobSASPermissions.parse('r'),
      startsOn: new Date(),
      expiresOn: expiryTime,
    },
    blobServiceClient.credential,
  );

  return `${blobClient.url}?${sasQueryParameters.toString()}`;
};

const getBase64FromUrl = async (url) => {
  try {
    const response = await axios({
      method: 'get',
      url,
      responseType: 'arraybuffer',
    });

    const base64 = Buffer.from(response.data).toString('base64');
    const mimeType = response.headers['content-type'];
    return `data:${mimeType};base64,${base64}`;
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error converting image');
  }
};

const uploadCloudinaryFile = async (url, folder) => {
  try {
    const fileBase64 = await getBase64FromUrl(url);
    const response = await cloudinary.uploader.upload(fileBase64, { resource_type: 'auto', folder });
    return { url: response.secure_url, publicId: response.public_id };
  } catch (error) {
    logger.error(error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error saving media');
  }
};

const uploadCloudinaryFiles = async (files, folder) => {
  try {
    const filePromises = files.map(async (file) => uploadCloudinaryFile(file, folder));

    const fileObjects = await Promise.all(filePromises);
    return fileObjects;
  } catch (error) {
    logger.error(error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error saving media');
  }
};

const deleteCloudinaryFiles = async (publicIds) => {
  try {
    const deletePromises = publicIds.map(async (publicId) => cloudinary.uploader.destroy(publicId));

    await Promise.all(deletePromises);
  } catch (error) {
    logger.error(error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error deleting media');
  }
};

const validateCloudinaryPublicIds = async (publicIds) => {
  // check if publicIds for agenda job still exists on cloudinary
  // if it exists, return the valid publicIds to be scheduled for deleting
  const validPublicIds = [];
  const publicIdArray = Array.isArray(publicIds) ? publicIds : [publicIds];

  const promises = publicIdArray.map(async (publicId) => {
    try {
      const result = await cloudinary.api.resource(publicId);
      if (result) {
        validPublicIds.push(publicId);
      }
    } catch (error) {
      // logger.error(`error validating publicId: ${publicId}`);
    }
  });
  await Promise.all(promises);

  return validPublicIds;
};

module.exports = {
  deleteFileById,
  deleteManyFiles,
  processFileUpload,
  processFileUploads,
  generateSasUrlHelper,

  uploadCloudinaryFiles,
  deleteCloudinaryFiles,
  validateCloudinaryPublicIds,
};
