const Joi = require('joi');
const { objectId } = require('./custom.validation');

const search = {
  query: Joi.object().keys({
    searchText: Joi.string(),
    tab: Joi.string()
      .valid('users', 'groups', 'forum_posts', 'posts', 'services', 'businesses', 'scholarships')
      .default('users'),
    sortBy: Joi.string().valid('createdAt:asc', 'createdAt:desc'),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
  }),
};

const contactUs = {
  body: Joi.object().keys({
    fullName: Joi.string().required(),
    email: Joi.string().required().email(),
    message: Joi.string().required(),
  }),
};

const reportSchema = {
  body: Joi.object().keys({
    contentId: Joi.string().custom(objectId).required(),
    uri: Joi.string(),
    reason: Joi.string().required(),
    modelName: Joi.string().valid('Post', 'ForumPost', 'Comment', 'ForumReply').required(),
  }),
};

const searchUsers = {
  query: Joi.object().keys({
    searchTerm: Joi.string().default(''),
    sortBy: Joi.string(),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer(),
  }),
};

const generateSasUrl = {
  query: Joi.object().keys({
    fileIds: Joi.alternatives().try(Joi.string(), Joi.array().items(Joi.string())).required(),
  }),
};

const userAnalytics = {
  query: Joi.object().keys({
    startDate: Joi.date().iso().default(new Date(0).toISOString()),
    endDate: Joi.date()
      .iso()
      .default(new Date(new Date().getTime() + 86400000).toISOString()), // Add 1 day for possible deployment timezone differences
    groupingFormat: Joi.string().valid('yearly', 'monthly', 'weekly', 'daily').default('monthly'),
  }),
};

const getLanguages = {
  query: Joi.object().keys({
    name: Joi.string(),
    sortBy: Joi.string().valid('name:asc', 'name:desc').default('name:asc'),
    limit: Joi.number().integer().default(50),
    page: Joi.number().integer(),
  }),
};

module.exports = {
  search,
  contactUs,
  reportSchema,
  searchUsers,
  generateSasUrl,
  userAnalytics,
  getLanguages,
};
