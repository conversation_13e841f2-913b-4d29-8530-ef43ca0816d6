const { BotRecord } = require('../models');
const { basicUserPopulate } = require('./user.service');

const createBotRecord = async (data) => {
  const botRecord = await BotRecord.create(data);
  return botRecord;
};

const getBotRecords = async (filter, options) => {
  const botRecords = await BotRecord.paginate(filter, { ...options, populate: [{ path: 'user', ...basicUserPopulate }] });
  return botRecords;
};

module.exports = {
  createBotRecord,
  getBotRecords,
};
