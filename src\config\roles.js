const allRoles = {
  business: ['manageBusiness'],
  student: [],
  schoolAdmin: [],
  admin: [
    'internal',
    'getUsers',
    'manageUsers',
    'manageBlogPosts',
    'manageSchools',
    'manageCareers',
    'manageDisputes',
    'manageResourceLibrary',
    'manageScholarships',
    'verifyBusinesses',
    'verifyServices',
    'manageIssueReports',
    'manageDeletedUsers',
    'manageFeedbacks',
  ],
  developer: ['internal', 'manageIssueReports'],
  superAdmin: [
    'internal',
    'manageScholarships',
    'manageIssueReports',
    'assignVerifier',
    'verifyBusinesses',
    'verifyServices',
    'getBotRecommendationProfile',
    'manageDeletedUsers',
  ],
};

const roles = Object.keys(allRoles);
const roleRights = new Map(Object.entries(allRoles));

module.exports = {
  roles,
  roleRights,
};
