const Async = require('async');
const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const Service = require('../service.model');

const initializeServicesArray = async () => {
  const migrated = await GlobalVariable.findOne({ name: 'Initialize Services Array' });

  if (migrated) {
    return;
  }

  const services = await Service.find();

  await Async.eachSeries(services, async (service) => {
    await Service.updateOne({ _id: service._id }, { $set: { requirements: [] } });
  });

  await GlobalVariable.create({ name: 'Initialize Services Array', value: 'true' });
  logger.info('Services Array Initialized');
};

module.exports = initializeServicesArray;
