const express = require('express');

const router = express.Router();
const { settingValidation } = require('../../validations');
const validate = require('../../middlewares/validate');
const { settingController } = require('../../controllers');
const { auth } = require('../../middlewares/auth');

router.use(auth());

router.get('/', settingController.getSettingById);

router.patch('/', validate(settingValidation.updateSetting), settingController.updateSetting);

module.exports = router;
