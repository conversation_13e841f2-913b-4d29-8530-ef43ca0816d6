const nodemailer = require('nodemailer');
const cron = require('node-cron');
const { convert } = require('html-to-text');
const config = require('../config/config');
const logger = require('../config/logger');
// const {
//   getVerifyEmailTemplate,
//   getConfirmEmailVerifyTemplate,
//   getPasswordResetTemplate,
//   getPasswordResetConfirmTemplate,
//   getSendContactUsEmailTemplate,
//   getSendReportEmailTemplate,
//   getFollowEmailTemplate,
//   getJoinGroupRequestEmailTemplate,
//   getAcceptRejectGroupRequestEmailTemplate,
//   getCreateNewAdminEmailTemplate,
//   getNewCoAdminEmailTemplate,
//   getLikeResourceEmailTemplate,
//   getMessageNotificationEmailTemplate,
//   getRepostEmailTemplate,
//   getCommentEmailTemplate,
//   getGroupInviteEmailTemplate,
//   getMentionEmailTemplate,
//   getGroupNotificationEmailTemplate,
//   getverifyDuplicateSignupEmailTemplate,
//   getNotifyProviderOfRevisionRequestEmailTemplate
// } = require('../public/emailTemplates');
const TEMPLATES = require('../public/emailTemplates');
const { capitalizeFirstChar } = require('../utils/stringFormatting');
const { incomingEmailAddresses, businessEnums } = require('../config/constants');
const { notificationTypes } = require('./shared/notification.handler');
const { Message, Reaction, User, Post } = require('../models');

const transport = nodemailer.createTransport(config.email.smtp);
const notifTransport = nodemailer.createTransport(config.email.smtpNotif);

if (config.env !== 'test') {
  Promise.all([
    transport.verify().then(() => logger.info('Connected to email server')),
    notifTransport.verify().then(() => logger.info('Connected to notification email server')),
  ]).catch((e) =>
    logger.warn(`Unable to connect to email server. Make sure you have configured the SMTP options in .env\n\n${e}`),
  );
}

/**
 * Send an email
 * @param {string} to
 * @param {string} subject
 * @param {string} text
 * @returns {Promise}
 */
const sendEmail = async ({ to, subject, html, text, isNotif = false }) => {
  // eslint-disable-next-line no-param-reassign
  text = text || convert(html);
  const from = isNotif ? config.email.notifFrom : config.email.from;
  const msg = { from, to, subject, html, text };

  if (config.env !== 'production' || process.env.ENVIRON === 'development') {
    msg.subject = `Development: ${msg.subject}`;
  }

  try {
    if (isNotif) await notifTransport.sendMail(msg);
    else await transport.sendMail(msg);
  } catch (error) {
    logger.error(error);
  }
};

/**
 * Send reset password email
 * @param {string} to
 * @param {string} token
 * @returns {Promise}
 */
const sendResetPasswordEmail = async (user, token) => {
  const subject = 'Reset Password';
  const resetPasswordUrl = `${config.client.baseUrl}/reset-password?token=${token}`;
  const html = TEMPLATES.getPasswordResetTemplate(capitalizeFirstChar(user.firstName || user.username), resetPasswordUrl);
  try {
    await sendEmail({ to: user.email, subject, html });
    // eslint-disable-next-line no-param-reassign
    user.pendingPasswordResetCount += 1;
    await user.save();
  } catch (error) {
    logger.error(error);
  }
};

/**
 * Send verification email
 * @param {string} to
 * @param {string} token
 * @returns {Promise}
 */
const sendVerificationEmail = async (user, token, signupAs, returnUrl) => {
  const subject = `${capitalizeFirstChar(signupAs)} Account Verification`;
  let verificationEmailUrl = `${config.client.baseUrl}/email-verification?email=${user.email}&token=${token}&signupAs=${signupAs}`;
  verificationEmailUrl += returnUrl ? `&returnUrl=${returnUrl}` : '';

  const html = TEMPLATES.getVerifyEmailTemplate(capitalizeFirstChar(user.firstName || user.username), verificationEmailUrl);
  try {
    await sendEmail({ to: user.email, subject, html });
  } catch (error) {
    logger.error(error);
  }
};

const sendVerifyDuplicateSignupEmail = async (user, token, signupAs) => {
  const subject = `${capitalizeFirstChar(signupAs)} Account Verification`;
  const verificationEmailUrl = `${config.client.baseUrl}/verify?email=${user.email}&token=${token}&signupAs=${signupAs}`;
  const html = TEMPLATES.getverifyDuplicateSignupEmailTemplate(
    capitalizeFirstChar(user.firstName || user.username),
    verificationEmailUrl,
  );
  try {
    await sendEmail({ to: user.email, subject, html });
  } catch (error) {
    logger.error(error);
  }
};

const sendPasswordResetConfirmEmail = async (user) => {
  const subject = 'Password Reset Successful';
  const html = TEMPLATES.getPasswordResetConfirmTemplate(capitalizeFirstChar(user.firstName || user.username));
  await sendEmail({ to: user.email, subject, html });
};

const sendAccountActivationConfirmEmail = async (user) => {
  const subject = 'Welcome to UnykEd!';
  const html = TEMPLATES.getConfirmEmailVerifyTemplate(capitalizeFirstChar(user.firstName || user.username));
  await sendEmail({ to: user.email, subject, html });
};

const sendContactUsEmail = async (messageBody) => {
  const { fullName: senderName, email: senderEmail, message } = messageBody;
  const subject = 'Contact Us Form Submission';
  const html = TEMPLATES.getSendContactUsEmailTemplate(senderName, senderEmail, message);
  await sendEmail({ to: incomingEmailAddresses.CONTACT_US, subject, html });
};

const sendReportUsEmail = async (messageBody, userId) => {
  const html = TEMPLATES.getSendReportEmailTemplate(messageBody, userId);
  const subject = 'Report Notification';
  await sendEmail({ to: incomingEmailAddresses.REPORT, subject, html });
};

// isNotif is set to true from here downwards

const sendFollowNotificationEmail = async (sender, recipient) => {
  const html = TEMPLATES.getFollowEmailTemplate(sender, recipient);
  const subject = 'You have a new follower';
  await sendEmail({ to: recipient.email, subject, html, isNotif: true });
};

const sendJoinGroupRequestEmail = async (sender, recipient, details) => {
  const html = TEMPLATES.getJoinGroupRequestEmailTemplate(sender, recipient, details);
  const subject = `${sender.firstName} has requested to join your group`;
  await sendEmail({ to: recipient.email, subject, html, isNotif: true });
};

const sendAcceptRejectGroupRequestEmail = async (recipient, details) => {
  // details -> {groupid, groupName, accepted (true/false), actor}
  const html = TEMPLATES.getAcceptRejectGroupRequestEmailTemplate(recipient, details);
  const subject = `You've been ${details.accept ? 'accepted to join' : 'rejected from joining'} a group`;
  await sendEmail({ to: recipient.email, subject, html, isNotif: true });
};

const sendCreateNewAdminEmail = async (sender, recipient, details, isRandom) => {
  // details -> {groupId, groupName}
  const html = TEMPLATES.getCreateNewAdminEmailTemplate(sender, recipient, details, isRandom);
  const subject = 'New Admin Notification';
  await sendEmail({ to: recipient.email, subject, html, isNotif: true });
};

const sendNewCoAdminEmail = async (sender, recipient, details) => {
  // details -> {groupId, groupName}
  const html = TEMPLATES.getNewCoAdminEmailTemplate(sender, recipient, details);
  const subject = 'New Co-Admin Notification';
  await sendEmail({ to: recipient.email, subject, html, isNotif: true });
};

const sendReactionNotificationEmail = async (sender, recipient, details) => {
  // details -> {resourceId, resourceName}
  const html = TEMPLATES.getLikeResourceEmailTemplate(sender, recipient, details);
  const subject = `Your ${details?.resourceName?.toLowerCase()} got a reaction`;
  await sendEmail({ to: recipient.email, subject, html, isNotif: true });
};
const sendMessageNotificationEmail = async (sender, recipient, details) => {
  // details -> {conversation}
  // sender, { receiver: await User.findById(recipientId), type }
  const html = TEMPLATES.getMessageNotificationEmailTemplate(sender, recipient, details);
  const subject = `You have a new message from ${sender.firstName}`;
  await sendEmail({ to: recipient.email, subject, html, isNotif: true });
};

const sendRepostNotificationEmail = async (sender, recipient, details) => {
  // details -> {resourceId, resourceName}
  const html = TEMPLATES.getRepostEmailTemplate(sender, recipient, details);
  const subject = `${sender.firstName || 'Someone'} repost you post`;
  await sendEmail({ to: recipient.email, subject, html, isNotif: true });
};

const sendCommentNotificationEmail = async (sender, recipient, details, type) => {
  // details -> {resourceId, resourceName, secondRecipient?}
  const html = TEMPLATES.getCommentEmailTemplate(sender, recipient, details, type);
  const subject = `Your ${type === notificationTypes.COMMENT ? 'post' : 'comment'} has a new comment`;
  await sendEmail({ to: recipient.email, subject, html, isNotif: true });
};

const sendGroupInviteNotificationEmail = async (sender, recipient, inviteUrl, group) => {
  // group -> group object
  const details = { groupName: group?.name, groupId: group?._id, profilePhoto: group?.profilePhoto };
  const html = TEMPLATES.getGroupInviteEmailTemplate(sender, recipient, inviteUrl, details);
  const subject = 'You have been invited to a group';
  await sendEmail({ to: recipient.email, subject, html, isNotif: true });
};

const sendMentionNotificationEmail = async (sender, recipient, details) => {
  // details -> {resourceId, resourceName}
  const html = TEMPLATES.getMentionEmailTemplate(sender, recipient, details);
  const subject = 'You were mentioned';
  await sendEmail({ to: recipient.email, subject, html, isNotif: true });
};

const sendGroupNotificationEmail = async (sender, recipient, details, type) => {
  // details -> {groupId, groupPostId, groupName}
  const html = TEMPLATES.getGroupNotificationEmailTemplate(sender, recipient, details, type);
  const subject = `Check out the latest ${type.split('-').splice(-1)[0].toLowerCase()} in ${details.groupName}`;
  await sendEmail({ to: recipient.email, subject, html, isNotif: true });
};

const sendEmailToVerifyEmail = async (user) => {
  const html = '';
  const subject = '';
  await sendEmail({ to: user.email, subject, html, isNotif: true });
};

const sendEmailToCompleteProfile = async (user) => {
  const html = TEMPLATES.getNotifyUserToCompleteProfileEmailTemplate(user);
  const subject = 'We Miss You! Let’s Pick Up Where You Left Off';
  await sendEmail({ to: user.email, subject, html, isNotif: true });
};

const ServiceRevisionNotifications = {
  sendRevisionRequestEmail: async (sender, recipient, details, type) => {
    // details -> {receiverType: provider, orderId}
    const html = TEMPLATES.getNotifyProviderOfRevisionRequestEmailTemplate(sender, recipient, details, type);
    const subject = 'Service Revision Request';
    await sendEmail({ to: recipient.contactPerson.email, subject, html, isNotif: true });
  },

  sendRevisionFulfilledEmail: async (sender, recipient, details, type) => {
    // details -> {receiverType: client, orderId}
    const html = TEMPLATES.getNotifyClientOfRevisionFulfilledEmailTemplate(sender, recipient, details, type);
    const subject = 'Service Revision Fulfilled';
    await sendEmail({ to: recipient.email, subject, html, isNotif: true });
  },

  sendRevisionCompletedEmail: async (sender, recipient, details, type) => {
    // details -> {receiverType: provider, orderId}
    const html = TEMPLATES.getNotifyProviderOfRevisionCompletedEmailTemplate(sender, recipient, details, type);
    const subject = 'Service Revision Completed';
    await sendEmail({ to: recipient.contactPerson.email, subject, html, isNotif: true });
  },

  sendRevisionCancelledEmail: async (sender, recipient, details, type) => {
    // details -> {receiverType: provider, orderId}
    const html = TEMPLATES.getNotifyProviderOfRevisionCancelledEmailTemplate(sender, recipient, details, type);
    const subject = 'Service Revision Cancelled';
    await sendEmail({ to: recipient.contactPerson.email, subject, html, isNotif: true });
  },
};

const OrderNotifications = {
  sendNewOrderPlacedEmailToProvider: async (sender, recipient, details, type) => {
    // details -> { receiverType: 'client' || 'provider', orderId: order._id, transactionAmount, serviceFee, transactionType, transactionStatus, destination}
    const html = TEMPLATES.getNotifyProviderOfNewOrderEmailTemplate(sender, recipient, details, type);
    const subject = 'New Order Placed';
    await sendEmail({ to: recipient.contactPerson.email, subject, html, isNotif: true });
  },

  sendNewOrderPlacedEmailToClient: async (sender, recipient, details, type) => {
    // details -> { receiverType: 'client' || 'provider', orderId: order._id, transactionAmount, serviceFee, transactionType, transactionStatus, destination}
    const html = TEMPLATES.getNotifyClientOfNewOrderEmailTemplate(sender, recipient, details, type);
    const subject = 'New Order Placed';
    await sendEmail({ to: recipient.email, subject, html, isNotif: true });
  },

  sendOrderRequirementsSubmittedEmail: async (sender, recipient, details, type) => {
    // details -> {receiverType: provider, orderId}
    const html = TEMPLATES.getNotifyProviderOfSubmittedRequirementsEmailTemplate(sender, recipient, details, type);
    const subject = 'Order Requirements Submitted';
    await sendEmail({ to: recipient.contactPerson.email, subject, html, isNotif: true });
  },

  sendOrderDeliveredEmail: async (sender, recipient, details, type) => {
    // details -> {receiverType: client, orderId}
    const html = TEMPLATES.getNotifyClientOfDeliveredOrderEmailTemplate(sender, recipient, details, type);
    const subject = 'Order Delivered';
    await sendEmail({ to: recipient.email, subject, html, isNotif: true });
  },

  sendOrderCompletedEmail: async (sender, recipient, details, type) => {
    // details -> {receiverType: provider, orderId}
    const html = TEMPLATES.getNotifyProviderOfCompletedOrderEmailTemplate(sender, recipient, details, type);
    const subject = 'Order Completed';
    await sendEmail({ to: recipient.contactPerson.email, subject, html, isNotif: true });
  },

  sendOrderCancelledEmail: async (sender, recipient, details, type) => {
    // details -> details = { receiverType: 'provider', orderId };
    const html = TEMPLATES.getNotifyProviderOfCancelledOrderEmailTemplate(sender, recipient, details, type);
    const subject = 'Order Cancelled';
    await sendEmail({ to: recipient.contactPerson.email, subject, html, isNotif: true });
  },

  sendDisputeOpenedEmail: async (sender, recipient, details, type) => {
    // details -> {receiverType: provider, orderId, serviceName}
    const html = TEMPLATES.getNotifyProviderOfDisputeOpenedEmailTemplate(sender, recipient, details, type);
    const subject = 'Dispute Reported';
    await sendEmail({ to: recipient.contactPerson.email, subject, html, isNotif: true });
  },

  sendExtensionRequestedEmail: async (sender, recipient, details, type) => {
    // details -> {receiverType: client, orderId}
    const html = TEMPLATES.getNotifyClientOfExtensionRequestedEmailTemplate(sender, recipient, details, type);
    const subject = 'Extension Requested';
    await sendEmail({ to: recipient.email, subject, html, isNotif: true });
  },

  sendExtensionApprovedEmail: async (sender, recipient, details, type) => {
    // details -> {receiverType: provider, orderId}
    const html = TEMPLATES.getNotifyProviderOfExtensionApprovedEmailTemplate(sender, recipient, details, type);
    const subject = 'Extension Approved';
    await sendEmail({ to: recipient.contactPerson.email, subject, html, isNotif: true });
  },

  sendExtensionDeniedEmail: async (sender, recipient, details, type) => {
    // details -> {receiverType: provider, orderId}
    const html = TEMPLATES.getNotifyProviderOfExtensionDeniedEmailTemplate(sender, recipient, details, type);
    const subject = 'Extension Denied';
    await sendEmail({ to: recipient.contactPerson.email, subject, html, isNotif: true });
  },
};

const businessVerifications = {
  sendVerifierAssignedEmail: async (recipient, details) => {
    // details -> {business, verification}
    const html = TEMPLATES.getNotifyVerifierOfAssignedTask(recipient, details);
    const subject = 'Business Verification';
    await sendEmail({ to: recipient.email, subject, html, isNotif: true });
  },

  sendVerificationResubmitEmail: async (recipient, details) => {
    // details -> {business, verification}
    const html = TEMPLATES.getNotifyVerifierOfResubmittedVerification(recipient, details);
    const subject = 'Resubmission: Business Verification';
    logger.info(`Sending verification resubmit email: ${recipient.email}`);
    await sendEmail({ to: recipient.email, subject, html, isNotif: true });
  },

  sendVerificationResultEmail: async (recipient, details) => {
    const html = TEMPLATES.getNotifyBusinessOfVerificationResult(recipient, details);
    const subject =
      details.status === businessEnums.businessVerificationStatuses.APPROVED
        ? 'Your Profile is Verified'
        : 'Your Profile Verification Result';
    await sendEmail({ to: recipient, subject, html, isNotif: true });
  },

  sendNotifySuperAdminToVerifyBusiness: async (recipient, details) => {
    // details -> {business}
    const html = TEMPLATES.getNotifySuperAdminToVerifyBusiness(recipient, details);
    const subject = 'New Business to Verify';
    await sendEmail({ to: recipient.email, subject, html, isNotif: true });
  },

  // service verifications notifications
  sendServiceVerifierAssignedEmail: async (recipient, details) => {
    // details -> { service, serviceVerification }
    const html = TEMPLATES.getNotifyServiceVerifierOfAssignedTask(recipient, details);
    const subject = 'Service Verification';
    await sendEmail({ to: recipient.email, subject, html, isNotif: true });
  },

  sendServiceVerificationResult: async (recipient, details) => {
    const html = TEMPLATES.getNotifyServiceOfVerificationResult(recipient, details);
    const subject =
      details.status === businessEnums.businessVerificationStatuses.APPROVED
        ? 'Your Service is Verified'
        : 'Your Service Verification Result';
    await sendEmail({ to: recipient.email, subject, html, isNotif: true });
  },

  sendNotifySuperAdminToVerifyService: async (recipient, details) => {
    // details -> { service }
    const html = TEMPLATES.getNotifySuperAdminToVerifyService(recipient, details);
    const subject = 'Service to Verify';
    await sendEmail({ to: recipient.email, subject, html, isNotif: true });
  },
};

const premumSubscriptionNotifications = {
  sendPremiumWelcomeEmail: async (recipient) => {
    const html = TEMPLATES.getWelcomeToPremiumTemplate(recipient);
    const subject = 'Welcome to Premium';
    await sendEmail({ to: recipient.email, subject, html });
  },

  sendPremiumSubscriptionExpirationEmail: async (recipient, details) => {
    // recipient - user, details - {subscriptionId, expiresAt}
    const html = TEMPLATES.getPremiumSubscriptionExpirationEmailTemplate(recipient, details);
    const subject = 'Premium Subscription Expiring Soon';
    await sendEmail({ to: recipient.email, subject, html });
  },
};

const sendNewScholarshipEmail = async (recipient, details) => {
  // details -> { scholarships }
  const html = TEMPLATES.getNotifyUsersOfScholarshipsEmailTemplate(details);
  const subject = 'New Scholarships Available';
  await sendEmail({ to: recipient.email, subject, html });
};

const sendNewUnykEdPostEmail = async (sender, recipient, details) => {
  // details -> { resourceId }
  const html = TEMPLATES.getNotifyUsersOfNewUnykEdPostEmailTemplate(sender, recipient, details);
  const subject = 'New UnykEd Post Available';
  await sendEmail({ to: recipient.email, subject, html });
};

const sendScholarshipExpirationEmail = async (recipient, details) => {
  // details -> { scholarships }
  const html = TEMPLATES.getNotifyUsersOfScholarshipExpirationEmailTemplate(details);
  const subject = 'Scholarships Deadline Approaching';
  await sendEmail({ to: recipient.email, subject, html });
};

const sendNewJobApplicantEmail = async (recipient, details) => {
  // details -> { newJobApplication, careerJob }c
  const html = TEMPLATES.getNotifySuperAdminOfNewJobApplicantEmailTemplate(details);
  const subject = 'New Job Applicant';
  await sendEmail({ to: recipient.email, subject, html });
};

const createReactionScheduler = (params) => {
  const job = cron.schedule(
    '*/5 * * * *',
    async () => {
      const { sender, recipient, details } = params;
      const { resourceId, type } = details;
      const resourceName = details.resourceName.toLowerCase();
      const reacted = await Reaction.findOne({ [resourceName]: resourceId, user: sender._id, type });
      if (reacted) {
        logger.info('Sending email notification');
        await sendReactionNotificationEmail(sender, recipient, details);
      }
      job.stop();
    },
    { scheduled: false },
  );
  return job;
};

const createMessageEmailScheduler = (params) => {
  const job = cron.schedule(
    '*/5 * * * *', // every 5 minutes
    async () => {
      const { sender, recipient, details } = params;
      const message = await Message.findById(details.messageId);
      if (!message.readBy.includes(recipient._id.toString())) {
        await sendMessageNotificationEmail(sender, recipient, details);
      }
      job.stop();
    },
    { scheduled: false },
  );
  return job;
};

const createFollowScheduler = (params) => {
  const job = cron.schedule(
    '*/5 * * * *',
    async () => {
      const { sender, recipient } = params;
      const user = await User.findById(recipient._id);
      if (user.following.includes(sender._id)) {
        logger.info('Sending email follow notification');
        await sendFollowNotificationEmail(sender, recipient);
      }
      job.stop();
    },
    { scheduled: false },
  );
  return job;
};

const createRepostScheduler = (params) => {
  const job = cron.schedule(
    '*/5 * * * *',
    async () => {
      const { sender, details } = params;
      const { resourceId } = details;
      const resource = await Post.findById(resourceId);
      if (resource.reposts.includes(sender._id)) {
        sendRepostNotificationEmail(sender, params.recipient, details);
      }
      job.stop();
    },
    { scheduled: false },
  );
  return job;
};

module.exports = {
  transport,
  sendEmail,
  sendResetPasswordEmail,
  sendVerificationEmail,
  sendVerifyDuplicateSignupEmail,
  sendPasswordResetConfirmEmail,
  sendAccountActivationConfirmEmail,
  sendContactUsEmail,
  sendReportUsEmail,
  sendFollowNotificationEmail,
  sendJoinGroupRequestEmail,
  sendAcceptRejectGroupRequestEmail,
  sendCreateNewAdminEmail,
  sendNewCoAdminEmail,
  sendReactionNotificationEmail,
  sendRepostNotificationEmail,
  sendCommentNotificationEmail,
  sendGroupInviteNotificationEmail,
  sendMentionNotificationEmail,
  sendGroupNotificationEmail,
  sendMessageNotificationEmail,
  createMessageEmailScheduler,
  createReactionScheduler,
  createFollowScheduler,
  createRepostScheduler,
  ...ServiceRevisionNotifications,
  ...OrderNotifications,
  ...premumSubscriptionNotifications,
  sendEmailToVerifyEmail,
  sendEmailToCompleteProfile,
  ...businessVerifications,
  sendNewScholarshipEmail,
  sendNewUnykEdPostEmail,
  sendScholarshipExpirationEmail,
  sendNewJobApplicantEmail,
};
