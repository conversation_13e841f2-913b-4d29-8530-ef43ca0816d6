const replies = {
  type: 'object',
  properties: {
    user: {
      type: 'object',
      properties: {
        firstName: { type: 'string' },
        lastName: { type: 'string' },
        middleName: { type: 'string' },
        username: { type: 'string' },
        photo: { type: ['string', 'null'] },
        id: { type: 'string', format: 'uuid' },
      },
    },
    forumPost: { type: 'string', format: 'uuid' },
    text: { type: 'string' },
    media: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          url: { type: 'string', format: 'uri' },
        },
      },
    },
    upvotes: { type: 'array', items: {} },
    downvotes: { type: 'array', items: {} },
    accepted: { type: 'boolean' },
    id: { type: 'string', format: 'uuid' },
  },
  example: {
    user: {
      firstName: 'John',
      lastName: 'Doe',
      middleName: 'Jr',
      username: 'charlienwa22',
      photo: null,
      id: '777y6fb971c38a9b93066b9d',
    },
    forumPost: '777f740956baabd787bbd58',
    text: 'reply x',
    media: [],
    upvotes: [],
    downvotes: [],
    accepted: false,
    id: '777f740956baabd787uuy678',
  },
};

module.exports = {
  type: 'object',
  properties: {
    user: {
      type: 'object',
      properties: {
        firstName: { type: 'string', example: 'John' },
        lastName: { type: 'string', example: 'Doe' },
        middleName: { type: 'string', example: 'Jr' },
        username: { type: 'string', example: 'john_doe' },
        photo: {
          type: ['object', 'null'],
          properties: {
            url: { type: 'string', example: 'https://example.com/photo.jpg' },
          },
          example: { url: 'https://false-url.com/photo.jpg' },
        },
        id: { type: 'string', example: '6009c0eee65f6dce28fb3e50' },
        text: { type: 'string', example: 'Lorem ipsum' },
        media: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              url: { type: 'string', example: 'https://example.com/file.jpg' },
            },
          },
          example: [{ url: 'https://false-url.com/file.jpg' }],
        },
        tags: {
          type: 'array',
          items: {
            type: 'string',
          },
          example: ['admissions', 'funding'],
        },
        category: { type: 'string', example: 'Financial Aid' },
        replies: {
          type: 'array',
          items: { type: 'object' },
          example: [{ reply: 'False reply' }],
        },
        solved: { type: 'boolean', example: true },
        topic: { type: 'string', example: 'Fake Topic' },
      },
      required: ['firstName', 'lastName', 'username', 'id'],
    },
    tags: { type: 'array', description: 'Forum post tags', example: ['New'] },
    category: {
      type: 'string',
      description: 'Forum post category',
      enum: [
        'All',
        'General Discussion',
        'Admissions',
        'Test Scores',
        'SAT',
        'ACT',
        'College Applications',
        'Scholarships',
        'Financial Aid',
        'Study Tips',
        'Student Life',
        'Internships',
        'Career Advice',
        'Graduate School',
      ],
    },
    text: { type: 'string' },
    media: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          url: { type: 'string' },
        },
      },
    },
    replies: {
      type: 'array',
      items: { type: 'object' },
      example: replies,
    },
    solved: { type: 'boolean' },
    topic: { type: 'string' },
    id: { type: 'string' },
  },
};
