const express = require('express');

const router = express.Router();
const validate = require('../../middlewares/validate');
const { resourceLibraryValidation } = require('../../validations');
const { auth } = require('../../middlewares/auth');
const { resourceLibraryController } = require('../../controllers');
const { uploadResourceLibraryFiles } = require('../../config/multer.config');

router.use(auth());

router.get('/enums', resourceLibraryController.getResourceLibraryEnums);

router.get('/categories', resourceLibraryController.getResourceCategoriesData);

router.get(
  '/analytics',
  auth('manageResourceLibrary'),
  validate(resourceLibraryValidation.getResourcesAnalytics),
  resourceLibraryController.getResourcesAnalytics,
);

router.get('/', validate(resourceLibraryValidation.getResources), resourceLibraryController.getResources);

router.get('/:id', validate(resourceLibraryValidation.getResource), resourceLibraryController.getResource);

// only GET routes do not require admin access
router.post(
  '/',
  auth('manageResourceLibrary'),
  uploadResourceLibraryFiles,
  validate(resourceLibraryValidation.addResource),
  resourceLibraryController.addResource,
);

router.patch(
  '/:id',
  uploadResourceLibraryFiles,
  auth('manageResourceLibrary'),
  validate(resourceLibraryValidation.updateResource),
  resourceLibraryController.updateResource,
);

router.delete(
  '/:id',
  auth('manageResourceLibrary'),
  validate(resourceLibraryValidation.deleteResource),
  resourceLibraryController.deleteResource,
);

router.patch('/:id/analytics', validate(resourceLibraryValidation.resourceCounter), resourceLibraryController.saveDownloads);

module.exports = router;
