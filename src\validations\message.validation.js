const Joi = require('joi');
const { objectId } = require('./custom.validation');
const { messageEnums } = require('../config/constants');
// const { fileSchema } = require('./file.validation');

const memberSchema = Joi.object()
  .keys({
    user: Joi.string().custom(objectId),
    business: Joi.string().custom(objectId),
  })
  .xor('user', 'business');
// .required();

const createMessage = Joi.object({
  text: Joi.string().min(1).when('buffers', {
    is: Joi.exist(),
    then: Joi.optional(),
    otherwise: Joi.required(),
  }),
  conversationId: Joi.string().custom(objectId).required(),
  clientMessageId: Joi.string().required(),
  sentBy: memberSchema.required(),
  order: Joi.string().custom(objectId),

  filenames: Joi.alternatives().try(Joi.array().items(Joi.string().max(200)), Joi.string().max(200)),
  buffers: Joi.alternatives().try(Joi.array().items(Joi.binary()), Joi.binary().max(200)).when('filenames', {
    is: Joi.exist(),
    then: Joi.required(),
    otherwise: Joi.forbidden(),
  }),
}).custom((value, helpers) => {
  const { filenames, buffers } = value;

  const filenameIsArray = Array.isArray(filenames);
  const bufferIsArray = Array.isArray(buffers);

  if (filenameIsArray && bufferIsArray) {
    if (filenames.length !== buffers.length) {
      return helpers.message('"filename" and "buffer" arrays must be of the same length');
    }
  } else if (filenameIsArray && !bufferIsArray) {
    return helpers.message('"filename" and "buffer" must be of the same length');
  }

  return value;
});

const editMessage = Joi.object({
  text: Joi.string().min(1).required(),
  // conversationId: Joi.string().custom(objectId).required(),
  messageId: Joi.string().custom(objectId).required(),
  sentBy: memberSchema.required(),
});

const deleteMessageViaSocket = Joi.object({
  // conversationId: Joi.string().custom(objectId).required(), Will be gotten from the message
  messageId: Joi.string().custom(objectId).required(),
  sentBy: memberSchema.required(),
});

const createConversation = {
  body: Joi.object().keys({
    // participants: Joi.array().items(Joi.string().custom(objectId)).min(1).required(),
    members: Joi.array()
      .items(memberSchema)
      .min(1)
      .required()
      .unique((a, b) => (a.user && b.user && a.user === b.user) || (a.business && b.business && a.business === b.business)),
    directMessage: Joi.string()
      .valid('true', 'false')
      .default('true')
      .when('members', {
        is: Joi.array().max(2),
        then: Joi.valid('true'),
        otherwise: Joi.valid('false'),
      }),
  }),
  query: Joi.object().keys({
    createdFor: Joi.string()
      .valid(...Object.values(messageEnums.createdForValues))
      .required(),
  }),
};

const getConversationWithSocket = Joi.object({
  conversationId: Joi.string().custom(objectId).required(),
  member: memberSchema.required(),
  sortBy: Joi.string().default('createdAt:desc'),
  limit: Joi.number().integer().default(10),
  page: Joi.number().integer().default(1),
});

const getConversations = {
  query: Joi.object().keys({
    createdFor: Joi.string(),
    member: Joi.string().required(),
    sortBy: Joi.string().default('updatedAt:desc'),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
  }),
  // body: Joi.object().keys({
  //   member: memberSchema.required(),
  // }),
};

const getConversation = {
  params: Joi.object().keys({
    conversationId: Joi.string().required(),
  }),
  // body: Joi.object().keys({
  //   member: memberSchema.required(),
  // }),
  query: Joi.object().keys({
    member: Joi.string().required(),
    sortBy: Joi.string().default('createdAt:desc'),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
  }),
};

const markAsRead = Joi.object({
  messageIds: Joi.alternatives().try(Joi.string(), Joi.array().items(Joi.string())).required(),
  member: memberSchema.required(),
});

const deleteMessage = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required(),
  }),
};

const updateMessage = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required(),
  }),
};

const markTyping = Joi.object({
  conversationId: Joi.string().custom(objectId).required(),
  isTyping: Joi.string().valid('true', 'false').default('true'),
  member: memberSchema.required(),
});

module.exports = {
  createMessage,
  createConversation,
  getConversation,
  getConversations,
  memberSchema,
  getConversationWithSocket,
  markAsRead,
  deleteMessage,
  updateMessage,
  markTyping,
  editMessage,
  deleteMessageViaSocket,
};
