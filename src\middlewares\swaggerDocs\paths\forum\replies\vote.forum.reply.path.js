module.exports = {
  patch: {
    summary: 'Vote on a specific forum post reply',
    tags: ['Forum Replies'],
    parameters: [
      {
        name: 'forumPostId',
        in: 'path',
        description: 'ID of the forum post',
        example: '6549f740956baabd787bbd58',
        required: true,
        type: 'string',
        format: 'objectId',
      },
      {
        name: 'id',
        in: 'path',
        description: 'ID of the forum post reply',
        example: '6549f740956baabd787bbd58',
        required: true,
        type: 'string',
        format: 'objectId',
      },
    ],
    requestBody: {
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              upvote: { type: 'boolean', example: true },
              downvote: { type: 'boolean', example: false },
            },
            required: ['upvote', 'downvote'],
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Reply vote updated successfully',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Reply vote updated successfully',
            },
          },
        },
      },
      404: {
        description: 'Reply not found',
        content: {
          'application/json': {
            example: {
              status: 'FAILED',
              message: 'Reply record not found',
            },
          },
        },
      },
    },
  },
};
