const httpStatus = require('http-status');
const { Setting } = require('../models');
const ApiError = require('../utils/ApiError');
// const { userDefaultSettings } = require('../config/constants');

const getSettingById = async (settingId) => {
  //
  const setting = await Setting.findById(settingId);

  if (!setting) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Oops! Something went wrong.');
  }

  return setting;
};

const updateSettingById = async (settingId, updateBody) => {
  const setting = await getSettingById(settingId);

  if (!setting) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Oops! Something went wrong.');
  }

  Object.assign(setting, updateBody);

  await setting.save();
  return setting;
};

module.exports = {
  getSettingById,
  updateSettingById,
};
