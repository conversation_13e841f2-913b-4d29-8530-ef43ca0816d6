module.exports = {};
const express = require('express');

const router = express.Router();
const { auth } = require('../../middlewares/auth');
const validate = require('../../middlewares/validate');
const { scholarshipController } = require('../../controllers');
const { scholarshipValidation } = require('../../validations');
const { uploadSingle } = require('../../config/multer.config');
// const { canGetServices, verifyBusinessOnboarding } = require('../../middlewares/cancan');

router.get('/enums', scholarshipController.getScholarshipEnums);
router.get('/ext', validate(scholarshipValidation.getScholarships), scholarshipController.getScholarships);
router.get(
  '/ext/:uniqueName',
  validate(scholarshipValidation.getScholarshipByUniqueName),
  scholarshipController.getScholarshipByUniqueName,
);

router.use(auth());
router.get('/:id', validate(scholarshipValidation.getScholarship), scholarshipController.getScholarship);
router.get('/', validate(scholarshipValidation.getScholarships), scholarshipController.getScholarships);

// router.get(
//   '/bookmarks',
//   validate(scholarshipValidation.getBokmarkedScholarships),
//   scholarshipController.getBookmarkedScholarships,
// );

router.post(
  '/',
  auth('manageScholarships'),
  uploadSingle,
  validate(scholarshipValidation.addScholarship),
  scholarshipController.addScholarship,
);

router.patch(
  '/:id',
  auth('manageScholarships'),
  uploadSingle,
  validate(scholarshipValidation.updateScholarship),
  scholarshipController.updateScholarship,
);

router.delete(
  '/:id',
  auth('manageScholarships'),
  validate(scholarshipValidation.getScholarship),
  scholarshipController.deleteScholarship,
);

router.patch(
  '/:id/bookmark',
  validate(scholarshipValidation.bookmarkScholarship),
  scholarshipController.bookmarkScholarship,
);

module.exports = router;
