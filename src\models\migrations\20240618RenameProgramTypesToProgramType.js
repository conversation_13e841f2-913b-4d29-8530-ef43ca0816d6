const Async = require('async');
const SchoolProgram = require('../school.program.model');
const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');

const renameProgramTypesToProgramType = async () => {
  const migrated = await GlobalVariable.findOne({ name: 'Rename programTypes to programType' });

  if (migrated) {
    return;
  }

  const programs = await SchoolProgram.find().populate('programTypes');

  const programsMap = {
    Bachelor: 'Bachelor',
    Master: 'Master',
    Doctorate: 'PhD',
  };

  await Async.eachOfSeries(programs, async (program, index) => {
    await SchoolProgram.findByIdAndUpdate(program._id, { programType: programsMap[program.programTypes[0].name] });
    logger.info(`(${index + 1}) Program ${program.name} updated`);
  });

  await GlobalVariable.create({ name: 'Rename programTypes to programType', value: 'true' });
  logger.info('Renamed programTypes to programType');
};

module.exports = renameProgramTypesToProgramType;
