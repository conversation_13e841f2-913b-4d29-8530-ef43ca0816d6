const Joi = require('joi');
const { schoolsEnums } = require('../config/constants');
const { fileSchema } = require('./file.validation');

const imageMimeTypes = ['image/.+'];

const getScholarships = {
  query: Joi.object().keys({
    tab: Joi.string().valid('my_saved', 'discover').default('discover'),
    name: Joi.string(),
    fundingType: Joi.string().valid(...Object.values(schoolsEnums.fundingTypes)),
    hostCountry: Joi.string(),
    // eligibleCountries: Joi.alternatives().try(Joi.array().items(Joi.string()), Joi.string()),
    status: Joi.string().valid(...Object.values(schoolsEnums.scholarshipStatuses)),
    programType: Joi.string().valid(...Object.values(schoolsEnums.programTypes)),
    program: Joi.alternatives().try(Joi.array().items(Joi.string()).unique(), Joi.string()),
    sortBy: Joi.string()
      .valid('createdAt:asc', 'createdAt:desc', 'updatedAt:asc', 'updatedAt:desc', 'name:asc', 'name:desc')
      .default('createdAt:desc'),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
  }),
};

const getBookmarkedScholarships = {
  query: Joi.object().keys({
    status: Joi.string().valid(...Object.values(schoolsEnums.scholarshipStatuses)),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
    sortBy: Joi.string()
      .valid('createdAt:asc', 'createdAt:desc', 'updatedAt:asc', 'updatedAt:desc', 'name:asc', 'name:desc')
      .default('createdAt:desc'),
  }),
};

const getScholarship = {
  param: Joi.object().keys({
    id: Joi.string().required(),
  }),
};

const getScholarshipByUniqueName = {
  param: Joi.object().keys({
    uniqueName: Joi.string().required(),
  }),
};

const bookmarkScholarship = {
  param: Joi.object().keys({
    id: Joi.string().required(),
  }),
};

const addScholarship = {
  body: Joi.object().keys({
    name: Joi.string().required(),
    fundingType: Joi.string().valid(...Object.values(schoolsEnums.fundingTypes)),
    description: Joi.string(),
    hostCountry: Joi.string(),
    eligibleCountries: Joi.alternatives().try(Joi.array().items(Joi.string()).unique(), Joi.string()),
    startDate: Joi.date(),
    durationType: Joi.string().valid(...Object.values(schoolsEnums.durationTypes)),
    deadline: Joi.date().when('durationType', {
      is: schoolsEnums.durationTypes.DURATION_SPECIFIED,
      then: Joi.required(), // Otherwise, it is not allowed
      otherwise: Joi.forbidden(),
    }),
    // deadlineVaries: Joi.string().valid('true', 'false'),
    linkToApply: Joi.string(),
    programTypes: Joi.alternatives().try(
      Joi.array()
        .items(Joi.string().valid(...Object.values(schoolsEnums.programTypes)))
        .unique(),
      Joi.string().valid(...Object.values(schoolsEnums.programTypes)),
    ),
    programs: Joi.alternatives().try(Joi.array().items(Joi.string()).unique(), Joi.string()),
  }),
  // files: Joi.array().items(fileSchema(10, imageMimeTypes)),
  file: fileSchema(10, imageMimeTypes),
};

const updateScholarship = {
  body: Joi.object().keys({
    name: Joi.string(),
    fundingType: Joi.string().valid(...Object.values(schoolsEnums.fundingTypes)),
    description: Joi.string(),
    hostCountry: Joi.string(),
    eligibleCountries: Joi.alternatives().try(Joi.array().items(Joi.string()).unique(), Joi.string()),
    startDate: Joi.date(),
    deadline: Joi.date(),
    deadlineVaries: Joi.string().valid('true', 'false'),
    linkToApply: Joi.string(),
    status: Joi.string().valid(...Object.values(schoolsEnums.scholarshipStatuses)),
    durationType: Joi.string().valid(...Object.values(schoolsEnums.durationTypes)),
    programTypes: Joi.alternatives().try(
      Joi.array()
        .items(Joi.string().valid(...Object.values(schoolsEnums.programTypes)))
        .unique(),
      Joi.string().valid(...Object.values(schoolsEnums.programTypes)),
    ),
    programs: Joi.alternatives().try(Joi.array().items(Joi.string()).unique(), Joi.string()),
  }),
  file: fileSchema(10, imageMimeTypes),
};

module.exports = {
  getScholarships,
  getScholarship,
  addScholarship,
  updateScholarship,
  bookmarkScholarship,
  getBookmarkedScholarships,
  getScholarshipByUniqueName,
};
