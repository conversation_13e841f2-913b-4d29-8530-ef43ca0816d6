module.exports = {
  get: {
    summary: 'Get a specific group by ID',
    tags: ['Groups'],
    parameters: [
      {
        in: 'path',
        name: 'id',
        required: true,
        schema: {
          type: 'string',
          example: '507f1f77bcf86cd799439011',
        },
        description: 'ID of the group to retrieve',
      },
    ],
    responses: {
      200: {
        description: 'Group retrieved successfully',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Groups retrieved successfully',
              data: {
                name: 'Azure Group 2.0',
                posts: ['65de64a55ce992d3d539aaaa', '65e5ccaeb4806c7fe2bcaaaa', '65eaf0c84888458e7454aaaa'],
                description: 'A sample group x',
                admin: {
                  username: 'johndoe',
                  photo: {
                    url: 'https://storagesite/users%2Fphoto/74570fc0.jpg',
                  },
                  firstName: 'John <PERSON>',
                  lastName: 'Doe',
                  followers: ['652f6fb971c38a9b930aaaa', '65bb8ea12540b2c33e00aaaa'],
                  following: ['652f6fb971c38a9b9306aaaa'],
                  tagLine: 'Experience is the best teacher',
                  id: '651bb5bf4eb9240327eaaaaa',
                },
                coAdmins: ['652f6fb971c38a9b930aaaa', '65bb8ea12540b2c33e00aaaa'],
                coverPhoto: {
                  url: 'https://storagesite/users%2Fphoto/74570fc0.jpg',
                },
                profilePhoto: {
                  url: 'https://storagesite/users%2Fphoto/74570fc0.jpg',
                },
                rules: [
                  'Respect and Kindness: Treat all members with respect and kindness. Discriminatory behavior or hate speech will not be tolerated.',
                ],
                type: 'public',
                members: [
                  {
                    firstName: 'Rickter',
                    lastName: 'Belmont',
                    username: 'rickyy',
                    photo: {
                      url: 'https://storagesite/users%2Fphoto/74570fc0.jpg',
                    },
                    followers: ['652f6fb971c38a9b930aaaa', '65bb8ea12540b2c33e00aaaa'],
                    following: ['652f6fb971c38a9b9306aaaa'],
                    id: '652f6fb971c38a9b9306aaaa',
                  },
                ],
                createdAt: '2024-01-11T09:54:42.433Z',
                updatedAt: '2024-03-14T13:43:55.248Z',
                catchups: ['652f6fb971c38a9b930aaaa', '65bb8ea12540b2c33e00aaaa'],
                createdOn: '2024-01-11T09:54:42.433Z',
                id: '659fbae2fd65a4c8ac3b4be7',
              },
            },
          },
        },
      },
      404: {
        description: 'Group not found',
      },
    },
  },
};
