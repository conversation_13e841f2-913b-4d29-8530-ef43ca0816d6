const Joi = require('joi');
const { objectId } = require('./custom.validation');
const loadUniversityEnums = require('../utils/webScraper/loadUniversityEnums');

const getUniversityNames = {
  query: Joi.object().keys({
    searchText: Joi.string(),
    sortBy: Joi.string().valid('name.value:asc', 'name.value:desc').default('name.value:asc'),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
  }),
};

const getUniversity = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId),
  }),
};

const hideUniversityFromUser = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId),
  }),
};

const getUniversities = (async () => {
  return {
    query: Joi.object().keys({
      sortBy: Joi.string().valid('createdAt:asc', 'createdAt:desc', '_id:asc', '_id:desc'),
      limit: Joi.number().integer().default(10),
      page: Joi.number().integer(),
      searchText: Joi.string(),
      filter: Joi.string().valid('my_saved', 'discover', 'applied').default('my_saved'),
      city: Joi.alternatives().try(Joi.string(), Joi.array().items(Joi.string())),
      state: Joi.alternatives().try(Joi.string(), Joi.array().items(Joi.string())),
      country: Joi.alternatives().try(Joi.string(), Joi.array().items(Joi.string())),
      studentFacultyRatio: Joi.string(),
      offersDistantEducation: Joi.string(),
      ...Object.entries(await loadUniversityEnums()).reduce((acc, entry) => {
        return {
          ...acc,
          [entry[0]]: Joi.alternatives().try(
            Joi.string().valid(...entry[1]),
            Joi.array().items(Joi.string().valid(...entry[1])),
          ),
        };
      }, {}), // Await the result of getUniversityEnums
    }),
  };
})();

const saveUserUniversity = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId),
  }),
};

module.exports = {
  getUniversities,
  saveUserUniversity,
  getUniversity,
  hideUniversityFromUser,
  getUniversityNames,
};
