const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');

const deletedUserSchema = new mongoose.Schema(
  {
    email: { type: String, required: true },
    reason: { type: String },
    accountCreatedAt: { type: Date },
    deletedAt: { type: Date, default: Date.now },
  },
  { timestamps: true },
);

deletedUserSchema.index({
  email: 1,
  deletedAt: 1,
});

deletedUserSchema.plugin(toJSON);
deletedUserSchema.plugin(paginate);

const DeletedUser = mongoose.model('DeletedUser', deletedUserSchema);
module.exports = DeletedUser;
