const express = require('express');

const router = express.Router();
const { auth } = require('../../middlewares/auth');
const validate = require('../../middlewares/validate');
const { businessController: BusinessController } = require('../../controllers');
const { businessValidation: BusinessValidation } = require('../../validations');
const { uploadBusinessPhotos } = require('../../config/multer.config');

router.get('/enums', BusinessController.getEnums);
router.get('/private-enums', auth('internal'), BusinessController.getPrivateEnums);

router.get(
  '/username/:username',
  validate(BusinessValidation.getBusinessByUsername),
  BusinessController.getBusinessByUsername,
);

// router.patch(
//   '/:id/resource/:resourceId',
//   auth,
//   validate(BusinessValidation.updateBusinessResource),
//   BusinessController.updateBusinessResource,
// );

router.get('/:id/external', validate(BusinessValidation.getBusiness), BusinessController.getBusiness);

router.use(auth());

router.patch(
  '/onboard',
  uploadBusinessPhotos,
  validate(BusinessValidation.businessOnboard),
  BusinessController.onboardBusiness,
);

router.get(
  '/get-verifications',
  auth('verifyBusinesses'),
  validate(BusinessValidation.fetchVerifications),
  BusinessController.fetchVerifications,
);

// fetch unverified businesses
router.get('/verification', auth('verifyBusinesses'), BusinessController.fetchUnverifiedBusinesses);

// parent verification

router.get(
  '/verification/:id',
  auth('verifyBusinesses'),
  validate(BusinessValidation.getBusiness),
  BusinessController.fetchParentVerification,
);

router.get(
  '/:id/verification',
  auth('verifyBusinesses'),
  validate(BusinessValidation.getBusiness),
  BusinessController.fetchBusinessVerificationInfo,
);

// assign business verifier
router.post(
  '/:id/assign-verifier',
  auth('assignVerifier'),
  validate(BusinessValidation.assignVerifier),
  BusinessController.assignVerifier,
);

// verify business
router.post(
  '/:id/verify',
  auth('verifyBusinesses'),
  validate(BusinessValidation.verifyBusiness),
  BusinessController.verifyBusiness,
);

// router.post('/', uploadBusinessPhotos, validate(BusinessValidation.createBusiness), BusinessController.createBusiness);
router.get('/', validate(BusinessValidation.getBusinesses), BusinessController.getBusinesses); // Check populated properties before removing auth
router.get('/:id', validate(BusinessValidation.getBusiness), BusinessController.getBusiness);
router.patch('/:id', uploadBusinessPhotos, validate(BusinessValidation.updateBusiness), BusinessController.updateBusiness);
router.delete('/:id', validate(BusinessValidation.getBusiness), BusinessController.deleteBusiness);

router.post(
  '/:id/resource/',
  validate(BusinessValidation.addBusinessResource),
  BusinessController.addBusinessProfileResource,
);
router.patch(
  '/:id/resource/:resourceId',
  validate(BusinessValidation.updateBusinessResource),
  BusinessController.updateBusinessProfileResource,
);
router.delete(
  '/:id/resource/:resourceId',
  validate(BusinessValidation.updateBusinessResource),
  BusinessController.deleteBusinessProfileResource,
);

module.exports = router;
