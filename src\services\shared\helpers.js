const httpStatus = require('http-status');
const ApiError = require('../../utils/ApiError');
const activityLogService = require('../activity.log.service');

const removeQuotes = (str) => str.replace(/^"(.*)"$/, '$1');

const toggleVoteReply = async (resourceModel, resourceId, userId, voteType) => {
  const resource = await resourceModel.findById(resourceId);
  if (!resource) {
    throw new ApiError(httpStatus.NOT_FOUND, `${resourceModel.modelName} not found`);
  }

  const hasUpvoted = resource.upvotes.includes(userId);
  const hasDownvoted = resource.downvotes.includes(userId);

  if (voteType === 'upvote') {
    const actionType = 'UPVOTE_REPLY';
    if (hasUpvoted) {
      resource.upvotes.pull(userId);
    } else if (hasDownvoted) {
      resource.downvotes.pull(userId);
      resource.upvotes.push(userId);
      activityLogService.logActivity(userId, actionType, resourceId);
    } else {
      resource.upvotes.push(userId);
      activityLogService.logActivity(userId, actionType, resourceId);
    }
  } else if (voteType === 'downvote') {
    const actionType = 'DOWNVOTE_REPLY';
    if (hasDownvoted) {
      resource.downvotes.pull(userId);
    } else if (hasUpvoted) {
      resource.upvotes.pull(userId);
      resource.downvotes.push(userId);
      activityLogService.logActivity(userId, actionType, resourceId);
    } else {
      resource.downvotes.push(userId);
      activityLogService.logActivity(userId, actionType, resourceId);
    }
  }

  await resource.save();
  return resource;
};

module.exports = {
  removeQuotes,
  toggleVoteReply,
};
