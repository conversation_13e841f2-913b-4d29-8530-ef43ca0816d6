const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const Service = require('../service.model');
const { businessEnums } = require('../../config/constants');

const migrationName = 'set existing services verification status and verifications';

const setExistingServicesVerificationStatusesAndVerifications = async () => {
  const migrated = await GlobalVariable.findOne({ name: migrationName });
  if (migrated) return;

  await Service.updateMany(
    {},
    { $set: { verificationStatus: businessEnums.businessVerificationStatuses.APPROVED, verifications: [] } },
  );

  await GlobalVariable.create({ name: migrationName, value: 'true' });
  logger.info('Services verification status and verifications');
};

module.exports = setExistingServicesVerificationStatusesAndVerifications;
