module.exports = {
  get: {
    summary: 'Get notifications',
    tags: ['Notifications'],
    description: 'Get notifications for the current user',
    parameters: [
      {
        in: 'query',
        name: 'sortBy',
        description: 'Sort notifications by field and order',
        schema: {
          type: 'string',
          default: 'createdAt:desc',
        },
      },
      {
        in: 'query',
        name: 'limit',
        description: 'Limit the number of notifications per page',
        schema: {
          type: 'integer',
          format: 'int32',
        },
      },
      {
        in: 'query',
        name: 'page',
        description: 'Page number for pagination',
        schema: {
          type: 'integer',
          format: 'int32',
        },
      },
      {
        in: 'query',
        name: 'group',
        description: 'Filter notifications by group Id',
        schema: {
          type: 'string',
          format: 'ObjectId',
          example: '651bb5bf4eb9240327eaaaaa',
        },
      },
    ],
    responses: {
      200: {
        description: 'Notifications retrieved successfully',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Notifications retrieved successfully',
              data: {
                results: [
                  {
                    recipient: '651bb5bf4eb9240327ea1111',
                    actor: {
                      username: 'char<PERSON><PERSON><PERSON>',
                      photo: {
                        _id: '65bb7f69f2b37ca660a3c05f',
                        url: 'https://storagesite/users%2Fphoto/74570fc0-c0f4-11ee-92c2-61dcf2fafad1-866-536x354.jpg',
                      },
                      firstName: 'George O',
                      lastName: 'Darwin',
                      tagLine: '',
                      id: '651bb5bf4eb9240327ea1111',
                    },
                    type: 'mention',
                    message: '@U{651bb5bf4eb9240327ea1111**George O Darwin} mentioned you in a  @P{xxxx}',
                    read: false,
                    createdAt: '2024-03-04T13:18:55.679Z',
                    updatedAt: '2024-03-04T13:18:55.679Z',
                    id: '65e5ca3f20ddd2bd661d76d8',
                  },
                  {
                    recipient: '651bb5bf4eb9240327ea1111',
                    actor: {
                      firstName: 'Rickter',
                      lastName: 'Belmont',
                      username: 'charlienwa22',
                      photo: null,
                      id: '652f6fb971c38a9b9306111',
                    },
                    type: 'join-group-request',
                    message: '@U{652f6fb971c38a9b9306111**Rickter Belmont} requested to join @G{65699eb8ff316eff38c4264d}',
                    read: false,
                    createdAt: '2024-02-29T17:55:17.925Z',
                    updatedAt: '2024-02-29T17:55:17.925Z',
                    id: '65e0c505c14c31cd5ecc9ac5',
                  },
                ],
                page: 1,
                limit: 10,
                totalPages: 19,
                totalResults: 188,
              },
            },
          },
        },
      },
    },
  },
};
