const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const { resourceLibraryCategories, schoolsEnums } = require('../config/constants');

const resourceLibrarySchema = new mongoose.Schema(
  {
    title: { type: String, required: true, trim: true },
    description: { type: String, trim: true },
    categories: [{ type: String, enum: Object.values(resourceLibraryCategories) }],
    tags: [{ type: String, trim: true }],
    programTypes: [{ type: String, enum: Object.values(schoolsEnums.programTypes) }],
    programs: [{ type: String }],
    file: { type: mongoose.Schema.Types.ObjectId, ref: 'File' },
    thumbnails: [{ type: mongoose.Schema.Types.ObjectId, ref: 'File' }],
    downloadsCount: { type: Number, default: 0 },
    viewsCount: { type: Number, default: 0 },
  },
  {
    timestamps: true,
  },
);

resourceLibrarySchema.index({ createdAt: 1, updatedAt: 1, title: 1 });

resourceLibrarySchema.plugin(toJSON);
resourceLibrarySchema.plugin(paginate);

const ResourceLibrary = mongoose.model('ResourceLibrary', resourceLibrarySchema);

module.exports = ResourceLibrary;
