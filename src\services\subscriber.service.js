const httpStatus = require('http-status');
const { Subscriber } = require('../models');
const ApiError = require('../utils/ApiError');

const getSubscribers = async (filterParams, options) => {
  const filter = { ...filterParams, subscribed: true };
  const subscribers = await Subscriber.paginate(filter, { ...options });
  return subscribers;
};

const addSubscriber = async (subscriberBody) => {
  const { email, subscribedFor } = subscriberBody;
  const subscriberExists = await Subscriber.findOne({ email, subscribedFor });
  if (subscriberExists) {
    subscriberExists.subscribed = true;
    await subscriberExists.save();
    return subscriberExists;
  }

  const subscriber = await Subscriber.create(subscriberBody);
  return subscriber;
};

const unsubscribe = async (unsubscribeBody) => {
  const { email, subscribedFor } = unsubscribeBody;
  const subscriber = await Subscriber.findOne({ email, subscribedFor, subscribed: true });
  if (!subscriber) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Subscriber not found');
  }
  subscriber.subscribed = false;
  await subscriber.save();
};

module.exports = {
  getSubscribers,
  addSubscriber,
  unsubscribe,
};
