const express = require('express');

const router = express.Router();
const { auth, canManageResource } = require('../../middlewares/auth');
const validate = require('../../middlewares/validate');
const { postController: PostController } = require('../../controllers');
const { postValidation: PostValidation } = require('../../validations');
const { uploadMultiple } = require('../../config/multer.config');
const { canCreateGroupPost, canGetGroupPost, canGetGroupPosts } = require('../../middlewares/cancan/groups');

router.get('/:id', validate(PostValidation.getPost), PostController.getPostById);

router.use(auth());

router.post('/', uploadMultiple, validate(PostValidation.createPost), canCreateGroupPost(), PostController.createPost);
router.get('/', validate(PostValidation.getPosts), canGetGroupPosts(), PostController.getPosts);

// router.get('/feed', validate(PostValidation.getPosts), PostController.getUserProfilePosts);

router.patch(
  '/:id',
  canManageResource('Post'),
  uploadMultiple,
  validate(PostValidation.updatePost),
  PostController.updatePost,
);
router.post('/react', validate(PostValidation.reactToPost), PostController.reactToPost);
router.delete('/:id', validate(PostValidation.deletePost), canManageResource('Post'), PostController.deletePost);

router.post('/:id/bookmark', validate(PostValidation.bookmarkPost), canGetGroupPost(), PostController.bookmarkPost);
router.get('/:id/reactions', validate(PostValidation.getUserReactions), canGetGroupPost(), PostController.getUsersReactions);

module.exports = router;
