module.exports = {
  patch: {
    summary: 'Update a forum post by ID',
    tags: ['Forum Posts'],
    parameters: [
      {
        name: 'id',
        in: 'path',
        description: 'ID of the forum post',
        required: true,
        type: 'string',
        format: 'objectId',
      },
      {
        in: 'formData',
        name: 'files',
        type: 'file',
        description: 'File files',
      },
      {
        in: 'formData',
        name: 'body',
        required: true,
        description: 'Comment data',
        schema: {
          type: 'object',
          properties: {
            text: {
              type: 'string',
              description: 'Forum post text',
              example: 'We have a new post to search for the best college',
            },
            topic: {
              type: 'string',
              description: 'Forum post topic',
              example: 'Find a new college',
            },
            tags: { type: 'array', description: 'Forum post tags', example: ['New'] },
            category: {
              type: 'string',
              description: 'Forum post category',
              enum: [
                'All',
                'General Discussion',
                'Admissions',
                'Test Scores',
                'SAT',
                'ACT',
                'College Applications',
                'Scholarships',
                'Financial Aid',
                'Study Tips',
                'Student Life',
                'Internships',
                'Career Advice',
                'Graduate School',
              ],
              example: 'General Discussion',
            },
            files: { type: 'file', description: 'File files' },
          },
        },
      },
    ],
    responses: {
      200: {
        description: 'Forum post updated successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string', example: 'SUCCESS' },
                message: { type: 'string', example: 'Forum post updated successfully' },
              },
              required: ['status', 'data', 'message'],
            },
          },
        },
      },
      404: {
        description: 'Forum post not found',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string', example: 'FAILED' },
                message: { type: 'string', example: 'Forum post record not found' },
              },
              required: ['status', 'message'],
            },
          },
        },
      },
    },
  },
};
