/* eslint-disable global-require */
const Agenda = require('agenda');
const config = require('../../config/config');
const logger = require('../../config/logger');
const defineJobs = require('./jobDefinitions');

const agenda = new Agenda({
  db: {
    address: config.mongoose.url,
    ...config.mongoose.options,
    collection: 'agendaJobs',
    readPreference: 'primary',
  },
  processEvery: '10 seconds',
});

const gracefulShutdown = async () => {
  logger.info('Shutting down Agenda');
  await agenda.stop();
  process.exit(0);
};

const startAgenda = async () => {
  await defineJobs(agenda);

  await agenda._collection.createIndex({ nextRunAt: 1, priority: -1 }, { name: 'findAndLockNextJobIndex_sort' });
  await agenda._collection.createIndex({ lockedAt: 1 });
  await agenda._collection.createIndex({ lastRunAt: 1 });

  process.on('SIGTERM', gracefulShutdown);
  process.on('SIGINT', gracefulShutdown);
};

module.exports = {
  agenda,
  startAgenda,
};
