const mongoose = require('mongoose');
const ShortUniqueId = require('short-unique-id');
const { businessEnums } = require('../config/constants');

const { ObjectId } = mongoose.Schema.Types;

const referralSchema = mongoose.Schema(
  {
    referredUser: { type: ObjectId, ref: 'User' }, // the referred person
    referrerBusiness: { type: ObjectId, ref: 'Business' }, // the business that referred the user
    referrerUser: { type: ObjectId, ref: 'User' }, // the user that referred the referred person
    referralCode: { type: String, trim: true },
    service: { type: ObjectId, ref: 'Service' },
    hasBoughtService: { type: Boolean, default: false },

    referralBonus: { type: Number, default: 0 },
    referralBonusType: { type: String, enum: Object.values(businessEnums.referralBonusTypes) },
    hasWithdrawnBonus: { type: <PERSON>olean, default: false },
    bonusIsWithdrawable: { type: <PERSON>olean, default: false },
    order: { type: ObjectId, ref: 'Order' },
  },
  { timestamps: true },
);

referralSchema.statics.generateUniqueReferralCode = async function () {
  let referralCode = new ShortUniqueId({ length: 10 }).rnd();
  let referral = await this.findOne({ referralCode });

  while (referral) {
    referralCode = new ShortUniqueId({ length: 10 }).rnd();
    // eslint-disable-next-line no-await-in-loop
    referral = await this.findOne({ referral });
  }

  return referralCode;
};

const Referral = mongoose.model('Referral', referralSchema);

module.exports = Referral;
