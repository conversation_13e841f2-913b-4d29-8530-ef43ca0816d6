const express = require('express');

const router = express.Router();
const { auth, canManageResource } = require('../../middlewares/auth');
const { commentValidation } = require('../../validations');
const { commentController } = require('../../controllers');
const validate = require('../../middlewares/validate');
const { uploadMultiple } = require('../../config/multer.config');
const canGetGroupPost = require('../../middlewares/cancan/groups/canGetGroupPost');

router.use(auth());

router.post(
  '/',
  uploadMultiple,
  validate(commentValidation.createComment),
  canGetGroupPost(),
  commentController.createComment,
);
router.get('/', validate(commentValidation.getComments), canGetGroupPost(), commentController.getComments);

router.get('/:id', validate(commentValidation.getComment), commentController.getCommentById);
router.patch(
  '/:id',
  canManageResource('Comment'),
  uploadMultiple,
  validate(commentValidation.updateComment),
  commentController.updateComment,
);
router.delete(
  '/:id',
  canManageResource('Comment'),
  validate(commentValidation.deleteComment),
  commentController.deleteComment,
);

module.exports = router;
