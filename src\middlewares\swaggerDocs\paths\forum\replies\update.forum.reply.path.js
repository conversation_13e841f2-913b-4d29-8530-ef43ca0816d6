module.exports = {
  patch: {
    summary: 'Update a specific forum post reply',
    tags: ['Forum Replies'],
    parameters: [
      {
        name: 'forumPostId',
        in: 'path',
        description: 'ID of the forum post',
        example: '6549f740956baabd787bbd58',
        required: true,
        type: 'string',
        format: 'objectId',
      },
      {
        name: 'id',
        in: 'path',
        description: 'ID of the forum post reply',
        example: '6549f740956baabd787bbd58',
        required: true,
        type: 'string',
        format: 'objectId',
      },
      {
        in: 'formData',
        name: 'files',
        type: 'string',
        format: 'base64',
        required: false,
        description: 'Array of files to upload (optional)',
      },
      {
        in: 'formData',
        name: 'body',
        required: true,
        description: 'Forum reply data',
        schema: {
          type: 'object',
          properties: {
            text: { type: 'string', example: 'This is a reply text' },
            deletedUrls: { type: 'string', example: 'https://example.com/file.jpg https://example.com/file2.jpg' },
            accepted: { type: 'boolean', example: true },
          },
        },
      },
    ],
    responses: {
      200: {
        description: 'Reply updated successfully',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Reply updated successfully',
            },
          },
        },
      },
      404: {
        description: 'Reply not found',
        content: {
          'application/json': {
            example: {
              status: 'FAILED',
              message: 'Reply record not found',
            },
          },
        },
      },
    },
  },
};
