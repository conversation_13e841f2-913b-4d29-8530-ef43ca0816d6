/* eslint-disable global-require */
const fs = require('fs');
const { agenda } = require('./index');
const Models = require('../../models');
const jobEnums = require('./jobEnums');
const logger = require('../../config/logger');
const { postService, commentService, forumPostService, forumReplyService } = require('../../services');

const scheduleMarkOrderAsCompleted = async (params) => {
  await agenda.schedule('in 7 days', jobEnums.orderJobs.MARK_ORDER_AS_COMPLETED, params);
};

const scheduleMarkRevisionAsCompleted = async (params) => {
  await agenda.schedule('in 7 days', jobEnums.orderJobs.MARK_REVISION_AS_COMPLETED, params);
};

const scheduleDeleteTemporaryCloudinaryFiles = async (params) => {
  // change to 30 minutes
  const jobName = `${jobEnums.fileJobs.DELETE_TEMPORARY_CLOUDINARY_FILES}-${params.publicIds[0]}`;
  agenda.define(jobName, { priority: jobEnums.priority.HIGH }, async (job) => {
    const { publicIds } = job.attrs.data;
    const { fileService } = require('../../services'); // Import here to avoid circular dependencies
    await fileService.deleteCloudinaryFiles(publicIds);
    await job.remove();
  });

  await agenda.schedule('in 30 minute', jobName, params, { skipImmediate: true });
};

const scheduleCreateMessageEmail = async (params) => {
  await agenda.schedule('in 5 minutes', jobEnums.emailJobs.CREATE_MESSAGE_EMAIL, params);
};

// const scheduleCreateReaction = async (params) => {
//   await agenda.schedule('in 5 minutes', jobEnums.emailJobs.CREATE_REACTION_EMAIL, params);
// };

const scheduleCreateReaction = async (params) => {
  const jobName = `${jobEnums.emailJobs.CREATE_REACTION_EMAIL}-${Date.now()}`;

  agenda.define(jobEnums.emailJobs.CREATE_REACTION_EMAIL, async (job) => {
    const { sender, recipient, details } = job.attrs.data;
    const { resourceId, type } = details;
    const resourceName = details.resourceName.toLowerCase();
    const reacted = await Models.Reaction.findOne({ [resourceName]: resourceId, user: sender._id, type });
    if (reacted) {
      logger.info('Sending email notification');
      const { emailService } = require('../../services'); // Import here to avoid circular dependencies
      await emailService.sendReactionNotificationEmail(sender, recipient, details);
    }
  });
  await agenda.schedule('in 5 minutes', jobName, params, { skipImmediate: true });
};

const scheduleCreateFollower = async (params) => {
  await agenda.schedule('in 5 minutes', jobEnums.emailJobs.CREATE_FOLLOWER_EMAIL, params);
};

const scheduleRepost = async (params) => {
  await agenda.schedule('in 5 minutes', jobEnums.emailJobs.CREATE_REPOST_EMAIL, params);
};

const scheduleNotifyPremiumSubscriptionExpiration = async (params) => {
  // params -> {userId, subscriptionId, expiresAt}
  const jobNames = [
    {
      name: `${jobEnums.emailJobs.NOTIFY_PREMIUM_SUBSCRIBERS}-${params.subscriptionId}-3days`,
      scheduledAt: new Date(new Date(params.expiresAt).getTime() - 3 * 24 * 60 * 60 * 1000),
    },
    {
      name: `${jobEnums.emailJobs.NOTIFY_PREMIUM_SUBSCRIBERS}-${params.subscriptionId}-7days`,
      scheduledAt: new Date(new Date(params.expiresAt).getTime() - 7 * 24 * 60 * 60 * 1000),
    },
  ];

  const promises = jobNames.map(async ({ name, scheduledAt }) => {
    agenda.define(name, { priority: jobEnums.priority.HIGH }, async (job) => {
      const { userId, subscriptionId, expiresAt } = job.attrs.data;

      const { emailService } = require('../../services'); // Import here to avoid circular dependencies
      const { User } = require('../../models');

      const user = await User.findById(userId).select('firstName lastName email');
      await emailService.sendPremiumSubscriptionExpirationEmail(user, { subscriptionId, expiresAt });

      await job.remove();
    });
    await agenda.schedule(scheduledAt, name, params);
  });
  await Promise.all(promises);
};

const scheduleDeleteFolder = async (params) => {
  // eslint-disable-next-line no-console
  console.log('Scheduling delete folder:', params.folder);
  agenda.define(params.folder, { priority: jobEnums.priority.HIGH }, async (job) => {
    const { folder } = job.attrs.data;
    // eslint-disable-next-line no-console
    console.log('Deleting folder:', folder);
    try {
      // eslint-disable-next-line no-console
      console.log('Deleting folder:', folder);
      await fs.promises.rm(folder, { recursive: true, force: true });
      await job.remove();
    } catch (error) {
      logger.error(`Error deleting folder: ${folder}`);
      logger.error(error);
    }
  });

  await agenda.schedule('in 5 minutes', params.folder, params, { skipImmediate: true });
  // eslint-disable-next-line no-console
  console.log('Scheduled delete folder:', params.folder);
};

const scheduleDeleteUserInfo = async (params) => {
  // params -> {userId}
  const jobName = `${jobEnums.userJobs.DELETE_USER_INFO}-${params.userId}`;
  agenda.define(jobName, { priority: jobEnums.priority.HIGH }, async (job) => {
    const { userId } = job.attrs.data;
    const promises = [];

    const postIds = await Models.Post.find({ user: userId }).distinct('_id');
    const commentIds = await Models.Comment.find({ user: userId }).distinct('_id');

    const forumPostsIds = await Models.ForumPost.find({ user: userId }).distinct('_id');
    const forumReplies = await Models.ForumReply.find({ user: userId }).select('_id forumPost');

    promises.push(
      ...postIds.map((postId) => postService.deletePost(postId.toString())),
      ...commentIds.map((commentId) => commentService.deleteCommentById(commentId.toString(), null)),
      ...forumPostsIds.map((forumPostId) => forumPostService.deleteForumPost(forumPostId.toString())),
      ...forumReplies.map((forumReply) =>
        forumReplyService.deleteForumReply(forumReply._id.toString(), forumReply.forumPost.toString()),
      ),
      Models.Reaction.deleteMany({ user: userId }),
      Models.ActivityLog.deleteMany({ user: userId }),
      Models.Notification.deleteMany({ recipient: userId }),
    );
    await Promise.all(promises);

    await job.remove();
  });

  await agenda.schedule('in 5 minutes', jobName, params, { skipImmediate: true });
};

const scheduleSendEmailForNewUnykEdPost = async (params) => {
  // params -> { postId, authorId }
  const jobName = `${jobEnums.emailJobs.CREATE_POST_EMAIL}-${params.postId}`;
  agenda.define(jobName, { priority: jobEnums.priority.HIGH }, async (job) => {
    const { postId, authorId } = job.attrs.data;
    const { emailService } = require('../../services'); // Import here to avoid circular dependencies

    const sender = await Models.User.findById(authorId).select('firstName lastName email _id').lean();
    const users = await Models.User.find({ isEmailVerified: true, locked: false, $ne: { _id: sender._id } })
      .select('email firstName lastName _id')
      .lean();
    // await emailService.sendNewUnykEdPostEmail(sender, postId);
    const promises = users.map(async (user) => {
      await emailService.sendNewUnykEdPostEmail(sender, user, { resourceId: postId });
    });
    await Promise.all(promises);

    await job.remove();
  });

  await agenda.schedule('in 5 minutes', jobName, params, { skipImmediate: true });
};

const scheduleSetBusinessOnlineStatus = async (params) => {
  const jobName = `Business_Online-${params.businessId}`;
  const { Business } = require('../../models'); // Import here to avoid circular dependencies

  const business = await Business.findById(params.businessId);
  agenda.define(jobName, { priority: jobEnums.priority.HIGH }, async (job) => {
    try {
      const { businessId } = job.attrs.data;

      const { socketConnection } = require('../../services/sockets');
      const contactPersonId = business.contactPerson.toString();

      if (socketConnection.connectedUsers[contactPersonId]?.length) {
        socketConnection.connectedUsers[contactPersonId].forEach((socket) => {
          socket.emit('business-online-status', { businessId, online: true });
        });
      } else {
        // Business should be marked as offline and job removed
        await Business.findByIdAndUpdate(params.businessId, { online: false });
        await job.remove();
      }
    } catch (error) {
      logger.error(`Error setting business online status`);
      logger.error(error);
    }
  });

  await Business.findByIdAndUpdate(params.businessId, { online: true });
  // Run every 5 mins
  await agenda.every('*/5 * * * *', jobName, params, { skipImmediate: true });
};

module.exports = {
  scheduleMarkOrderAsCompleted,
  scheduleMarkRevisionAsCompleted,
  scheduleDeleteTemporaryCloudinaryFiles,
  scheduleCreateMessageEmail,
  scheduleCreateReaction,
  scheduleCreateFollower,
  scheduleRepost,
  scheduleNotifyPremiumSubscriptionExpiration,
  scheduleDeleteFolder,
  scheduleDeleteUserInfo,
  scheduleSendEmailForNewUnykEdPost,
  scheduleSetBusinessOnlineStatus,
};
