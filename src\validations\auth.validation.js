const Joi = require('joi');
const { password } = require('./custom.validation');
const { clientUserTypes } = require('../config/constants');

const register = {
  body: Joi.object()
    .keys({
      email: Joi.string().required().email(),
      password: Joi.string().required().custom(password),
      firstName: Joi.string().required(),
      lastName: Joi.string().required(),
      middleName: Joi.string(),
      username: Joi.string().required(),
      signupAs: Joi.string()
        .valid(...Object.values(clientUserTypes))
        .required(),
      subscribed: Joi.string().valid('true', 'false').required(),

      referrerBusiness: Joi.string(),
      service: Joi.string(),
      // returnUrl: Joi.string(),
    })
    .custom((value, helpers) => {
      const { referrerBusiness, service } = value;

      if ((referrerBusiness && !service) || (!referrerBusiness && service)) {
        return helpers.error('any.invalid', {
          message: 'Both referrerBusiness and service must be provided together or not at all.',
        });
      }

      return value; // Valid input passes through unchanged
    }),
};

const login = {
  body: Joi.object().keys({
    email: Joi.string().required().email(),
    password: Joi.string().required(),
  }),
};

const logout = {
  body: Joi.object().keys({
    refreshToken: Joi.string().required(),
  }),
};

const refreshTokens = {
  query: Joi.object().keys({
    refreshToken: Joi.string().required(),
  }),
};

const forgotPassword = {
  body: Joi.object().keys({
    email: Joi.string().email().required(),
  }),
};

const resetPassword = {
  body: Joi.object().keys({
    token: Joi.string().required(),
    password: Joi.string().required().custom(password),
    confirmPassword: Joi.string().required().custom(password),
  }),
};

const verifyEmail = {
  query: Joi.object().keys({
    token: Joi.string().required(),
    email: Joi.string().required(),
    signupAs: Joi.string()
      .valid(...Object.values(clientUserTypes))
      .required(),
  }),
};

const verifyDuplicateSignupEmail = {
  query: Joi.object().keys({
    email: Joi.string().required(),
    signupAs: Joi.string()
      .valid(...Object.values(clientUserTypes))
      .required(),
    token: Joi.string().required(),
  }),
};

const sendVerificationEmail = {
  body: Joi.object().keys({
    email: Joi.string().email().required(),
    signupAs: Joi.string()
      .valid(...Object.values(clientUserTypes))
      .required(),
  }),
};

const usernameEmailAvail = {
  body: Joi.object().keys({
    email: Joi.string().email(),
    username: Joi.string(),
  }),
};

const oauth = {
  query: Joi.object().keys({
    code: Joi.string().required(),
    signupAs: Joi.string().valid(...Object.values(clientUserTypes)),
    subscribed: Joi.string().valid('true', 'false').default('true'),

    referrerBusiness: Joi.string(),
    service: Joi.string(),
    // returnUrl: Joi.string(),
  }),
};

const verifyAccess = {
  query: Joi.object().keys({
    accessToken: Joi.string().required(),
    refreshToken: Joi.string().required(),
  }),
};

const sendOTP = {
  body: Joi.object().keys({
    phone: Joi.string().required(),
    channel: Joi.string().valid('sms', 'call').required(),
  }),
};

const verifyOTP = {
  body: Joi.object().keys({
    phone: Joi.string().required(),
    otp: Joi.string().required(),
  }),
};

module.exports = {
  register,
  login,
  logout,
  refreshTokens,
  forgotPassword,
  resetPassword,
  verifyEmail,
  verifyDuplicateSignupEmail,
  sendVerificationEmail,
  usernameEmailAvail,
  oauth,
  verifyAccess,
  sendOTP,
  verifyOTP,
};
