const router = require('express').Router();
const { messageController } = require('../../controllers');
const { messageValidation } = require('../../validations');
const validate = require('../../middlewares/validate');
const { auth } = require('../../middlewares/auth');

router.use(auth());

router.get('/conversation/:conversationId', messageController.getConversation);
router.post('/create-conversation', validate(messageValidation.createConversation), messageController.createConversation);
// router.post('/add-participants', validate(messageValidation.addParticipants), messageController.addParticipants);
router.get('/get-conversations', validate(messageValidation.getConversations), messageController.getConversations);
router.get(
  '/get-conversation/:conversationId',
  validate(messageValidation.getConversation),
  messageController.getConversation,
);
router.get('/count-unread-conversations', messageController.countUnreadConversations);

module.exports = router;
