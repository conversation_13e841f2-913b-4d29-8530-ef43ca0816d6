const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');

const { ObjectId } = mongoose.SchemaTypes;

const serviceRevisionSchema = new mongoose.Schema(
  {
    client: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    fulfilledAt: { type: Date },
    provider: { type: mongoose.Schema.Types.ObjectId, ref: 'Business', required: true },
    order: { type: mongoose.Schema.Types.ObjectId, ref: 'Order', required: true },
    service: { type: mongoose.Schema.Types.ObjectId, ref: 'Service', required: true },
    description: { type: String, required: true }, // Description of what should be revised
    supportingDocuments: [{ type: mongoose.Schema.Types.ObjectId, ref: 'File' }], // files uploaded by the provider after revision
    deliverable: {
      files: [{ type: ObjectId, ref: 'File' }],
      comment: { type: String },
    },
  },
  {
    timestamps: true,
  },
);

serviceRevisionSchema.index({ order: 1, service: 1 });

serviceRevisionSchema.plugin(toJSON);
serviceRevisionSchema.plugin(paginate);

const ServiceRevision = mongoose.model('ServiceRevision', serviceRevisionSchema);

module.exports = ServiceRevision;
