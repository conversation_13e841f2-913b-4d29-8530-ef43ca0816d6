const httpStatus = require('http-status');
const { issueReportService } = require('../services');
const catchAsync = require('../utils/catchAsync');
const { pick } = require('../utils/pick');

const createIssueReport = catchAsync(async (req, res) => {
  await issueReportService.createIssueReport(req);
  res.status(httpStatus.CREATED).json({ status: 'SUCCESS', message: 'Issue created successfully' });
});

const getIssueReportEnums = catchAsync(async (req, res) => {
  const enums = await issueReportService.getIssueReportEnums();
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: enums });
});

const getIssueReportPrivateEnums = catchAsync(async (req, res) => {
  const enums = await issueReportService.getIssueReportEnums(true);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: enums });
});

const getIssueReportById = catchAsync(async (req, res) => {
  const issueReport = await issueReportService.getIssueReportById(req.params.id);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: issueReport });
});

const getIssueReports = catchAsync(async (req, res) => {
  const filter = pick(req.query, ['reporter', 'status', 'assignedTo', 'resolvedBy', 'title', 'type', 'priority']);
  const { searchText } = req.query;
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const issueReports = await issueReportService.getIssueReports(filter, options, searchText);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: issueReports });
});

const updateIssueReport = catchAsync(async (req, res) => {
  await issueReportService.updateIssueReport(req.params.id, req.body);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: `Issue status successfully updated as ${req.body.status}` });
});

const getIssueReportStats = catchAsync(async (req, res) => {
  const stats = await issueReportService.getIssueReportStats();
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: stats });
});

module.exports = {
  createIssueReport,
  getIssueReports,
  updateIssueReport,
  getIssueReportEnums,
  getIssueReportPrivateEnums,
  getIssueReportStats,
  getIssueReportById,
};
