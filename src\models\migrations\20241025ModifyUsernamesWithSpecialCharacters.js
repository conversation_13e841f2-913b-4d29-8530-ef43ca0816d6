const Async = require('async');
const logger = require('../../config/logger');
const User = require('../user.model');
const GlobalVariable = require('../global.variable.model');
const { checkUsernameExists } = require('../../services/auth.service');

const varName = 'Modify usernames with special characters';

const modifyUsernamesWithSpecialCharacters = async () => {
  const migrated = await GlobalVariable.findOne({ name: varName });

  if (migrated) {
    return;
  }

  const users = await User.find({ username: /[^a-zA-Z0-9]/i });

  await Async.eachOfSeries(users, async (user, index) => {
    const username = await checkUsernameExists(user.username);
    await User.updateOne({ _id: user._id }, { username });
    logger.info(`(${index})Updated username for ${user.username} to ${username}`);
  });

  await GlobalVariable.create({ name: varName, value: 'true' });
  logger.info('Modified usernames with special characters');
};

module.exports = modifyUsernamesWithSpecialCharacters;
