const renameVolunteerToVolunteering = require('./20240218VolunteerToVolunteering');
const createGroupSettingForGroupsAndMembers = require('./20240222CreateGroupSettingForGroupsAndMembers');
const createLanguageModel = require('./20240715CreateLanguageModel');
const modifyProfileDefaultVisibities = require('./20240228ModifyProfileDefaultVisibities');
const moveLikesToReactions = require('./20240508MoveLikesToReactions');
// const resendEmailVerification = require('./20240522ResendVerification');
// const { createDBSeed } = require('./db.seed');
const recreateCountriesAndStatesModels = require('./20240606RecreateCountriesAndStatesModels');
const renameProgramTypesToProgramType = require('./20240618RenameProgramTypesToProgramType');
const loadMessagesToConversations = require('./20240710LoadMessagesInConversation');
const setUserSignupMedium = require('./20240718SetUserSignupMedium');
const combineRepostsAndRepostsWithThought = require('./20240801CombineRepostsAndRepostsWithThought');
const setPendingStudentProperty = require('./20240808SetPendingStudentPropertyForExistingUsers');
const addUsersToSubscribers = require('./20240807AddUsersToSubscribers');
const addServicesPropertyToBusiness = require('./20240926AddServicesPropertyToBusinessModel');
const addOrderNumberToExistingOrders = require('./20240930AddOrderNumberForExistingOrders');
const initializeServicesArray = require('./20241011InitializeServicesRequirementsArray');
const addRevisionsMaxCountToExistingOrders = require('./20241013AddRevisionsMaxCountToExistingOrders');
const updateReviewsArrayInServices = require('./20241016UpdateReviewArrayInService');
const makeOrdersInBusinessUnique = require('./20241021MakeOrdersInBusinessUnique');
const modifyUsernamesWithSpecialCharacters = require('./20241025ModifyUsernamesWithSpecialCharacters');
const addTypeToTransaction = require('./20241030AddTypeToTransaction');
const updateAccountBalances = require('./20241108UpdateAccountBalances');
const convertAmountsToCent = require('./20241111ConvertAmountsToCent');
const addCommentToTransaction = require('./20241118AddCommentToTransaction');
const addClientToOrder = require('./20241121AddClientToOrder');
const renameSomeMessagingProperties = require('./20241212RenameSomeMessagingProperties');
const generateBusinessReferralCode = require('./20241213GenerateBusinessReferralCode');
const addCreatedForToMessageConversation = require('./20241225AddCreatedForToMessageConversation');
const setExistsingBusinessVerificationStatus = require('./20250116SetExistsingBusinessVerificationStatus');
const notifyUsersToCompleteProfileScript = require('./20250113NotifyUsersToCompleteProfile');
const notifyUsersToCompleteProfileCreateToken = require('./20250113NotifyUsersToCompleteProfile_CreateToken');
const setExistingServicesVerificationStatusAndVerifications = require('./20250309SetVerificationsInExistingServiceRecords');
const makeEmailInProfilePublic = require('./20250327MakeEmailInProfilePublic');
const setScholarshipsFieldInUserRecords = require('./20250323SetUserScholarshipsField');
const renameScholarshipTypesToFundingTypes = require('./20250402RenameScholarshipTypesToFundingTypes');
const changeDeadlineVariesToDurationType = require('./20250404ChangeDeadlineVariesToDurationType');
const setEmailInProfileAsPublic = require('./20250409MakeEmailInProfilePrivate');
const generateUniqueNameForExistingSchipScholarships = require('./20250516GenerateUniqueNameForExistingScholarships');

const migrations = async () => {
  await renameVolunteerToVolunteering();
  await createGroupSettingForGroupsAndMembers();
  await createLanguageModel();
  await modifyProfileDefaultVisibities();
  await moveLikesToReactions();
  // await resendEmailVerification();
  // await createDBSeed();
  await recreateCountriesAndStatesModels();
  await renameProgramTypesToProgramType();
  await loadMessagesToConversations();
  await setUserSignupMedium();
  await combineRepostsAndRepostsWithThought();
  await setPendingStudentProperty();
  await addUsersToSubscribers();
  await addServicesPropertyToBusiness();
  await addOrderNumberToExistingOrders();
  await initializeServicesArray();
  await addRevisionsMaxCountToExistingOrders();
  await updateReviewsArrayInServices();
  await makeOrdersInBusinessUnique();
  await modifyUsernamesWithSpecialCharacters();
  await addTypeToTransaction();
  await updateAccountBalances();
  await convertAmountsToCent();
  await addCommentToTransaction();
  await addClientToOrder();
  await renameSomeMessagingProperties();
  await generateBusinessReferralCode();
  await addCreatedForToMessageConversation();
  await setExistsingBusinessVerificationStatus();
  await notifyUsersToCompleteProfileScript();
  await notifyUsersToCompleteProfileCreateToken();
  await setExistingServicesVerificationStatusAndVerifications();
  await makeEmailInProfilePublic();
  await setScholarshipsFieldInUserRecords();
  await renameScholarshipTypesToFundingTypes();
  await changeDeadlineVariesToDurationType();
  await setEmailInProfileAsPublic();
  await generateUniqueNameForExistingSchipScholarships();
};

module.exports = migrations;
