const httpStatus = require('http-status');
const mongoose = require('mongoose');
const logger = require('../config/logger');
const ApiError = require('../utils/ApiError');
const validateId = require('./shared/validateId');
const { Scholarship, User } = require('../models');
const { schoolsEnums, azureContainers } = require('../config/constants');
const { processFileUpload, deleteManyFiles } = require('./azure.file.service');

const convertSortOption = (sortBy) => {
  const [field, direction] = sortBy.split(':');
  return { [field]: direction === 'asc' ? 1 : -1 };
};

const getBookmarkedScholarships = async (user, options) => {
  const page = parseInt(options.page, 10) || 1;
  const limit = parseInt(options.limit, 10) || 10;
  const sort = options.sortBy ? convertSortOption(options.sortBy) : { deadline: 1 };

  const matchFilter = { _id: { $in: user.scholarships } };

  // if (!filter.status) {
  //   matchFilter.$or = [
  //     { status: schoolsEnums.scholarshipStatuses.OPEN },
  //     { status: schoolsEnums.scholarshipStatuses.CLOSED },
  //   ];
  // } else {
  //   matchFilter.status = filter.status;
  // }

  // Get paginated results and counts in parallel
  const [paginatedResults, openCount, closedCount, savedScholarshipIds, allScholarships] = await Promise.all([
    Scholarship.find(matchFilter)
      .populate({ path: 'logo', select: 'url _id' })
      .sort(sort)
      .skip((page - 1) * limit)
      .limit(limit)
      .lean(),
    Scholarship.countDocuments({
      _id: { $in: user.scholarships },
      status: schoolsEnums.scholarshipStatuses.OPEN,
    }),
    Scholarship.countDocuments({
      _id: { $in: user.scholarships },
      status: schoolsEnums.scholarshipStatuses.CLOSED,
    }),
    // Scholarship.countDocuments({ _id: { $in: user.scholarships } }),
    Scholarship.find({ _id: { $in: user.scholarships } })
      .select('_id')
      .lean(),
    Scholarship.countDocuments(),
  ]);

  const totalCount = savedScholarshipIds.length;

  return {
    results: paginatedResults.map((scholarship) => ({
      ...scholarship,
      id: scholarship._id.toString(),
    })),
    page,
    limit,
    totalPages: Math.ceil(totalCount / limit),
    totalResults: totalCount,
    open: openCount,
    closed: closedCount,
    savedScholarships: savedScholarshipIds.map((s) => s._id.toString()),
    savedCount: savedScholarshipIds.length,
    allScholarships,
  };
};

const getScholarships = async (user = null, filter = {}, options = {}) => {
  const queryFilter = { ...filter };

  const { tab } = queryFilter;
  delete queryFilter.tab;
  queryFilter.$or = [];

  // Handle bookmark tab only if user exists
  if (tab === 'my_saved') {
    if (!user) {
      throw new Error('Authentication required to access saved scholarships');
    }
    return getBookmarkedScholarships(user, options);
  }

  // Text search handling (unchanged)
  const textSearchFields = ['name', 'fundingType', 'hostCountry'];
  textSearchFields.forEach((queryParam) => {
    if (queryFilter[queryParam]) {
      queryFilter[queryParam] = { $regex: queryFilter[queryParam], $options: 'i' };
    }
  });

  // Filter processing (unchanged)
  if (queryFilter.eligibleCountries) {
    const countries = Array.isArray(queryFilter.eligibleCountries)
      ? queryFilter.eligibleCountries
      : queryFilter.eligibleCountries.split(' ').map((country) => country.trim());

    const escapeRegExp = (string) => string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    // eslint-disable-next-line security/detect-non-literal-regexp
    const countryRegexes = countries.map((country) => new RegExp(`^${escapeRegExp(country)}$`, 'i'));
    queryFilter.eligibleCountries = { $in: countryRegexes };
  }

  if (queryFilter.deadline) {
    const deadline = new Date(queryFilter.deadline);
    queryFilter.deadline = { $gte: deadline };
  }

  if (queryFilter.programType) {
    const programTypes = Array.isArray(queryFilter.programType)
      ? queryFilter.programType
      : queryFilter.programType.split(',').map((programType) => programType.trim());
    queryFilter.programTypes = { $in: programTypes };
    delete queryFilter.programType;
  }

  // if (!queryFilter.status) {
  //   queryFilter.$or.push(
  //     ...[{ status: schoolsEnums.scholarshipStatuses.OPEN }, { status: schoolsEnums.scholarshipStatuses.CLOSED }],
  //   ); // No need for this statement because when no status is provided, all statuses are included

  //   delete queryFilter.status;
  // }

  if (queryFilter.program) {
    const programs = Array.isArray(queryFilter.program) ? queryFilter.program : [queryFilter.program];
    if (!programs.includes('All Programs')) {
      queryFilter.programs = { $in: programs };
    }
    delete queryFilter.program;
  }

  if (!queryFilter.$or.length) {
    delete queryFilter.$or;
  }

  // Prepare parallel operations
  const operations = [
    Scholarship.paginate(queryFilter, {
      ...options,
      lean: true,
      select:
        'name hostCountry programType deadline durationType status logo startDate fundingType programTypes description uniqueName',
      populate: [{ path: 'logo', select: 'url _id' }],
    }),
    // Count operations (modified for anonymous users)
    Scholarship.aggregate([
      {
        $facet: {
          openCount: [{ $match: { status: schoolsEnums.scholarshipStatuses.OPEN } }, { $count: 'count' }],
          closedCount: [{ $match: { status: schoolsEnums.scholarshipStatuses.CLOSED } }, { $count: 'count' }],
          // Only include bookmarkedCount if user exists
          ...(user
            ? {
                bookmarkedCount: [{ $match: { _id: { $in: user.scholarships } } }, { $count: 'count' }],
              }
            : {}),
        },
      },
    ]),
    Scholarship.countDocuments(),
  ];

  // Only fetch saved scholarships if user exists
  if (user) {
    operations.push(
      Scholarship.find({ _id: { $in: user.scholarships } })
        .select('_id')
        .lean(),
    );
  }

  const [scholarships, counts, allScholarships, savedScholarshipIds] = await Promise.all(operations);

  // Extract counts
  const [countResult] = counts;
  const openCount = countResult.openCount[0]?.count || 0;
  const closedCount = countResult.closedCount[0]?.count || 0;
  const bookmarkedCount = user ? countResult.bookmarkedCount?.[0]?.count || 0 : 0;

  // Handle saved scholarships
  const savedScholarships = user ? savedScholarshipIds.map((s) => s._id.toString()) : [];

  return {
    ...scholarships,
    open: openCount,
    closed: closedCount,
    savedScholarships,
    savedCount: bookmarkedCount,
    allScholarships,
  };
};

const getScholarship = async (scholarshipId) => {
  // check if the "scholarshipId" is a valid ObjectId or a string for uniqueTitle
  const isObjectId = validateId(scholarshipId, 'Scholarship', false);

  let searchFilter = {};
  if (isObjectId) searchFilter = { _id: mongoose.Types.ObjectId(scholarshipId) };
  else searchFilter = { uniqueName: scholarshipId };

  // modify such that a valid scholarship is returned .i.e. not expired
  const scholarship = await Scholarship.findOne(searchFilter)
    .populate([{ path: 'logo', select: 'url _id' }])
    .lean();
  if (!scholarship) {
    logger.error('Scholarship is invalid');
    throw new ApiError(httpStatus.NOT_FOUND, 'Scholarship is invalid');
  }

  return scholarship;
};

const getScholarshipByUniqueName = async (uniqueName) => {
  const scholarship = await Scholarship.findOne({ uniqueName })
    .populate([{ path: 'logo', select: 'url _id' }])
    .lean();
  if (!scholarship) {
    logger.error('Scholarship is invalid');
    throw new ApiError(httpStatus.NOT_FOUND, 'Scholarship is invalid');
  }
  return scholarship;
};

const addScholarship = async (data, file) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  if (data.name) {
    const existingScholarship = await Scholarship.findOne({ name: data.name });
    if (existingScholarship) {
      throw new ApiError(httpStatus.CONFLICT, 'Scholarship name already exists');
    }
  }

  const newData = { ...data };

  newData.uniqueName = newData.name
    .replaceAll(/[^a-zA-Z0-9\s.]+/g, '')
    .replaceAll(' ', '-')
    .toLowerCase()
    .replace(/^-+|-+$/g, '');
  const uniqueNameExists = await Scholarship.findOne({ uniqueName: newData.uniqueName });
  if (uniqueNameExists) {
    const currentDateTime = new Date().toISOString().replace(/[-:.]/g, '');
    newData.uniqueName = `${newData.uniqueName}-${currentDateTime}`;
  }

  if (data.deadlineVaries) {
    newData.deadlineVaries = data.deadlineVaries === 'true';
  }

  if (data.deadline) {
    const deadline = new Date(data.deadline);
    if (deadline < today) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Deadline cannot be in the past');
    }
    newData.deadline = deadline;
  }

  if (newData.startDate) {
    const startDate = new Date(newData.startDate);
    newData.startDate = startDate;

    if (newData.deadline && startDate > newData.deadline) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Start date cannot be greater than deadline');
    }

    if (startDate > today) {
      newData.status = schoolsEnums.scholarshipStatuses.CLOSED;
    }
  }

  if (data.eligibleCountries) {
    const countries = Array.isArray(data.eligibleCountries)
      ? data.eligibleCountries
      : data.eligibleCountries.split(',').map((country) => country.trim());
    newData.eligibleCountries = countries;
  }

  ['programTypes', 'programs'].forEach((field) => {
    if (data[field]) {
      newData[field] = Array.isArray(data[field]) ? data[field] : [data[field]];
    }
  });

  // if (data.programTypes) {
  //   newData.programTypes = Array.isArray(data.programTypes) ? data.programTypes : [data.programTypes]; // .split(' ').map((programType) => programType.trim());
  // }

  if (file) {
    newData.logo = await processFileUpload(file, azureContainers.scholarships);
  }

  const scholarship = await Scholarship.create(newData);
  return scholarship;
};

const updateScholarship = async (scholarshipId, data, file) => {
  validateId(scholarshipId, 'Scholarship');
  const scholarship = await Scholarship.findById(scholarshipId);
  if (!scholarship) {
    logger.error('Scholarship is invalid');
    throw new ApiError(httpStatus.NOT_FOUND, 'Scholarship is invalid');
  }

  const updateData = { ...data };
  if (data.name) {
    const existingScholarship = await Scholarship.findOne({ name: data.name, _id: { $ne: scholarshipId } });
    if (existingScholarship) {
      throw new ApiError(httpStatus.CONFLICT, 'Scholarship name already exists');
    }

    updateData.uniqueName = data.name
      .replaceAll(/[^a-zA-Z0-9\s.]+/g, '')
      .replaceAll(' ', '-')
      .toLowerCase()
      .replace(/^-+|-+$/g, '');
    const uniqueNameExists = await Scholarship.findOne({ uniqueName: updateData.uniqueName });
    if (uniqueNameExists) {
      const currentDateTime = new Date().toISOString().replace(/[-:.]/g, '');
      updateData.uniqueName = `${updateData.uniqueName}-${currentDateTime}`;
    }
  }

  if (data.deadlineVaries) {
    updateData.deadlineVaries = data.deadlineVaries === 'true';
  }

  const today = new Date();
  today.setHours(0, 0, 0, 0);
  if (data.deadline) {
    const newDeadline = new Date(data.deadline);
    if (newDeadline < today) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Deadline cannot be in the past');
    }

    if (scholarship.status === schoolsEnums.scholarshipStatuses.CLOSED) {
      updateData.status = schoolsEnums.scholarshipStatuses.OPEN;
    }
  }

  if (updateData.startDate) {
    const startDate = new Date(updateData.startDate);
    updateData.startDate = startDate;

    if (updateData.deadline && startDate > updateData.deadline) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Start date cannot be greater than deadline');
    }

    if (startDate > today) {
      updateData.status = schoolsEnums.scholarshipStatuses.CLOSED;
    }
  }

  if (file) {
    await deleteManyFiles([scholarship.logo]);
    updateData.logo = await processFileUpload(file, azureContainers.scholarships);
  }

  const updateOperation = { $set: updateData };
  if (data.eligibleCountries) {
    const countries = Array.isArray(data.eligibleCountries)
      ? data.eligibleCountries
      : data.eligibleCountries.split(',').map((country) => country.trim());

    updateOperation.eligibleCountries = countries;
    delete updateData.replaceCountries;
    delete updateData.eligibleCountries;
  }

  ['programTypes', 'programs'].forEach((field) => {
    if (data[field]) {
      updateOperation[field] = Array.isArray(data[field]) ? data[field] : [data[field]];
    }
  });

  // if (data.programTypes) {
  //   updateOperation.programTypes = Array.isArray(data.programTypes) ? data.programTypes : [data.programTypes]; // .split(' ').map((programType) => programType.trim());
  // }

  await Scholarship.findByIdAndUpdate(scholarshipId, updateOperation);
};

const deleteScholarship = async (scholarshipId) => {
  validateId(scholarshipId, 'Scholarship');
  const scholarship = await Scholarship.findById(scholarshipId);
  if (!scholarship) {
    logger.error('Scholarship is invalid');
    throw new ApiError(httpStatus.NOT_FOUND, 'Scholarship is invalid');
  }

  await Scholarship.findByIdAndDelete(scholarshipId);
};

const bookmarkScholarship = async (scholarshipId, user) => {
  validateId(scholarshipId, 'Scholarship');

  const scholarship = await Scholarship.findById(scholarshipId);
  if (!scholarship) {
    logger.error('Scholarship is invalid');
    throw new ApiError(httpStatus.NOT_FOUND, 'Scholarship is invalid');
  }
  // remove from bookmarks if already bookmarked
  const isBookmarked = user.scholarships.includes(scholarshipId);

  let update;
  if (isBookmarked) {
    update = { $pull: { scholarships: scholarshipId } };
  } else {
    update = { $addToSet: { scholarships: scholarshipId } };
  }

  await User.findByIdAndUpdate(user._id, update);

  return !isBookmarked;
};

module.exports = {
  getScholarships,
  getScholarship,
  addScholarship,
  updateScholarship,
  deleteScholarship,
  bookmarkScholarship,
  getBookmarkedScholarships,
  getScholarshipByUniqueName,
};
