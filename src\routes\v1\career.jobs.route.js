const express = require('express');

const router = express.Router();
const validate = require('../../middlewares/validate');
const { auth } = require('../../middlewares/auth');
const { careerJobValidation } = require('../../validations');
const { careerJobsController } = require('../../controllers');
const { uploadJobApplicationFiles } = require('../../config/multer.config');

// Public Routes
router.get('/jobs', validate(careerJobValidation.getCareerJobsExt), careerJobsController.getCareerJobs);
router.get('/analytics', careerJobsController.getJobAnalytics);
router.get('/enums', careerJobsController.getCareerJobEnums);
router.post(
  '/apply',
  uploadJobApplicationFiles,
  validate(careerJobValidation.createJobApplication),
  careerJobsController.createJobApplication,
);
router.get(
  '/applications',
  auth('manageCareers'),
  validate(careerJobValidation.getJobApplications),
  careerJobsController.getJobApplications,
);
router.get('/jobs/:id', validate(careerJobValidation.getCareerJob), careerJobsController.getCareerJob);

// Protected Routes
router.use(auth('manageCareers'));
router.get('/', validate(careerJobValidation.getCareerJobs), careerJobsController.getCareerJobs);
router.get('/:id', validate(careerJobValidation.getCareerJob), careerJobsController.getCareerJob);
router.get('/applications/:id', validate(careerJobValidation.getCareerJob), careerJobsController.getJobApplication);
router.patch(
  '/applications/:id',
  validate(careerJobValidation.updateJobApplication),
  careerJobsController.updateJobApplication,
);
router.post('/', validate(careerJobValidation.addCareerJob), careerJobsController.addCareerJob);
router.patch('/:id', validate(careerJobValidation.updateCareerJob), careerJobsController.updateCareerJob);
router.delete('/:id', validate(careerJobValidation.getCareerJob), careerJobsController.deleteCareerJob);

module.exports = router;
