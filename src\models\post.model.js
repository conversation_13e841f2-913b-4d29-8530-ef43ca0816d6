const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const { postVisibilityTypes } = require('../config/constants');

const postSchema = new mongoose.Schema(
  {
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    group: { type: mongoose.Schema.Types.ObjectId, ref: 'Group' },
    text: { type: String, trim: true },
    media: [{ type: mongoose.Schema.Types.ObjectId, ref: 'File' }],
    // likes: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }], // Holds id of users that liked a post
    reactions: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Reaction' }], // Holds id of reactions to a post
    comments: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Comment' }], // Holds id of comments to a post
    allCommentsCount: { type: Number, default: 0 },
    reposts: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
    repostWithThought: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
    parentPost: { type: mongoose.Schema.Types.ObjectId, ref: 'Post' },
    bookmarks: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
    visibility: { type: String, enum: Object.values(postVisibilityTypes), default: postVisibilityTypes.PUBLIC },
    tags: [{ type: String, trim: true }],
    edited: { type: Boolean, default: false },
  },
  {
    timestamps: true,
  },
);

postSchema.index({ createdAt: 1, updatedAt: 1 });

postSchema.plugin(toJSON);
postSchema.plugin(paginate);

const Post = mongoose.model('Post', postSchema);

module.exports = Post;
