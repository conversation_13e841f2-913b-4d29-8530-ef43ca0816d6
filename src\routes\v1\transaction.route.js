const express = require('express');

const router = express.Router();
const { auth } = require('../../middlewares/auth');
const validate = require('../../middlewares/validate');
const { transactionController } = require('../../controllers');
const { transactionValidation } = require('../../validations');
const { interceptResponseBody } = require('../../middlewares/responseInterceptor');

// Stripe webhook requiring raw body to validate the event
router.post('/webhook', express.raw({ type: 'application/json' }), transactionController.webhook);

router.use(express.json());
router.use(interceptResponseBody);

// Authenticate routes for business
router.use(['/withdraw'], auth('manageBusiness'));
router.post('/withdraw', validate(transactionValidation.withdraw), transactionController.withdraw);

router.use(auth());

router.get('/', validate(transactionValidation.getTransactions), transactionController.getTransactions);
router.get('/account-balance', validate(transactionValidation.getAccountBalance), transactionController.getAccountBalance);
router.get(
  '/referral-history',
  validate(transactionValidation.getReferralHistory),
  transactionController.getReferralHistory,
);
router.post(
  '/withdraw-bonus',
  validate(transactionValidation.getAccountBalance),
  transactionController.withdrawReferralBonus,
);

router.post(
  '/create-checkout-session',
  validate(transactionValidation.createPaymentIntent),
  transactionController.createCheckoutSession,
);

router.post('/create-stripe-account', transactionController.createStripeAccount);
router.post('/stripe-account-connect/:account', transactionController.connectStripeAccount);
router.get('/stripe-connect-status/:stripeAccountId', transactionController.getStripeConnectStatus);

router.post('/account-session', transactionController.createAccountSession);

router.get('/session-status', validate(transactionValidation.getSessionStatus), transactionController.getSessionStatus);
router.get('/:id', transactionController.getTransaction);

module.exports = router;
