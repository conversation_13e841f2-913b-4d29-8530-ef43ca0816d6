module.exports = {
  delete: {
    tags: ['Posts'],
    summary: 'Delete a post by ID',
    description: 'Delete a single post based on the provided post ID',
    parameters: [
      {
        in: 'path',
        name: 'id',
        schema: {
          type: 'string',
          example: '507f1f77bcf86cd799439011',
        },
        description: 'ID of the post to delete',
        required: true,
      },
    ],
    responses: {
      200: {
        description: 'Post deleted successfully',
        content: {
          'application/json': {
            schema: {
              status: {
                type: 'string',
                example: 'SUCCESS',
              },
              message: {
                type: 'string',
                example: 'Post with ID 507f1f77bcf86cd799439011 deleted',
              },
            },
          },
        },
      },
      404: {
        description: 'Record not found',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error404',
            },
          },
        },
      },
      500: {
        description: 'Internal server error',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error500',
            },
          },
        },
      },
    },
  },
};
