const { removeStopwords } = require('stopword');

const capitalizeEachWord = (string = '') => {
  const nonStopWords = removeStopwords(string.split(' '));
  return string
    .trim()
    .split(' ')
    .filter((word) => word !== '') // Remove empty strings
    .map((word) => {
      return nonStopWords.includes(word) ? word[0].toUpperCase() + word.slice(1).toLowerCase() : word.toLowerCase();
    })
    .join(' ');
};

module.exports = capitalizeEachWord;
