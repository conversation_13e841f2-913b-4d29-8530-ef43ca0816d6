const mongoose = require('mongoose');

const { Schema, SchemaTypes } = mongoose;
const { toJSON, paginate } = require('./plugins');
const { issueReport } = require('../config/constants');

const issueReportSchema = new Schema(
  {
    reporter: { type: SchemaTypes.ObjectId, ref: 'User', required: true },
    title: { type: String, required: true },
    type: { type: String, enum: Object.values(issueReport.types) },
    description: { type: String, required: true },
    priority: { type: String, enum: Object.values(issueReport.priorities) },
    status: { type: String, enum: Object.values(issueReport.statuses), default: issueReport.statuses.OPEN },
    assignedTo: { type: SchemaTypes.ObjectId, ref: 'User' },
    resolvedAt: { type: Date },
    resolvedBy: { type: SchemaTypes.ObjectId, ref: 'User' },
    closedBy: { type: SchemaTypes.ObjectId, ref: 'User' },
    closedAt: { type: Date },
    attachment: [{ type: SchemaTypes.ObjectId, ref: 'File' }],
  },
  {
    timestamps: true,
  },
);

issueReportSchema.index({ createdAt: 1 });
issueReportSchema.index({ updatedAt: 1 });
issueReportSchema.index({ resolvedAt: 1 });

issueReportSchema.plugin(toJSON);
issueReportSchema.plugin(paginate);

const Issue = mongoose.model('IssueReport', issueReportSchema);

module.exports = Issue;
