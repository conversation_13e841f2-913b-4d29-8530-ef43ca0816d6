const Joi = require('joi');
const httpStatus = require('http-status');
const ApiError = require('../../utils/ApiError');
const { objectId } = require('../../validations/custom.validation');

const validateId = (id, property = 'Property', throwError = true) => {
  const { error } = Joi.string()
    .required()
    .custom(objectId)
    .validate(String(id) || '');
  if (error) {
    if (throwError) {
      throw new ApiError(httpStatus.NOT_FOUND, `${property} not found`);
    } else {
      return;
    }
  }
  return id;
};

module.exports = validateId;
