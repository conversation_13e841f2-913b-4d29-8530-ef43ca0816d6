const Joi = require('joi');
const { visibilityTypes, themeTypes, notificationSettingModelFields } = require('../config/constants');
const { objectId } = require('./custom.validation');

const getSettingById = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required(),
  }),
};

// const updateSetting = {
//   body: Joi.object().keys({
//     theme: Joi.string().valid(...Object.values(themeTypes)),
//     email: Joi.string().valid(...Object.values(visibilityTypes)),
//     dateOfBirth: Joi.string().valid(...Object.values(visibilityTypes)),
//     skills: Joi.string().valid(...Object.values(visibilityTypes)),
//     hobbies: Joi.string().valid(...Object.values(visibilityTypes)),
//     nationality: Joi.string().valid(...Object.values(visibilityTypes)),
//     phone: Joi.string().valid(...Object.values(visibilityTypes)),
//     countryOfResidence: Joi.string().valid(...Object.values(visibilityTypes)),
//     targetedCountries: Joi.string().valid(...Object.values(visibilityTypes)),
//     preferredFieldsOfStudy: Joi.array().items(Joi.string().valid(...Object.values(visibilityTypes))),
//     preferredSchoolsOfStudy: Joi.array().items(Joi.string().valid(...Object.values(visibilityTypes))),
//     preferredCountryOfStudy: Joi.string().valid(...Object.values(visibilityTypes)),
//     preferredDegreeOfStudy: Joi.string().valid(...Object.values(visibilityTypes)),
//     interestedInScholarship: Joi.string().valid(...Object.values(visibilityTypes)),
//     basicInformation: Joi.string().valid(...Object.values(visibilityTypes)),
//     education: Joi.string().valid(...Object.values(visibilityTypes)),
//     certification: Joi.string().valid(...Object.values(visibilityTypes)),
//     testscore: Joi.string().valid(...Object.values(visibilityTypes)),
//     experience: Joi.string().valid(...Object.values(visibilityTypes)),
//     volunteer: Joi.string().valid(...Object.values(visibilityTypes)),
//     project: Joi.string().valid(...Object.values(visibilityTypes)),
//     award: Joi.string().valid(...Object.values(visibilityTypes)),

//     // notifications
//     follow: Joi.boolean(),
//     unfollow: Joi.boolean(),
//     joinGroupRequest: Joi.boolean(),
//     acceptGroupRequest: Joi.boolean(),
//     rejectGroupRequest: Joi.boolean(),
//     like: Joi.boolean(),
//     comment: Joi.boolean(),
//     repost: Joi.boolean(),
//     commentOfComment: Joi.boolean(),
//     newAdmin: Joi.boolean(),
//     newRandomAdmin: Joi.boolean(),
//     newCoAdmin: Joi.boolean(),
//     message: Joi.boolean(),
//     groupMessage: Joi.boolean(),
//   }),
// };

const stringFields = [
  'theme',
  'email',
  'dateOfBirth',
  'skills',
  'hobbies',
  'nationality',
  'phone',
  'countryOfResidence',
  'targetedCountries',
  'preferredCountryOfStudy',
  'preferredDegreeOfStudy',
  'interestedInScholarship',
  'basicInformation',
  'education',
  'certification',
  'testscore',
  'experience',
  'volunteer',
  'project',
  'award',
  'preferredFieldsOfStudy',
  'preferredSchoolsOfStudy',
];

const updateSettingSchema = stringFields.reduce((acc, key) => {
  acc[key] =
    key === 'theme'
      ? Joi.string().valid(...Object.values(themeTypes))
      : Joi.string().valid(...Object.values(visibilityTypes));
  return acc;
}, {});

Object.values(notificationSettingModelFields).forEach((key) => {
  updateSettingSchema[key] = Joi.boolean();
});

const updateSetting = {
  body: Joi.object().keys({
    theme: Joi.string().valid(...Object.values(themeTypes)),
    email: Joi.string().valid(...Object.values(visibilityTypes)),
    dateOfBirth: Joi.string().valid(...Object.values(visibilityTypes)),
    skills: Joi.string().valid(...Object.values(visibilityTypes)),
    hobbies: Joi.string().valid(...Object.values(visibilityTypes)),
    nationality: Joi.string().valid(...Object.values(visibilityTypes)),
    phone: Joi.string().valid(...Object.values(visibilityTypes)),
    countryOfResidence: Joi.string().valid(...Object.values(visibilityTypes)),
    targetedCountries: Joi.string().valid(...Object.values(visibilityTypes)),
    preferredFieldsOfStudy: Joi.array().items(Joi.string().valid(...Object.values(visibilityTypes))),
    preferredSchoolsOfStudy: Joi.array().items(Joi.string().valid(...Object.values(visibilityTypes))),
    preferredCountryOfStudy: Joi.string().valid(...Object.values(visibilityTypes)),
    preferredDegreeOfStudy: Joi.string().valid(...Object.values(visibilityTypes)),
    interestedInScholarship: Joi.string().valid(...Object.values(visibilityTypes)),
    basicInformation: Joi.string().valid(...Object.values(visibilityTypes)),
    education: Joi.string().valid(...Object.values(visibilityTypes)),
    certification: Joi.string().valid(...Object.values(visibilityTypes)),
    testscore: Joi.string().valid(...Object.values(visibilityTypes)),
    experience: Joi.string().valid(...Object.values(visibilityTypes)),
    volunteering: Joi.string().valid(...Object.values(visibilityTypes)),
    project: Joi.string().valid(...Object.values(visibilityTypes)),
    award: Joi.string().valid(...Object.values(visibilityTypes)),

    // notifications settings
    receiveScholarshipNewsletter: Joi.boolean(),
  }),
};

module.exports = {
  updateSetting,
  getSettingById,
};
