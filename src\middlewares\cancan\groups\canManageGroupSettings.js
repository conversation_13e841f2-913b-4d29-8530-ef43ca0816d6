/* eslint-disable prettier/prettier */
const httpStatus = require('http-status');
const { Group } = require('../../../models');
const ApiError = require('../../../utils/ApiError');

const canManageGroupSettings = () => async (req, res, next) => {
  let group;
  try {
    group = await Group.findById(req.params.id);
  } catch (err) {
    return next(new ApiError(httpStatus.NOT_FOUND, `Group not found`));
  }

  if (!group) {
    return next(new ApiError(httpStatus.NOT_FOUND, `Group not found`));
  }
  if (
    !(
      String(group.admin) === String(req.user._id) || // Admin
      group.coAdmins.includes(req.user._id) || // coAdmin
      group.members.includes(req.user._id) // Member
    )
  ) {
    return next(new ApiError(httpStatus.FORBIDDEN, 'Forbidden: You are not allowed to perform this operation'));
  }
  // Add the record found to the request object. It can be used in subsequent middlewares without getting by ID to reduce database queries
  req.resourceRecord = group;
  next();
};

module.exports = canManageGroupSettings;
