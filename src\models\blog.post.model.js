const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const { blogStatuses } = require('../config/constants');

const blogPostSchema = new mongoose.Schema(
  {
    author: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    category: { type: String, trim: true },
    coverImage: { type: mongoose.Schema.Types.ObjectId, ref: 'File' },
    deletedAt: { type: Date },
    downvotes: { type: Number, default: 0 },
    estimatedReadingMinutes: { type: Number, required: true },
    // lastEdited: { type: Date, default: new Date() },
    media: [{ type: mongoose.Schema.Types.ObjectId, ref: 'File' }],
    publishedAt: { type: Date },
    scheduledAt: { type: Date },
    status: { enum: Object.values(blogStatuses), type: String, default: blogStatuses.DRAFT },
    statusOnDelete: { enum: [...Object.values(blogStatuses), null], type: String },
    subtitle: { type: String, trim: true },
    tags: [{ type: String, trim: true }],
    text: { type: String, trim: true },
    title: { type: String, trim: true },
    uniqueTitle: { type: String, unique: true },
    upvotes: { type: Number, default: 0 },
  },
  {
    timestamps: true,
  },
);

blogPostSchema.index({ createdAt: 1, updatedAt: 1, publishedAt: 1 });

blogPostSchema.plugin(toJSON);
blogPostSchema.plugin(paginate);

const BlogPost = mongoose.model('Blog', blogPostSchema);

module.exports = BlogPost;
