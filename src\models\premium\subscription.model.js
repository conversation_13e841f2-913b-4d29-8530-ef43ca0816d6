const mongoose = require('mongoose');

const { toJSON, paginate } = require('../plugins');
const { businessEnums } = require('../../config/constants');

const { ObjectId } = mongoose.SchemaTypes;
/**
 * Subscription is monthly or yearly
 * There'll be a new subscription entry for every month or year of subscription
 */
const premiumSubscriptionSchema = new mongoose.Schema(
  {
    subscriber: { type: ObjectId, ref: 'PremiumSubscriber' },
    price: { type: Number, required: true }, // Original price without discount
    discountPercent: { type: Number, default: 0 },
    period: { type: String, enum: Object.values(businessEnums.premiumSubscriptionPeriods).map((p) => p[0]), required: true },
    periodCount: { type: Number, default: 1 },

    startDate: { type: Date, required: true },
    active: { type: Boolean, default: true }, // Add a job that sets this active property as false at the time of expiry. It's also set to false when there's a renewal
    expiresAt: { type: Date, required: true },

    autoRenew: { type: Boolean, default: true },
    autoRenewCancelledAt: { type: Date },

    /**
     * A subscription will only be counted as renewal when the subscriber has an active subscription at the time of subcribing
     */
    renewedAt: { type: Date },
    renewingChild: { type: ObjectId, ref: 'PremiumSubscription' }, // The subscription that renewed a subscription
    renewingParent: { type: ObjectId, ref: 'PremiumSubscription' }, // The subscription that was renewed

    transaction: { type: ObjectId, ref: 'Transaction', required: true },
  },
  {
    timestamps: true,
  },
);

premiumSubscriptionSchema.index({ createdAt: 1, updatedAt: 1, expiresAt: 1, startDate: 1 });

premiumSubscriptionSchema.plugin(toJSON);
premiumSubscriptionSchema.plugin(paginate);

const PremiumSubscription = mongoose.model('PremiumSubscription', premiumSubscriptionSchema);

module.exports = PremiumSubscription;
