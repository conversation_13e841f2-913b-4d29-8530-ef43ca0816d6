/* eslint-disable security/detect-non-literal-fs-filename */
/* eslint-disable global-require */
const Async = require('async');
const httpStatus = require('http-status');
const { v1: uuidv1 } = require('uuid');
const { BlockBlobClient } = require('@azure/storage-blob');
const ApiError = require('../utils/ApiError');
const { File } = require('../models');
const logger = require('../config/logger');

const saveFile = async (url, filename, containerName, thumbnail, metadata) => {
  const file = await File.create({ filename, url, containerName, thumbnail, metadata });
  if (file) return file;
  throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Unable to save file to database');
};

const getBlobName = (originalName) => {
  const identifier = uuidv1();
  return `${identifier}-${originalName.replace(/[\s]/g, '-').replace(/[^a-zA-Z0-9-.]/g, '')}`; // remove spaces and special characters
};

const uploadAzureBlob = async (fileToUpload, containerName) => {
  // fileToUpload is req.file
  const { default: getStream } = await import('into-stream');
  try {
    let stream;
    let streamLength;
    let blobName;
    // const today = Date.now().toString();

    if (typeof fileToUpload === 'string') {
      // compressed file
      const fs = require('fs');
      stream = fs.createReadStream(fileToUpload);
      const stats = fs.statSync(fileToUpload);

      streamLength = stats.size;
      // if (fileToUpload.endsWith('thumbnail.png')) {
      //   blobName = getBlobName(`thumbnail-${today}.png`);
      // }
      // else if (fileToUpload.endsWith('.ts')) blobName = getBlobName(fileToUpload.split('/').pop());
      blobName = fileToUpload.split(/[\\/]/).pop();
    } else {
      stream = getStream(fileToUpload.buffer);
      streamLength = fileToUpload.length;
      blobName = getBlobName(fileToUpload.originalname);
    }

    const blobService = new BlockBlobClient(process.env.AZURE_STORAGE_CONNECTION_STRING, containerName, blobName);
    await blobService.uploadStream(stream, streamLength);
    return { filename: blobName, url: blobService.url };
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, error);
  }
};

const addSubContainerFromFilePath = (containerName, filePath) =>
  `${containerName}/${filePath.split(/[\\/]/).slice(-2, -1)[0]}`.slice(0, 64);

const uploadAndSaveMultipleBlobs = async (files, containerNameParam) => {
  // files can be -> [{ file }, [{file, dimensions, thumbnail}], [filePath1, filePath2]]

  try {
    const filePromises = files.map(async (fileParam) => {
      let containerName = containerNameParam;
      let thumbnailFile;
      let file;

      if (typeof fileParam === 'object' && fileParam.file) {
        // .m3u8 file
        // other files or unprocessed files

        if (fileParam.isVideoFile) {
          containerName = addSubContainerFromFilePath(containerName, fileParam.file); // Add the containing folder name to the container name so that files are grouped together
        }

        const thumbnailBlob = fileParam.thumbnail ? await uploadAzureBlob(fileParam.thumbnail, containerName) : undefined;
        const videoBlob = await uploadAzureBlob(fileParam.file, containerName);

        thumbnailFile = thumbnailBlob ? await saveFile(thumbnailBlob.url, thumbnailBlob.filename, containerName) : undefined;
        file = await saveFile(videoBlob.url, videoBlob.filename, containerName, thumbnailFile?.id, fileParam.metadata);
      } else if (typeof fileParam === 'string' && fileParam.endsWith('.ts')) {
        // processed .ts files
        containerName = addSubContainerFromFilePath(containerName, fileParam);
        const fileBlob = await uploadAzureBlob(fileParam, containerName);
        return fileBlob;
      }
      //   // first if condition already handles unprocessed files because of the map nonVideoFiles.map((file) => ({ file }))
      // else {
      //   // normal file or unprocessed file
      //   const { filename, url } = await uploadAzureBlob(fileParam, containerName);
      //   file = await saveFile(url, filename, containerName);
      // }
      if (file) return file._id;
    });

    const fileIds = await Promise.all(filePromises);
    return fileIds;
  } catch (error) {
    logger.error(error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, `Error saving file`);
  }
};

const deleteManyFiles = async (ids) => {
  await Async.eachSeries(ids, async (_id) => {
    const file = await File.findById(_id);
    if (file) await file.remove();
  });
};

const processFileUpload = async (file, containerName) => {
  const { filename, url } = await uploadAzureBlob(file, containerName);
  const savedFile = await saveFile(url, filename, containerName);
  return savedFile._id;
};

module.exports = {
  uploadAzureBlob,
  saveFile,
  uploadAndSaveMultipleBlobs,
  deleteManyFiles,
  processFileUpload,
};
