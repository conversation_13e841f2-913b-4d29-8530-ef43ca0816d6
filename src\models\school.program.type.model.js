const mongoose = require('mongoose');
const { toJSON, paginate, privacy } = require('./plugins');

const schoolProgramTypeSchema = new mongoose.Schema(
  {
    name: { type: String, required: true, trim: true, unique: true },
    description: { type: String, trim: true },
    abbreviation: { type: String, trim: true },
  },
  {
    timestamps: true,
  },
);

schoolProgramTypeSchema.index({ name: 1 });

schoolProgramTypeSchema.plugin(toJSON);
schoolProgramTypeSchema.plugin(paginate);
schoolProgramTypeSchema.plugin(privacy);

const SchoolProgramType = mongoose.model('SchoolProgramType', schoolProgramTypeSchema);

module.exports = SchoolProgramType;
