const httpStatus = require('http-status');
const { messageService } = require('../services');
const { messageValidation } = require('../validations');
const { pick } = require('../utils/pick');
const catchAsync = require('../utils/catchAsync');
const validateId = require('../services/shared/validateId');
const { validateObjectData } = require('../utils/helperMethods');
const { memberSchema } = require('../validations/message.validation');
const { verifyRequester } = require('../services/message.service');

const createMessage = async (socket, data) => {
  const { value, error } = messageValidation.createMessage.validate(data);
  if (error) {
    socket.emit('error', error.details[0].message);
  }
  await messageService.createMessage(socket.user, value);
};

const createConversation = catchAsync(async (req, res) => {
  const conversation = await messageService.createConversation(req.body, req.user, req.query.createdFor);

  res
    .status(httpStatus.CREATED)
    .json({ status: 'SUCCESS', data: conversation, message: 'Conversation created successfully' });
});

const getConversations = catchAsync(async (req, res) => {
  req.query.member = validateObjectData(req.query.member, memberSchema.required(), 'Invalid member data');
  const query = pick(req.query, ['member', 'createdFor']);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);

  const conversations = await messageService.getConversations({ query, options }, false, false);

  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: conversations });
});

const getConversation = catchAsync(async (req, res) => {
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  validateId(req.params.conversationId);
  const member = validateObjectData(req.query.member, memberSchema.required(), 'Invalid member data');
  verifyRequester(member, req.user);
  const conversation = await messageService.getConversation(req.params.conversationId, member, options);

  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: conversation });
});

const updateMessage = catchAsync(async (req, res) => {
  const message = await messageService.updateMessageById(req.params.id, req.body);

  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: message });
});

const countUnreadConversations = catchAsync(async (req, res) => {
  const count = await messageService.countUnreadConversations(req.user._id);

  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: count });
});

module.exports = {
  createMessage,
  createConversation,
  getConversations,
  getConversation,
  updateMessage,
  countUnreadConversations,
};
