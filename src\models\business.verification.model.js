const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const { businessEnums } = require('../config/constants');

const { ObjectId } = mongoose.SchemaTypes;

const businessVerificationSchema = new mongoose.Schema(
  {
    parentVerification: { type: ObjectId, ref: 'BusinessVerification' },
    business: { type: ObjectId, ref: 'Business', required: true },
    service: { type: ObjectId, ref: 'Service' },
    assignee: { type: ObjectId, ref: 'User', required: true },
    assigner: { type: ObjectId, ref: 'User', required: true },
    comment: { type: String },
    verificationStatus: {
      type: String,
      trim: true,
      enum: Object.values(businessEnums.businessVerificationStatuses),
      // default: businessEnums.businessVerificationStatuses.IN_PROGRESS,
    },
    assignedAt: { type: Date },
    reviewedAt: { type: Date },
    verifiedAt: { type: Date },
  },
  {
    timestamps: true,
  },
);

businessVerificationSchema.index({ createdAt: 1, updatedAt: 1 });

businessVerificationSchema.plugin(toJSON);
businessVerificationSchema.plugin(paginate);

const BusinessVerification = mongoose.model('BusinessVerification', businessVerificationSchema);

module.exports = BusinessVerification;
