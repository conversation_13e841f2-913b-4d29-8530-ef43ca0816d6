module.exports = {
  get: {
    tags: ['Activity Logs'],
    summary: 'Get user activity logs',
    description: 'Retrieve user activity logs based on specified filters',
    parameters: [
      {
        in: 'query',
        name: 'userId',
        schema: {
          type: 'string',
          description: 'User ID to filter activity logs',
          example: '610ad0e66f0f4621387ad6f3',
        },
      },
      {
        in: 'query',
        name: 'sortBy',
        schema: {
          type: 'string',
          enum: ['timestamp:asc', 'timestamp:desc'],
          description: 'Sort order for logs',
        },
      },
      {
        in: 'query',
        name: 'limit',
        schema: {
          type: 'integer',
          description: 'Maximum number of logs to return per page',
          example: 10,
        },
      },
      {
        in: 'query',
        name: 'page',
        schema: {
          type: 'integer',
          description: 'Page number for paginated results',
          example: 3,
        },
      },
    ],
    responses: {
      200: {
        description: 'Successful response',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                data: {
                  type: 'object',
                  properties: {
                    results: {
                      type: 'array',
                      items: {
                        $ref: '#/components/schemas/ActivityLogs',
                      },
                    },
                    page: { type: 'integer' },
                    limit: { type: 'integer' },
                    totalPages: { type: 'integer' },
                    totalResults: { type: 'integer' },
                  },
                },
                message: { type: 'string' },
                status: { type: 'string' },
              },
              example: {
                data: {
                  results: [
                    {
                      user: '651bb5bf4eb9240327ea9d56',
                      text: 'You liked @P652d4d71e8fb3c03691f91d8',
                      timestamp: '2023-12-08T11:12:48.225Z',
                      id: '6572fa307d2c0ec6b3834cda',
                    },
                  ],
                  page: 1,
                  limit: 50,
                  totalPages: 1,
                  totalResults: 24,
                },
                message: 'Logs retrieved successfully',
                status: 'SUCCESS',
              },
            },
          },
        },
      },
    },
  },
};

// data: {
//                   type: 'array',
//                   items: {
//                     $ref: '#/components/schemas/ActivityLogs',
//                   },
//                 },
