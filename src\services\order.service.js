const httpStatus = require('http-status');
const Async = require('async');
const moment = require('moment');
const logger = require('../config/logger');
const ApiError = require('../utils/ApiError');
const { azureContainers, businessEnums } = require('../config/constants');
const fileService = require('./file.service');
const notificationService = require('./notification.service');
const { capitalizeFirstChar } = require('../utils/stringFormatting');
const {
  Business,
  Order,
  OrderActivity,
  Service,
  Transaction,
  Referral,
  ServiceOffer,
  ServiceRequirementSpecify,
  ServiceRequirementProvide,
  OrderDispute,
  User,
} = require('../models');
const validateId = require('./shared/validateId');
// const transactionService = require('./transaction.service');
const { stripe } = require('../config/config');
const { defaultServiceRequirements } = require('../models/migrations/seed');
const { validateObjectData } = require('../utils/helperMethods');
const { completeOrderBody } = require('../validations/order.validation');
const { servicePopulate } = require('./service.service');
const Account = require('../models/account.model');
const { notificationTypes } = require('./shared/notification.handler');
const { createConversation } = require('./message.service');
const { scheduleMarkOrderAsCompleted } = require('../utils/jobs/jobSchedulers');
const { basicUserPopulate } = require('./user.service');

const { requiredOffers } = businessEnums;
// const { notificationTypes } = require('./shared/notification.handler');
// const notificationService = require('./notification.service');

// const orderPopulatePaths = [{ path: 'supportingDocuments' }];
const orderPopulatePaths = (requestingUser) => [
  { path: 'client', ...basicUserPopulate },
  {
    path: 'service',
    select: 'name provider media bookmarks category offers',
    populate: servicePopulate(requestingUser),
  },
  { path: 'transaction', select: 'amount currency' },
  { path: 'requirements', select: '-createdAt -updatedAt -order', populate: { path: 'files', select: 'url' } },
];

// const initializeOrder = async (data) => {
//   const orderData = { ...data };
//   validateId(orderData.service, 'Service');
//   const service = await Service.findById(orderData.service).populate('provider').lean();

//   if (!service) {
//     logger.error('Invalid service');
//     throw new ApiError(httpStatus.NOT_FOUND, 'Invalid service');
//   }
//   if (service.provider.contactPerson.toString() === orderData.client._id.toString()) {
//     logger.error('Owner cannot place an order');
//     throw new ApiError(httpStatus.BAD_REQUEST, 'Owner cannot place an order');
//   }

//   const { clientSecret, paymentIntentId } = await transactionService.createPaymentIntent(orderData.client, {
//     serviceId: orderData.service,
//     plan: orderData.plan,
//   });
//   // Order will be created on payment success in webhook

//   return { clientSecret, paymentIntentId };
// };

const getRequiredOffers = async (serviceId, plan) => {
  const offers = (
    await ServiceOffer.find({
      service: serviceId,
      $or: [{ name: [requiredOffers.COMPLETION_DAYS[0]] }, { name: requiredOffers.MAXIMUM_REVISIONS[0] }],
    }).select({
      value: { $elemMatch: { plan } },
      name: 1,
    })
  ).reduce((acc, offer) => ({ ...acc, [offer.name]: offer.value[0].offer }), {});

  if (Object.keys(offers).length !== 2) {
    logger.error('Encountered service without completion days offer and/or maximum revisions');
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'An error occurred while processing your request');
  }
  return offers;
};

const createOrder = async (checkoutSession, transaction) => {
  const existingOrder = await Order.findOne({ transaction: transaction._id });

  const service = await Service.findById(checkoutSession.metadata.serviceId);

  // Utilize mongoose transaction
  const session = await Order.startSession();
  session.startTransaction();
  try {
    if (!existingOrder) {
      const offers = await getRequiredOffers(service._id, checkoutSession.metadata.plan);
      // Create an order
      const order = await Order.create({
        client: checkoutSession.metadata.userId,
        provider: service.provider,
        service: service._id,
        plan: checkoutSession.metadata.plan,
        transaction: transaction._id,
        status: businessEnums.orderStatuses.AWAITING_REQUIREMENTS,
        revisionsMaxCount: Number(offers[requiredOffers.MAXIMUM_REVISIONS[0]]),
      });

      await Service.findByIdAndUpdate(service._id, {
        $addToSet: { orders: order._id },
      }).populate('provider');
      await Business.findByIdAndUpdate(service.provider, { $addToSet: { orders: order._id } });

      // log activity
      await OrderActivity.create({
        order: order._id,
        client: order.client,
        provider: service.provider,
        actor: businessEnums.orderActivityActors.CLIENT,
        action: businessEnums.orderActivityActions.ORDER_PLACED,
      });

      await Account.updateOne(
        { business: service.provider },
        {
          $inc: { totalBalance: transaction.amount },
        },
      );
      await Account.updateOne(
        { user: transaction.account.user },
        {
          $inc: { allTimeDeposit: transaction.amount, allTimeWithdrawal: transaction.amount },
        },
      );

      // notify client of successful order placement (email(invoice) and app)
      // amount: use parseInt() for transaction.amount + transaction.transactionFee
      // service fee: transaction.transactionFee
      // type: DEBIT for client, CREDIT for provider
      const details = {
        receiverType: 'client',
        orderId: order._id,
        transactionAmount: parseInt(transaction.amount, 10) + transaction.transactionFee,
        serviceFee: transaction.transactionFee,
        transactionType: transaction.type,
        transactionStatus: transaction.status,
        destination: capitalizeFirstChar(transaction.paymentProvider),
        serviceName: service.name,
      };

      const client = await User.findById(order.client);
      await notificationService.createNewOrderNotification(client, client, notificationTypes.NEW_ORDER, {
        ...details,
        receiverType: 'client',
      });

      // notify provider of new order
      const provider = await Business.findById(service.provider).populate('contactPerson');
      await notificationService.createNewOrderNotification(client, provider, notificationTypes.NEW_ORDER, {
        ...details,
        receiverType: 'provider',
      });

      // update referral record
      if (checkoutSession.metadata.referralId) {
        await Referral.findByIdAndUpdate(checkoutSession.metadata.referralId, { hasBoughtService: true });
        order.referral = checkoutSession.metadata.referralId;
        await order.save();
      }
      const conversation = await createConversation(
        {
          members: [{ user: order.client.toString() }, { business: order.provider.toString() }],
          directMessage: true,
        },
        client,
        'order',
      );
      order.conversation = conversation._id;
      await order.save();

      return order._id;
    }
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    session.endSession();
  }
};

const completeCreateOrder = async (req) => {
  const { user, params } = req;
  let data = {};

  try {
    const pureStringKeys = ['acceptDisclaimer'];
    Object.keys(req.body).forEach((key) => {
      data[key] = pureStringKeys.includes(key) ? req.body[key] : JSON.parse(req.body[key]);
    });
  } catch (error) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid formating');
  }

  data = validateObjectData(data, completeOrderBody);

  let { files } = req;
  const { sessionId } = params;

  if (data.acceptDisclaimer !== 'true') {
    throw new ApiError(httpStatus.BAD_REQUEST, 'You need to accept the disclaimer to continue.');
  }

  const transaction = await Transaction.findOne({ checkoutSessionId: sessionId });

  const orderQuery = {};
  if (transaction) {
    orderQuery.transaction = transaction._id;
  } else {
    validateId(sessionId, 'Order');
    orderQuery._id = sessionId;
  }
  const order = await Order.findOne(orderQuery).populate('service');
  if (!order) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Order does not exist');
  }

  if (order.status !== businessEnums.orderStatuses.AWAITING_REQUIREMENTS) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Order is not awaiting requirements');
  }

  if (order.client.toString() !== user._id.toString()) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You cannot perform this operation.');
  }

  const expectedFilesCount = (data.fileRequirements || []).reduce((acc, f) => {
    validateId(f.id, 'Requirement for Service');
    return acc + f.count;
  }, 0);
  if (expectedFilesCount !== files?.length || 0) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'File requirements do not match files uploaded');
  }

  const defaultServiceRequirementIds = defaultServiceRequirements.map((r) => r.id);
  const providedDefaultRequirements = [];
  const providedProviderRequirements = [];

  const providedRequirementsData = (data.requirements || []).reduce((acc, requirement) => {
    if (defaultServiceRequirementIds.includes(Number(requirement.requirement))) {
      providedDefaultRequirements.push(Number(requirement.requirement));
    } else {
      validateId(requirement.requirement, 'Requirement for Service');
      providedProviderRequirements.push(requirement.requirement);
    }
    return { ...acc, [requirement.requirement]: requirement };
  }, {});

  // Upload files to Azure Blob Storage
  if (files.length) files = await fileService.processFileUploads(files, azureContainers.businessOrders);

  (data.fileRequirements || []).forEach((fileRequirement) => {
    providedRequirementsData[fileRequirement.id] = {
      requirement: fileRequirement.id,
      files: files.splice(0, fileRequirement.count),
    };
    providedProviderRequirements.push(fileRequirement.id);
  });

  let specifiedRequirements = await ServiceRequirementSpecify.find({
    plansIncluded: { $in: [order.plan] },
    service: order.service._id,
  });
  specifiedRequirements = [...defaultServiceRequirements, ...specifiedRequirements];

  const existingProvidedRequirements = await ServiceRequirementSpecify.find({
    _id: { $in: providedProviderRequirements },
    service: order.service._id,
    plansIncluded: { $in: [order.plan] },
  });

  if (existingProvidedRequirements.length !== providedProviderRequirements.length) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Some provided requirements are invalid');
  }

  const dataToSave = [];
  try {
    // Validate each provided requirement
    specifiedRequirements.forEach((requirementSpecified) => {
      const requirementId = requirementSpecified._id ? requirementSpecified._id.toString() : requirementSpecified.id;

      if (providedRequirementsData[requirementId]) {
        const providedData = { ...providedRequirementsData[requirementId], order: order._id };
        if (!requirementSpecified._id) {
          // Default requirement
          providedData.defaultRequirementId = requirementId;
          delete providedData.requirement;
        }

        // Validate requirement with choice
        if (
          requirementSpecified.type === businessEnums.serviceRequirementTypes.MULTI_SELECT ||
          requirementSpecified.type === businessEnums.serviceRequirementTypes.SELECT
        ) {
          if (!providedData.choices) {
            throw new ApiError(httpStatus.BAD_REQUEST, `Choices are required for question ${requirementSpecified.question}`);
          }
          if (
            requirementSpecified.type === businessEnums.serviceRequirementTypes.SELECT &&
            providedData.choices.length !== 1
          ) {
            throw new ApiError(httpStatus.BAD_REQUEST, 'This requirement requires single selection');
          }
          if (!providedData.choices.every((choice) => requirementSpecified.options.includes(choice))) {
            throw new ApiError(httpStatus.BAD_REQUEST, 'Choices must be in options');
          }
        } else if (providedData.choices) {
          throw new ApiError(httpStatus.BAD_REQUEST, `Question "${requirementSpecified.question}" does not accept choices`);
        }

        // Validate text requirement
        if (requirementSpecified.type === businessEnums.serviceRequirementTypes.TEXT) {
          if (!providedData.text) {
            throw new ApiError(
              httpStatus.BAD_REQUEST,
              `Question "${requirementSpecified.question}" requires a text response`,
            );
          }
        }

        // Validate file requirement
        if (requirementSpecified.type === businessEnums.serviceRequirementTypes.FILE) {
          if (!providedData.files || providedData.files.length === 0) {
            throw new ApiError(httpStatus.BAD_REQUEST, `Question "${requirementSpecified.question}" requires a file upload`);
          }
        }
        dataToSave.push(providedData);
      } else if (!requirementSpecified.optional) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          `All non-optional requirements must be provided. Missing "${requirementSpecified.question}"`,
        );
      }
    });
    const offers = await getRequiredOffers(order.service._id, order.plan);
    const savedProvidedRequirements = await ServiceRequirementProvide.insertMany(dataToSave);
    await Order.updateOne(
      { _id: order._id },
      {
        $set: {
          requirements: savedProvidedRequirements.map((r) => r._id),
          status: businessEnums.orderStatuses.IN_PROGRESS,
          acceptDisclaimer: true,
          startedAt: new Date(),
          dueAt: new Date(new Date().getTime() + Number(offers[requiredOffers.COMPLETION_DAYS[0]]) * 24 * 60 * 60 * 1000),
        },
      },
    );

    if (savedProvidedRequirements?.length) {
      // log activity
      await OrderActivity.create({
        order: order._id,
        client: order.client,
        provider: order.service.provider,
        actor: businessEnums.orderActivityActors.CLIENT,
        action: businessEnums.orderActivityActions.ORDER_REQUIREMENTS_SUBMITTED,
      });
    }
    // log activity
    await OrderActivity.create({
      order: order._id,
      client: order.client,
      provider: order.service.provider,
      actor: businessEnums.orderActivityActors.CLIENT,
      action: businessEnums.orderActivityActions.ORDER_IN_PROGRESS,
    });

    // notify provider that requirements have been submitted and order started
    const provider = await Business.findById(order.service.provider).populate('contactPerson');
    const details = { receiverType: 'provider', orderId: order._id, serviceName: order.service.name };
    await notificationService.createServiceNotification(
      user,
      provider,
      notificationTypes.ORDER_REQUIREMENTS_SUBMITTED,
      details,
    );
  } catch (error) {
    if (files) {
      await fileService.deleteManyFiles(files);
    }
    throw error;
  }
};

const getOrder = async (requestingUser, orderId, provider) => {
  validateId(orderId, 'Order');
  if (provider) {
    validateId(provider, 'Provider');
    const business = await Business.findById(provider).populate('services');
    if (!provider) throw new ApiError(httpStatus.NOT_FOUND, 'Provider not found');
    if (business.contactPerson.toString() !== requestingUser._id.toString()) {
      throw new ApiError(httpStatus.FORBIDDEN, "Forbidden: You're not the provider of the order");
    }
  }
  const order = await Order.findById(orderId).populate(orderPopulatePaths(requestingUser));
  if (!order) {
    logger.error('Order is invalid');
    throw new ApiError(httpStatus.NOT_FOUND, 'Order is invalid');
  }

  if (!provider && order.client._id.toString() !== requestingUser._id.toString()) {
    throw new ApiError(httpStatus.FORBIDDEN, "Forbidden: You're not the client of this order.");
  }

  order._doc.transaction._doc.amount = parseFloat(order.transaction.amount);
  return order;
};

const getOrders = async (filterParam, options, requestingUser) => {
  const filter = filterParam;
  if (filter.provider) {
    validateId(filter.provider, 'Business');
    const business = await Business.findById(filter.provider);
    if (!business) {
      logger.error('Business is invalid');
      throw new ApiError(httpStatus.BAD_REQUEST, 'Business is invalid');
    }
    if (requestingUser?.business?.toString() !== filter.provider) {
      logger.error('You are unauthorized to perform this action');
      throw new ApiError(httpStatus.UNAUTHORIZED, 'You are unauthorized to perform this action');
    }
    delete filter.provider;
    filter.service = { $in: business.services };
  } else {
    filter.client = requestingUser._id;
  }

  if (process.env.STRIPE_LIVE_TEST === 'true') {
    filter.createdAt = { $gt: new Date('2024-11-19T16:00:00.000+00:00') };
  }

  const orders = await Order.paginate(filter, {
    ...options,
    populate: orderPopulatePaths(requestingUser),
  });

  orders.results.forEach((order, index) => {
    orders.results[index]._doc.transaction._doc.amount = parseFloat(order.transaction.amount);
  });
  return orders;
};

const completeOrder = async (user, orderId) => {
  // use match to check that user is order.client
  validateId(orderId, 'Order');
  const order = await Order.findById(orderId).populate([
    { path: 'service', select: 'provider name' },
    { path: 'transaction', select: 'amount' },
  ]);
  if (!order) {
    logger.error('Order is invalid');
    throw new ApiError(httpStatus.NOT_FOUND, 'Order is invalid');
  }

  if (user._id.toString() !== order.client.toString()) {
    throw new ApiError(httpStatus.FORBIDDEN, `Forbidden: You're not authorized to perform this operation`);
  }

  if (order.status !== businessEnums.orderStatuses.AWAITING_CLIENT_APPROVAL) {
    throw new ApiError(httpStatus.BAD_REQUEST, `Order is ${order.status.split('_').join(' ')}`);
  }

  order.status = businessEnums.orderStatuses.COMPLETED;
  if (order.referral) await Referral.findByIdAndUpdate(order.referral, { bonusIsWithdrawable: true });
  await order.save();

  // log activity
  await OrderActivity.create({
    order: order._id,
    client: order.client,
    provider: order.service.provider,
    actor: businessEnums.orderActivityActors.CLIENT,
    action: businessEnums.orderActivityActions.ORDER_COMPLETED,
  });

  await Account.updateOne(
    { business: order.service.provider },
    {
      $inc: { withdrawableBalance: parseInt(order.transaction.amount, 10) },
    },
  );

  // notify provider of completed order
  const provider = await Business.findById(order.service.provider).populate('contactPerson');
  const details = { receiverType: 'provider', orderId: order._id, serviceName: order.service.name };
  await notificationService.createServiceNotification(user, provider, notificationTypes.ORDER_COMPLETED, details);

  return order;
};

const fulfillOrder = async (user, orderId, body, files) => {
  const orderExists = await Order.findById(orderId).populate('service');
  if (!orderExists) {
    logger.error('Order does not exist');
    throw new ApiError(httpStatus.NOT_FOUND, 'Order does not exist');
  }

  if (user?.business?.toString() !== orderExists.service.provider.toString()) {
    throw new ApiError(httpStatus.FORBIDDEN, `Forbidden: You're not authorized to perform this operation`);
  }

  if (orderExists.status !== businessEnums.orderStatuses.IN_PROGRESS) {
    throw new ApiError(httpStatus.BAD_REQUEST, `Order is ${orderExists.status.split('_').join(' ')}`);
  }

  const updateBody = { deliverable: {} };
  if (files) {
    updateBody.deliverable.files = await fileService.processFileUploads(files.deliverables, azureContainers.businessOrders);
  }
  if (body.comment) {
    updateBody.deliverable.comment = body.comment;
  }

  if (!files && !body.comment) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Deliverable(s) or comment is required');
  }
  updateBody.status = businessEnums.orderStatuses.AWAITING_CLIENT_APPROVAL;
  updateBody.fulfilledAt = new Date();

  const order = await Order.findByIdAndUpdate(orderId, { $set: updateBody }, { new: true }).populate('transaction');

  // log activity
  await OrderActivity.create({
    order: order._id,
    client: order.client,
    provider: orderExists.service.provider,
    actor: businessEnums.orderActivityActors.PROVIDER,
    action: businessEnums.orderActivityActions.ORDER_DELIVERED,
  });

  await Account.updateOne(
    { business: orderExists.service.provider },
    {
      $inc: { withdrawableBalance: parseInt(order.transaction.amount, 10) },
    },
  );

  // notify client of order delivery
  const client = await User.findById(order.client);
  const details = { receiverType: 'client', orderId: order._id, serviceName: orderExists.service.name };
  await notificationService.createServiceNotification(user, client, notificationTypes.ORDER_DELIVERED, details);

  // schedule
  await scheduleMarkOrderAsCompleted({ orderId: order._id });

  return order;
};

const cancelOrder = async (user, orderId, cancelReason) => {
  /**
   * Order can be cancelled by client if:
   * - order is awaiting requirements (15% (transaction fee) less full refund)
   * - order is in progress (percentage of refund is based on time elapsed compared to due date.
   *   15% transaction fee applies to both the client and provider shares)
   *
   * No refund if:
   * - order is awaiting client approval
   * - order is completed
   */
  validateId(orderId, 'Order');
  const order = await Order.findById(orderId).populate('service transaction');
  let refundPercentage;

  if (!order) {
    logger.error('Order is invalid');
    throw new ApiError(httpStatus.NOT_FOUND, 'Order is invalid');
  }
  if (order.client.toString() !== user._id.toString()) {
    throw new ApiError(httpStatus.FORBIDDEN, `Forbidden: You're not client of this order`);
  }

  if (order.dueAt <= new Date()) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      'Order cannot be cancelled after due date. Contact provider for resolution report a dispute',
    );
  } else if (
    order.status === businessEnums.orderStatuses.IN_PROGRESS ||
    order.status === businessEnums.orderStatuses.AWAITING_REQUIREMENTS
  ) {
    if (order.dueAt) {
      const timeElapsed = new Date().getTime() - order.startedAt.getTime();
      const timeRemaining = order.dueAt.getTime() - new Date().getTime();
      refundPercentage = timeRemaining / (timeElapsed + timeRemaining); // Existence of dueAt tells that order is in progress
    }
  } else {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Order cannot be cancelled');
  }
  refundPercentage = refundPercentage === undefined ? 1 : refundPercentage; // Default to full refund when order hasn't started
  const orderAmount = parseInt(order.transaction.amount, 10);
  const clientRefundAmount = Math.round(orderAmount * refundPercentage);
  const providerPayment = Math.round(orderAmount * (1 - refundPercentage));

  try {
    // Update provider's account and initiate refund
    const session = await stripe.client.checkout.sessions.retrieve(order.transaction.checkoutSessionId);

    const stripeRefund = await stripe.client.refunds.create({
      payment_intent: session.payment_intent,
      amount: Math.round(clientRefundAmount * 0.85),
      metadata: { order: order._id.toString() },
    });

    const refundTransaction = await Transaction.create({
      account: order.transaction.account,
      amount: clientRefundAmount,
      currency: order.transaction.currency,
      paymentProvider: 'stripe',
      status: businessEnums.transactionStatuses.COMPLETE,
      type: businessEnums.transactionTypes.REFUND,
      stripeRefundId: stripeRefund.id,
      transactionFee: Math.round(clientRefundAmount * 0.15),
      comment: businessEnums.transactionComments.ORDER_REFUND,
    });

    await Transaction.findByIdAndUpdate(order.transaction._id, { refundTransactionId: refundTransaction._id });

    await Account.updateOne(
      { business: order.service.provider },
      {
        $inc: {
          totalBalance: -orderAmount + providerPayment,
          withdrawableBalance: providerPayment,
          totalRefund: clientRefundAmount,
        },
      },
    );
    order.status = businessEnums.orderStatuses.CANCELLED;
    order.cancelReason = cancelReason;
    order.cancelledAt = new Date();
    await order.save();

    // log activity
    await OrderActivity.create({
      order: order._id,
      client: order.client,
      provider: order.service.provider,
      actor: businessEnums.orderActivityActors.CLIENT,
      action: businessEnums.orderActivityActions.ORDER_CANCELLED,
    });

    // notify provider of cancelled order (email and app notification)
    const provider = await Business.findById(order.service.provider).populate('contactPerson');
    const details = { receiverType: 'provider', orderId: order._id, serviceName: order.service.name };
    await notificationService.createServiceNotification(user, provider, notificationTypes.ORDER_CANCELLED, details);

    return refundTransaction;
  } catch (error) {
    logger.error(error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'An error occurred while processing your request');
  }
};

const requestExtraTime = async (user, orderId, body) => {
  // user must be provider
  // order must be in progress
  // the requested time must be less than 7 days
  // the order must not have been extended before
  validateId(orderId, 'Order');
  const order = await Order.findById(orderId);
  if (!order) {
    logger.error('Order does not exist');
    throw new ApiError(httpStatus.NOT_FOUND, 'Order does not exist');
  }

  if (user?.business?.toString() !== order.provider.toString()) {
    throw new ApiError(httpStatus.FORBIDDEN, `Forbidden: You're not authorized to perform this operation`);
  }

  if (order.status !== businessEnums.orderStatuses.IN_PROGRESS) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Order is not in progress');
  }

  if (order.extendedAt) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Order has already been extended');
  }

  if (body.extensionDays > 7) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Maximum extension is 7 days');
  }

  await Order.findByIdAndUpdate(orderId, { requestedExtension: true });

  // notify client of extension request
  const client = await User.findById(order.client);
  const details = { receiverType: 'client', orderId: order._id, serviceName: order.service.name };
  await notificationService.createServiceNotification(user, client, notificationTypes.EXTENSION_REQUESTED, details);
};

const approveExtraTime = async (user, orderId, body) => {
  // body = {approved: boolean, extensionDays: Number}
  // user must be client
  // order must be in progress
  // order must have requestedExtension as true
  validateId(orderId, 'Order');
  const order = await Order.findById(orderId);
  if (!order) {
    logger.error('Order does not exist');
    throw new ApiError(httpStatus.NOT_FOUND, 'Order does not exist');
  }

  if (user._id.toString() !== order.client.toString()) {
    throw new ApiError(httpStatus.FORBIDDEN, `Forbidden: You're not authorized to perform this operation`);
  }

  if (order.status !== businessEnums.orderStatuses.IN_PROGRESS) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Order is not in progress');
  }

  if (!order.requestedExtension) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'An extension has not been requested for this order');
  }

  if (body.approved) {
    const newDueDate = new Date(order.dueAt.getTime() + 1000 * 60 * 60 * 24 * body.extensionDays);
    await Order.findByIdAndUpdate(orderId, { dueAt: newDueDate, extendedAt: new Date() });
  }

  // notify provider of extension request status
  const provider = await Business.findById(order.provider).populate('contactPerson');
  const details = { receiverType: 'provider', orderId: order._id, serviceName: order.service.name };
  const notificationType = body.approved ? notificationTypes.EXTENSION_APPROVED : notificationTypes.EXTENSION_DENIED;
  await notificationService.createServiceNotification(user, provider, notificationType, details);
};

const OrderActivityHandler = {
  createOrderActivity: async (data) => {
    await OrderActivity.create(data);
  },

  getOrderActivities: async (user, filter, options) => {
    validateId(filter.order, 'Order');
    const orderExists = await Order.findById(filter.order).populate('service');
    if (!orderExists) {
      logger.error('Order is invalid');
      throw new ApiError(httpStatus.NOT_FOUND, 'Order is invalid');
    }

    if (
      !(
        user?.business?.toString() === orderExists.service.provider.toString() ||
        user._id.toString() === orderExists.client.toString()
      )
    ) {
      throw new ApiError(httpStatus.FORBIDDEN, `Forbidden: You're not authorized to perform this operation`);
    }

    return OrderActivity.paginate(filter, {
      ...options,
      populate: [
        { path: 'client', select: 'firstName lastName middleName' },
        { path: 'provider', select: 'displayName username profilePhoto' },
        { path: 'order', select: 'deliverable status cancelReason', populate: { path: 'deliverable.files', select: 'url' } },
        {
          path: 'revision',
          select: 'deliverable supportingDocuments description',
          populate: [
            { path: 'deliverable.files', select: 'url' },
            { path: 'supportingDocuments', select: 'url' },
          ],
        },
      ],
    });
  },
};

const reportDispute = async (req) => {
  const { user, body, files } = req;
  const orderId = req.params.id;
  validateId(orderId, 'Order');
  const order = await Order.findById(orderId).populate('service');
  if (!order) {
    logger.error('Order not found');
    throw new ApiError(httpStatus.NOT_FOUND, 'Order not found');
  }

  if (order.client.toString() !== user._id.toString()) {
    logger.error('You are unauthorized to perform this action');
    throw new ApiError(httpStatus.FORBIDDEN, 'You are unauthorized to perform this action');
  }

  const existingDispute = await OrderDispute.findOne({ order: orderId, status: businessEnums.orderDisputeStatuses.OPEN });
  if (existingDispute) {
    logger.error('Dispute already reported');
    throw new ApiError(httpStatus.BAD_REQUEST, 'A pending dispute already exists for this order');
  }

  if (
    order.status === businessEnums.orderStatuses.AWAITING_REQUIREMENTS ||
    order.status === businessEnums.orderStatuses.COMPLETED ||
    order.status === businessEnums.orderStatuses.CANCELLED
  ) {
    logger.error('Order is not in a disputeable state');
    throw new ApiError(httpStatus.BAD_REQUEST, 'Order is not in a disputeable state');
  }

  if (!body.reason) {
    logger.error('Reason is required');
    throw new ApiError(httpStatus.BAD_REQUEST, 'Reason is required');
  }
  if (files) {
    body.supportingDocuments = await fileService.processFileUploads(
      files.supportingDocuments,
      azureContainers.businessOrders,
    );
  }

  await OrderDispute.create({
    order: orderId,
    reporter: user._id,
    provider: order.service.provider,
    service: order.service._id,
    reason: body.reason,
    supportingDocuments: body.supportingDocuments,
    status: businessEnums.orderDisputeStatuses.OPEN,
  });

  // update order status to dispute
  await Order.updateOne({ _id: orderId }, { status: businessEnums.orderStatuses.DISPUTED });

  // log activity
  await OrderActivity.create({
    order: orderId,
    client: user._id,
    provider: order.service.provider,
    actor: businessEnums.orderActivityActors.CLIENT,
    action: businessEnums.orderActivityActions.DISPUTE_REPORTED,
    comment: body.reason,
  });

  // notify provider that client has reported a dispute
  const provider = await Business.findById(order.service.provider).populate('contactPerson');
  const details = { receiverType: 'provider', orderId, serviceName: order.service.name };
  await notificationService.createServiceNotification(user, provider, notificationTypes.DISPUTE_REPORTED, details);
};

const getDisputes = async (filterParam, options) => {
  const filter = filterParam;
  if (filter.reporter) {
    validateId(filter.reporter, 'User');
  }
  if (filter.provider) {
    validateId(filter.provider, 'Business');
  }
  ['reporter', 'provider', 'service', 'order'].forEach((key) => {
    if (filter[key]) {
      if (validateId(filter[key], 'Order Dispute', false)) {
        filter[key] = undefined;
      }
    }
  });
  if (filter.order && !Array.isArray(filter.order)) filter.order = { $in: [filter.order] };

  await Async.eachSeries(['provider', 'service'], async (key) => {
    if (filter[key]) {
      const orders = await Order.find({ [key]: filter[key] }).select('_id');
      filter.order.$in.push(...orders.map((o) => o._id));
      delete filter[key];
    }
  });

  const disputes = await OrderDispute.paginate(filter, options);

  return disputes;
};

const scheduleCompleteOrder = async (orderId) => {
  // order.revisions must be empty
  // order.status must be awaiting_client_approval
  // order.fulfilledAt must be greater than 7 days
  const order = await Order.findById(orderId);
  if (!order) {
    logger.error('Order does not exist');
    throw new ApiError(httpStatus.NOT_FOUND, 'Order does not exist');
  }

  if (order.status !== businessEnums.orderStatuses.AWAITING_CLIENT_APPROVAL) {
    logger.error('Order is not awaiting client approval');
  } else if (order.fulfilledAt <= moment().subtract(7, 'days').toDate()) {
    logger.error('Order has not been fulfilled for 7 days');
  } else if (order.revisions.length) {
    logger.error('Order has revisions');
  } else {
    await Order.findByIdAndUpdate(orderId, { status: businessEnums.orderStatuses.COMPLETED });
    // notify
  }
};

module.exports = {
  // initializeOrder,
  createOrder,
  completeCreateOrder,
  getOrder,
  getOrders,
  completeOrder,
  fulfillOrder,
  cancelOrder,
  reportDispute,
  getDisputes,
  requestExtraTime,
  approveExtraTime,
  orderPopulatePaths,
  ...OrderActivityHandler,
  scheduleCompleteOrder,
};
