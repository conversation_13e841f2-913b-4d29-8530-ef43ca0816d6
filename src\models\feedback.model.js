const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');

const feedbackSchema = new mongoose.Schema(
  {
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    text: { type: String, trim: true },
    rating: { type: Number, min: 0, max: 5, required: true },
  },
  { timestamps: true },
);

feedbackSchema.index({ createdAt: 1, updatedAt: 1, rating: 1 });

feedbackSchema.plugin(toJSON);
feedbackSchema.plugin(paginate);

const Feedback = mongoose.model('Feedback', feedbackSchema);

module.exports = Feedback;
