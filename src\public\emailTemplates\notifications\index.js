module.exports.getFollowEmailTemplate = require('./follow');
module.exports.getJoinGroupRequestEmailTemplate = require('./joinGroupRequest');
module.exports.getAcceptRejectGroupRequestEmailTemplate = require('./acceptRejectGroupRequest');
module.exports.getCreateNewAdminEmailTemplate = require('./createNewAdmin');
module.exports.getNewCoAdminEmailTemplate = require('./newCoAdmin');
module.exports.getLikeResourceEmailTemplate = require('./reactionToResource');
module.exports.getRepostEmailTemplate = require('./repost');
module.exports.getCommentEmailTemplate = require('./comment');
module.exports.getGroupInviteEmailTemplate = require('./groupInvite');
module.exports.getMentionEmailTemplate = require('./mention');
module.exports.getMessageNotificationEmailTemplate = require('./messageNotification');
module.exports.getGroupNotificationEmailTemplate = require('./groupNotifs');
module.exports.getNotifyClientOfDeliveredServiceEmailTemplate = require('./notifyClientOfDeliveredService');
module.exports.getNotifyProviderOfApprovedServiceEmailTemplate = require('./notifyProviderOfApprovedService');
module.exports.getNotifyUsersOfNewUnykEdPostEmailTemplate = require('./newUnykEdPost');
module.exports.getNotifyUsersOfScholarshipsEmailTemplate = require('./newScholarships');
module.exports.getNotifyUsersOfScholarshipExpirationEmailTemplate = require('./sendScholarshipExpirationEmail');
module.exports.getNotifySuperAdminOfNewJobApplicantEmailTemplate = require('./sendNewJobAppplicantEmail');

// service revision notifications
module.exports.getNotifyProviderOfRevisionRequestEmailTemplate = require('./serviceRevisions/notifyProviderOfRevisionRequest');
module.exports.getNotifyClientOfRevisionFulfilledEmailTemplate = require('./serviceRevisions/notifyClientOfRevisionFulfilled');
module.exports.getNotifyProviderOfRevisionCompletedEmailTemplate = require('./serviceRevisions/notifyProviderOfRevisionCompleted');
module.exports.getNotifyProviderOfRevisionCancelledEmailTemplate = require('./serviceRevisions/notifyProviderOfRevisionCancelled');

// order notifications
module.exports.getNotifyProviderOfNewOrderEmailTemplate = require('./orders/notifyProviderOfNewOrder');
module.exports.getNotifyClientOfNewOrderEmailTemplate = require('./orders/notifyClientOfNewOrder');
module.exports.getNotifyProviderOfCancelledOrderEmailTemplate = require('./orders/notifyProviderOfCancelledOrder');
module.exports.getNotifyClientOfDeliveredOrderEmailTemplate = require('./orders/notifyClientOfDeliveredOrder');
module.exports.getNotifyProviderOfCompletedOrderEmailTemplate = require('./orders/notifyProviderOfCompletedOrder');
module.exports.getNotifyProviderOfSubmittedRequirementsEmailTemplate = require('./orders/notifyProviderOfSubmittedRequirements');
module.exports.getNotifyProviderOfDisputeOpenedEmailTemplate = require('./orders/notifyProviderOfDisputeOpened');
module.exports.getNotifyClientOfExtensionRequestedEmailTemplate = require('./orders/notifyClientOfExtensionRequest');
module.exports.getNotifyProviderOfExtensionApprovedEmailTemplate = require('./orders/notifyProviderOfExtensionApproved');
module.exports.getNotifyProviderOfExtensionDeniedEmailTemplate = require('./orders/notifyProviderOfExtensionDenied');

// complete profile
module.exports.getNotifyUserToCompleteProfileEmailTemplate = require('./completeProfile');
module.exports.getNotifyUserToVerifyEmail = require('./verifyEmail');

// welcome new user
module.exports.getWelcomeNewUserEmailTemplate = require('./welcomeNewUser');

// business verifications
module.exports.getNotifySuperAdminToVerifyBusiness = require('./notifySuperAdminToVerifyBusiness');
module.exports.getNotifyVerifierOfAssignedTask = require('./notifyVerifierOfAssignedTask');
module.exports.getNotifyVerifierOfResubmittedVerification = require('./notifyVerifierOfResubmittedVerification');
module.exports.getNotifyBusinessOfVerificationResult = require('./notifyBusinessOfVerificationResult');

// service verifications
module.exports.getNotifySuperAdminToVerifyService = require('./notifySuperAdminToVerifyService');
module.exports.getNotifyServiceVerifierOfAssignedTask = require('./notifyVerifierOfAssignedService');
module.exports.getNotifyServiceOfVerificationResult = require('./notifyServiceOfVerificationResult');

// notify premium subscribers
module.exports.getPremiumSubscriptionExpirationEmailTemplate = require('./notifyPremiumSubscriptionExpiration');
