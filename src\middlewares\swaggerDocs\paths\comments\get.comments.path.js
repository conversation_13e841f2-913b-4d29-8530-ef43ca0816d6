module.exports = {
  get: {
    tags: ['Comments'],
    summary: 'Get comments',
    description: 'Retrieve comments based on provided filters',
    parameters: [
      {
        in: 'query',
        name: 'postId',
        description: 'ID of the post to get comments for',
        required: true,
        schema: {
          type: 'string',
          example: '6123456789abcdef01234567',
        },
      },
      {
        in: 'query',
        name: 'userId',
        description: 'ID of the user whose comments to retrieve (optional)',
        schema: {
          type: 'string',
          example: '6123456789abcdef0123456a',
        },
      },
      {
        in: 'query',
        name: 'parentCommentId',
        description: 'ID of the parent comment to retrieve its replies (optional)',
        schema: {
          type: 'string',
          example: '6123456789abcdef0123456b',
        },
      },
      {
        in: 'query',
        name: 'sortBy',
        description: 'Sort order of comments',
        schema: {
          type: 'string',
          enum: ['createdAt:asc', 'createdAt:desc', 'updatedAt:asc', 'updatedAt:desc'],
          example: 'createdAt:desc',
        },
      },
      {
        in: 'query',
        name: 'limit',
        description: 'Maximum number of comments to return per page',
        schema: {
          type: 'integer',
          example: 10,
        },
      },
      {
        in: 'query',
        name: 'page',
        description: 'Page number of comments to retrieve',
        schema: {
          type: 'integer',
          example: 1,
        },
      },
    ],
    responses: {
      200: {
        description: 'Comments retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                data: {
                  type: 'object',
                  properties: {
                    results: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          text: { type: 'string', example: 'This is a sample comment text.' },
                          post: { type: 'string', example: '659fc3858b69db37953066f3' },
                          user: {
                            type: 'object',
                            properties: {
                              username: { type: 'string', example: 'charlienwa' },
                              photo: {
                                type: 'object',
                                properties: {
                                  url: {
                                    type: 'string',
                                    format: 'url',
                                    example:
                                      'https://www.storage.com/users%2Fphoto/74570fc0-h8i0-777-92c2-61dcf2fafad1-866-536x354.jpg',
                                  },
                                },
                              },
                              firstName: { type: 'string', example: 'George O' },
                              lastName: { type: 'string', example: 'Darwin' },
                              tagLine: { type: 'string', example: '' },
                              middleName: { type: 'string', example: '' },
                              id: { type: 'string', example: '651bb5bf4eb9240327ea5554' },
                            },
                          },
                          likes: { type: 'array', items: { type: 'string', example: '651bb5bf4eb9240327ea5554' } },
                          replies: { type: 'array', items: { type: 'string', example: '651bb5bf4eb9240327ea5554' } },
                          media: { type: 'array', items: { type: 'string', example: '651bb5bf4eb9240327ea5554' } },
                          createdAt: { type: 'string', format: 'date-time' },
                          updatedAt: { type: 'string', format: 'date-time' },
                          createdOn: { type: 'string', format: 'date-time' },
                          updatedOn: { type: 'string', format: 'date-time' },
                          id: { type: 'string', example: '659fd7434857f088ed7d3bc5' },
                        },
                      },
                    },
                    page: { type: 'integer', example: 1 },
                    limit: { type: 'integer', example: 10 },
                    totalPages: { type: 'integer', example: 1 },
                    totalResults: { type: 'integer', example: 1 },
                  },
                },
                message: { type: 'string', example: 'Comments retrieved successfully' },
                status: { type: 'string', example: 'SUCCESS' },
              },
            },
          },
        },
      },
    },
  },
};
