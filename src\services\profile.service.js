const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { Profile } = require('../models');
const Models = require('../models');
const { profileValidation } = require('../validations');

const getResource = async (resourceModel, resourceId) => {
  // find any resource
  const resource = await resourceModel.findById(resourceId);
  if (!resource) {
    throw new ApiError(httpStatus.NOT_FOUND, `${resourceModel.modelName} resource not found`);
  }
  return resource;
};

const getProfiles = async () => {
  const profiles = await Profile.find();
  return profiles;
};

const getProfile = async (profileId) => {
  // await Profile.settings({ resourceId: profileId });
  const profile = await Profile.findById(profileId).populate([
    'education',
    'certification',
    'testscore',
    'experience',
    'project',
    'volunteering',
    'award',
  ]);
  if (!profile) {
    throw new ApiError(httpStatus.NOT_FOUND, `profile not found`);
  }
  return profile;
};

// const getProfileById = async (userId) => {
//   // TODO - tailor returned info to settings
//   const mongoDbPropsToRemove =
//     '-password -countryOfResidence -targetedCountries -preferredFieldsOfStudy -preferredSchoolOfStudy -preferredCountryOfStudy -preferredDegreeOfStudy -dateOfBirth -interestedInScholarship -recoveryEmail -postBookmarks -postLikes -reposts -commentLikes -savedUniversities -appliedUniversities -hiddenUniversities -roles -isEmailVerified -registrationStatus';
//   const user = await User.findById(userId)
//     .select(mongoDbPropsToRemove)
//     .populate([
//       { path: 'photo', select: 'url' },
//       { path: 'banner', select: 'url' },
//       {
//         path: 'profile',
//         populate: ['education', 'certification', 'testscore', 'experience', 'project', 'volunteering', 'award'],
//       },
//     ]);
//   // const data = { user };
//   return user;
// };

const createProfile = async (user, data) => {
  if (user.profile) {
    throw new ApiError(httpStatus.BAD_REQUEST, `User profile aready exists`);
  }
  const profile = await Profile.create(data);
  await Models.User.findByIdAndUpdate(user._id, { profile: profile._id });
  return profile;
};

const updateBasicInformation = async (profileId, updateData) => {
  const profile = await getResource(Profile, profileId);
  await Object.assign(profile, updateData);
  await profile.save();

  return profile;
};

const addResourceInfo = async (profileId, modelName, data) => {
  const { [`add${modelName}Schema`]: validationSchema } = profileValidation;
  if (!validationSchema) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid resource specified');
  }

  const { error, value } = await validationSchema.validate(data);
  if (error) {
    const errorMessage = error.details.map((details) => details.message.replace(/"/g, '')).join(', ');
    throw new ApiError(httpStatus.BAD_REQUEST, `${errorMessage}`);
  }

  const profile = await getResource(Profile, profileId);
  const { [modelName]: Model } = Models;
  const fieldToUpdate = `${modelName.toLowerCase()}`;
  const resourceInfo = await Model.create(value);

  if (resourceInfo) {
    profile[fieldToUpdate].push(resourceInfo._id);
    await profile.save();
  } else {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, `Error creating ${modelName}`);
  }
  return profile;
};

const getResourceInfo = async (resourceId, modelName) => {
  const { [modelName]: Model } = Models;
  const data = await Model.findById(resourceId);
  if (!data) {
    throw new ApiError(httpStatus.NOT_FOUND, `${modelName} resource not found`);
  }
  return data;
};

const updateResourceInfo = async (profileId, resourceId, modelName, updateData) => {
  const { [`update${modelName}Schema`]: validationSchema } = profileValidation;
  if (!validationSchema) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid resource Specified');
  }

  const { error, value } = await validationSchema.validate(updateData);
  if (error) {
    const errorMessage = error.details.map((details) => details.message.replace(/"/g, '')).join(', ');
    throw new ApiError(httpStatus.BAD_REQUEST, `${errorMessage}`);
  }

  const { [modelName]: Model } = Models;
  const fieldToUpdate = `${modelName.toLowerCase()}`;

  const profile = await getResource(Profile, profileId);
  if (!profile[fieldToUpdate].includes(resourceId)) {
    throw new ApiError(httpStatus.FORBIDDEN, 'Cannot access resource');
  }

  if (['Education', 'Experience', 'Volunteering'].includes(modelName)) {
    const { current, end } = updateData;
    if (current && end) {
      throw new ApiError(httpStatus.FORBIDDEN, 'Cannot have an end date when current is true');
    }

    if (end) {
      // eslint-disable-next-line no-param-reassign
      updateData.current = false;
    }
  }

  const resourceInfo = await getResource(Model, resourceId);
  Object.assign(resourceInfo, value);
  await resourceInfo.save();
};

const deleteResourceInfo = async (profileId, resourceId, resourceName) => {
  // a dynamic delete function irrespective of model name
  const modelName = resourceName.replace(/\b\w/g, (char) => char.toUpperCase()); // Make the first letter uppercase
  const { [modelName]: Model } = Models;
  const fieldToUpdate = `${modelName.toLowerCase()}`;

  const profile = await getResource(Profile, profileId);
  if (!profile[fieldToUpdate].includes(resourceId)) {
    throw new ApiError(httpStatus.FORBIDDEN, 'Cannot access resource');
  }

  const resourceInfo = await getResource(Model, resourceId);
  await resourceInfo.remove();

  profile[fieldToUpdate] = profile[fieldToUpdate].filter((item) => item.toString() !== resourceId);
  await profile.save();
};

const deleteProfile = async (profileId) => {
  const profile = await getResource(Profile, profileId);

  const modelDeletions = [
    { field: 'experience', model: Models.Experience },
    { field: 'certification', model: Models.Certification },
    { field: 'testscore', model: Models.TestScore },
    { field: 'volunteering', model: Models.Volunteering },
    { field: 'project', model: Models.Project },
    { field: 'award', model: Models.Award },
  ];

  const promises = modelDeletions
    .filter(({ field }) => profile[field] && profile[field].length > 0)
    .map(({ field, model }) => model.deleteMany({ _id: { $in: profile[field] } }));

  await Promise.all(promises);
  await profile.remove();
};

module.exports = {
  createProfile,
  getProfile,
  getProfiles,
  updateBasicInformation,
  updateResourceInfo,
  addResourceInfo,
  deleteResourceInfo,
  getResourceInfo,
  deleteProfile,
  // getProfileById,
};
