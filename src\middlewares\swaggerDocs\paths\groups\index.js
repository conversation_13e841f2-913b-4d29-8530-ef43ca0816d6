const createGroup = require('./create.group.path');
// const getGroups = require('./get.groups.path');
const getGroup = require('./get.group.path');
const getUserGroups = require('./get.user.groups.path');
const updateGroup = require('./update.group.path');
const deleteGroup = require('./delete.group.path');
const getGroupMembers = require('./get.group.members.path');
const getGroupRequests = require('./get.group.requests.path');
const getUserGroupSettings = require('./get.user.group.settings.path');
const handleMembershipRequest = require('./group.memberships.requests.path');
const handleCoAdmins = require('./handle.coAdmins.path');
const inviteUsers = require('./invite.path');
const joinGroup = require('./join.group.path');
const leaveGroup = require('./leave.group.path');
const removeUsers = require('./remove.users.path');
const updateGroupSettings = require('./update.group.settings.path');

module.exports = {
  '/groups/': { ...createGroup, ...getUserGroups },
  '/groups/{id}': { ...getGroup, ...updateGroup, ...deleteGroup },
  '/groups/{id}/invite': { ...inviteUsers },
  '/groups/{id}/join': { ...joinGroup },
  '/groups/{id}/settings': { ...getUserGroupSettings, ...updateGroupSettings },
  '/groups/{id}/members': { ...getGroupMembers },
  '/groups/{id}/members/remove': { ...removeUsers },
  '/groups/{id}/members/coAdmins': { ...handleCoAdmins },
  '/groups/{id}/requests': { ...getGroupRequests },
  '/groups/{id}/members/{userId}/handle': { ...handleMembershipRequest },
  '/groups/{id}/leave-group/{userId}': { ...leaveGroup },
};
