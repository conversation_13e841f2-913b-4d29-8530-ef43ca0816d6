const dotenv = require('dotenv');
const path = require('path');
const Joi = require('joi');

dotenv.config({ path: path.join(__dirname, '../../.env') });

const envVarsSchema = Joi.object()
  .keys({
    NODE_ENV: Joi.string().valid('production', 'development', 'test').required(),
    PORT: Joi.number().default(4000),
    DATABASE_URL: Joi.string().required().description('Mongo DB url'),
    JWT_SECRET: Joi.string().required().description('JWT secret key'),
    JWT_ACCESS_EXPIRATION_MINUTES: Joi.number().default(30).description('minutes after which access tokens expire'),
    JWT_REFRESH_EXPIRATION_DAYS: Joi.number().default(30).description('days after which refresh tokens expire'),
    JWT_RESET_PASSWORD_EXPIRATION_MINUTES: Joi.number()
      .default(10)
      .description('minutes after which reset password token expires'),
    JWT_VERIFY_EMAIL_EXPIRATION_MINUTES: Joi.number()
      .default(10)
      .description('minutes after which verify email token expires'),
    SERVER_BASE_URL: Joi.string().required().description('base url of the server'),
    SMTP_SERVICE: Joi.string().description('name of service associated with smtp server'),
    SMTP_HOST: Joi.string().description('server that will send the emails'),
    SMTP_HOST_NOTIF: Joi.string().description('server that will send the notif emails'),
    SMTP_PORT: Joi.number().description('port to connect to the email server'),
    SMTP_USERNAME: Joi.string().description('username for email server'),
    SMTP_PASSWORD: Joi.string().description('password for email server'),
    SMTP_REPLY_TO: Joi.string().description('the reply to field in the emails sent by the app'),
    EMAIL_FROM: Joi.string().description('the from field in the emails sent by the app'),
    EMAIL_FROM_NOTIF: Joi.string().description('the from field in the email notifs sent by the app'),
    SMTP_USERNAME_NOTIF: Joi.string().description('the from field in the email notifs sent by the app'),
    SMTP_PASSWORD_NOTIF: Joi.string().description('password for email notifs server'),
    CLIENT_BASE_URL: Joi.string().required().description('the client base url'),
    CLOUDINARY_CLOUD_NAME: Joi.string().required().description('Cloudinary cloud name'),
    CLOUDINARY_API_KEY: Joi.string().required().description('Cloudinary api key'),
    CLOUDINARY_API_SECRET: Joi.string().required().description('Cloudinary api secret'),
    GOOGLE_OAUTH_CLIENT_ID: Joi.string().required().description('Google Oauth Client ID'),
    GOOGLE_OAUTH_CLIENT_SECRET: Joi.string().required().description('Google Oauth Client Secret'),
    GOOGLE_OAUTH_REDIRECT_URL: Joi.string().required().description('Google Oauth Client Secret'),

    LINKEDIN_OAUTH_CLIENT_ID: Joi.string().required().description('Linkedin Oauth Client ID'),
    LINKEDIN_OAUTH_CLIENT_SECRET: Joi.string().required().description('Linkedin Oauth Client Secret'),
    LINKEDIN_OAUTH_REDIRECT_URL: Joi.string().required().description('Linkedin Oauth Client Secret'),
    AZURE_STORAGE_CONNECTION_STRING: Joi.string().required().description('Azure storage connection string'),

    TWILIO_ACCOUNT_SID: Joi.string().required().description('Twilio account SID'),
    TWILIO_AUTH_TOKEN: Joi.string().required().description('Twilio auth token'),
    TWILIO_FRIENDLY_NAME: Joi.string().default('UnykEd').description('Twilio friendly name'),
    TWILIO_SERVICE_ID: Joi.string().description('Twilio service ID'),
    PERSONA_API_KEY: Joi.string().required().description('Persona API key'),
    STRIPE_PUBLISHABLE_KEY: Joi.string().required().description('Stripe publishable key'),
    STRIPE_SECRET_KEY: Joi.string().required().description('Stripe secret key'),
    STRIPE_WEBHOOK_SECRET: Joi.string().description('Stripe webhook secret'),
  })
  .unknown();

const { value: envVars, error } = envVarsSchema.prefs({ errors: { label: 'key' } }).validate(process.env);

if (error) {
  throw new Error(`Config validation error: ${error.message}`);
}

module.exports = {
  env: envVars.NODE_ENV,
  port: envVars.PORT,
  mongoose: {
    url: envVars.DATABASE_URL + (envVars.NODE_ENV === 'test' ? '-test' : ''),
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    },
  },
  jwt: {
    secret: envVars.JWT_SECRET,
    refreshSecret: envVars.JWT_SECRET,
    accessExpirationMinutes: envVars.JWT_ACCESS_EXPIRATION_MINUTES,
    refreshExpirationDays: envVars.JWT_REFRESH_EXPIRATION_DAYS,
    resetPasswordExpirationMinutes: envVars.JWT_RESET_PASSWORD_EXPIRATION_MINUTES,
    verifyEmailExpirationMinutes: envVars.JWT_VERIFY_EMAIL_EXPIRATION_MINUTES,
  },
  email: {
    smtp: {
      service: envVars.SMTP_SERVICE,
      host: envVars.SMTP_HOST,
      port: envVars.SMTP_PORT,
      auth: {
        user: envVars.SMTP_USERNAME,
        pass: envVars.SMTP_PASSWORD,
      },
      replyTo: envVars.SMTP_REPLY_TO || '<EMAIL>',
    },
    smtpNotif: {
      service: envVars.SMTP_SERVICE,
      host: envVars.SMTP_HOST_NOTIF || envVars.SMTP_HOST,
      port: envVars.SMTP_PORT,
      auth: {
        user: envVars.SMTP_USERNAME_NOTIF || envVars.SMTP_USERNAME,
        pass: envVars.SMTP_PASSWORD_NOTIF || envVars.SMTP_PASSWORD,
      },
      replyTo: envVars.SMTP_REPLY_TO || '<EMAIL>',
    },
    from: envVars.EMAIL_FROM,
    notifFrom: envVars.EMAIL_FROM_NOTIF || envVars.EMAIL_FROM,
  },
  azureConfig: {
    AZURE_STORAGE_CONNECTION_STRING: envVars.AZURE_STORAGE_CONNECTION_STRING,
  },
  googleOauth: {
    GOOGLE_OAUTH_CLIENT_ID: envVars.GOOGLE_OAUTH_CLIENT_ID,
    GOOGLE_OAUTH_CLIENT_SECRET: envVars.GOOGLE_OAUTH_CLIENT_SECRET,
    GOOGLE_OAUTH_REDIRECT_URL: `${envVars.CLIENT_BASE_URL}/${envVars.GOOGLE_OAUTH_REDIRECT_URL}`,
  },
  linkedInOauth: {
    LINKEDIN_OAUTH_CLIENT_ID: envVars.LINKEDIN_OAUTH_CLIENT_ID,
    LINKEDIN_OAUTH_CLIENT_SECRET: envVars.LINKEDIN_OAUTH_CLIENT_SECRET,
    LINKEDIN_OAUTH_REDIRECT_URL: `${envVars.CLIENT_BASE_URL}/${envVars.LINKEDIN_OAUTH_REDIRECT_URL}`,
  },
  client: {
    baseUrl: envVars.CLIENT_BASE_URL,
  },
  cloudinaryConfig: {
    cloud_name: envVars.CLOUDINARY_CLOUD_NAME,
    api_key: envVars.CLOUDINARY_API_KEY,
    api_secret: envVars.CLOUDINARY_API_SECRET,
  },
  server: {
    baseUrl: envVars.SERVER_BASE_URL,
  },
  twilio: {
    accountSid: envVars.TWILIO_ACCOUNT_SID,
    authToken: envVars.TWILIO_AUTH_TOKEN,
    // eslint-disable-next-line global-require
    client: require('twilio')(envVars.TWILIO_ACCOUNT_SID, envVars.TWILIO_AUTH_TOKEN),
    serviceId: envVars.TWILIO_SERVICE_ID,
    friendlyName: envVars.TWILIO_FRIENDLY_NAME,
  },
  persona: {
    apiKey: envVars.PERSONA_API_KEY,
  },
  stripe: {
    publishableKey: envVars.STRIPE_PUBLISHABLE_KEY,
    secretKey: envVars.STRIPE_SECRET_KEY,
    // eslint-disable-next-line global-require
    client: require('stripe')(envVars.STRIPE_SECRET_KEY),
    webhookSecret: envVars.STRIPE_WEBHOOK_SECRET,
  },
};
