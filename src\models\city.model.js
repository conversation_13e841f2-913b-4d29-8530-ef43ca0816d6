const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');

// Data source: https://github.com/dr5hn/countries-states-cities-database
// https://github.com/dr5hn/countries-states-cities-database/blob/master/countries%2Bstates%2Bcities.json
const citySchema = new mongoose.Schema(
  {
    name: { type: String, trim: true },
    latitude: { type: String },
    longitude: { type: String },
  },
  {
    timestamps: true,
  },
);

citySchema.index({ name: 1 });

citySchema.plugin(toJSON);
citySchema.plugin(paginate);

const City = mongoose.model('City', citySchema);

module.exports = City;
