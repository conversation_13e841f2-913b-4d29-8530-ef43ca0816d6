module.exports = {
  post: {
    summary: 'Bookmark a post',
    description: 'Bookmark a post based on the provided post ID',
    tags: ['Posts'],
    parameters: [
      {
        in: 'path',
        name: 'id',
        schema: {
          type: 'string',
          example: '507f1f77bcf86cd799439011',
        },
        description: 'ID of the post to bookmark',
        required: true,
      },
    ],
    responses: {
      200: {
        description: 'Post bookmarked successfully',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Post bookmarked successfully',
            },
          },
        },
      },
    },
  },
};
