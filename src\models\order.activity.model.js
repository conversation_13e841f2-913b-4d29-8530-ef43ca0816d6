const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const { businessEnums } = require('../config/constants');

const { ObjectId } = mongoose.SchemaTypes;

const orderActivitySchema = new mongoose.Schema(
  {
    order: { type: ObjectId, ref: 'Order', required: true },
    revision: { type: ObjectId, ref: 'ServiceRevision' },
    client: { type: ObjectId, ref: 'User', required: true },
    provider: { type: ObjectId, ref: 'Business', required: true },
    actor: { type: String, enum: Object.values(businessEnums.orderActivityActors), required: true },
    action: { type: String, required: true, enum: Object.values(businessEnums.orderActivityActions) },
  },
  {
    timestamps: true,
  },
);

orderActivitySchema.index({ order: 1, createdAt: 1 });

orderActivitySchema.plugin(toJSON);
orderActivitySchema.plugin(paginate);

const OrderActivity = mongoose.model('OrderActivity', orderActivitySchema);

module.exports = OrderActivity;
