const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { activityLogService } = require('../services');
const { pick, removeUndefinedKeys } = require('../utils/pick');

const getUserActivityLogs = catchAsync(async (req, res) => {
  const filter = pick({ ...req.query, user: req?.user?._id }, ['user']);
  removeUndefinedKeys(filter);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const data = await activityLogService.getUserActivityLogs(filter, options);
  return res.status(httpStatus.OK).json({ data, message: 'Logs retrieved successfully', status: 'SUCCESS' });
});

module.exports = { getUserActivityLogs };
