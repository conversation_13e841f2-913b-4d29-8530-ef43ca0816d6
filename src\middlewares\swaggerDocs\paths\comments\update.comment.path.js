module.exports = {
  patch: {
    summary: 'Update Comment',
    description: 'Updates a comment by its unique identifier.',
    tags: ['Comments'],
    consumes: ['multipart/form-data'],
    parameters: [
      {
        in: 'path',
        name: 'id',
        required: true,
        description: 'ID of the comment to retrieve',
        schema: { type: 'string', example: '659fd7434857f088ed7d3bc5' },
      },
      {
        in: 'formData',
        name: 'files',
        type: 'file',
        description: 'Array of files to upload (optional)',
      },
      {
        in: 'formData',
        name: 'body',
        required: true,
        description: 'Comment data',
        schema: {
          type: 'object',
          properties: {
            text: { type: 'string', example: 'This is a comment text' },
            deletedUrls: { type: 'string', example: 'www.file1.com www.file2.com' },
          },
        },
      },
    ],
    responses: {
      200: {
        description: 'Comment updated successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                data: {
                  type: 'object',
                  properties: {
                    id: {
                      type: 'string',
                      example: '6123456789abcdef01234567',
                    },
                    text: {
                      type: 'string',
                      example: 'This is a sample comment text.',
                    },
                    post: {
                      type: 'string',
                      example: '6123456789abcdef01234568',
                    },
                    parentComment: {
                      type: 'string',
                      example: '6123456789abcdef01234569',
                    },
                    user: {
                      type: 'string',
                      example: '6123456789abcdef0123456a',
                    },
                    likes: {
                      type: 'array',
                      items: {
                        type: 'string',
                        example: '6123456789abcdef0123456b',
                      },
                    },
                    replies: {
                      type: 'array',
                      items: {
                        type: 'string',
                        example: '6123456789abcdef0123456c',
                      },
                    },
                    media: {
                      type: 'array',
                      items: {
                        type: 'string',
                        example: '6123456789abcdef0123456d',
                      },
                    },
                    createdAt: {
                      type: 'string',
                      format: 'date-time',
                      example: '2024-03-16T12:00:00Z',
                    },
                    updatedAt: {
                      type: 'string',
                      format: 'date-time',
                      example: '2024-03-16T12:00:00Z',
                    },
                    createdOn: {
                      type: 'string',
                      format: 'date-time',
                      example: '2024-03-16T12:00:00Z',
                    },
                    updatedOn: {
                      type: 'string',
                      format: 'date-time',
                      example: '2024-03-16T12:00:00Z',
                    },
                  },
                  required: ['text', 'post', 'user'],
                },
                message: { type: 'string', example: 'Comment created successfully' },
                status: { type: 'string', example: 'SUCCESS' },
              },
            },
          },
        },
      },
    },
  },
};
