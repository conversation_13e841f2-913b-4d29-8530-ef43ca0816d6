/* eslint-disable no-param-reassign */
const Async = require('async');
const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const User = require('../user.model');
const { registrationStatuses } = require('../../config/constants');
const { generateVerifyEmailToken } = require('../../services/token.service');
const { tokenTypes } = require('../../config/tokens');
const Token = require('../token.model');

const varName = 'Notify users to complete profile: create token';

const notifyUsersToCompleteProfileScript = async () => {
  const migrated = await GlobalVariable.findOne({ name: varName });
  if (migrated) {
    return;
  }

  const usersPromise = User.find({
    registrationStatus: { $in: [registrationStatuses.ACCOUNT_VERIFIED, registrationStatuses.SIGNUP_COMPLETED] },
  }).select('_id email');

  const [users] = await Promise.all([usersPromise]);

  await Async.eachOfSeries(users, async (user, index) => {
    await User.findByIdAndUpdate(user._id, { emailVerificationCount: 0 });
    await Token.deleteMany({ user: user._id, type: tokenTypes.VERIFY_EMAIL });
    await generateVerifyEmailToken(user, user.pendingBusiness ? 'business' : 'student');
    logger.info(`User ${index + 1} of ${users.length} token generated.`);
  });

  await GlobalVariable.create({ name: varName, value: 'true' });
  logger.info('Created Token for users to complete profile');
};

module.exports = notifyUsersToCompleteProfileScript;
