const createForumPost = require('./create.forum.post.path');
const getForumPosts = require('./get.forum.posts.path');
const getForumPost = require('./get.forum.post.path');
const updateForumPost = require('./update.forum.post.path');
const deleteForumPost = require('./delete.forum.post.path');
const searchForumPosts = require('./search.forum.posts.path');

const createForumReply = require('./replies/create.forum.reply.path');
const getForumReplies = require('./replies/get.forum.replies.path');
const getForumReply = require('./replies/get.forum.reply.path');
const updateForumReply = require('./replies/update.forum.reply.path');
const deleteForumReply = require('./replies/delete.forum.reply.path');
const voteForumReply = require('./replies/vote.forum.reply.path');

module.exports = {
  '/forums/': { ...createForumPost, ...getForumPosts },
  '/forums/{id}': { ...getForumPost, ...updateForumPost, ...deleteForumPost },
  '/forums/{id}/reply': { ...createForumReply, ...getForumReplies },
  '/forums/search': { ...searchForumPosts },
  '/{forumPostId}/reply/{id}': { ...getForumReply, ...updateForumReply, ...deleteForumReply },
  '/{forumPostId}/reply/{id}/vote': { ...voteForumReply },
};
