const mongoose = require('mongoose');
const { toJSON, paginate } = require('../plugins');

const experienceSchema = new mongoose.Schema({
  title: { type: String, trim: true, required: true },
  companyName: { type: String, trim: true, required: true },
  start: { type: mongoose.Schema.Types.Date, trim: true, required: true },
  end: { type: mongoose.Schema.Types.Date, trim: true },
  current: { type: Boolean },
  description: { type: String, trim: true },
});

experienceSchema.plugin(toJSON);
experienceSchema.plugin(paginate);

const Experience = mongoose.model('Experience', experienceSchema);
module.exports = Experience;
