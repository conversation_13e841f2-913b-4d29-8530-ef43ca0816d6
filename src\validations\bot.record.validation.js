const Joi = require('joi');

const createBotRecord = {
  body: Joi.object().keys({
    userQuestion: Joi.string().required(),
    botResponse: Joi.string().required(),
  }),
};

const getBotRecords = {
  query: Joi.object().keys({
    sortBy: Joi.string()
      .valid('createdAt:desc', 'createdAt:asc', 'updatedAt:asc', 'updatedAt:desc')
      .default('createdAt:desc'),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
  }),
};

module.exports = {
  createBotRecord,
  getBotRecords,
};
