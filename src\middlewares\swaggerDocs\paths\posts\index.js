const createPost = require('./create.post.path');
const getPosts = require('./get.posts.path');
const getPost = require('./get.post.path');
const updatePost = require('./update.post.path');
const deletePost = require('./delete.post.path');
// const getProfilePosts = require('./get.profile.posts.path');

const likePost = require('./like.post.path');
const repost = require('./repost.post.path');
const bookmarkPost = require('./bookmark.post.path');
const reactToPost = require('./react.to.post.path');

module.exports = {
  '/posts/': { ...createPost, ...getPosts },
  '/posts/{id}': { ...getPost, ...updatePost, ...deletePost },
  '/posts/{id}/like': { ...likePost },
  '/posts/{id}/repost': { ...repost },
  '/posts/{id}/bookmark': { ...bookmarkPost },
  '/posts/{id}/reactions': { ...reactToPost },
  // '/posts/feed': { ...getProfilePosts },
};
