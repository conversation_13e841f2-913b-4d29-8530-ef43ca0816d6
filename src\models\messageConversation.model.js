const mongoose = require('mongoose');

const { Schema, SchemaTypes } = mongoose;
const { toJSON, paginate } = require('./plugins');
const { messageEnums } = require('../config/constants');

const messageConversationSchema = new Schema(
  {
    messages: [{ type: SchemaTypes.ObjectId, ref: 'Message' }],
    // participants: [{ type: SchemaTypes.ObjectId, ref: 'User' }],
    members: [
      // Replaces participants
      {
        user: { type: SchemaTypes.ObjectId, ref: 'User' }, // Either user or business will be present not both
        business: { type: SchemaTypes.ObjectId, ref: 'Business' },
      },
    ],
    directMessage: { type: Boolean, default: true },
    createdFor: [{ type: String, enum: Object.values(messageEnums.createdForValues) }],
  },
  {
    timestamps: true,
  },
);

messageConversationSchema.index({ createdAt: 1, updatedAt: 1 });

messageConversationSchema.plugin(toJSON);
messageConversationSchema.plugin(paginate);

const MessageConversation = mongoose.model('MessageConversation', messageConversationSchema);

module.exports = MessageConversation;
