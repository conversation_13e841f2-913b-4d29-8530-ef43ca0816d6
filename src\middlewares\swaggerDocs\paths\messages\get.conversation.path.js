module.exports = {
  get: {
    summary: 'Get a conversation',
    description: 'Get a conversation by its ID',
    tags: ['Messages'],
    parameters: [
      {
        in: 'path',
        name: 'conversationId',
        required: true,
        schema: {
          type: 'string',
          example: '5f4d5d2e7c213e2c6c9d3e7d',
        },
      },
      {
        in: 'query',
        name: 'limit',
        required: false,
        schema: {
          type: 'integer',
          example: 5,
        },
      },
      {
        in: 'query',
        name: 'page',
        required: false,
        schema: {
          type: 'integer',
          example: 1,
        },
      },
    ],
    responses: {
      200: {
        description: 'Conversation retrieved successfully',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              data: {
                results: [
                  {
                    sender: {
                      photo: {
                        _id: '65bb7f69f2b37ca660a3c05f',
                        url: 'https://storagesite.com/users%2Fphoto/74570fc0-c0f4-11ee-92c2-61dcf2fafad1-866-536x354.jpg',
                      },
                      firstName: '<PERSON>',
                      lastName: 'Darwin',
                      id: '651bb5bf4eb9240327ea9d56',
                    },
                    conversation: {
                      participants: [
                        {
                          photo: {
                            _id: '65bb7f69f2b37ca660a3c05f',
                            url: 'https://storagesite.com/users%2Fphoto/74570fc0-c0f4-11ee-92c2-61dcf2fafad1-866-536x354.jpg',
                          },
                          firstName: 'George O',
                          lastName: 'Darwin',
                          id: '651bb5bf4eb9240327ea9d56',
                        },
                        {
                          firstName: 'Rickter',
                          lastName: 'Belmont',
                          photo: null,
                          id: '652f6fb971c38a9b93066b9d',
                        },
                      ],
                      id: '6579836cf193b41d63b82b56',
                    },
                    text: 'Hello',
                    file: [],
                    read: false,
                    createdAt: '2023-12-13T10:25:55.044Z',
                    updatedAt: '2024-02-14T16:10:22.371Z',
                    readBy: ['652f6fb971c38a9b93066b9d'],
                    receivedBy: ['652f6fb971c38a9b93066b9d'],
                    id: '657986b3d5defe1d9226ef82',
                  },
                  {
                    sender: {
                      photo: {
                        _id: '65bb7f69f2b37ca660a3c05f',
                        url: 'https://storagesite.com/users%2Fphoto/74570fc0-c0f4-11ee-92c2-61dcf2fafad1-866-536x354.jpg',
                      },
                      firstName: 'George O',
                      lastName: 'Darwin',
                      id: '651bb5bf4eb9240327ea9d56',
                    },
                    conversation: {
                      participants: [
                        {
                          photo: {
                            _id: '65bb7f69f2b37ca660a3c05f',
                            url: 'https://storagesite.com/users%2Fphoto/74570fc0-c0f4-11ee-92c2-61dcf2fafad1-866-536x354.jpg',
                          },
                          firstName: 'George O',
                          lastName: 'Darwin',
                          id: '651bb5bf4eb9240327ea9d56',
                        },
                        {
                          firstName: 'Rickter',
                          lastName: 'Belmont',
                          photo: null,
                          id: '652f6fb971c38a9b93066b9d',
                        },
                      ],
                      id: '6579836cf193b41d63b82b56',
                    },
                    text: 'Hello',
                    file: [],
                    read: false,
                    createdAt: '2023-12-13T10:28:54.716Z',
                    updatedAt: '2024-02-14T16:10:22.389Z',
                    readBy: ['652f6fb971c38a9b93066b9d'],
                    receivedBy: ['652f6fb971c38a9b93066b9d'],
                    id: '6579876610f888e0675e5b7b',
                  },
                  {
                    sender: {
                      photo: {
                        _id: '65bb7f69f2b37ca660a3c05f',
                        url: 'https://storagesite.com/users%2Fphoto/74570fc0-c0f4-11ee-92c2-61dcf2fafad1-866-536x354.jpg',
                      },
                      firstName: 'George O',
                      lastName: 'Darwin',
                      id: '651bb5bf4eb9240327ea9d56',
                    },
                    conversation: {
                      participants: [
                        {
                          photo: {
                            _id: '65bb7f69f2b37ca660a3c05f',
                            url: 'https://storagesite.com/users%2Fphoto/74570fc0-c0f4-11ee-92c2-61dcf2fafad1-866-536x354.jpg',
                          },
                          firstName: 'George O',
                          lastName: 'Darwin',
                          id: '651bb5bf4eb9240327ea9d56',
                        },
                        {
                          firstName: 'Rickter',
                          lastName: 'Belmont',
                          photo: null,
                          id: '652f6fb971c38a9b93066b9d',
                        },
                      ],
                      id: '6579836cf193b41d63b82b56',
                    },
                    text: 'Hello',
                    file: [],
                    read: false,
                    createdAt: '2023-12-13T10:53:16.805Z',
                    updatedAt: '2024-02-14T16:10:22.422Z',
                    readBy: ['652f6fb971c38a9b93066b9d'],
                    receivedBy: ['652f6fb971c38a9b93066b9d'],
                    id: '65798d1c33104bf226775740',
                  },
                ],
                page: 1,
                limit: 5,
                totalPages: 26,
                totalResults: 129,
              },
            },
          },
        },
      },
    },
  },
};
