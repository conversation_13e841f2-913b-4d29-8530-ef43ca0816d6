const awardSchemaSwagger = {
  type: 'object',
  properties: {
    id: { type: 'string', example: '6009c0eee65f6dce28fb3e50' },
    awardName: {
      type: 'string',
      description: 'Name of the award',
      example: 'Outstanding Achievement Award',
    },
    issuingBody: {
      type: 'string',
      description: 'Issuing organization of the award',
      example: 'ABC Foundation',
    },
    date: {
      type: 'string',
      format: 'date',
      description: 'Date when the award was received',
      example: '2023-10-15',
    },
    awardLink: {
      type: 'string',
      description: 'Link to information about the award',
      example: 'https://example.com/award-info',
    },
  },
};

module.exports = awardSchemaSwagger;
