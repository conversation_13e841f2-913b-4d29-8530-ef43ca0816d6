module.exports = {
  delete: {
    summary: 'Delete a specific forum post reply',
    tags: ['Forum Replies'],
    parameters: [
      {
        name: 'forumPostId',
        in: 'path',
        description: 'ID of the forum post',
        required: true,
        type: 'string',
        format: 'objectId',
      },
      {
        name: 'id',
        in: 'path',
        description: 'ID of the forum post reply',
        required: true,
        type: 'string',
        format: 'objectId',
      },
    ],
    responses: {
      200: {
        description: 'Reply deleted successfully',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Reply with ID 12345 deleted',
            },
          },
        },
      },
      404: {
        description: 'Reply not found',
        content: {
          'application/json': {
            example: {
              status: 'FAILED',
              message: 'Reply record not found',
            },
          },
        },
      },
    },
  },
};
