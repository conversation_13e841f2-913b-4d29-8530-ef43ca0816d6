const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { careerJobsService } = require('../services');
const { pick, removeUndefinedKeys } = require('../utils/pick');

const addCareerJob = catchAsync(async (req, res) => {
  const careerJob = await careerJobsService.addCareerJob(req.body);
  return res
    .status(httpStatus.CREATED)
    .json({ data: careerJob, message: 'Career record created successfully', status: 'SUCCESS' });
});

const getCareerJobs = catchAsync(async (req, res) => {
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const filter = pick(req.query, ['jobType', 'jobCategory', 'title', 'jobMode', 'searchText']);
  const isAdmin = !!req.user && req.user.roles.includes('admin');

  if (isAdmin) {
    filter.publishStatus = req.query.publishStatus;
  }

  removeUndefinedKeys(filter);

  const careerJobs = await careerJobsService.getCareerJobs(filter, options, isAdmin);
  res.status(httpStatus.OK).json({ data: careerJobs, message: 'Career job records fetched successfully' });
});

const getCareerJob = catchAsync(async (req, res) => {
  const isAdmin = !!req.user && req.user.roles.includes('admin');
  const careerJob = await careerJobsService.getCareerJob(req.params.id, isAdmin);
  return res
    .status(httpStatus.OK)
    .json({ data: careerJob, message: 'Career job record fetched successfully', status: 'SUCCESS' });
});

const getCareerJobEnums = catchAsync(async (req, res) => {
  const enums = await careerJobsService.getCareerJobEnums();
  res.status(httpStatus.OK).json({ data: enums, message: 'Enums for career jobs fetched successfully', status: 'SUCCESS' });
});

const getJobAnalytics = catchAsync(async (req, res) => {
  const jobAnalytics = await careerJobsService.getJobAnalytics();
  res
    .status(httpStatus.OK)
    .json({ data: jobAnalytics, message: 'Career job analytics fetched successfully', status: 'SUCCESS' });
});

const updateCareerJob = catchAsync(async (req, res) => {
  const careerJobId = req.params.id;
  const careerJob = await careerJobsService.updateCareerJob(careerJobId, req.body);
  return res
    .status(httpStatus.OK)
    .json({ data: careerJob, message: 'Career job record updated successfully', status: 'SUCCESS' });
});

const deleteCareerJob = catchAsync(async (req, res) => {
  await careerJobsService.deleteCareerJob(req.params.id);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Career job record successfully deleted' });
});

// job applicatioons
const createJobApplication = catchAsync(async (req, res) => {
  const jobApplication = await careerJobsService.createJobApplication(req.body, req.files);
  res
    .status(httpStatus.CREATED)
    .json({ status: 'SUCCESS', data: jobApplication, message: 'Job application successfully created' });
});

const getJobApplications = catchAsync(async (req, res) => {
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const filter = pick(req.query, ['careerJob', 'status', 'name']);
  removeUndefinedKeys(filter);
  const jobApplications = await careerJobsService.getJobApplications(filter, options);
  res
    .status(httpStatus.CREATED)
    .json({ status: 'SUCCESS', data: jobApplications, message: 'Job applications successfully fetched' });
});

const getJobApplication = catchAsync(async (req, res) => {
  const jobApplication = await careerJobsService.getJobApplication(req.params.id);
  res
    .status(httpStatus.CREATED)
    .json({ status: 'SUCCESS', data: jobApplication, message: 'Job application successfully fetched' });
});

const updateJobApplication = catchAsync(async (req, res) => {
  const jobApplication = await careerJobsService.updateJobApplication(req.params.id, req.body);
  res
    .status(httpStatus.CREATED)
    .json({ status: 'SUCCESS', data: jobApplication, message: 'Job application successfully updated' });
});

module.exports = {
  addCareerJob,
  getCareerJobs,
  getCareerJob,
  getCareerJobEnums,
  updateCareerJob,
  deleteCareerJob,
  getJobAnalytics,
  createJobApplication,
  getJobApplication,
  getJobApplications,
  updateJobApplication,
};
