/* eslint-disable global-require */
const httpStatus = require('http-status');
const { commentService } = require('../services');
const catchAsync = require('../utils/catchAsync');
const { pick, removeUndefinedKeys } = require('../utils/pick');

const createComment = catchAsync(async (req, res) => {
  const comment = await commentService.createComment(req);
  res.status(httpStatus.CREATED).json({ data: comment, message: 'Comment created successfully', status: 'SUCCESS' });

  const postService = require('../services/post.service');

  let postsQuery = { post: comment.post.toString(), parentComment: null };
  if (comment.parentComment) postsQuery = { ...postsQuery, parentComment: comment.parentComment.toString() };
  const commentsEventName = comment.parentComment ? '/comment-replies-broadcast' : '/post-comments-broadcast';
  const post = await postService.getPostById(comment.post.toString(), req.user._id, true);
  const postComments = await commentService.queryComments(postsQuery, { sortBy: 'createdAt:desc', limit: 100 });
  await postService.emitPostToUsers(post, '/post-broadcast', undefined);
  await postService.emitCommentToUsers(postComments.results, commentsEventName, undefined);
});

const getCommentById = catchAsync(async (req, res) => {
  const comment = await commentService.getCommentById(req?.params?.id);
  res.status(httpStatus.OK).json({ data: comment, message: 'Comment retrieved successfully', status: 'SUCCESS' });
});

const getComments = catchAsync(async (req, res) => {
  const { userId: user, postId: post, parentCommentId: parentComment } = req.query; // parentCommentId of null will get all comments directly under the post
  const filter = pick({ ...req.query, user, post, parentComment: parentComment || null }, ['post', 'user', 'parentComment']);
  removeUndefinedKeys(filter); // Remove user and post if undefined

  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const comments = await commentService.queryComments(filter, options);
  res.status(httpStatus.OK).json({ data: comments, message: 'Comments retrieved successfully', status: 'SUCCESS' });
});

const updateComment = catchAsync(async (req, res) => {
  const updatedComment = await commentService.updateCommentById(req);
  res.status(httpStatus.OK).json({ data: updatedComment, message: 'Comment updated successfully', status: 'SUCCESS' });
});

const deleteComment = catchAsync(async (req, res) => {
  const deletedComment = await commentService.deleteCommentById(req.params.id, req.resourceRecord);
  res.status(httpStatus.OK).json({ message: `Comment with ID ${deletedComment._id} deleted`, status: 'SUCCESS' });

  const postService = require('../services/post.service');
  let postsQuery = { post: deletedComment.post.toString(), parentComment: null };
  if (deletedComment.parentComment) postsQuery = { ...postsQuery, parentComment: deletedComment.parentComment.toString() };
  const commentsEventName = deletedComment.parentComment ? '/comment-replies-broadcast' : '/post-comments-broadcast';

  const post = await postService.getPostById(deletedComment.post.toString(), req.user._id, true);
  const postComments = await require('../services/comment.service').queryComments(postsQuery, {
    sortBy: 'createdAt:desc',
    limit: 100,
  });
  await postService.emitPostToUsers(post, '/post-broadcast', undefined);
  await postService.emitCommentToUsers(postComments.results, commentsEventName, undefined);
});

module.exports = {
  createComment,
  getCommentById,
  getComments,
  updateComment,
  deleteComment,
};
