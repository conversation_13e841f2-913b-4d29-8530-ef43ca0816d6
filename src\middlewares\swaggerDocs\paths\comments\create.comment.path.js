module.exports = {
  post: {
    summary: 'Create Comment',
    description: 'Create a new comment on a post',
    tags: ['Comments'],
    consumes: ['multipart/form-data'],
    parameters: [
      {
        in: 'formData',
        name: 'files',
        type: 'file',
        description: 'Array of files to upload (optional)',
      },
      {
        in: 'formData',
        name: 'body',
        required: true,
        description: 'Comment data',
        schema: {
          type: 'object',
          properties: {
            text: { type: 'string', example: 'This is a comment text' },
            postId: { type: 'string', example: '6123456789abcdef01234569' },
            parentCommentId: { type: 'string', example: '6123456789abcdef01234569' },
          },
        },
      },
    ],
    responses: {
      201: {
        description: 'Successful operation',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                data: {
                  type: 'object',
                  properties: {
                    id: {
                      type: 'string',
                      example: '6123456789abcdef01234567',
                    },
                    text: {
                      type: 'string',
                      example: 'This is a sample comment text.',
                    },
                    post: {
                      type: 'string',
                      example: '6123456789abcdef01234568',
                    },
                    parentComment: {
                      type: 'string',
                      example: '6123456789abcdef01234569',
                    },
                    user: {
                      type: 'string',
                      example: '6123456789abcdef0123456a',
                    },
                    likes: {
                      type: 'array',
                      items: {
                        type: 'string',
                        example: '6123456789abcdef0123456b',
                      },
                    },
                    replies: {
                      type: 'array',
                      items: {
                        type: 'string',
                        example: '6123456789abcdef0123456c',
                      },
                    },
                    media: {
                      type: 'array',
                      items: {
                        type: 'string',
                        example: '6123456789abcdef0123456d',
                      },
                    },
                    createdAt: {
                      type: 'string',
                      format: 'date-time',
                      example: '2024-03-16T12:00:00Z',
                    },
                    updatedAt: {
                      type: 'string',
                      format: 'date-time',
                      example: '2024-03-16T12:00:00Z',
                    },
                    createdOn: {
                      type: 'string',
                      format: 'date-time',
                      example: '2024-03-16T12:00:00Z',
                    },
                    updatedOn: {
                      type: 'string',
                      format: 'date-time',
                      example: '2024-03-16T12:00:00Z',
                    },
                  },
                  required: ['text', 'post', 'user'],
                },
                message: { type: 'string', example: 'Comment created successfully' },
                status: { type: 'string', example: 'SUCCESS' },
              },
            },
          },
        },
      },
      400: {
        description: 'Bad request',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Parent comment mismatch' },
                status: { type: 'string', example: 'FAILED' },
              },
            },
          },
        },
      },
      401: {
        description: 'Unauthorized',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Parent comment not found' },
                status: { type: 'string', example: 'FAILED' },
              },
            },
          },
        },
      },
      404: {
        description: 'Not found',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Post not found' },
                status: { type: 'string', example: 'FAILED' },
              },
            },
          },
        },
      },
    },
  },
};
