module.exports = {
  post: {
    tags: ['Comments'],
    summary: 'Like a comment by ID',
    description: 'Likes a comment by its unique identifier',
    parameters: [
      {
        in: 'path',
        name: 'id',
        required: true,
        description: 'ID of the comment to retrieve',
        schema: { type: 'string', example: '659fd7434857f088ed7d3bc5' },
      },
    ],
    responses: {
      200: {
        description: 'Comment retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                data: {
                  type: 'object',
                  properties: {
                    text: { type: 'string' },
                    post: { type: 'string', example: '659fc3858b69db379530fef3' },
                    user: {
                      type: 'object',
                      properties: {
                        username: { type: 'string', example: 'charlienwa' },
                        photo: {
                          type: 'object',
                          properties: {
                            url: {
                              type: 'string',
                              format: 'url',
                              example:
                                'https://www.storage.com/users%2Fphoto/74570fc0-h8i0-777-92c2-61dcf2fafad1-866-536x354.jpg',
                            },
                          },
                        },
                        firstName: { type: 'string', example: '<PERSON>' },
                        lastName: { type: 'string', example: 'Darwin' },
                        tagLine: { type: 'string', example: '' },
                        middleName: { type: 'string', example: '' },
                        id: { type: 'string', example: '651bb5bf4eb9240327ea5554' },
                      },
                    },
                    likes: { type: 'array', items: { type: 'string', example: '651bb5bf4eb9240327ea5554' } },
                    replies: { type: 'array', items: { type: 'string', example: '651bb5bf4eb9240327ea5554' } },
                    media: { type: 'array', items: { type: 'string', example: '651bb5bf4eb9240327ea5554' } },
                    createdAt: { type: 'string', format: 'date-time' },
                    updatedAt: { type: 'string', format: 'date-time' },
                    createdOn: { type: 'string', format: 'date-time' },
                    updatedOn: { type: 'string', format: 'date-time' },
                    id: { type: 'string', example: '659fd7434857f088ed7d3bc5' },
                  },
                },
                message: { type: 'string', example: 'Comment retrieved successfully' },
                status: { type: 'string', example: 'SUCCESS' },
              },
            },
          },
        },
      },
    },
  },
};
