const router = require('express').Router();
const { issueReportController } = require('../../controllers');
const { issueReportValidation } = require('../../validations');
const validate = require('../../middlewares/validate');
const { auth } = require('../../middlewares/auth');
const { uploadIssueReportFiles } = require('../../config/multer.config');

router.post(
  '/',
  auth(),
  uploadIssueReportFiles,
  validate(issueReportValidation.createIssueReport),
  issueReportController.createIssueReport,
);
router.get('/enums', issueReportController.getIssueReportEnums);
router.get('/private-enums', auth('internal'), issueReportController.getIssueReportPrivateEnums);

router.use(auth('manageIssueReports'));

router.get('/', validate(issueReportValidation.getIssueReports), issueReportController.getIssueReports);
router.get('/stats', issueReportController.getIssueReportStats);
router.get('/:id', issueReportController.getIssueReportById);
router.patch('/:id', validate(issueReportValidation.updateIssueReport), issueReportController.updateIssueReport);

module.exports = router;
