module.exports = {
  get: {
    summary: 'Perform a global search to find Users, Posts, Forum Posts, e.t.c',
    tags: ['Global'],
    parameters: [
      {
        in: 'query',
        name: 'searchText',
        description: 'Text to search for',
        required: true,
        type: 'string',
        example: '<PERSON>',
      },
      {
        in: 'query',
        name: 'tab',
        description: 'Entity to search in. e.g. users, groups, forum_posts, posts',
        required: false,
        default: 'users',
        type: 'string',
        example: 'posts',
      },
      {
        in: 'query',
        name: 'sortBy',
        schema: {
          type: 'string',
        },
        description: 'Sort order for the posts',
      },
      {
        in: 'query',
        name: 'limit',
        schema: {
          type: 'integer',
          format: 'int32',
          example: '12',
        },
        description: 'Number of posts to return',
      },
      {
        in: 'query',
        name: 'page',
        schema: {
          type: 'integer',
          format: 'int32',
          example: '2',
        },
        description: 'Page number',
      },
    ],
    responses: {
      200: {
        description: 'Forum posts retrieved successfully',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Forum posts retrieved successfully',
              data: {
                results: [],
                page: 1,
                limit: 10,
                totalPages: 1,
                totalResults: 1,
              },
            },
          },
        },
      },
    },
  },
};
