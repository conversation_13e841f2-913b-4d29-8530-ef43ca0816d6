const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const { visibilityTypes, themeTypes } = require('../config/constants');

const settingSchema = new mongoose.Schema({
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },

  theme: { type: String, trim: true, enum: Object.values(themeTypes) },

  dateOfBirth: { type: String, trim: true, enum: Object.values(visibilityTypes) },
  skills: { type: String, trim: true, enum: Object.values(visibilityTypes) },
  hobbies: { type: String, trim: true, enum: Object.values(visibilityTypes) },

  email: { type: String, trim: true, enum: Object.values(visibilityTypes), default: visibilityTypes.PRIVATE },
  nationality: { type: String, trim: true, enum: Object.values(visibilityTypes) },
  phone: { type: String, trim: true, enum: Object.values(visibilityTypes), default: visibilityTypes.PRIVATE },

  gender: {
    type: String,
    trim: true,
    enum: Object.values(visibilityTypes),
  },
  countryOfResidence: {
    type: String,
    trim: true,
    enum: Object.values(visibilityTypes),
  },
  stateOfResidence: {
    type: String,
    trim: true,
    enum: Object.values(visibilityTypes),
  },
  targetedCountries: {
    type: String,
    trim: true,
    enum: Object.values(visibilityTypes),
  },
  preferredFieldOfStudy: {
    type: String,
    trim: true,
    enum: Object.values(visibilityTypes),
  },
  preferredFieldsOfStudy: {
    type: String,
    trim: true,
    enum: Object.values(visibilityTypes),
  },
  preferredSchoolOfStudy: { type: String, trim: true, enum: Object.values(visibilityTypes) },
  preferredSchoolsOfStudy: { type: String, trim: true, enum: Object.values(visibilityTypes) },
  preferredCountryOfStudy: {
    type: String,
    trim: true,
    enum: Object.values(visibilityTypes),
  },
  preferredDegreeOfStudy: {
    type: String,
    trim: true,
    enum: Object.values(visibilityTypes),
  },
  interestedInScholarship: {
    type: String,
    trim: true,
    enum: Object.values(visibilityTypes),
  },
  basicInformation: {
    type: String,
    trim: true,
    enum: Object.values(visibilityTypes),
  },
  education: {
    type: String,
    trim: true,
    enum: Object.values(visibilityTypes),
  },
  certification: {
    type: String,
    trim: true,
    enum: Object.values(visibilityTypes),
  },
  testscore: {
    type: String,
    trim: true,
    enum: Object.values(visibilityTypes),
  },
  experience: {
    type: String,
    trim: true,
    enum: Object.values(visibilityTypes),
  },
  volunteering: {
    type: String,
    trim: true,
    enum: Object.values(visibilityTypes),
  },
  project: {
    type: String,
    trim: true,
    enum: Object.values(visibilityTypes),
  },
  award: {
    type: String,
    trim: true,
    enum: Object.values(visibilityTypes),
  },

  // notifications settings
  followNotification: { type: String, default: true },
  joinGroupRequestNotification: { type: Boolean, default: true },
  acceptGroupRequestNotification: { type: Boolean, default: true },
  rejectGroupRequestNotification: { type: Boolean, default: true },
  repostNotification: { type: Boolean, default: true },
  likeNotification: { type: Boolean, default: true },
  reactionNotification: { type: Boolean, default: true },
  commentNotification: { type: Boolean, default: true },
  commentOfCommentNotification: { type: Boolean, default: true },
  messageNotification: { type: Boolean, default: true },
  // add to new model
  groupMessageNotification: { type: Boolean, default: true },
  receiveScholarshipNewsletter: { type: Boolean, default: true },
});

settingSchema.plugin(toJSON);
settingSchema.plugin(paginate);

const Setting = mongoose.model('Setting', settingSchema);

module.exports = Setting;
