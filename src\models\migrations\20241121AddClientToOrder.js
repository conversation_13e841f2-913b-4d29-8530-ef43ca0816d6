/* eslint-disable no-param-reassign */
const Async = require('async');
const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const Order = require('../order.model');

const varName = 'Add client to order';

const addClientToOrder = async () => {
  const migrated = await GlobalVariable.findOne({ name: varName });

  if (migrated) {
    return;
  }

  const orders = await Order.find().populate([
    {
      path: 'transaction',
      select: 'account',
      populate: { path: 'account', select: 'user' },
    },
    {
      path: 'service',
      select: 'provider',
    },
  ]);

  await Async.eachOfSeries(orders, async (order) => {
    order.client = order.transaction.account.user;
    if (!order.provider) {
      order.provider = order.service.provider;
      logger.info(`Order ${order._id} has no provider`);
    }

    await order.save();
  });

  await GlobalVariable.create({ name: varName, value: 'true' });
  logger.info('Added client to existing orders.');
};

module.exports = addClientToOrder;
