const httpStatus = require('http-status');
const { universityService } = require('../services');
const catchAsync = require('../utils/catchAsync');
const loadUniversityEnums = require('../utils/webScraper/loadUniversityEnums');
const { pick } = require('../utils/pick');

const getUniversityById = catchAsync(async (req, res) => {
  const university = await universityService.getUniversityById(req);

  if (!university) {
    return res.status(httpStatus.BAD_REQUEST).json({ message: 'University record not found', status: 'FAILED' });
  }

  return res
    .status(httpStatus.OK)
    .json({ data: university, message: 'User record retrieved successfully', status: 'SUCCESS' });
});

const getUniversityNames = catchAsync(async (req, res) => {
  const filter = {};
  if (req.query.searchText) filter['name.value'] = { $regex: req.query.searchText, $options: 'i' };

  const options = pick(req.query, ['sortBy', 'limit', 'page']);

  const universities = await universityService.getUniversityNames(filter, options);

  return res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data: universities, message: 'University names retrieved successfully' });
});

const getUniversityEnums = catchAsync(async (req, res) => {
  const enums = await loadUniversityEnums();

  return res.status(httpStatus.OK).json({ status: 'SUCCESS', data: enums, message: 'Filter Enums retrieved successfully' });
});

const getUniversities = catchAsync(async (req, res) => {
  const data = await universityService.getUniversities(req);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data, message: 'Universities retrieved successfully' });
});

const saveUserUniversity = catchAsync(async (req, res) => {
  const response = await universityService.saveUserUniversity(req?.params?.id, req.user);
  res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data: response, message: `University ${response.saved ? 'saved' : 'unsaved'} successfully` });
});

const hideUniversityFromUser = catchAsync(async (req, res) => {
  const response = await universityService.hideUniversityFromUser(req?.params?.id, req.user);
  res.status(httpStatus.OK).json({
    status: 'SUCCESS',
    data: response,
    message: `University is successfully ${response.hidden ? 'hidden' : 'unhidden'} for you`,
  });
});

const markAsApplied = catchAsync(async (req, res) => {
  const response = await universityService.markAsApplied(req?.params?.id, req.user);
  res.status(httpStatus.OK).json({
    status: 'SUCCESS',
    data: response,
    message: `University is successfully ${response.applied ? 'marked' : 'unmarked'} as applied`,
  });
});

module.exports = {
  getUniversities,
  saveUserUniversity,
  markAsApplied,
  getUniversityById,
  hideUniversityFromUser,
  getUniversityEnums,
  getUniversityNames,
};
