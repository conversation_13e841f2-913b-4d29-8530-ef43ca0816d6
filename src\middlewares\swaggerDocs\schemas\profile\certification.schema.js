const certificationSchemaSwagger = {
  type: 'object',
  properties: {
    id: { type: 'string', example: '6009c0eee65f6dce28fb3e50' },
    certName: {
      type: 'string',
      description: 'Name of the certification',
      example: 'Certified Professional Programmer',
    },
    issuingOrganization: {
      type: 'string',
      description: 'Issuing organization of the certification',
      example: 'ABC Certification Institute',
    },
    dateIssued: {
      type: 'string',
      description: 'Date when the certification was issued',
      example: '2023-11-20',
    },
    certLink: {
      type: 'string',
      description: 'Link to information about the certification',
      example: 'https://example.com/certification-info',
    },
  },
};

module.exports = certificationSchemaSwagger;
