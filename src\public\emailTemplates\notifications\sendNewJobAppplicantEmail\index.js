/* eslint-disable prettier/prettier */
const config = require('../../../../config/config');
const { formatDate } = require('../../../../utils/helperMethods');

module.exports = (details) => {
  // details -> { newJobApplication, careerJob }
  const { newJobApplication, careerJob } = details;
  const department = careerJob.jobCategories.join(', ');
  const careerJobId = careerJob._id;
  const careerJobUrl = `${config.client.baseUrl}/manage-jobs/${careerJobId}`;
  const applicationUrl = `${config.client.baseUrl}/manage-jobs/${careerJobId}/${newJobApplication._id}`;
  const applicantFullName = `${newJobApplication?.firstName} ${newJobApplication?.lastName}`;
  const applicantEmail = newJobApplication?.email;


  const html = `
      <!DOCTYPE html
      PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
      <html xmlns="http://www.w3.org/1999/xhtml" lang="en">

      <head>
        <meta http-equiv="Content-Type" content="text/html" charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@300;400;500;600;700;800&display=swap"
          rel="stylesheet">
        <title>New Job Application Received</title>
      </head>

      <body style="margin: 0;font-family: 'Plus Jakarta Sans', sans-serif;background: #f6f9fc; border-radius: 4px">
        <center class="wrapper" style="width: 100%; table-layout: fixed;">
          <div class="webkit">
            <table class="outer" align="center"
              style="width: 100%; max-width: 600px; border-spacing: 0; line-height: 1.65rem; color: #4a4a4a; margin: 0 auto;">
              <!-- Header -->
              <tr>
                <td style="text-align: center; border-bottom: 2px solid rgb(243 244 246);">
                  <center>
                    <a href="${config.client.baseUrl}" style="text-decoration:none;font-weight: bolder;padding: 15px;background: #f6f9fc;">
                      <img src="https://res.cloudinary.com/emmii/image/upload/v1696345178/unyked/sv1zvadt6si3uwkevzki.png" width="100px" alt="" style="border: 0;padding: 15px;">
                    </a>
                  </center>
                </td>
              </tr>

              <!-- Main Content -->
              <tr>
                <td style="background: #f6f9fc; padding: 0;">
                  <table style="width: 100%; border-spacing: 0;">
                    <tr>
                      <td style="padding: 30px 20px 0 20px; text-align: center;">
                        <h1 style="margin: 0; font-size: 24px; color: #2a2a2a;">New Job Application Received</h1>
                        <p style="font-size: 16px; color: #4a4a4a; margin: 10px 0 30px 0;">A candidate has applied for the
                          position below</p>
                      </td>
                    </tr>

                    <!-- Alert Banner -->
                    <tr>
                      <td style="">
                        <table
                          style="width: 100%; border-spacing: 0; background: #cdedff; border-left: 4px solid #3772ff; border-radius: 4px;">
                          <tr>
                            <td style="padding: 12px; font-size: 0.875rem; color: #3772ff; font-weight: 500;">
                              🎯 New applicant received at ${formatDate(newJobApplication.createdAt)}
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>

                    <!-- Job Details -->
                    <tr>
                      <td style="">
                        <table style="width: 100%; border-spacing: 0; background: white; border-radius: 8px;">
                          <tr>
                            <td style="padding: 16px;">
                              <h2 style="margin: 0 0 8px 0; font-size: 1.1rem; color: #3772ff;">${careerJob.title}</h2>

                              <table style="width: 100%; border-spacing: 0; margin-bottom: 8px;">
                                <tr>
                                  <td style="width: 50%; padding: 0; vertical-align: top;">
                                    <p style="margin: 0 0 4px 0; font-size: 0.8rem; color: #666; font-weight: 500;">
                                      Department:</p>
                                    <p style="margin: 0; font-size: 0.9rem; color: #2a2a2a;">${department}</p>
                                  </td>
                                  <td style="width: 50%; padding: 0; vertical-align: top;">
                                    <p style="margin: 0 0 4px 0; font-size: 0.8rem; color: #666; font-weight: 500;">Location:
                                    </p>
                                    <p style="margin: 0; font-size: 0.9rem; color: #2a2a2a;">${careerJob.location}</p>
                                  </td>
                                </tr>
                              </table>

                              <table style="width: 100%; border-spacing: 0;">
                                <tr>
                                  <td style="padding: 0; text-align: right; vertical-align: bottom;">
                                    <a href="${careerJobUrl}"
                                      style="font-size: 0.8rem; color: #3772ff; font-weight: 600; text-decoration: none;">View
                                      Job Posting →</a>
                                  </td>
                                </tr>
                              </table>
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>

                    <!-- Applicant Details -->
                    <tr>
                      <td style="">
                        <table style="width: 100%; border-spacing: 0; background: white; border-radius: 8px;">
                          <tr>
                            <td style="padding: 16px;">
                              <h3 style="margin: 0 0 12px 0; font-size: 1rem; color: #2a2a2a;">Applicant Information</h3>

                              <table style="width: 100%; border-spacing: 0; margin-bottom: 12px;">
                                <tr>
                                  <td style="width: 50%; padding: 0 10px 0 0; vertical-align: top;">
                                    <p style="margin: 0 0 4px 0; font-size: 0.8rem; color: #666; font-weight: 500;">Full Name:
                                    </p>
                                    <p style="margin: 0; font-size: 0.9rem; color: #2a2a2a;">${applicantFullName}</p>
                                  </td>
                                  <td style="width: 50%; padding: 0; vertical-align: top;">
                                    <p style="margin: 0 0 4px 0; font-size: 0.8rem; color: #666; font-weight: 500;">Email:</p>
                                    <p style="margin: 0; font-size: 0.9rem; color: #2a2a2a;">${applicantEmail}</p>
                                  </td>
                                </tr>
                              </table>

                              <table style="width: 100%; border-spacing: 0; margin-bottom: 12px;">
                                <tr>
                                  <td style="width: 50%; padding: 0; vertical-align: top;">
                                    <p style="margin: 0 0 4px 0; font-size: 0.8rem; color: #666; font-weight: 500;">Applied
                                      On:</p>
                                    <p style="margin: 0; font-size: 0.9rem; color: #2a2a2a;">${formatDate(newJobApplication.createdAt)}</p>
                                  </td>
                                </tr>
                              </table>

                              <table style="width: 100%; border-spacing: 0; margin-top: 12px;">
                                <tr>
                                  <td style="padding: 0; text-align: center;">
                                    <a href="${applicationUrl}"
                                      style="padding: 8px 16px; background: #3772ff; border: none; font-weight: bold; color: white; border-radius: 4px; font-size: 0.875rem; text-decoration: none; display: inline-block;">View
                                      Full Application</a>
                                  </td>
                                </tr>
                              </table>
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>

              <!-- Footer -->
              <tr>
                <td style="padding: 20px; background: #f6f9fc; text-align: center;">
                  <p style="font-size: 0.875rem; color: #666; margin: 0 0 4px;">You're receiving this email because you're
                    listed as an administrator.</p>
                </td>
              </tr>

              <tr>
                <td style="height: 10px; background-color: #d7e3ff; padding: 0; border-radius: 0 0 10px 10px;">
                  <p style="font-size: smaller; padding: 30px 20px;">&copy;2025 UnykEd Inc.</p>
                </td>
              </tr>
            </table>
          </div>
        </center>
      </body>

      </html>
  `;
  return html;
};
