const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');

const activityLogSchema = new mongoose.Schema({
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  text: { type: String, required: true },
  timestamp: { type: Date, default: Date.now },
});

activityLogSchema.index({ timestamp: 1 });

activityLogSchema.plugin(toJSON);
activityLogSchema.plugin(paginate);

const ActivityLog = mongoose.model('ActivityLog', activityLogSchema);
module.exports = ActivityLog;
