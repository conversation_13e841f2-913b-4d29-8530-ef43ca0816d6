const stopword = require('stopword');
const httpStatus = require('http-status');
const { Post, User, Group, ForumPost, ForumReply, File, Language, Service } = require('../models');
const { wordsArrayToFilterArray } = require('./university.service');
const businessService = require('./business.service');
const scholarshipService = require('./scholarship.service');
const fileService = require('./file.service');
const emailService = require('./email.service');
const serviceService = require('./service.service');
const { registrationStatuses } = require('../config/constants');
const validateId = require('./shared/validateId');
const ApiError = require('../utils/ApiError');
const { postOptions } = require('./post.service');

const search = async (userParam, filter, options) => {
  const searchText = stopword.removeStopwords((filter.searchText || '').split(' ')); // Remove stopwords e.g 'of', 'the', 'a', 'an' etc

  let result;
  if (filter.tab === 'posts') {
    const subFilter = {
      // isEmailVerified: true,
      pendingStudent: 'false',
      locked: false,
      registrationStatus: { $nin: [registrationStatuses.SIGNUP_COMPLETED, registrationStatuses.ACCOUNT_VERIFIED] },
    };

    if (searchText.length > 0) {
      subFilter.$and = searchText.map((item) => ({
        $or: ['firstName', 'lastName', 'middleName', 'username'].map((field) => ({
          [field]: { $regex: item, $options: 'i' },
        })),
      }));
    }

    const users = (await User.find(subFilter)).map((user) => user._id);

    result = await Post.paginate(
      {
        group: null,
        $or: [
          { $and: wordsArrayToFilterArray(searchText, 'text') },
          {
            user: { $in: users },
          },
        ],
      },
      {
        ...options,
        ...postOptions,
      },
    );
  } else if (filter.tab === 'users') {
    const subFilter = {
      isEmailVerified: true,
      locked: false,
      registrationStatus: { $nin: [registrationStatuses.SIGNUP_COMPLETED, registrationStatuses.ACCOUNT_VERIFIED] },
    };

    if (searchText.length > 0) {
      subFilter.$and = searchText.map((item) => ({
        $or: ['firstName', 'lastName', 'middleName', 'username'].map((field) => ({
          [field]: { $regex: item, $options: 'i' },
        })),
      }));
    } else {
      subFilter.$or = [{ firstName: { $ne: null } }, { lastName: { $ne: null } }];
    }

    result = await User.paginate(subFilter, {
      ...options,
      sortBy: 'firstName:asc lastName:asc',
      select:
        'firstName lastName middleName _id photo tagLine online followers following username countryOfResidence stateOfResidence',
      populate: { path: 'photo', select: 'url' },
    });
  } else if (filter.tab === 'groups') {
    const subFilter = { type: 'public' };

    if (searchText.length > 0) {
      const $or = [];
      ['name', 'description', '$elemMatch'].forEach((field) => {
        const filterArray = wordsArrayToFilterArray(searchText, field);
        if (field === '$elemMatch') {
          $or.push({ $and: filterArray.map((item) => ({ rules: item })) });
        } else {
          $or.push({ $and: filterArray });
        }
      });
      subFilter.$or = $or;
    }

    result = await Group.paginate(subFilter, {
      ...options,
      populate: { path: 'profilePhoto', select: 'url' },
    });
  } else if (filter.tab === 'forum_posts') {
    const subFilter1 = {};
    if (searchText.length > 0) {
      subFilter1.$or = [...wordsArrayToFilterArray(searchText, 'text')];
    }

    const forumReplies = await ForumReply.find({
      subFilter1,
    });

    const subFilter = { replies: { $in: forumReplies.map((item) => item._id) } };

    if (searchText.length > 0) {
      const $or = [];
      ['topic', 'text', 'category', '$elemMatch'].forEach((field) => {
        const filterArray = wordsArrayToFilterArray(searchText, field);
        if (field === '$elemMatch') {
          $or.push({ $and: filterArray.map((item) => ({ rules: item })) });
        } else {
          $or.push({ $and: filterArray });
        }
      });
      subFilter.$or = $or;
    }

    result = await ForumPost.paginate(subFilter, options);
  } else if (filter.tab === 'services') {
    // result = await serviceService.getServices(userParam, { searchText: filter.searchText || null }, options, {});
    const subFilter = {};

    if (filter.searchText) {
      const searchFields = ['name', 'tags', 'category'];
      const searchConditions = searchFields.flatMap((field) => wordsArrayToFilterArray(searchText, field));

      subFilter.$or = searchConditions;
    }

    result = await Service.paginate(subFilter, { ...options, populate: serviceService.servicePopulate(userParam) });
  } else if (filter.tab === 'businesses') {
    const subFilter = {};
    if (filter.searchText) {
      subFilter.name = filter.searchText;
    }

    result = await businessService.getBusinesses(subFilter, options);
  } else if (filter.tab === 'scholarships') {
    const subFilter = { tab: 'discover' };
    if (filter.searchText) {
      subFilter.name = filter.searchText;
    }
    result = await scholarshipService.getScholarships(userParam, subFilter, options);
  }

  return result;
};

const report = async (messageBody, userId) => {
  await emailService.sendReportUsEmail(messageBody, userId);
};

const searchUsers = async (searchTerm, options) => {
  const searchText = stopword.removeStopwords(searchTerm.split(' '));

  const filter = {
    isEmailVerified: true,
    locked: false,
    registrationStatus: { $nin: [registrationStatuses.SIGNUP_COMPLETED, registrationStatuses.ACCOUNT_VERIFIED] },
  };

  if (searchText.length > 0) {
    filter.$and = searchText.map((item) => ({
      $or: ['firstName', 'lastName', 'middleName', 'username'].map((field) => ({
        [field]: { $regex: item, $options: 'i' },
      })),
    }));
  }

  const users = await User.paginate(filter, {
    ...options,
    select: 'firstName lastName middleName username _id photo tagLine',
    populate: { path: 'photo', select: 'url' },
  });
  return users;
};

const generateSasUrl = async (fileId) => {
  const fileIds = Array.isArray(fileId) ? fileId : [fileId];
  fileIds.map((id) => validateId(id, 'File'));

  const urls = await Promise.all(
    fileIds.map(async (id) => {
      const file = await File.findById(id);
      if (!file) {
        throw new ApiError(httpStatus.NOT_FOUND, 'File not found');
      }
      const sasUrl = await fileService.generateSasUrlHelper(file);
      return sasUrl;
    }),
  );

  return urls.length === 1 ? urls[0] : urls;
};

const userAnalytics = async (query) => {
  // Send data about user signup, login, and other activities
  const { startDate, endDate, groupingFormat } = query;

  const format = { yearly: '%Y', monthly: '%Y-%m', daily: '%Y-%m-%d' }[groupingFormat];
  const dateParam = { lastLogin: '$lastLogin', signUp: '$createdAt' };

  const grouping = (funcFor) =>
    groupingFormat === 'weekly'
      ? [
          {
            $group: {
              _id: {
                year: { $isoWeekYear: '$createdAt' },
                week: { $isoWeek: '$createdAt' },
              },
              count: { $sum: 1 },
            },
          },
          {
            $project: {
              _id: 0,
              date: {
                $concat: [
                  { $toString: '$_id.year' },
                  ' - W',
                  {
                    $cond: {
                      if: { $lt: ['$_id.week', 10] },
                      then: { $concat: ['0', { $toString: '$_id.week' }] },
                      else: { $toString: '$_id.week' },
                    },
                  },
                ],
              },
              count: 1,
            },
          },
        ]
      : [
          {
            $group: {
              _id: {
                $dateToString: { format, date: dateParam[funcFor] },
              },
              count: { $sum: 1 },
            },
          },
          {
            $project: {
              _id: 0,
              date: '$_id',
              count: 1,
            },
          },
        ];

  const signupCount = await User.aggregate([
    {
      $match: {
        createdAt: { $gte: new Date(startDate), $lte: new Date(endDate) },
      },
    },
    ...grouping('signUp'),
    { $sort: { date: 1 } },
  ]);

  const loginCount = await User.aggregate([
    {
      $match: {
        lastLogin: { $gte: new Date(startDate), $lte: new Date(endDate) },
      },
    },

    ...grouping('lastLogin'),
    { $sort: { date: 1 } },
  ]);

  const data = {
    signupCount,
    loginCount,
  };

  return { ...data, totalUsers: await User.countDocuments() };
};

const getLanguages = async (filterParam, options) => {
  const filter = { ...filterParam };
  if (filter.name) {
    const name = stopword.removeStopwords((filter.name || '').split(' '));
    filter.$or = [
      { name: { $regex: name.join('|'), $options: 'i' } },
      { native: { $regex: name.join('|'), $options: 'i' } },
      { code: { $regex: name.join('|'), $options: 'i' } },
    ];
    delete filter.name;
  }

  const languages = await Language.paginate(filter, options);
  return languages;
};

module.exports = {
  search,
  report,
  searchUsers,
  generateSasUrl,
  userAnalytics,
  getLanguages,
};
