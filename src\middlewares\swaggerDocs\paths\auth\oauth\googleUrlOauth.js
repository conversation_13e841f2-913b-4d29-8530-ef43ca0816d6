module.exports = {
  get: {
    summary: 'Get Google OAuth URL',
    description: 'Returns the URL for initiating Google OAuth authentication',
    tags: ['Authentication'],
    responses: {
      200: {
        description: 'Successful operation',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                url: {
                  type: 'string',
                  format: 'url',
                  example:
                    'https://accounts.google.com/o/oauth2/v2/auth?redirect_uri=https%3A%2F%2Fyourwebsite.com%2Foauth%2Fgoogle-callback&client_id=your_client_id&access_type=offline&response_type=code&prompt=consent&scope=https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.profile%20https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.email',
                },
              },
            },
          },
        },
      },
    },
  },
};
