const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');

const commentSchema = new mongoose.Schema(
  {
    text: {
      type: String,
      required: true,
      index: true,
    },
    post: {
      type: mongoose.SchemaTypes.ObjectId,
      ref: 'Post',
      required: true,
    },
    parentComment: {
      type: mongoose.SchemaTypes.ObjectId,
      ref: 'Comment',
    },
    user: {
      type: mongoose.SchemaTypes.ObjectId,
      ref: 'User',
      required: true,
    },
    likes: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
    reactions: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Reaction' }],
    replies: [
      {
        type: mongoose.SchemaTypes.ObjectId,
        ref: 'Comment',
      },
    ],
    media: [
      {
        type: mongoose.SchemaTypes.ObjectId,
        ref: 'File',
      },
    ],
  },
  {
    timestamps: true,
  },
);

commentSchema.index({ createdAt: 1, updatedAt: 1 });

// add plugin that converts mongoose to json
commentSchema.plugin(toJSON);
commentSchema.plugin(paginate);

const Comment = mongoose.model('Comment', commentSchema);

module.exports = Comment;
