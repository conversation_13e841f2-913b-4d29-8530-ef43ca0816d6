const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const { currencies, careerJobsEnums } = require('../config/constants');

const careerJobsSchema = new mongoose.Schema(
  {
    title: { type: String, required: true, trim: true },
    location: { type: String, required: true, trim: true, enum: careerJobsEnums.careerJobLocations },
    description: { type: String, required: true, trim: true },
    // responsibilities: { type: String, required: true, trim: true },
    // requirements: { type: String, required: true },
    // benefits: { type: String, trim: true },
    // skills: { type: String, trim: true },
    applicationEndDate: { type: Date },
    rollingApplication: { type: Boolean, default: false },
    currency: { type: String, enum: Object.values(currencies) },
    jobType: { type: String, enum: Object.values(careerJobsEnums.careerJobType) },
    jobCategories: [{ type: String, enum: Object.values(careerJobsEnums.careerJobCategories) }],
    jobMode: { type: String, enum: Object.values(careerJobsEnums.careerJobMode) },
    proposedAnnualSalary: { type: String, default: null },
    publishStatus: { type: String, enum: Object.values(careerJobsEnums.careerJobPublishStatus) },
    applications: [{ type: mongoose.Schema.Types.ObjectId, ref: 'JobApplication' }],
    applicationStatus: {
      rejected: [{ type: mongoose.Schema.Types.ObjectId, ref: 'JobApplication' }],
      inReview: [{ type: mongoose.Schema.Types.ObjectId, ref: 'JobApplication' }],
      interview: [{ type: mongoose.Schema.Types.ObjectId, ref: 'JobApplication' }],
      hired: [{ type: mongoose.Schema.Types.ObjectId, ref: 'JobApplication' }],
    },
    publishedDate: { type: Date, default: null },
  },
  { timestamps: true },
);

careerJobsSchema.index({
  title: 'text',
  location: 'text',
  description: 'text',
  responsibilities: 'text',
  requirements: 'text',
  benefits: 'text',
  skills: 'text',
  currency: 'text',
  createdAt: 1,
  updatedAt: 1,
});

careerJobsSchema.plugin(toJSON);
careerJobsSchema.plugin(paginate);

const CareerJobs = mongoose.model('CareerJobs', careerJobsSchema);
module.exports = CareerJobs;
