module.exports = {
  path: {
    summary: 'Update group settings',
    tags: ['Groups'],
    parameters: [
      {
        in: 'path',
        name: 'id',
        schema: {
          type: 'string',
          example: '5f9d1e9a6f3f4b001f6d4c0b',
        },
        description: 'ID of the group to update settings for',
      },
    ],
    requestBody: {
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              messages: {
                type: 'string',
                description: 'Messages setting',
                example: 'admin',
              },
              posts: {
                type: 'string',
                description: 'Posts setting',
                example: 'all',
              },
            },
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Group setting successfully updated',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Group setting successfully updated',
            },
          },
        },
      },
    },
  },
};
