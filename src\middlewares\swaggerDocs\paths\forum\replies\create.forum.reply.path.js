module.exports = {
  post: {
    summary: 'Create a forum post reply',
    tags: ['Forum Replies'],
    consumes: ['multipart/form-data'],
    parameters: [
      {
        name: 'id',
        in: 'path',
        description: 'ID of the forum post',
        required: true,
        type: 'string',
        format: 'objectId',
      },
      {
        in: 'formData',
        name: 'files',
        type: 'string',
        format: 'base64',
        required: false,
        description: 'Array of files to upload (optional)',
      },
      {
        in: 'formData',
        name: 'body',
        required: true,
        description: 'Forum reply data',
        schema: {
          type: 'object',
          properties: {
            text: { type: 'string', example: 'This is a reply text' },
          },
        },
      },
    ],
    responses: {
      201: {
        description: 'Forum post reply created successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string', example: 'SUCCESS' },
                message: { type: 'string', example: 'Forum post reply created successfully' },
              },
            },
          },
        },
      },
      400: {
        description: 'Bad request - Missing required fields',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string', example: 'FAILED' },
                message: { type: 'string', example: 'Forum Post Reply must have text' },
              },
            },
          },
        },
      },
      404: {
        description: 'Forum post not found',
        schema: {
          type: 'object',
          properties: {
            status: { type: 'string', example: 'FAILED' },
            message: { type: 'string', example: 'Forum post not found' },
          },
        },
      },
      500: {
        description: 'Internal Server Error',
        schema: {
          type: 'object',
          properties: {
            status: { type: 'string', example: 'ERROR' },
            message: { type: 'string', example: 'Error creating forum post reply' },
          },
        },
      },
    },
  },
};
