const experienceSchemaSwagger = {
  type: 'object',
  properties: {
    id: { type: 'string', example: '6009c0eee65f6dce28fb3e50' },
    title: {
      type: 'string',
      description: 'Title of the experience',
      example: 'Software Developer Intern',
    },
    companyName: {
      type: 'string',
      description: 'Name of the company',
      example: 'TechCorp Inc.',
    },
    start: {
      type: 'string',
      description: 'Start date of the experience',
      example: '2021-01-01',
    },
    end: {
      type: 'string',
      description: 'End date of the experience (if applicable)',
      example: '2021-06-30',
    },
    current: {
      type: 'boolean',
      description: 'Indicates if the experience is current or ongoing',
      example: false,
    },
    description: {
      type: 'string',
      description: 'Description of the experience',
      example: 'Developed web applications using React.js',
    },
  },
};

module.exports = experienceSchemaSwagger;
