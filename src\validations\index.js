module.exports.activityLogValidation = require('./activity.log.validation');
module.exports.authValidation = require('./auth.validation');
module.exports.businessValidation = require('./business.validation');
module.exports.blogPostValidation = require('./blog.post.validation');
module.exports.botRecordValidation = require('./bot.record.validation');
module.exports.careerJobValidation = require('./career.jobs.validation');
module.exports.commentValidation = require('./comment.validation');
module.exports.countryValidation = require('./country.validation');
module.exports.feedbackValidation = require('./feedback.validation');
module.exports.forumValidation = require('./forum.validation');
module.exports.groupValidation = require('./group.validation');
module.exports.globalValidation = require('./global.validation');
module.exports.messageValidation = require('./message.validation');
module.exports.notificationValidation = require('./notification.validation');
module.exports.orderValidation = require('./order.validation');
module.exports.postValidation = require('./post.validation');
module.exports.profileValidation = require('./profile.validation');
module.exports.premiumSubscriberValidation = require('./premium.subscriber.validation');
module.exports.resourceLibraryValidation = require('./resource.library.validation');
module.exports.scholarshipValidation = require('./scholarship.validation');
module.exports.serviceValidation = require('./service.validation');
module.exports.settingValidation = require('./setting.validation');
module.exports.schoolValidation = require('./school.validation');
module.exports.subscriberValidation = require('./susbcriber.validation');
module.exports.issueReportValidation = require('./issue.report.validation');
module.exports.transactionValidation = require('./transaction.validation');
module.exports.universityValidation = require('./university.validation');
module.exports.userValidation = require('./user.validation');
