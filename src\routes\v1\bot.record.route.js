const express = require('express');

const router = express.Router();
const { auth } = require('../../middlewares/auth');
const validate = require('../../middlewares/validate');
const { botRecordController: <PERSON>tRecordController } = require('../../controllers');
const { botRecordValidation: BotRecordValidation } = require('../../validations');

router.use(auth());

router.post('/', validate(BotRecordValidation.createBotRecord), BotRecordController.createBotRecord);
router.get('/', validate(BotRecordValidation.getBotRecords), BotRecordController.getBotRecords);

module.exports = router;
