const express = require('express');

const router = express.Router();
const { auth } = require('../../middlewares/auth');
const { blogPostController } = require('../../controllers');
const validate = require('../../middlewares/validate');
const { blogPostValidation } = require('../../validations');
const { uploadBlogFiles } = require('../../config/multer.config');

router.get('/stats', auth('manageBlogPosts'), blogPostController.getStats);

router.get('/', validate(blogPostValidation.getBlogPosts), blogPostController.getBlogPosts);
router.get('/categories', blogPostController.getCategories);
router.get('/:uniqueTitle', blogPostController.getBlogPostByUniqueTitle);
router.post('/:id/react', validate(blogPostValidation.react), blogPostController.react);

router.use(auth('manageBlogPosts'));

router.post('/', uploadBlogFiles, validate(blogPostValidation.createBlogPost), blogPostController.createBlogPost);
router.patch('/:id', uploadBlogFiles, validate(blogPostValidation.updateBlogPost), blogPostController.updateBlogPost);
router.patch(
  '/:id/update-status',
  validate(blogPostValidation.updateBlogPostStatus),
  blogPostController.updateBlogPostStatus,
);
router.delete('/:id', blogPostController.deleteBlogPost);

module.exports = router;
