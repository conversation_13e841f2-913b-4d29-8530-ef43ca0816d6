const httpStatus = require('http-status');
const Async = require('async');
const ApiError = require('../utils/ApiError');
const { ForumPost, ForumReply, File } = require('../models');
const { uploadAndSaveMultipleBlobs, deleteManyFiles } = require('./azure.file.service');
const { toggleVoteReply } = require('./shared');
const { azureContainers } = require('../config/constants');

const findForumPost = async (forumPostId) => {
  const forumPost = await ForumPost.findById(forumPostId);
  if (!forumPost) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Forum post record not found');
  }
  return forumPost;
};

const createForumReply = async (data, files) => {
  const forumPost = await findForumPost(data.forumPost);
  if (!data.text) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Forum Post Reply must have text');
  }
  try {
    const fileIds = files ? await uploadAndSaveMultipleBlobs(files, azureContainers.forums) : [];

    const forumReplyBody = { ...data, media: fileIds };
    const createdForumReply = await ForumReply.create(forumReplyBody);
    forumPost.replies.push(createdForumReply._id);
    await forumPost.save();

    return createdForumReply;
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error creating forum post reply');
  }
};

const queryReplies = async (filter, options) => {
  const replies = await ForumReply.paginate(filter, {
    ...options,
    populate: [
      {
        path: 'user',
        select: 'firstName lastName middleName username photo _id',
        populate: { path: 'photo', select: 'url -_id' },
      },
      { path: 'media', select: 'url -_id' },
    ],
  });
  return replies;
};

const getReplyById = async (id) => {
  const forumReply = await ForumReply.findById(id).populate([
    { path: 'media', select: 'url -_id' },
    {
      path: 'user',
      select: 'firstName lastName middleName username photo _id',
      populate: { path: 'photo', select: 'url -_id' },
    },
  ]);
  if (!forumReply) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Reply record not found');
  }
  return forumReply;
};

const updateForumReply = async (req) => {
  const { files, resourceRecord, body: updateBody } = req;
  const forumReply = resourceRecord || (await ForumReply.findById(req.params.id));

  if (!forumReply) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Reply record not found');
  }

  // check if answer is accepted: default is false
  // if forum post is solved, reject request to accept reply
  // else accept the reply and toggle the the forumPost to solved
  const forumPost = await findForumPost(req?.params?.forumPostId);
  if (forumPost.solved && updateBody.accepted) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'A reply has already been accepted and the question already solved.');
  } else {
    forumPost.solved = true;
    await forumPost.save();
  }

  if (updateBody.deletedUrls) {
    await Async.eachOfSeries(updateBody.deletedUrls.split(','), async (url) => {
      // eslint-disable-next-line no-useless-escape
      const urlExtract = url.match(/^(https?:\/\/[^\/\s]+\/[^\?\s]*)/)[0].replace(/\/$/, '');
      const file = await File.findOne({ url: urlExtract });
      if (file) {
        forumReply.media.pull(file._id);
        file.remove();
      }
    });
  }

  try {
    if (files && files.length > 0) {
      const fileIds = await uploadAndSaveMultipleBlobs(files, azureContainers.forums);
      updateBody.media = [...forumReply.media, ...fileIds];
    }

    Object.assign(forumReply, updateBody);
    await forumReply.save();
    return forumReply;
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error updating forum post');
  }
};

const voteAReply = async (vote, forumReplyId, userId) => {
  const voteType = vote.upvote ? 'upvote' : vote.downvote && 'downvote';

  const forumReply = await toggleVoteReply(ForumReply, forumReplyId, userId, voteType);
  return forumReply;
};

const deleteForumReply = async (forumPostId, forumReplyId) => {
  const forumPost = await findForumPost(forumPostId);
  const forumReply = await ForumReply.findOne({ _id: forumReplyId, forumPost: forumPostId }).exec();
  if (!forumReply) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Reply record not found');
  }

  if (forumReply.media && forumReply.media.length > 0) {
    await deleteManyFiles(forumReply.media);
  }
  await forumReply.remove();
  forumPost.replies = forumPost.replies.filter((reply) => reply !== forumReplyId);
  await forumPost.save();
  return forumReply;
};

module.exports = {
  createForumReply,
  queryReplies,
  getReplyById,
  updateForumReply,
  deleteForumReply,
  voteAReply,
};
