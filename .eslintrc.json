{"env": {"node": true, "jest": true}, "extends": ["airbnb-base", "plugin:jest/recommended", "plugin:security/recommended", "plugin:prettier/recommended"], "plugins": ["jest", "security", "prettier"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {"no-console": "error", "func-names": "off", "no-underscore-dangle": "off", "consistent-return": "off", "jest/expect-expect": "off", "security/detect-object-injection": "off", "import/no-cycle": "error"}}