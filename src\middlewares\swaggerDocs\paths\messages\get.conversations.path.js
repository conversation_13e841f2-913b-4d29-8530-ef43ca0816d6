module.exports = {
  get: {
    summary: 'Get all conversations',
    description: 'Get all conversations of the user',
    tags: ['Messages'],
    responses: {
      200: {
        description: 'Conversations retrieved successfully',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              data: [
                {
                  messages: ['6593f5d89c1abf43554a1597', '6593f5df9c1abf43554a15a7'],
                  participants: [
                    {
                      firstName: '<PERSON><PERSON>',
                      lastName: '<PERSON>',
                      username: 'char<PERSON><PERSON><PERSON><PERSON>',
                      photo: null,
                      id: '652f6fb971c38a9b93066b9d',
                    },
                  ],
                  directMessage: true,
                  createdAt: '2023-12-13T10:11:56.907Z',
                  updatedAt: '2024-01-02T11:39:11.410Z',
                  id: '6579836cf193b41d63b82b56',
                },
              ],
            },
          },
        },
      },
    },
  },
};
