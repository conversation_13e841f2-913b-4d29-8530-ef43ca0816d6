module.exports = {
  post: {
    summary: 'Make a report',
    tags: ['Global'],
    requestBody: {
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              modelName: {
                type: 'string',
                example: 'ForumPost',
              },
              contentId: {
                type: 'string',
                example: '659fd7434857f088ed7d3bc5',
              },
              uri: {
                type: 'string',
                example: 'www.unyked.com/post/659fd7434857f088ed7d3bc5',
              },
              reason: {
                type: 'string',
                example: 'Spam',
              },
            },
            required: ['contentId', 'modelName'],
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Report successfully sent',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Report successfully sent',
            },
          },
        },
      },
    },
  },
};
