const express = require('express');
const { notificationController: NotificationController } = require('../../controllers');
const validate = require('../../middlewares/validate');
const { notificationValidation: NotificationValidation } = require('../../validations');

const router = express.Router();
const { auth } = require('../../middlewares/auth');

router.use(auth());

router.get('/', validate(NotificationValidation.getNotifications), NotificationController.getNotifications);
// router.patch('/:id', validate(NotificationValidation.updateNotification), NotificationController.updateNotification);
router.get('/unread-count', validate(NotificationValidation.getUnreadCount), NotificationController.getUnreadCount);
router.patch('/:id/toggle-read', validate(NotificationValidation.toggleRead), NotificationController.toggleRead);

module.exports = router;
