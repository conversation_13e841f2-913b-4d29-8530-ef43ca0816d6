const { Country, City, State } = require('../models');
const { pick } = require('../utils/pick');
const validateId = require('./shared/validateId');

const getCountries = async () => {
  const countries = await Country.paginate({}, { select: 'name _id', sortBy: 'name:asc', limit: 1000 });
  return countries;
};

const getStates = async (countryId) => {
  let countryIds = countryId;
  const filter = {};
  filter.$or = [];
  if (countryIds) {
    countryIds = typeof countryIds === 'string' ? [countryIds] : countryIds;
    const validIds = [];
    countryIds.forEach((id) => {
      // If id isn't a valid mongoid, take it to be a name
      if (validateId(id, 'Country', false)) {
        validIds.push(id);
      } else {
        filter.$or.push({ name: { $regex: id, $options: 'i' } });
      }
    });
    if (validIds.length) filter._id = { $in: validIds };
  }

  if (filter.$or.length === 0) delete filter.$or;

  const countries = await Country.find(filter).populate({ path: 'states', select: 'name _id' });
  const states = countries.reduce((acc, country) => acc.concat(country.states), []);

  return { results: states, count: states.length };
};

const getCities = async (req) => {
  let options = pick(req.query, ['sortBy', 'limit', 'page']);
  const filter = {};

  options = {
    ...options,
    sortBy: options.sortBy || 'name: asc',
    select: 'name _id',
  };

  if (req.query.searchText) {
    filter.name = { $regex: req.query.searchText, $options: 'i' };
  }

  if (req.query.country) {
    const subFilter = { $or: [] };
    const validIds = [];
    req.query.country = typeof req.query.country === 'string' ? [req.query.country] : req.query.country;
    req.query.country.forEach((id) => {
      if (validateId(id, 'Country', false)) {
        validIds.push(id);
      } else {
        subFilter.$or.push({ name: { $regex: id, $options: 'i' } });
      }
    });
    if (validIds.length) subFilter._id = { $in: validIds };
    if (filter.$or && !filter.$or.length) delete filter.$or;

    const countries = await Country.find(subFilter).select('states').populate('states');
    const cities = countries.reduce(
      (acc, country) => acc.concat(country.states.reduce((acc2, state) => acc2.concat(state.cities), [])),
      [],
    );
    filter._id = { $in: cities };
  }
  if (req.query.state) {
    const subFilter = { $or: [] };
    const validIds = [];
    req.query.state = typeof req.query.state === 'string' ? [req.query.state] : req.query.state;
    req.query.state.forEach((id) => {
      if (validateId(id, 'State', false)) {
        validIds.push(id);
      } else {
        subFilter.$or.push({ name: { $regex: id, $options: 'i' } });
      }
    });

    if (validIds.length) subFilter._id = { $in: validIds };
    if (!filter?.$or?.length) delete filter.$or;

    const states = await State.find(subFilter).select('cities');
    const cities = states.reduce((acc, state) => acc.concat(state.cities), []);
    filter._id = { $in: cities };
  }

  const cities = await City.paginate(filter, options);
  return cities;
};

module.exports = {
  getStates,
  getCountries,
  getCities,
};
