module.exports = {
  patch: {
    tags: ['Posts'],
    summary: 'Update a post by ID',
    description: 'Update a single post based on the provided post ID and data',
    parameters: [
      {
        in: 'path',
        name: 'id',
        schema: {
          type: 'string',
          example: '507f1f77bcf86cd799439011',
        },
        description: 'ID of the post to update',
        required: true,
      },
      {
        name: 'body',
        in: 'formData',
        description: 'The data to upload',
        required: true,
        schema: {
          type: 'object',
          properties: {
            text: {
              type: 'string',
              description: 'Text content of the post',
              example: 'This is a new post.',
            },
            deletedUrls: {
              type: 'array',
              description: 'The urls of the file files to delete',
              items: {
                type: 'string',
                example: 'https://storagesite/users%2Fphoto/74570fc0.jpg',
              },
            },
            visibility: {
              type: 'string',
              description: 'The visibility of the post',
              example: 'only_me',
              default: 'public',
            },
            tags: {
              type: 'array',
              description: 'The tags for the post',
              items: {
                type: 'string',
                example: 'tag1',
              },
            },
          },
        },
      },
    ],
    responses: {
      200: {
        description: 'Post updated successfully',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              data: {
                repostWithThought: [],
                tags: [],
                user: {
                  username: 'johndoe',
                  photo: {
                    url: 'https://storagesite/users%2Fphoto/74570fc0.jpg',
                  },
                  firstName: 'John O',
                  lastName: 'Doe',
                  tagLine: '',
                  middleName: '',
                  id: '651bb5bf4eb9240327eaaaaa',
                },
                text: 'Updated Visibility 2345',
                media: [
                  {
                    url: 'https://storagesite/users%2Fphoto/74570fc0.jpg',
                    _id: '65bb7f69f2b37ca660a3c05f',
                  },
                ],
                likes: ['65bb7f69f2b37ca660a3c05f', '65bb7f69f2b37ca660a3c05f', '65bb7f69f2b37ca660a3c05f'],
                comments: ['65bb7f69f2b37ca660a3c05f', '65bb7f69f2b37ca660a3c05f', '65bb7f69f2b37ca660a3c05f'],
                reposts: ['65bb7f69f2b37ca660a3c05f', '65bb7f69f2b37ca660a3c05f', '65bb7f69f2b37ca660a3c05f'],
                bookmarks: ['65bb7f69f2b37ca660a3c05f', '65bb7f69f2b37ca660a3c05f', '65bb7f69f2b37ca660a3c05f'],
                visibility: 'only me',
                createdAt: '2024-02-14T14:04:02.231Z',
                updatedAt: '2024-02-14T14:06:05.128Z',
                id: '65ccc85202c7a6b4db5ababab',
              },
              message: 'Post retrieved successfully',
            },
          },
        },
      },
      404: {
        description: 'Record not found',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error404',
            },
          },
        },
      },
      500: {
        description: 'Internal server error',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error500',
            },
          },
        },
      },
    },
  },
};
