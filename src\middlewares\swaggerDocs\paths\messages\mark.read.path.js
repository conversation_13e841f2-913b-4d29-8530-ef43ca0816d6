module.exports = {
  post: {
    summary: 'Mark a message as read',
    description: 'Mark a message or an array of messages as read',
    tags: ['Messages'],
    requestBody: {
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              messageId: {
                type: 'string',
                example: '5f4d5d2e7c213e2c6c9d3e7d',
              },
            },
            required: ['messageId'],
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Messages marked as read',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Messages marked as read',
            },
          },
        },
      },
    },
  },
};
