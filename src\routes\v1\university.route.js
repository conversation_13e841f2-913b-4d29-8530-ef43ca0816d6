const express = require('express');

const router = express.Router();
const { universityValidation } = require('../../validations');
const validate = require('../../middlewares/validate');
const { universityController } = require('../../controllers');
const { auth } = require('../../middlewares/auth');

router.route('/enum').get(universityController.getUniversityEnums);
router.get('/names', validate(universityValidation.getUniversityNames), universityController.getUniversityNames);

router.use(auth());

router.route('/').get(validate(universityValidation.getUniversities), universityController.getUniversities);

router.post('/:id/save', validate(universityValidation.saveUserUniversity), universityController.saveUserUniversity);

router
  .route('/:id/hide')
  .post(validate(universityValidation.hideUniversityFromUser), universityController.hideUniversityFromUser);

router.route('/:id').get(validate(universityValidation.getUniversity), universityController.getUniversityById);

router
  .route('/:id/mark_applied')
  .post(validate(universityValidation.saveUserUniversity), universityController.markAsApplied);

module.exports = router;
