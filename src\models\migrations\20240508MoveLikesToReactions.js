const Async = require('async');
const Comment = require('../comment.model');
const GlobalVariable = require('../global.variable.model');
const Post = require('../post.model');
const User = require('../user.model');
const Reaction = require('../reaction.model');
const Setting = require('../setting.model');
const Message = require('../message.model');
const logger = require('../../config/logger');

const moveLikesToReactions = async () => {
  const migrated = await GlobalVariable.findOne({ name: 'move likes to reactions' });

  if (migrated) {
    return;
  }
  const allPosts = await Post.find({});
  const allComments = await Comment.find({});
  const allSettings = await Setting.find({});
  const allMessages = await Message.find({});

  await Async.eachOfSeries(allPosts, async (post) => {
    if (!post.likes) {
      // eslint-disable-next-line no-param-reassign
      post.likes = undefined;
      await post.save();
      await User.updateOne({ _id: post.user }, { $set: { postLikes: undefined } });
      return;
    }
    await Async.eachOfSeries(post.likes, async (like) => {
      logger.info(`Processing like ${like}`);
      logger.info(like);
      const existingReaction = await Reaction.findOne({
        user: like,
        post: post._id,
      });
      if (!existingReaction) {
        const reaction = await Reaction.create({
          user: like,
          post: post._id,
          type: 'like',
        });

        post.reactions.push(reaction._id);
        await post.save();
      }
      // eslint-disable-next-line no-param-reassign
      await Post.updateOne({ _id: post._id }, { $set: { likes: undefined } });
      await User.updateOne({ _id: like }, { $set: { postLikes: undefined } });
    });
  });

  await Async.eachOfSeries(allComments, async (comment) => {
    if (!comment.likes) {
      // eslint-disable-next-line no-param-reassign
      comment.likes = undefined;
      await comment.save();
      await User.updateOne({ _id: comment.user }, { $set: { commentLikes: undefined } });
      return;
    }
    await Async.eachOfSeries(comment.likes, async (like) => {
      const existingReaction = await Reaction.findOne({
        user: like,
        comment: comment._id,
      });
      if (!existingReaction) {
        const reaction = await Reaction.create({
          user: like,
          comment: comment._id,
          type: 'like',
        });

        comment.reactions.push(reaction._id);
        await comment.save();
      }

      await Comment.updateOne({ _id: comment._id }, { $set: { likes: undefined } });
      await User.updateOne({ _id: like }, { $set: { commentLikes: undefined } });
    });
  });

  await Async.eachOfSeries(allSettings, async (settingParam) => {
    const setting = settingParam;
    if (setting.likeNotification) {
      setting.reactionNotification = setting.likeNotification;
      setting.likeNotification = undefined;
      await setting.save();
    } else {
      setting.reactionNotification = true;
      await setting.save();
    }
  });

  await Async.eachOfSeries(allMessages, async (message) => {
    await Message.updateOne(
      { _id: message._id },
      {
        $set: {
          readBy: [...new Set([...message.readBy, message.sender])],
          receivedBy: [...new Set([...message.receivedBy, message.sender])],
        },
      },
    );
  });

  // For each like in posts, create a reaction and save a reference to the user and post
  // Rename Like settings to Reaction settings
  // For each like in comments, create a reaction and save a reference to the user and comment
  // Delete likes property from posts and comments
  // Delete postLikes and commentLikes from user
  // Delete user.reactions from user

  // For all messages, add the sender to the array of readBy and receivedBy

  await GlobalVariable.create({ name: 'move likes to reactions', value: 'true' });
};

module.exports = moveLikesToReactions;
