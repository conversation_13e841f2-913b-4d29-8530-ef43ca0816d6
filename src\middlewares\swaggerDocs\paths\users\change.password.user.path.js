module.exports = {
  patch: {
    summary: 'Change User Password by ID',
    tags: ['Users'],
    description: 'Change the password of a user by their ID.',
    parameters: [
      {
        name: 'id',
        in: 'path',
        required: true,
        description: 'User ID to change password',
        schema: {
          type: 'string',
          example: '507f1f77bcf86cd799439011',
        },
      },
    ],
    requestBody: {
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              old_password: { type: 'string' },
              confirm_password: { type: 'string' },
              new_password: { type: 'string' },
            },
          },
        },
      },
    },
    responses: {
      200: {
        description: 'User password changed successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  example: 'User password changed',
                },
                status: {
                  type: 'string',
                  example: 'SUCCESS',
                },
              },
            },
          },
        },
      },
      400: {
        description: 'Bad Request. Invalid password change request',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  example: 'Invalid password change request',
                },
                status: {
                  type: 'string',
                  example: 'FAILED',
                },
              },
            },
          },
        },
      },
    },
  },
};
