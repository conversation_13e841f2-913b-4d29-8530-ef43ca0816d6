module.exports = {
  patch: {
    summary: 'Update User',
    tags: ['Users'],
    description: 'Update an existing user information by their ID.',
    parameters: [
      {
        name: 'id',
        in: 'path',
        required: true,
        description: 'User ID to update',
        schema: {
          type: 'string',
          example: '507f1f77bcf86cd799439011',
        },
      },
    ],
    requestBody: {
      required: true,
      content: {
        'multipart/form-data': {
          schema: {
            type: 'object',
            properties: {
              firstName: { type: 'string' },
              lastName: { type: 'string' },
              username: { type: 'string' },
              recoveryEmail: { type: 'string', format: 'email' },
              nationality: { type: 'string' },
              phone: { type: 'string' },
              gender: { type: 'string', enum: ['male', 'female', 'other'] },
              countryOfResidence: { type: 'string' },
              targetedCountries: { type: 'array', items: { type: 'string' }, uniqueItems: true },
              preferredFieldsOfStudy: { type: 'string' },
              preferredSchoolsOfStudy: { type: 'array', items: { type: 'string' }, uniqueItems: true },
              preferredDegreeOfStudy: { type: 'string' },
              interestedInScholarship: { type: 'boolean' },
              preferredCountryOfStudy: { type: 'string' },
              dateOfBirth: { type: 'string' },
              banner: {
                type: 'string',
                format: 'binary',
                description: 'Banner image file',
              },
              photo: {
                type: 'string',
                format: 'binary',
                description: 'Profile photo image file',
              },
            },
          },
        },
      },
    },
  },
  responses: {
    200: {
      description: 'User updated successfully',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              message: {
                type: 'string',
                example: 'johndoe updated',
              },
              status: {
                type: 'string',
                example: 'SUCCESS',
              },
            },
          },
        },
      },
    },
    400: {
      description: 'Bad Request. User record not found',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              message: {
                type: 'string',
                example: 'User record not found',
              },
              status: {
                type: 'string',
                example: 'FAILED',
              },
            },
          },
        },
      },
    },
  },
};
