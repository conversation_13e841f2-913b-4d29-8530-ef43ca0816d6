const httpStatus = require('http-status');
const bcrypt = require('bcryptjs');
const { google } = require('googleapis');
const axios = require('axios');
const queryString = require('querystring');
// const personaidentities = require('@api/personaidentities');

const tokenService = require('./token.service');
const userService = require('./user.service');
const emailService = require('./email.service');
const { Referral, Token, User, Business, Service } = require('../models');
const config = require('../config/config');
const ApiError = require('../utils/ApiError');
const { tokenTypes } = require('../config/tokens');
const { registrationStatuses, clientUserTypes, OTPEnums } = require('../config/constants');
const logger = require('../config/logger');
const subscriberService = require('./subscriber.service');
const validateId = require('./shared/validateId');

const clientId = config.googleOauth.GOOGLE_OAUTH_CLIENT_ID;
const clientSecret = config.googleOauth.GOOGLE_OAUTH_CLIENT_SECRET;
const redirectUri = config.googleOauth.GOOGLE_OAUTH_REDIRECT_URL;

const oauth2Client = new google.auth.OAuth2(clientId, clientSecret, redirectUri);

const generateReferralRecord = async (referredUser, referrerBusiness, service) => {
  validateId(service, 'Service');

  let businessQuery;
  if (validateId(referrerBusiness, 'Business', false)) {
    businessQuery = { _id: referrerBusiness };
  } else {
    businessQuery = { referralCode: referrerBusiness };
  }

  const business = await Business.findOne(businessQuery);
  if (!business) throw new ApiError(httpStatus.NOT_FOUND, 'Referrer business not found');

  const serviceExists = await Service.findById(service);
  if (!serviceExists) throw new ApiError(httpStatus.NOT_FOUND, 'Service not found');

  if (!business.referralCode) {
    business.referralCode = await Referral.generateUniqueReferralCode();
  }

  const { referralCode } = business;

  const referral = await Referral.create({ referredUser, referrerBusiness: business._id, referralCode, service });

  business.referrals.push(referral._id);
  await business.save();

  return referral;
};

const loginUserWithEmailAndPassword = async (email, password) => {
  const user = await userService.getUserByEmail(email, false);
  if (!user?.password) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'Incorrect email or password');
  }
  if (!(await user.isPasswordMatch(password))) {
    logger.info('Debug 3');
    throw new ApiError(httpStatus.UNAUTHORIZED, 'Incorrect email or password');
  }
  if (!user.isEmailVerified) {
    // User has created account but hasn't verified email
    const token = await Token.findOne({ user: user._id, type: tokenTypes.VERIFY_EMAIL });
    let message = 'Kindly activate your account to login.';
    if (token?.signupAs) {
      await userService.sendVerificationEmail(user.email, user, token.signupAs);
      message = 'Kindly activate your account to login. A new verification email has been sent to your email.';
    }

    throw new ApiError(httpStatus.UNAUTHORIZED, message);
  }

  user.lastLogin = new Date();
  await user.save();
  return user;
};

/**
 * Logout
 * @param {string} refreshToken
 * @returns {Promise}
 */
const logout = async (refreshToken) => {
  const refreshTokenDoc = await Token.findOne({ token: refreshToken, type: tokenTypes.REFRESH, blacklisted: false });
  if (!refreshTokenDoc) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Not found');
  }
  await refreshTokenDoc.remove();
};

/**
 * Refresh auth tokens
 * @param {string} refreshToken
 * @returns {Promise<Object>}
 */
const refreshAuth = async (refreshToken) => {
  try {
    const { tokenDoc: refreshTokenDoc } = await tokenService.verifyToken(refreshToken, tokenTypes.REFRESH);
    const user = await userService.getUserById(refreshTokenDoc.user);
    if (!user) {
      throw new Error();
    }
    await refreshTokenDoc.remove();
    return tokenService.generateAuthTokens(user, undefined, refreshTokenDoc.clientUserType);
  } catch (error) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'Please authenticate');
  }
};

const forgotPassword = async (email) => {
  const { resetPasswordToken, user } = await tokenService.generateResetPasswordToken(email);
  await emailService.sendResetPasswordEmail(user, resetPasswordToken);
};

/**
 * Reset password
 * @param {string} resetPasswordToken
 * @param {string} newPassword
 * @returns {Promise}
 */
const resetPassword = async (resetPasswordToken, newPassword) => {
  try {
    const { tokenDoc: resetPasswordTokenDoc } = await tokenService.verifyToken(
      resetPasswordToken,
      tokenTypes.RESET_PASSWORD,
    );
    const user = await userService.getUserById(resetPasswordTokenDoc.user);
    if (!user) {
      throw new Error();
    }
    const salt = await bcrypt.genSalt(10);
    const hashedPwd = await bcrypt.hash(newPassword, salt);
    await userService.updateUserById(user.id, { password: hashedPwd });
    await Token.deleteMany({ user: user.id, type: tokenTypes.RESET_PASSWORD });

    user.pendingPasswordResetCount = 0;
    await user.save();

    await emailService.sendPasswordResetConfirmEmail(user);
  } catch (error) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'Password reset failed. Link may have expired.');
  }
};

/**
 * Verify email
 * @param {string} verifyEmailToken
 * @returns {Promise}
 */
// const verifyEmail = async (verifyEmailToken, email) => {
//   try {
//     const { tokenDoc } = await tokenService.verifyToken(verifyEmailToken, tokenTypes.VERIFY_EMAIL);
//     const user = await userService.getUserById(tokenDoc.user);
//     if (!user) {
//       throw new Error();
//     }
//     await Token.deleteMany({ user: user.id, type: tokenTypes.VERIFY_EMAIL });
//     await userService.updateUserById(user.id, {
//       isEmailVerified: true,
//       registrationStatus: registrationStatuses.ACCOUNT_VERIFIED,
//     });

//     await emailService.sendAccountActivationConfirmEmail(user);
//   } catch (error) {
//     const user = await User.findOne({ email });
//     if (user) {
//       // User is valid but token is expired
//       if (!user.isEmailVerified) throw new ApiError(httpStatus.TEMPORARY_REDIRECT, 'Link is expired or invalid.');

//       throw new ApiError(httpStatus.ALREADY_REPORTED, 'Account is already verified');
//     }

//     throw new ApiError(httpStatus.UNAUTHORIZED, 'Account verification failed. Link is invalid.');
//   }
// };

const verifyEmail = async (email, signupAs, token) => {
  let user;
  let returnData = { registrationStatus: registrationStatuses.ACCOUNT_VERIFIED };

  let verifyTokenDoc;
  try {
    const { tokenDoc, payload } = await tokenService.verifyToken(token, tokenTypes.VERIFY_EMAIL);
    verifyTokenDoc = tokenDoc;
    verifyTokenDoc.signupAs = payload.signupAs;

    user = await User.findById(verifyTokenDoc.user?._id || verifyTokenDoc.sub);
    if (!user) {
      throw new Error();
    }
    await Token.deleteMany({ user: user._id, type: tokenTypes.VERIFY_EMAIL });
  } catch (error) {
    user = await User.findOne({ email });
    if (user) {
      if (user.isEmailVerified) {
        return httpStatus.ALREADY_REPORTED;
      }
      // Resend verification email
      const verifyEmailToken = await tokenService.generateVerifyEmailToken(user);
      await emailService.sendVerificationEmail(user, verifyEmailToken, signupAs);
      throw new ApiError(httpStatus.TEMPORARY_REDIRECT, 'Link is expired. A new link has been sent to your email.');
    }
    throw new ApiError(httpStatus.UNAUTHORIZED, 'Verification failed. Link is invalid');
  }

  if (user.email !== email) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'Inconsistent data.');
  }

  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, 'An error occurred. User record not found');
  }

  if (user.isEmailVerified) {
    // throw new ApiError(httpStatus.ALREADY_REPORTED, 'Account is already verified');
    return httpStatus.ALREADY_REPORTED;
  }

  if (
    signupAs === clientUserTypes.BUSINESS &&
    (verifyTokenDoc.signupAs === clientUserTypes.BUSINESS || user.createdAt < new Date('2024-12-16')) // This is to allow users created before adding signUpAs in payload to be able to verify their email
  ) {
    await User.updateOne(
      { _id: user._id },
      {
        pendingBusiness: 'true',
        isEmailVerified: true,
        $addToSet: { roles: clientUserTypes.BUSINESS },
        registrationStatus: registrationStatuses.ACCOUNT_VERIFIED,
      },
    );
    returnData = {
      ...returnData,
      pendingBusiness: 'true',
      nextStep: 'onboard_business',
      isEmailVerified: true,
    };
  } else if (
    signupAs === clientUserTypes.STUDENT &&
    (verifyTokenDoc.signupAs === clientUserTypes.STUDENT || user.createdAt < new Date('2024-12-16')) // This is to allow users created before adding signUpAs in payload to be able to verify their email
  ) {
    await User.updateOne(
      { _id: user._id },
      {
        pendingStudent: 'true',
        isEmailVerified: true,
        $addToSet: { roles: clientUserTypes.STUDENT },
        registrationStatus: registrationStatuses.ACCOUNT_VERIFIED,
      },
    );
    returnData = { ...returnData, pendingStudent: 'true', nextStep: 'onboard_student', isEmailVerified: true };
    returnData.pendingStudent = 'true';
  } else {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid request');
  }

  await user.save();
  return httpStatus.OK;
};

const checkUsernameExists = async (email) => {
  const username = email
    .split('@')[0]
    .replace(/[^a-zA-Z0-9]/g, '')
    .toLowerCase();
  if (await userService.usernameExists(username)) {
    const random5Digits = Math.floor(Math.random() * 100)
      .toString()
      .padStart(2, '0');
    const modifiedUsername = `${username}${random5Digits}`;
    const newUsername = await checkUsernameExists(modifiedUsername);
    return newUsername;
  }
  return username;
};

const getLinkedInOauthURL = () => {
  const rootURL = 'https://www.linkedin.com/oauth/v2/authorization';

  const options = {
    redirect_uri: config.linkedInOauth.LINKEDIN_OAUTH_REDIRECT_URL,
    client_id: config.linkedInOauth.LINKEDIN_OAUTH_CLIENT_ID,
    response_type: 'code',
    scope: ['email', 'profile', 'openid'],
  };

  const qs = new URLSearchParams(options);
  return `${rootURL}?${qs.toString()}`;
};

const getLinkedInUserInfo = async (accessToken) => {
  const profileUrl = 'https://api.linkedin.com/v2/userinfo';
  const { data } = await axios.get(profileUrl, { headers: { Authorization: `Bearer ${accessToken}` } });
  return data;
};

const linkedInOauth = async (code, subscribed, signupAs, referralInfo) => {
  // referralInfo => { referrerBusiness, service}
  const tokenUrl = 'https://www.linkedin.com/oauth/v2/accessToken';

  const params = queryString.stringify({
    redirect_uri: config.linkedInOauth.LINKEDIN_OAUTH_REDIRECT_URL,
    client_id: config.linkedInOauth.LINKEDIN_OAUTH_CLIENT_ID,
    client_secret: config.linkedInOauth.LINKEDIN_OAUTH_CLIENT_SECRET,
    grant_type: 'authorization_code',
    code,
  });

  const { data } = await axios.post(tokenUrl, params, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });

  const userInfo = await getLinkedInUserInfo(data.access_token);
  let user = await userService.getUserByEmail(userInfo.email);
  if (!user) {
    if (!signupAs) {
      // Assume that it's a login
      throw new ApiError(httpStatus.BAD_REQUEST, 'You need to sign up first');
    }
    // create an account for the user
    const username = await checkUsernameExists(userInfo.email);
    const userBody = {
      email: userInfo.email,
      username,
      firstName: userInfo.given_name,
      lastName: userInfo?.family_name,
      signupMedium: 'linkedin',
    };

    if (signupAs === clientUserTypes.BUSINESS) userBody.pendingBusiness = 'true';
    if (signupAs === clientUserTypes.STUDENT) userBody.pendingStudent = 'true';
    user = await userService.createUser(userBody);

    if (referralInfo) {
      const { referrerBusiness, service } = referralInfo;
      user.referral = (await generateReferralRecord(user._id, referrerBusiness, service))._id;
    }
  }

  if (!user.isEmailVerified) {
    user.isEmailVerified = true;
    // create a new business account
    if (signupAs === clientUserTypes.BUSINESS) user.pendingBusiness = 'true';

    // create a new student account
    if (signupAs === clientUserTypes.STUDENT) user.pendingStudent = 'true';

    user.registrationStatus = registrationStatuses.ACCOUNT_VERIFIED;
  }

  user.lastLogin = new Date();
  await user.save();
  await subscriberService.addSubscriber({ email: user.email, subscribedFor: 'newsletter', subscribed });

  // eslint-disable-next-line global-require
  const { getSelectedUserInfo } = require('../controllers/auth.controller');
  return { ...getSelectedUserInfo(user) };
};

const getGoogleOauthURL = () => {
  const rootURL = 'https://accounts.google.com/o/oauth2/v2/auth';

  const options = {
    redirect_uri: config.googleOauth.GOOGLE_OAUTH_REDIRECT_URL,
    client_id: config.googleOauth.GOOGLE_OAUTH_CLIENT_ID,
    access_type: 'offline',
    response_type: 'code',
    prompt: 'consent',
    scope: ['https://www.googleapis.com/auth/userinfo.profile', 'https://www.googleapis.com/auth/userinfo.email'].join(' '),
  };

  const qs = new URLSearchParams(options);
  return `${rootURL}?${qs.toString()}`;
};

const googleOauth = async (code, subscribed, signupAs, referralInfo) => {
  let userInfo;
  try {
    const { tokens } = await oauth2Client.getToken(code);
    oauth2Client.setCredentials(tokens);
    const oauth2 = google.oauth2({ version: 'v2', auth: oauth2Client });
    const { data } = await oauth2.userinfo.get();
    userInfo = data;
  } catch (error) {
    logger.error(error);
    throw new ApiError(httpStatus.UNAUTHORIZED, 'Unauthorized');
  }

  let user = await userService.getUserByEmail(userInfo.email, false);
  if (!user) {
    if (!signupAs) {
      // Assume that it's a login
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        "Your email isn't registered yet. You'll be redirected to sign up",
        undefined,
        undefined,
        'GOOGLE_OAUTH_ERROR',
      );
    }
    // create an account for the user
    const username = await checkUsernameExists(userInfo.email);
    const userBody = {
      email: userInfo.email,
      username,
      firstName: userInfo.given_name,
      lastName: userInfo?.family_name,
      signupMedium: 'google',
      emailVerified: true,
      registrationStatus: registrationStatuses.ACCOUNT_VERIFIED,
    };

    if (signupAs === clientUserTypes.BUSINESS) userBody.pendingBusiness = 'true';
    if (signupAs === clientUserTypes.STUDENT) userBody.pendingStudent = 'true';

    user = await userService.createUser(userBody);

    if (referralInfo.service && referralInfo.referrerBusiness) {
      const { referrerBusiness, service } = referralInfo;
      user.referral = (await generateReferralRecord(user._id, referrerBusiness, service))._id;
    }
  }

  if (!user.isEmailVerified) {
    user.isEmailVerified = true;
    // create a new business account
    if (signupAs === clientUserTypes.BUSINESS) user.pendingBusiness = 'true';

    // create a new student account
    if (signupAs === clientUserTypes.STUDENT) user.pendingStudent = 'true';

    user.registrationStatus = registrationStatuses.ACCOUNT_VERIFIED;
  }

  await subscriberService.addSubscriber({ email: user.email, subscribedFor: 'newsletter', subscribed });
  user.lastLogin = new Date();
  await user.save();

  // eslint-disable-next-line global-require
  const { getSelectedUserInfo } = require('../controllers/auth.controller');
  return { ...getSelectedUserInfo(user) };
};

const checkOptionalAuth = async (req) => {
  const token = req.headers.authorization?.split(' ')[1];
  try {
    const { tokenDoc: decodedPayload } = await tokenService.verifyToken(token);

    req.user = await User.findById(decodedPayload.sub.userId);
  } catch (error) {
    // Do nothing;
    if (token) {
      throw new ApiError(httpStatus.UNAUTHORIZED, 'Unauthorized');
    }
  }
};

const emailOrPhone = (address) => [
  { email: { $regex: address, $options: 'i' } },
  { phone: { $regex: address, $options: 'i' } },
];

// Send phone OTP - Can be updated to send to email in the future
const sendOTP = async (address, channel, userParam) => {
  // If userParam is passed, it's assumed that a check for duplicate phone number has been made
  const users = userParam instanceof User ? [userParam] : await User.find({ $or: emailOrPhone(address) });
  // Care should be taken with sending OTP to non-users. This is to prevent spamming and abuse.

  let userReturn = {};
  if (users.length > 1) {
    logger.error('Multiple users found with the same phone number.');
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'An error occurred.');
  }
  const user = users[0];
  const earliestPhoneVerifyRequest = user?.earliestPhoneVerifyRequest;
  if (earliestPhoneVerifyRequest && new Date() - earliestPhoneVerifyRequest < 0) {
    throw new ApiError(httpStatus.TOO_MANY_REQUESTS, 'Too many requests. Try again later.', undefined, undefined, {
      code: 'PENDING_COUNTDOWN',
      earliestPhoneVerifyRequest,
      phoneVerifyCount: user.phoneVerifyCount,
      phone: address,
      channel,
    });
  }

  if (
    user &&
    (!earliestPhoneVerifyRequest ||
      earliestPhoneVerifyRequest?.getTime() === OTPEnums.maxDate ||
      user.phoneVerifyCount >= OTPEnums.resendOTPTimes.length)
  ) {
    throw new ApiError(httpStatus.TOO_MANY_REQUESTS, 'Too many requests. Contact Support.');
  }

  if (user?.phoneVerified) {
    throw new ApiError(httpStatus.ALREADY_REPORTED, 'Phone number already verified.');
  }
  const { twilio } = config;
  const verification = await twilio.client.verify.v2.services(twilio.serviceId).verifications.create({
    channel,
    to: address,
  });

  if (user) {
    const nextPhoneVerifyOTPGap = OTPEnums.resendOTPTimes[user.phoneVerifyCount + 1];
    const today = new Date();
    if (nextPhoneVerifyOTPGap) {
      user.earliestPhoneVerifyRequest = new Date(today.setMinutes(today.getMinutes() + nextPhoneVerifyOTPGap));
    } else {
      user.earliestPhoneVerifyRequest = new Date(OTPEnums.maxDate);
    }
    user.phoneVerifyCount = (user.phoneVerifyCount || 0) + 1;
    user.phone = address;
    await user.save();
    userReturn = { phoneVerifyCount: user.phoneVerifyCount, earliestPhoneVerifyRequest: user.earliestPhoneVerifyRequest };
  }

  return { status: verification.status, phone: address, channel, user: userReturn };
};

const verifyOTP = async (address, code) => {
  // Verify code
  try {
    const { twilio } = config;
    const verification = await twilio.client.verify.v2.services(twilio.serviceId).verificationChecks.create({
      code,
      to: address,
    });

    const users = await User.find({ $or: emailOrPhone(address) });
    const userReturn = {};
    if (users?.length > 1) {
      logger.error('Multiple users found with the same phone number.');
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'An error occurred.');
    }
    const user = users[0];
    if (user) {
      user.phoneVerified = verification.status === 'approved';
      await user.save();
      userReturn.phoneVerified = true;
    }
    if (verification.status !== 'approved') {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid OTP', undefined, undefined, { code: 'INVALID_OTP' });
    }

    return { status: verification.status, phone: address, user: userReturn };
  } catch (error) {
    logger.error(error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(httpStatus.BAD_REQUEST, 'Error verifying OTP', undefined, undefined, { code: error.code });
  }
};

// const createIdVerification = async (req, res) => {
//   // Using persona
//   personaidentities.auth(config.persona.apiKey);

// };

module.exports = {
  generateReferralRecord,
  loginUserWithEmailAndPassword,
  logout,
  refreshAuth,
  resetPassword,
  verifyEmail,
  forgotPassword,
  getGoogleOauthURL,
  googleOauth,
  getLinkedInOauthURL,
  linkedInOauth,
  checkOptionalAuth,
  sendOTP,
  verifyOTP,
  // createIdVerification,

  checkUsernameExists,
};
