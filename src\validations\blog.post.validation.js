const Joi = require('joi');
const { fileSchema } = require('./file.validation');
const { blogStatuses, blogCategories } = require('../config/constants');

const blogCreateStatuses = Object.values(blogStatuses).filter((status) => ![blogStatuses.DELETED].includes(status));
const blogChangeStatuses = Object.values(blogStatuses).filter(
  (status) => ![blogStatuses.DELETED, blogStatuses.DRAFT].includes(status),
);

const createBlogPost = {
  body: Joi.object().keys({
    estimatedReadingMinutes: Joi.number().required(),
    category: Joi.string()
      .valid(...blogCategories)
      .required(),
    text: Joi.string().required(),
    title: Joi.string().required(),
    subtitle: Joi.string(),
    status: Joi.string()
      .valid(...blogCreateStatuses)
      .default(blogStatuses.DRAFT),
    tags: Joi.alternatives().try(Joi.array().items(Joi.string()), Joi.string()),
    fileIds: Joi.alternatives().try(Joi.array().items(Joi.string()).max(5), Joi.string()),
    scheduledAt: Joi.date().when('status', {
      is: blogStatuses.SCHEDULED,
      then: Joi.required(),
    }),
  }),
  files: {
    coverImage: Joi.array().items(fileSchema(1)).max(1).required(),
    mediaFiles: Joi.array().items(fileSchema(1)).max(5),
  },
};

const updateBlogPost = {
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
  body: Joi.object().keys({
    estimatedReadingMinutes: Joi.number(),
    category: Joi.string().valid(...blogCategories),
    text: Joi.string(),
    title: Joi.string(),
    subtitle: Joi.string(),
    tags: Joi.alternatives().try(Joi.array().items(Joi.string()), Joi.string()),
    deletedUrls: Joi.alternatives().try(Joi.array().items(Joi.string()).max(5), Joi.string()),
    fileIds: Joi.alternatives().try(Joi.array().items(Joi.string()).max(5), Joi.string()),
  }),
  files: {
    coverImage: Joi.array().items(fileSchema(1)).max(1),
    mediaFiles: Joi.array().items(fileSchema(1)).max(5),
  },
};

const updateBlogPostStatus = {
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
  body: Joi.object().keys({
    status: Joi.string().valid(...blogChangeStatuses),
  }),
};

const getBlogPosts = {
  query: Joi.object().keys({
    sortBy: Joi.string().valid(
      'createdAt:asc',
      'createdAt:desc',
      'updatedAt:asc',
      'updatedAt:desc, publishedAt:asc',
      'publishedAt:desc',
    ),
    searchText: Joi.string(),
    category: Joi.string().valid(...blogCategories),
    status: Joi.string().valid(...Object.values(blogStatuses)),
    title: Joi.string(),
    uniqueTitle: Joi.string(),
    tags: Joi.string(),
    limit: Joi.number().integer(),
    page: Joi.number().integer(),
  }),
};

const react = {
  body: Joi.object().keys({
    reaction: Joi.string().valid('upvote', 'downvote').required(),
    existingReaction: Joi.string().valid('upvote', 'downvote'),
  }),
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
};

module.exports = {
  createBlogPost,
  getBlogPosts,
  react,
  updateBlogPost,
  updateBlogPostStatus,
};
