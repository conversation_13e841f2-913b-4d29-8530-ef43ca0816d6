module.exports = {
  get: {
    summary: 'Get user group settings',
    tags: ['Groups'],
    parameters: [
      {
        in: 'path',
        name: 'id',
        schema: {
          type: 'string',
          example: '5f9d1e9a6f3f4b001f6d4c0b',
        },
        description: 'ID of the group',
      },
    ],
    responses: {
      200: {
        description: 'Group setting successfully retrieved',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Group setting successfully retrieved',
              data: {
                settings: {
                  groupMessageNotification: true,
                  groupPostNotification: true,
                  messages: 'admin',
                  posts: 'all',
                },
                user: '651bb5bf4eb9240327eaaaa',
                _id: '65d7720388bd5f4e123eaaaa',
              },
            },
          },
        },
      },
    },
  },
};
