const express = require('express');

const router = express.Router();
const { auth, canManageResource } = require('../../middlewares/auth');
const { forumController: ForumController } = require('../../controllers');
const { uploadMultiple } = require('../../config/multer.config');
const { forumValidation: ForumValidation } = require('../../validations');
const validate = require('../../middlewares/validate');

router.use(auth());
router.get('/search', validate(ForumValidation.searchForumPosts), ForumController.searchForumPosts);
router.post('/', uploadMultiple, validate(ForumValidation.createForumPost), ForumController.createForumPost);
router.get('/', validate(ForumValidation.getForumPosts), ForumController.getForumPosts);

router.get('/:id', validate(ForumValidation.getPostById), ForumController.getForumPostById);
router.patch(
  '/:id',
  canManageResource('ForumPost'),
  uploadMultiple,
  validate(ForumValidation.updateForumPost),
  ForumController.updateForumPost,
);
router.delete(
  '/:id',
  canManageResource('ForumPost'),
  validate(ForumValidation.deleteForumPost),
  ForumController.deleteForumPost,
);

router.post('/:id/reply', uploadMultiple, validate(ForumValidation.createForumReply), ForumController.createForumReply);
router.get('/:id/reply', validate(ForumValidation.getForumReplies), ForumController.getReplies);
router.get('/:forumPostId/reply/:id', validate(ForumValidation.getForumReply), ForumController.getReplyById);
router.patch(
  '/:forumPostId/reply/:id',
  canManageResource('ForumReply'),
  uploadMultiple,
  validate(ForumValidation.updateForumReply),
  ForumController.updateForumReply,
);
router.patch('/:forumPostId/reply/:id/vote', validate(ForumValidation.voteReply), ForumController.voteForumReply);
router.delete(
  '/:forumPostId/reply/:id',
  canManageResource('ForumReply'),
  validate(ForumValidation.deleteForumReply),
  ForumController.deleteForumReply,
);

module.exports = router;
