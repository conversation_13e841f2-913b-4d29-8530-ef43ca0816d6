const Joi = require('joi');
const { objectId } = require('./custom.validation');

const createProfile = {
  body: Joi.object().keys({
    basicInformation: Joi.object().keys({
      personalStatement: Joi.string(),
    }),
    personalStatement: Joi.string(),
    skills: Joi.array().items(Joi.string()),
    hobbies: Joi.array().items(Joi.string()),
  }),
};

const getUserInformation = {
  params: Joi.object().keys({
    userId: Joi.string().custom(objectId),
  }),
};

const updateBasicInformation = {
  body: Joi.object().keys({
    basicInformation: Joi.object().keys({
      personalStatement: Joi.string(),
    }),
    skills: Joi.array().items(Joi.string()).unique(),
    hobbies: Joi.array().items(Joi.string()).unique(),
  }),
};

const requiredQuery = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required(),
  }),
  query: Joi.object().keys({
    resourceName: Joi.string()
      .valid('Education', 'Certification', 'Project', 'Award', 'Volunteering', 'Experience', 'TestScore')
      .required(),
  }),
};

const requiredQueryPart = {
  query: Joi.object().keys({
    resourceName: Joi.string()
      .valid('Education', 'Certification', 'Project', 'Award', 'Volunteering', 'Experience', 'TestScore')
      .required(),
  }),
};

// const addSkillsSchema = Joi.object({
//   skills: Joi.array().items(Joi.string()).required(),
// });

// const updateSkillsSchema = Joi.object({
//   skills: Joi.array().items(Joi.string()),
// });

// const addHobbiesSchema = Joi.object({
//   hobbies: Joi.array().items(Joi.string()).required(),
// });

// const updateHobbiesSchema = Joi.object({
//   hobbies: Joi.array().items(Joi.string()),
// });

const addEducationSchema = Joi.object({
  schoolName: Joi.string().trim().required(),
  degreeType: Joi.string().trim().required(),
  major: Joi.string().trim(),
  start: Joi.date().iso().required(),
  end: Joi.alternatives().conditional('current', {
    is: true,
    then: Joi.valid(null),
    otherwise: Joi.date().iso(),
  }),
  current: Joi.boolean(),
});

const updateEducationSchema = Joi.object({
  schoolName: Joi.string().trim(),
  degreeType: Joi.string().trim(),
  major: Joi.string().trim(),
  start: Joi.date().iso(),
  current: Joi.boolean(),
  end: Joi.alternatives().conditional('current', {
    is: true,
    then: Joi.valid(null),
    otherwise: Joi.date().iso(),
  }),
});

const addCertificationSchema = Joi.object({
  certName: Joi.string().trim().required(),
  issuingOrganization: Joi.string().trim().required(),
  dateIssued: Joi.string().trim().required(),
  certLink: Joi.string().trim(),
});

const updateCertificationSchema = Joi.object({
  certName: Joi.string().trim(),
  issuingOrganization: Joi.string().trim(),
  dateIssued: Joi.string().trim(),
  certLink: Joi.string().trim(),
});

const addTestScoreSchema = Joi.object({
  testName: Joi.string().trim().required(),
  issuingBody: Joi.string().trim().required(),
  date: Joi.string().trim().required(),
  testScore: Joi.string().trim().required(),
});

const updateTestScoreSchema = Joi.object({
  testName: Joi.string().trim(),
  issuingBody: Joi.string().trim(),
  date: Joi.string().trim(),
  testScore: Joi.string().trim(),
});

const addExperienceSchema = Joi.object({
  title: Joi.string().trim().required(),
  companyName: Joi.string().trim().required(),
  start: Joi.date().iso().required(),
  current: Joi.boolean(),
  end: Joi.alternatives().conditional('current', {
    is: true,
    then: Joi.valid(null),
    otherwise: Joi.date().iso(),
  }),
  description: Joi.string().trim(),
});

const updateExperienceSchema = Joi.object({
  title: Joi.string().trim(),
  companyName: Joi.string().trim(),
  start: Joi.date().iso(),
  current: Joi.boolean(),
  end: Joi.alternatives().conditional('current', {
    is: true,
    then: Joi.valid(null),
    otherwise: Joi.date().iso(),
  }),
  description: Joi.string().trim(),
});

const addProjectSchema = Joi.object({
  projectName: Joi.string().trim().required(),
  description: Joi.string().trim(),
  start: Joi.date().iso(),
  end: Joi.date().iso().allow(null),
  projectLink: Joi.string().trim().uri(),
});

const updateProjectSchema = Joi.object({
  projectName: Joi.string().trim(),
  description: Joi.string().trim(),
  start: Joi.date().iso(),
  end: Joi.date().iso().allow(null),
  projectLink: Joi.string().trim().uri(),
});

const addVolunteeringSchema = Joi.object({
  title: Joi.string().trim().required(),
  companyName: Joi.string().trim().required(),
  start: Joi.date().iso().required(),
  end: Joi.alternatives().conditional('current', {
    is: true,
    then: Joi.valid(null),
    otherwise: Joi.date().iso(),
  }),
  current: Joi.boolean(),
  description: Joi.string().trim(),
});

const updateVolunteeringSchema = Joi.object({
  title: Joi.string().trim(),
  companyName: Joi.string().trim(),
  start: Joi.date().iso(),
  current: Joi.boolean(),
  end: Joi.alternatives().conditional('current', {
    is: true,
    then: Joi.valid(null),
    otherwise: Joi.date().iso(),
  }),
  description: Joi.string().trim(),
});

const addAwardSchema = Joi.object({
  awardName: Joi.string().trim().required(),
  issuingBody: Joi.string().trim().required(),
  date: Joi.date().iso().required(),
  awardLink: Joi.string().trim(),
});

const updateAwardSchema = Joi.object({
  awardName: Joi.string().trim(),
  issuingBody: Joi.string().trim(),
  date: Joi.date().iso(),
  awardLink: Joi.string().trim(),
});

const deleteResource = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId),
  }),
  query: Joi.object().keys({
    resourceName: Joi.string()
      .valid(
        'Education',
        'Certification',
        'Project',
        'Award',
        'Volunteering',
        'Experience',
        'TestScore',
        'Skills',
        'Hobbies',
      )
      .required(),
  }),
};

module.exports = {
  createProfile,
  updateBasicInformation,
  deleteResource,
  addCertificationSchema,
  updateCertificationSchema,
  requiredQuery,
  requiredQueryPart,
  getUserInformation,
  // addHobbiesSchema,
  // updateHobbiesSchema,
  // addSkillsSchema,
  // updateSkillsSchema,
  addEducationSchema,
  addTestScoreSchema,
  updateEducationSchema,
  updateTestScoreSchema,
  addExperienceSchema,
  updateExperienceSchema,
  addProjectSchema,
  updateProjectSchema,
  addVolunteeringSchema,
  updateVolunteeringSchema,
  addAwardSchema,
  updateAwardSchema,
};
