const Async = require('async');
const GlobalVariable = require('../global.variable.model');
const GroupSetting = require('../groupSetting.model');
const Group = require('../group.model');
const logger = require('../../config/logger');
const { groupSettingsEnums } = require('../../config/constants');

const editGroupSettingFields = async () => {
  const renamed = await GlobalVariable.findOne({ name: 'edit group setting fields' });
  if (renamed) {
    return;
  }

  await Async.eachSeries(await Group.find({}), async (groupParam) => {
    const group = groupParam;
    const members = [group.admin, ...group.members, ...group.coAdmins];
    await GroupSetting.findOneAndDelete({ group: group._id });

    const groupSetting = await GroupSetting.create({ group: group._id });

    const settings = { posts: groupSettingsEnums.ALL, messages: groupSettingsEnums.ALL };
    members.forEach((member) => {
      groupSetting.users.push({ user: member, settings });
    });
    await groupSetting.save();
  });

  await GlobalVariable.create({ name: 'edit group setting fields', value: 'true' });
  logger.info('Group setting fields edited');
};

module.exports = editGroupSettingFields;
