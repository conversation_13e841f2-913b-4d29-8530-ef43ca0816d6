const httpStatus = require('http-status');
const ApiError = require('../../../utils/ApiError');
const { Group } = require('../../../models');

const canManageRequests = () => async (req, res, next) => {
  let group;
  try {
    group = await Group.findById(req.params.id).populate('profilePhoto');
  } catch (err) {
    return next(new ApiError(httpStatus.NOT_FOUND, `Group not found`));
  }

  if (!group) {
    return next(new ApiError(httpStatus.NOT_FOUND, `Group not found`));
  }

  // Admin or Co-Admin can view membership requests and handle coAdmins
  if (!(String(group?.admin) === String(req.user._id) || group?.coAdmins.includes(req.user._id))) {
    return next(new ApiError(httpStatus.FORBIDDEN, 'Forbidden: You are not allowed to perform this operation'));
  }

  req.resourceRecord = group;
  next();
};

module.exports = canManageRequests;
