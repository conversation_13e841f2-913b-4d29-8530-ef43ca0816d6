module.exports = {
  get: {
    summary: 'Perform a search to find Users',
    tags: ['Global'],
    parameters: [
      {
        in: 'query',
        name: 'searchTerm',
        description: 'User to search for',
        required: true,
        type: 'string',
        example: '<PERSON>',
      },
      {
        in: 'query',
        name: 'sortBy',
        schema: {
          type: 'string',
        },
        description: 'Sort order for the posts',
      },
      {
        in: 'query',
        name: 'limit',
        schema: {
          type: 'integer',
          format: 'int32',
          example: '12',
        },
        description: 'Number of posts to return',
      },
      {
        in: 'query',
        name: 'page',
        schema: {
          type: 'integer',
          format: 'int32',
          example: '2',
        },
        description: 'Page number',
      },
    ],
    responses: {
      200: {
        description: 'Search records retrieved',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Search records retrieved',
              data: {
                results: [
                  {
                    username: 'johndoe',
                    photo: {
                      _id: '65b00069f2b37ca660a3c05f',
                      url: 'https://storage-site/users%2Fphoto/74570fc0.jpg',
                    },
                    firstName: '<PERSON>',
                    lastName: 'Doe',
                    tagLine: '',
                    middleName: '',
                    id: '65bb7f69f2b37ca660a3000',
                  },
                ],
                page: 1,
                limit: 10,
                totalPages: 1,
                totalResults: 1,
              },
            },
          },
        },
      },
    },
  },
};
