const { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } = require('selenium-webdriver');
const { Options } = require('selenium-webdriver/edge');
const async = require('async');
const logger = require('../../config/logger');
const { Country } = require('../../models');

const getStripeSupportedCountries = async () => {
  const driver = await new Builder().forBrowser(Browser.EDGE).setEdgeOptions(new Options().headless()).build();
  const allowedCountries = [];

  try {
    await driver.get('https://stripe.com/global');

    const countryCollection = await driver.findElement(By.css('.GlobalCountryList.GlobalCountries'));
    const countries = await countryCollection.findElements(By.css('.GlobalCountryListItem'));

    await async.eachSeries(countries, async (countryElement) => {
      const countryName = await countryElement.findElement(By.css('a')).getText();
      logger.info(`Country: ${countryName}`);

      const country = await Country.findOne({ name: { $regex: new RegExp(`^${countryName.split('\n')[0].trim()}`, 'i') } });
      logger.info(`${country._id}: ${country.name}, ${countryName}`);
      if (!country) {
        logger.info(`Country not found: ${countryName}`);
        return;
      }

      allowedCountries.push(country._id.toString());
    });
  } catch (error) {
    logger.error(error);
  }

  return allowedCountries;
};

module.exports = getStripeSupportedCountries;
