const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');

// Data source: https://github.com/dr5hn/countries-states-cities-database
// https://github.com/dr5hn/countries-states-cities-database/blob/master/countries%2Bstates%2Bcities.json
const countrySchema = new mongoose.Schema(
  {
    name: { type: String, trim: true, index: true },
    iso2: { type: String },
    iso3: { type: String },
    phoneCode: { type: String },
    capital: { type: String },
    states: [{ type: mongoose.Schema.Types.ObjectId, ref: 'State' }],
  },
  {
    timestamps: true,
  },
);

countrySchema.index({ name: 1 });

countrySchema.plugin(toJSON);
countrySchema.plugin(paginate);

const Country = mongoose.model('Country', countrySchema);

module.exports = Country;
