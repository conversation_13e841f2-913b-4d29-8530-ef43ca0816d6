module.exports = {
  get: {
    summary: 'Get user reactions to a post',
    description: 'Get user reactions to a post based on the provided post ID',
    tags: ['Posts'],
    parameters: [
      {
        in: 'path',
        name: 'id',
        schema: {
          type: 'string',
          example: '507f1f77bcf86cd799439011',
        },
        description: 'ID of the post to react to',
        required: true,
      },
      {
        in: 'query',
        name: 'tab',
        schema: {
          type: 'string',
          example: 'likes',
          default: 'likes',
          enums: ['likes', 'reposts'],
        },
        description: 'Type of reaction to filter users by (optional). E.g. likes, reposts',
      },
    ],
    responses: {
      200: {
        description: 'Users who like post retrieved successfully',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Users who like post retrieved successfully',
            },
          },
        },
      },
    },
  },
};
