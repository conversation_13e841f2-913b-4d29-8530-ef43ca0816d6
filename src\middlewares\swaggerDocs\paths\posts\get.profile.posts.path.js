module.exports = {
  get: {
    tags: ['Posts'],
    summary: 'Get posts and reposts',
    description: 'Get user profile posts and reposts',
    parameters: [
      {
        in: 'query',
        name: 'limit',
        description: 'Number of posts and reposts per page',
        required: false,
        type: 'integer',
        example: '10',
      },
      {
        in: 'query',
        name: 'page',
        description: 'Page number',
        required: false,
        type: 'integer',
        example: '3',
      },
    ],
    responses: {
      200: {
        description: 'User profile posts retrieved successfully',
        schema: {
          type: 'array',
          items: {
            properties: {
              data: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    userId: {
                      type: 'object',
                      properties: {
                        firstName: { type: 'string' },
                        lastName: { type: 'string' },
                        middleName: { type: 'string' },
                        username: { type: 'string' },
                        email: { type: 'string' },
                        targetedCountries: {
                          type: 'array',
                          items: { type: 'string' },
                        },
                        interestedInScholarship: { type: 'boolean' },
                        roles: {
                          type: 'array',
                          items: { type: 'string' },
                        },
                        isEmailVerified: { type: 'boolean' },
                        registrationStatus: { type: 'string' },
                        id: { type: 'string' },
                      },
                    },
                    postId: {
                      type: 'object',
                      properties: {
                        userId: { type: 'string' },
                        text: { type: 'string' },
                        media: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              _id: { type: 'string' },
                              filename: { type: 'string' },
                              url: { type: 'string' },
                              publicId: { type: 'string' },
                              __v: { type: 'integer' },
                            },
                          },
                        },
                        comments: { type: 'array' }, // You can define a comment schema here
                        reposts: { type: 'integer' },
                        id: { type: 'string' },
                      },
                    },
                    id: { type: 'string' },
                  },
                },
              },
              status: {
                type: 'string',
                example: 'SUCCESS',
              },
              message: {
                type: 'string',
                example: 'Posts retrieved successfully',
              },
            },
          },
        },
      },
      500: {
        description: 'Internal Server Error',
      },
    },
  },
};
