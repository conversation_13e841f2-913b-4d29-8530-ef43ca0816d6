module.exports = {
  get: {
    summary: 'Get a forum post by ID',
    tags: ['Forum Posts'],
    parameters: [
      {
        name: 'id',
        in: 'path',
        description: 'ID of the forum post',
        required: true,
        type: 'string',
        format: 'objectId',
      },
    ],
    responses: {
      200: {
        description: 'Forum post retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string', example: 'SUCCESS' },
                data: {
                  type: 'object',
                  $ref: '#/components/schemas/ForumPosts',
                },
                message: { type: 'string', example: 'Forum post retrieved successfully' },
              },
              required: ['status', 'data', 'message'],
            },
          },
        },
      },
      404: {
        description: 'Forum post not found',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string', example: 'FAILED' },
                message: { type: 'string', example: 'Forum post record not found' },
              },
              required: ['status', 'message'],
            },
          },
        },
      },
    },
  },
};
