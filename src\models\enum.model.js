const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');

const enumSchema = new mongoose.Schema(
  {
    enumFor: { type: String, required: true, trim: true, unique: true },
    'Campus housing': [{ type: String }],
    'Awards offered': [{ type: String }],
    Type: [{ type: String }],
    'Carnegie Classification': [{ type: String }],
    'COMPLETIONS (NUMBER OF AWARDS CONFERRED) 2021-2022': [{ type: String }],
  },
  {
    timestamps: true,
  },
);

enumSchema.plugin(toJSON);
enumSchema.plugin(paginate);

const Enum = mongoose.model('Enum', enumSchema);

module.exports = Enum;
