const mongoose = require('mongoose');
const validator = require('validator');
const { toJSON, paginate } = require('./plugins');

const contactUsSchema = new mongoose.Schema(
  {
    fullName: {
      type: String,
      required: true,
    },
    email: {
      type: String,
      required: true,
      trim: true,
      lowercase: true,
      validate(value) {
        if (!validator.isEmail(value)) {
          throw new Error('Invalid email');
        }
      },
    },
    message: {
      type: String,
      required: true,
    },
  },
  {
    timestamps: true,
  },
);

contactUsSchema.index({ createdAt: 1, updatedAt: 1, email: 1, fullName: 1 });

contactUsSchema.plugin(toJSON);
contactUsSchema.plugin(paginate);

const ContactUs = mongoose.model('ContactUs', contactUsSchema);

module.exports = ContactUs;
