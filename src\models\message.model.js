const mongoose = require('mongoose');

const { toJSON, paginate } = require('./plugins');

const { ObjectId } = mongoose.SchemaTypes;

const messageSchema = new mongoose.Schema(
  {
    // sender: { type: ObjectId, ref: 'User' },
    sentBy: {
      // Replaces sender
      user: { type: ObjectId, ref: 'User' }, // Either user or business will be present not both
      business: { type: ObjectId, ref: 'Business' },
    },
    conversation: { type: ObjectId, ref: 'MessageConversation', required: true },
    order: { type: ObjectId, ref: 'Order' }, // When the message is related to an order
    text: { type: String },
    files: [{ type: ObjectId, ref: 'File' }],
    // readBy: [{ type: ObjectId, ref: 'User' }],
    isReadBy: [
      // Replaces readBy
      {
        user: { type: ObjectId, ref: 'User' }, // Either user or business will be present not both
        business: { type: ObjectId, ref: 'Business' },
      },
    ],
    // receivedBy: [{ type: ObjectId, ref: 'User' }],
    isReceivedBy: [
      // Replaces receivedBy
      {
        user: { type: ObjectId, ref: 'User' }, // Either user or business will be present not both
        business: { type: ObjectId, ref: 'Business' },
      },
    ],
    edited: { type: Boolean, default: false },
  },
  {
    timestamps: true,
  },
);

messageSchema.index({ createdAt: 1, updatedAt: 1 });

messageSchema.plugin(toJSON);
messageSchema.plugin(paginate);

const Message = mongoose.model('Message', messageSchema);

module.exports = Message;
