const mongoose = require('mongoose');
const fs = require('fs').promises;
const path = require('path');
const httpStatus = require('http-status');
const ApiError = require('./ApiError');
const logger = require('../config/logger');
const validateId = require('../services/shared/validateId');

const generateUniqueId = async (Model) => {
  let newId;

  const findUniqueCandidate = async () => {
    newId = new mongoose.Types.ObjectId();
    const existingDoc = await Model.findById(newId);
    if (existingDoc) {
      return findUniqueCandidate();
    }
    return newId;
  };

  await findUniqueCandidate();

  return newId;
};

const deepFreeze = (obj) => {
  const propNames = Object.getOwnPropertyNames(obj);

  // Freeze properties before freezing self
  propNames.forEach((name) => {
    const prop = obj[name];
    // If prop is an object, freeze it
    if (typeof prop === 'object' && prop !== null) {
      deepFreeze(prop);
    }
  });

  return Object.freeze(obj);
};

const validateObjectData = (objectParam, schema, errorMessageParam, errorCodeText = 'BAD_REQUEST') => {
  let errorMessage = errorMessageParam;
  let object = objectParam;
  if (typeof object === 'string') {
    try {
      object = JSON.parse(object);
    } catch (error) {
      throw new ApiError(httpStatus[errorCodeText], 'Invalid data formatting');
    }
  }
  const { value, error } = schema.validate(object);
  if (error) {
    if (error.details.find((detail) => detail.message.includes('duplicate value'))) {
      errorMessage = 'Duplicate values not allowed';
    }
    logger.error(error);
    throw new ApiError(httpStatus[errorCodeText], errorMessage || error.message);
  }
  return value;
};

// Validate data in an array of objects against a model
// input: dataParam: array of objects to contain id or array of ids, model: mongoose model
const validateModelDataArray = async (dataParam, model) => {
  const dataArray = Array.isArray(dataParam) ? dataParam : [dataParam];
  const { modelName } = model;
  await Promise.all(
    dataArray.map(async (data) => {
      validateId(data.id || data, modelName);
      const dataExists = await model.findById(data.id || data);
      if (!dataExists) {
        logger.error(`${modelName} with ID: ${data.id || data} not found`);
        throw new ApiError(httpStatus.NOT_FOUND, `${modelName} with ID: ${data.id || data} not found`);
      }
    }),
  );
};

const deleteFolder = async (folderPath) => {
  try {
    // Check if folder exists
    await fs.access(folderPath);

    // Read all files and subdirectories inside
    const files = await fs.readdir(folderPath);

    // Delete each file/subdirectory
    await Promise.all(
      files.map(async (file) => {
        const filePath = path.join(folderPath, file);
        const stat = await fs.lstat(filePath);

        if (stat.isDirectory()) {
          await deleteFolder(filePath); // Recursively delete subdirectories
        } else {
          await fs.unlink(filePath); // Delete file
        }
      }),
    );

    // Remove empty folder
    await fs.rmdir(folderPath);
    logger.info(`Deleted folder: ${folderPath}`);
  } catch (err) {
    if (err.code === 'ENOENT') {
      logger.error(`Folder does not exist: ${folderPath}`);
    } else {
      logger.error(`Error deleting folder: ${err.message}`);
    }
  }
};

const truncateText = (text, maxLength = 100) => {
  if (!text) return '';
  return text.length > maxLength ? `${text.slice(0, maxLength)}...` : text;
};

const timeAgo = (date) => {
  const now = new Date();
  const past = new Date(date);
  const seconds = Math.floor((now - past) / 1000);

  const intervals = [
    { label: 'day', seconds: 86400 },
    { label: 'hr', seconds: 3600 },
    { label: 'minute', seconds: 60 },
  ];

  // eslint-disable-next-line no-restricted-syntax
  for (const interval of intervals) {
    const count = Math.floor(seconds / interval.seconds);
    if (count >= 1) {
      return `${count} ${interval.label}${count > 1 ? 's' : ''} Ago`;
    }
  }

  return 'Just now';
};

const formatDate = (date) => {
  const d = new Date(date);
  const options = { year: 'numeric', month: 'short', day: 'numeric' };
  return d.toLocaleDateString('en-US', options);
};

/**
 * Resolves MongoDB projection conflicts by removing parent fields when child fields exist
 * This prevents the "specification contains two conflicting paths" error in $project stages
 * Handles conflicts at any nesting level (e.g., 'user' vs 'user.name', 'parentPost.user' vs 'parentPost.user.firstName')
 *
 * @param {Object} projection - MongoDB projection object
 * @returns {Object} - Resolved projection object without conflicts
 *
 * Example:
 * Input: { group: 1, 'group.name': 1, 'parentPost.user': 1, 'parentPost.user.firstName': 1 }
 * Output: { 'group.name': 1, 'parentPost.user.firstName': 1 }
 */
const resolveProjectionConflicts = (projection) => {
  const resolved = { ...projection };
  const projectionPaths = Object.keys(resolved);

  // Sort paths by length (shorter paths first) to handle parent-child relationships properly
  const sortedPaths = projectionPaths.sort((a, b) => a.length - b.length);

  // Check each path against all other paths to find conflicts
  const pathsToRemove = new Set();

  sortedPaths.forEach((currentPath) => {
    if (pathsToRemove.has(currentPath)) return;

    // Find all paths that start with the current path followed by a dot
    const childPaths = sortedPaths.filter(
      (otherPath) => otherPath !== currentPath && otherPath.startsWith(`${currentPath}.`),
    );

    // If this path has children, mark it for removal
    if (childPaths.length > 0) {
      pathsToRemove.add(currentPath);
    }
  });

  // Remove all conflicting parent paths
  pathsToRemove.forEach((pathToRemove) => {
    delete resolved[pathToRemove];
  });

  return resolved;
};

const convertSelectToProject = (selectParam) => {
  // e.g selectParam = '-likes -bookmarks -comments -tags -reposts -repostWithThought'
  const select = Array.isArray(selectParam) ? selectParam : selectParam.trim().split(' ');
  return select.reduce((acc, field) => ({ ...acc, [field.replace('-', '')]: field[0] === '-' ? 0 : 1 }), {});
};

const convertPopulateToLookup = (
  populateParam,
  finalProjectParam = {},
  firstLevelProjectAggregate = {},
  isSubPopulate = false,
  parentIsArray = false,
) => {
  // e.g populateParam = { path: 'user', select: 'firstName lastName photo _id business', populate: { path: 'photo', select: 'url' }, from: 'users' }
  const populate = Array.isArray(populateParam) ? populateParam : [populateParam];
  const aggregateStages = [];
  let finalProjectAggregate = finalProjectParam;

  populate.forEach((p) => {
    const pPath = p.as || p.path;
    aggregateStages.push({
      $lookup: {
        from: p.from,
        localField: p.path,
        foreignField: '_id',
        as: pPath,
      },
    });
    if (!p.isArray && !parentIsArray) {
      aggregateStages.push({ $unwind: { path: `$${pPath}`, preserveNullAndEmptyArrays: true } });
    }

    // let combinedProjectKeys = {};
    if (p.select) {
      const currentProjectAggregate = convertSelectToProject(
        p.select
          .split(' ')
          .map((item) => `${pPath}.${item}`)
          .join(' '),
      );
      finalProjectAggregate = { ...firstLevelProjectAggregate, ...finalProjectAggregate, ...currentProjectAggregate };
    }
    if (p.populate) {
      let subPopulate = Array.isArray(p.populate) ? p.populate : [p.populate];
      subPopulate = subPopulate.map((subP) => {
        const subPath = `${p.path}.${subP.path}`;
        return { ...subP, path: subPath };
      });
      const [subPopulates, subProjectAggregate] = convertPopulateToLookup(
        subPopulate,
        finalProjectAggregate,
        {},
        true,
        p.isArray || parentIsArray,
      );
      finalProjectAggregate = { ...finalProjectAggregate, ...subProjectAggregate };
      aggregateStages.push(...subPopulates);
    } else if (!isSubPopulate) {
      finalProjectAggregate = resolveProjectionConflicts(finalProjectAggregate);
      aggregateStages.push({ $project: finalProjectAggregate });
    }
  });

  return [aggregateStages, finalProjectAggregate];
};

/**
 * Test function to demonstrate resolveProjectionConflicts with a specific projection
 * You can call this function to see how conflicts are resolved
 */
const testProjectionConflictResolution = () => {
  const problematicProjection = {
    group: 1,
    text: 1,
    media: 1,
    reactions: 1,
    allCommentsCount: 1,
    parentPost: 1,
    visibility: 1,
    edited: 1,
    createdAt: 1,
    updatedAt: 1,
    'media.url': 1,
    'media.thumbnail': 1,
    'media.metadata': 1,
    'media._id': 1,
    'media.thumbnail.url': 1,
    'media.thumbnail._id': 1,
    'user.firstName': 1,
    'user.lastName': 1,
    'user.photo': 1,
    'user._id': 1,
    'user.business': 1,
    'user.online': 1,
    'user.followers': 1,
    'user.following': 1,
    'user.tagLine': 1,
    'user.photo.url': 1,
    'user.business.name': 1,
    'parentPost.user': 1,
    'parentPost.media': 1,
    'parentPost._id': 1,
    'parentPost.text': 1,
    'parentPost.visibility': 1,
    'parentPost.createdAt': 1,
    'parentPost.user.firstName': 1,
    'parentPost.user.lastName': 1,
    'parentPost.user.photo': 1,
    'parentPost.user._id': 1,
    'parentPost.user.business': 1,
    'parentPost.user.online': 1,
    'parentPost.user.tagLine': 1,
    'parentPost.user.photo.url': 1,
    'parentPost.user.business.name': 1,
    'parentPost.media.url': 1,
    'parentPost.media.thumbnail': 1,
    'parentPost.media.metadata': 1,
    'parentPost.media._id': 1,
    'parentPost.media.thumbnail.url': 1,
    'parentPost.media.thumbnail._id': 1,
    'group.name': 1,
    'group.type': 1,
    'group.profilePhoto': 1,
    'group._id': 1,
    'group.admin': 1,
    'group.coAdmins': 1,
    'group.members': 1,
    'group.profilePhoto.url': 1,
    'group.profilePhoto._id': 1,
    'reactions.user': 1,
    'reactions.type': 1,
  };

  const resolvedProjection = resolveProjectionConflicts(problematicProjection);

  logger.info('Original projection conflicts:');
  logger.info('- group: 1 conflicts with group.name, group.type, etc.');
  logger.info('- media: 1 conflicts with media.url, media.thumbnail, etc.');
  logger.info('- parentPost: 1 conflicts with parentPost.user, parentPost.media, etc.');
  logger.info('- reactions: 1 conflicts with reactions.user, reactions.type');

  logger.info('Resolved projection (conflicts removed):');
  logger.info(JSON.stringify(resolvedProjection, null, 2));

  return resolvedProjection;
};

module.exports = {
  generateUniqueId,
  deepFreeze,
  validateObjectData,
  validateModelDataArray,
  deleteFolder,
  truncateText,
  timeAgo,
  formatDate,
  convertPopulateToLookup,
  convertSelectToProject,
  testProjectionConflictResolution,
};
