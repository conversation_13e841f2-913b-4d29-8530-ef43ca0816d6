const jwt = require('jsonwebtoken');
const moment = require('moment');
const httpStatus = require('http-status');
const config = require('../config/config');
const { Token, User } = require('../models');
const ApiError = require('../utils/ApiError');
const { tokenTypes } = require('../config/tokens');

const generateToken = (data, expires, type, secret = config.jwt.secret, signupAs = undefined) => {
  const payload = {
    sub: data,
    iat: moment().unix(),
    exp: expires.unix(),
    type,
    signupAs,
  };

  if (!signupAs) delete payload.signupAs;

  return jwt.sign(payload, secret);
};

const saveToken = async (token, userId, expires, type, blacklisted = false, signupAs = undefined) => {
  const data = {
    token,
    user: userId,
    expires: expires.toDate(),
    type,
    blacklisted,
  };
  if (signupAs) data.signupAs = signupAs;
  const tokenDoc = await Token.create(data);
  return tokenDoc;
};

const verifyToken = async (token, type) => {
  const payload = jwt.verify(token, config.jwt.secret);
  const tokenDoc = await Token.findOne({ token, type, user: payload.sub.userId || payload.sub, blacklisted: false });
  return { tokenDoc: tokenDoc || payload, payload };
};

const generateAuthTokens = async (user, onlyAccessToken = false) => {
  const payloadData = { userId: user._id || user.id, roles: user.roles };
  if (user.pendingBusiness) {
    payloadData.businessId = user.business;
    payloadData.pendingBusiness = user.pendingBusiness;
  }

  if (onlyAccessToken) {
    const accessTokenExpires = moment().add(config.jwt.accessExpirationMinutes, 'minutes');
    const accessToken = generateToken(payloadData, accessTokenExpires, tokenTypes.ACCESS);
    return { access: accessToken };
  }
  const accessTokenExpires = moment().add(config.jwt.accessExpirationMinutes, 'minutes');
  const accessToken = generateToken(payloadData, accessTokenExpires, tokenTypes.ACCESS);

  const refreshTokenExpires = moment().add(config.jwt.refreshExpirationDays, 'days');
  const refreshToken = generateToken(payloadData, refreshTokenExpires, tokenTypes.REFRESH);
  await saveToken(refreshToken, user._id || user.id, refreshTokenExpires, tokenTypes.REFRESH);

  return {
    access: accessToken,
    refresh: refreshToken,
  };
};

const generateResetPasswordToken = async (email) => {
  const user = await User.findOne({ email });
  if (!user) {
    // Don't reveal if user does not exist
    throw new ApiError(
      httpStatus.OK,
      'Kindly follow the instructions in your email to reset your password.',
      undefined,
      undefined,
      'SUCCESS',
    );
  } else if (user.pendingPasswordResetCount > 3) {
    throw new ApiError(httpStatus.TOO_MANY_REQUESTS, 'Too many pending requests for password reset. Kindly contact support');
  } else {
    const expires = moment().add(config.jwt.resetPasswordExpirationMinutes, 'minutes');
    const resetPasswordToken = generateToken(user._id, expires, tokenTypes.RESET_PASSWORD);
    await saveToken(resetPasswordToken, user._id, expires, tokenTypes.RESET_PASSWORD);
    return { resetPasswordToken, user };
  }
};

const generateVerifyEmailToken = async (user, signupAs) => {
  const expires = moment().add(config.jwt.verifyEmailExpirationMinutes, 'minutes');
  const verifyEmailToken = generateToken(user._id, expires, tokenTypes.VERIFY_EMAIL, undefined, signupAs);
  await saveToken(verifyEmailToken, user._id, expires, tokenTypes.VERIFY_EMAIL, undefined, signupAs);
  return verifyEmailToken;
};

module.exports = {
  generateToken,
  saveToken,
  verifyToken,
  generateAuthTokens,
  generateResetPasswordToken,
  generateVerifyEmailToken,
};
