const httpStatus = require('http-status');
const { blogPostService } = require('../services');
const catchAsync = require('../utils/catchAsync');

const createBlogPost = catchAsync(async (req, res) => {
  const blogPost = await blogPostService.createBlogPost(req);
  res.status(httpStatus.CREATED).json({ data: blogPost, message: 'Blog created successfully', status: 'SUCCESS' });
});

const updateBlogPost = catchAsync(async (req, res) => {
  const blogPost = await blogPostService.updateBlogPost(req);
  res.status(httpStatus.OK).json({ data: blogPost, message: 'Blog updated successfully', status: 'SUCCESS' });
});

const updateBlogPostStatus = catchAsync(async (req, res) => {
  const blogPost = await blogPostService.updateBlogPostStatus(req);
  res.status(httpStatus.OK).json({ data: blogPost, message: 'Blog status updated successfully', status: 'SUCCESS' });
});

const getBlogPosts = catchAsync(async (req, res) => {
  const blogPosts = await blogPostService.getBlogPosts(req);
  res.status(httpStatus.OK).json({ data: blogPosts, message: 'Blog posts retrieved successfully', status: 'SUCCESS' });
});

const getBlogPostByUniqueTitle = catchAsync(async (req, res) => {
  const blogPost = await blogPostService.getBlogPostByUniqueTitle(req);
  res.status(httpStatus.OK).json({ data: blogPost, message: 'Blog post retrieved successfully', status: 'SUCCESS' });
});

const react = catchAsync(async (req, res) => {
  const { reaction, existingReaction } = req.body;
  const blogPost = await blogPostService.react(req?.params?.id, reaction, existingReaction);
  res.status(httpStatus.OK).json({ data: blogPost, message: 'Blog post upvoted successfully', status: 'SUCCESS' });
});

const deleteBlogPost = catchAsync(async (req, res) => {
  await blogPostService.deleteBlogPost(req?.params?.id);

  res.status(httpStatus.OK).json({ message: 'Blog post deleted successfully', status: 'SUCCESS' });
});

const getCategories = catchAsync(async (req, res) => {
  const categories = await blogPostService.getCategories();
  res.status(httpStatus.OK).json({ data: categories, message: 'Categories retrieved successfully', status: 'SUCCESS' });
});

const getStats = catchAsync(async (req, res) => {
  const response = await blogPostService.getStats();
  res.status(httpStatus.OK).json({ data: response, message: 'Stats retrieved successfully', status: 'SUCCESS' });
});

module.exports = {
  createBlogPost,
  getBlogPosts,
  getBlogPostByUniqueTitle,
  react,
  deleteBlogPost,
  getCategories,
  updateBlogPost,
  updateBlogPostStatus,
  getStats,
};
