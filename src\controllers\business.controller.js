/* eslint-disable no-case-declarations */
const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { businessService } = require('../services');
const { pick, removeUndefinedKeys } = require('../utils/pick');

const getEnums = catchAsync(async (req, res) => {
  const data = await businessService.getEnums();

  res.status(httpStatus.OK).json({ status: 'SUCCESS', data, message: 'Business Enums retrieved successfully' });
});

const getPrivateEnums = catchAsync(async (req, res) => {
  const data = await businessService.getEnums(true);

  res.status(httpStatus.OK).json({ status: 'SUCCESS', data, message: 'Business Enums retrieved successfully' });
});

const createBusiness = catchAsync(async (req, res) => {
  const business = await businessService.createBusiness(req.user, req.body, req.files);
  res.status(httpStatus.CREATED).json({ status: 'SUCCESS', data: business, message: 'Business created successfully' });
});

const onboardBusiness = catchAsync(async (req, res) => {
  const business = await businessService.onboardBusiness(req);
  res.status(httpStatus.CREATED).json({ status: 'SUCCESS', data: business, message: 'Business onboarded successfully' });
});

const getBusinesses = catchAsync(async (req, res) => {
  const filter = pick(req.query, ['name']);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  removeUndefinedKeys(filter);

  const businesses = await businessService.getBusinesses(filter, options);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: businesses, message: 'Businesses retrieved successfully' });
});

const getBusiness = catchAsync(async (req, res) => {
  const business = await businessService.getBusiness(req);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: business, message: 'Business retrieved successfully' });
});

const getBusinessByUsername = catchAsync(async (req, res) => {
  const business = await businessService.getBusinessByUsername(req);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: business, message: 'Business retrieved successfully' });
});

const updateBusiness = catchAsync(async (req, res) => {
  const isOwner = req.user?.business?.toString() === req.params.id;
  if (!isOwner)
    return res.status(httpStatus.UNAUTHORIZED).json({ status: 'FAILED', message: 'Only business owner can perform action' });
  const business = await businessService.updateBusiness(req.params.id, req.body, req.files);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: business, message: 'Business updated successfully' });
});

const deleteBusiness = catchAsync(async (req, res) => {
  const isOwner = req.user && req.user.business && req.user.business.toString() === req.params.id;
  if (!isOwner)
    return res.status(httpStatus.UNAUTHORIZED).json({ status: 'FAILED', message: 'Only business owner can perform action' });
  await businessService.deleteBusiness(req.user, req.params.id);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Business deleted successfully' });
});

const addBusinessProfileResource = catchAsync(async (req, res) => {
  const { id } = req.params;
  const isOwner = req.user && req.user.business && req.user.business.toString() === id;
  if (!isOwner)
    return res.status(httpStatus.UNAUTHORIZED).json({ status: 'FAILED', message: 'Only business owner can perform action' });

  const { resourceName } = req.query;
  await businessService.addBusinessProfileResource(req.user.business, resourceName, req.body);
  res.status(httpStatus.CREATED).json({ status: 'SUCCESS', message: 'Business resource added successfully' });
});

const updateBusinessProfileResource = catchAsync(async (req, res) => {
  const { id, resourceId } = req.params;
  const isOwner = req.user && req.user.business && req.user.business.toString() === id;
  if (!isOwner)
    return res.status(httpStatus.UNAUTHORIZED).json({ status: 'FAILED', message: 'Only business owner can perform action' });

  const { resourceName } = req.query;
  await businessService.updateBusinessProfileResource(req.user.business, resourceId, resourceName, req.body);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Business resource updated successfully' });
});

const deleteBusinessProfileResource = catchAsync(async (req, res) => {
  const { id, resourceId } = req.params;
  const isOwner = req.user && req.user.business && req.user.business.toString() === id;
  if (!isOwner)
    return res.status(httpStatus.UNAUTHORIZED).json({ status: 'FAILED', message: 'Only business owner can perform action' });

  const { resourceName } = req.query;
  await businessService.deleteBusinessProfileResource(req.user.business, resourceId, resourceName);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Business resource deleted successfully' });
});

const fetchVerifications = catchAsync(async (req, res) => {
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const filter = pick(req.query, ['verificationStatus', 'type', 'displayName']);
  const businesses = await businessService.fetchVerifications(filter, options);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: businesses, message: 'Businesses retrieved successfully' });
});

const fetchBusinessVerificationInfo = catchAsync(async (req, res) => {
  const businessInfo = await businessService.fetchBusinessVerificationInfo(req.params.id);
  res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data: businessInfo, message: 'Business information retrieved successfully' });
});

const fetchUnverifiedBusinesses = catchAsync(async (req, res) => {
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const filter = pick(req.query, ['verificationStatus', 'type', 'displayName']);
  const businesses = await businessService.fetchUnverifiedBusinesses(filter, options);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: businesses, message: 'Businesses retrieved successfully' });
});

const assignVerifier = catchAsync(async (req, res) => {
  const { verifier, parentVerification } = req.body;
  await businessService.assignVerifier(req.params.id, req.user.id, verifier, parentVerification);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Verifier assigned successfully' });
});

const verifyBusiness = catchAsync(async (req, res) => {
  await businessService.verifyBusiness(req.params.id, req.user.id, req.body);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Business verified successfully' });
});

const fetchParentVerification = catchAsync(async (req, res) => {
  const parentVerification = await businessService.fetchParentVerification(req.params.id);
  res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data: parentVerification, message: 'Parent verification retrieved successfully' });
});

module.exports = {
  getEnums,
  getPrivateEnums,
  createBusiness,
  onboardBusiness,
  getBusinesses,
  getBusiness,
  getBusinessByUsername,
  updateBusiness,
  addBusinessProfileResource,
  updateBusinessProfileResource,
  deleteBusinessProfileResource,
  deleteBusiness,
  fetchVerifications,
  fetchUnverifiedBusinesses,
  fetchBusinessVerificationInfo,
  assignVerifier,
  verifyBusiness,
  fetchParentVerification,
};
