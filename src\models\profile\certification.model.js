const mongoose = require('mongoose');
const { toJSON, paginate } = require('../plugins');

const certificationSchema = new mongoose.Schema({
  certName: { type: String, trim: true, required: true },
  issuingOrganization: { type: String, trim: true, required: true },
  dateIssued: { type: String, trim: true, required: true },
  certLink: { type: String, trim: true },
});

certificationSchema.plugin(toJSON);
certificationSchema.plugin(paginate);

const Certification = mongoose.model('Certification', certificationSchema);
module.exports = Certification;
