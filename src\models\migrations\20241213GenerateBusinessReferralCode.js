const Async = require('async');
const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const Business = require('../business.model');
const Referral = require('../referral.model');

const generateBusinessReferralCode = async () => {
  const migrated = await GlobalVariable.findOne({ name: 'Generate Business Referral Code' });
  if (migrated) {
    // eslint-disable-next-line no-useless-return
    return;
  }

  const businesses = await Business.find();
  await Async.eachOfSeries(businesses, async (business) => {
    const referralCode = await Referral.generateUniqueReferralCode();
    await Business.updateOne({ _id: business._id }, { $set: { referralCode } });
  });

  await GlobalVariable.create({ name: 'Generate Business Referral Code', value: 'true' });
  logger.info('Business referral codes generated');
};

module.exports = generateBusinessReferralCode;
