/* eslint-disable no-console */
const { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, until } = require('selenium-webdriver');
const { Options } = require('selenium-webdriver/edge');
const async = require('async');
const fs = require('fs');
const path = require('path');
const { capitalizeEachWord } = require('../stringFormatting');
// const loadUniversitiesData = require('./loadScrapeToDatabase');

const universities = [];
let currentState;
let currentStateName;
let totalStatePages;
let totalStates;
let currentPage;

const getProperty = async (element, selector) => {
  try {
    return await element.findElement(By.css(selector)).getText();
  } catch (error) {
    if (error.name === 'NoSuchElementError') {
      console.log('-------------------NoSuchElementError-----------------');
      console.log(error);
      console.log('-------------------NoSuchElementError-----------------');
      return undefined;
    }
    console.log('Another type of error');
    throw error;
  }
};

const makeObjectFromTable = async (tableElement, selectString, rowOneColOneAsKey = true) => {
  const resultObject = {};
  // eslint-disable-next-line no-param-reassign
  const tableRows = await tableElement.findElements(By.css(`${selectString || 'tbody tr'}`));
  const thElements = await tableElement.findElements(By.css('thead th'));
  await async.eachOfSeries(thElements, async (thElement, index) => {
    // map elements to their text content
    thElements[index] = (await thElement.getText()).trim().replace(/^:|:$/g, '');
  });
  if (thElements.length > 0 && rowOneColOneAsKey) {
    [resultObject.key, ,] = thElements;
  }
  resultObject.value = [];

  await async.eachOfSeries(tableRows, async (tableRow) => {
    const tdElements = await tableRow.findElements(By.css('td'));
    if (thElements.length > 0 && tdElements.length !== thElements.length) {
      throw new Error('Each row must have the same number of entries as header');
    }
    await async.eachOfSeries(tdElements, async (tdElement, index) => {
      // map elements to their text content
      tdElements[index] = (await tdElement.getText()).trim().replace(/^:|:$/g, '');
    });

    if (thElements.length > 0) {
      // Perform operation for when table has header
      if (rowOneColOneAsKey) {
        // handle when table has header and row one col one will be the parent key of the object
        for (let i = 1; i < thElements.length; i += 1) {
          resultObject.value.push({
            key: tdElements[0],
            value: { key: thElements[i], value: tdElements[i] },
          });
        }
      } else {
        // handle when table has header but row one col one will not be the parent key of the object
        // resultObject = [];
        // resultObject.push({});
      }
    } else {
      if (tdElements.length > 2) {
        throw new Error('Table must have header if it has more than 2 columns');
      }
      const key = tdElements[0];
      const value = tdElements[1];
      resultObject.value.push({ key, value });
    }
  });

  return resultObject;
};

const getGeneralInformation = async (driver) => {
  const generalInfoSectionArr = await driver.findElements(
    By.css(
      '#divctl00_cphCollegeNavBody_ucInstitutionMain_dtpGeneral .tabconstraint:not([id="ctl00_cphCollegeNavBody_ucInstitutionMain_divFaculty"])',
    ),
  );

  if (generalInfoSectionArr[0]) {
    const generalInfoSection = generalInfoSectionArr[0];
    const generalInfoTable = await generalInfoSection.findElement(By.css('table'));
    const generalInformation = (await makeObjectFromTable(generalInfoTable)).value; // Object will only have value attribute, no key attribute

    let mission = await generalInfoSection.findElements(By.css('.mscontainer'));
    [, ...mission] = mission[0] ? (await mission[0].getText()).split('\n') : [];

    generalInformation.push({ key: 'mission', value: mission.join('\n') });

    const divNPCArr = await driver.findElements(By.css('#ctl00_cphCollegeNavBody_ucInstitutionMain_divNPC'));
    if (divNPCArr[0]) {
      const divNPC = divNPCArr[0];
      const npcHeadingElements = await divNPC.findElements(By.css('div[style*="font-weight:bold"]'));
      let npcHeadings = [];
      await async.eachOfSeries(npcHeadingElements, async (npcHeading) => {
        npcHeadings.push(await npcHeading.getText());
      });
      npcHeadings = [...npcHeadings.slice(npcHeadings.length / 2 + 1), ...npcHeadings.slice(0, npcHeadings.length / 2 + 1)]; // Rearrange the headings
      const npcPattern = new RegExp(npcHeadings.map((heading) => `\\b${heading}\\b`).join('|'), 'g');
      let npcText = (await divNPC.getText())
        .split(npcPattern)
        .filter((text) => text.trim() !== '' && text.trim() !== '\n')
        .map((text) => text.replace(/^\n+|\n+$/g, ''));
      npcText = [...npcText.slice(npcText.length / 2 + 1), ...npcText.slice(0, npcText.length / 2 + 1)]; // Rearrange the text
      generalInformation.more = {};
      for (let i = 0; i < npcHeadings.length; i += 1) {
        generalInformation.push({ key: npcHeadings[i], value: npcText[i] });
        generalInformation.more[npcHeadings[i]] = npcText[i];
      }
    }

    return generalInformation;
  }
  return {};
};

const getFacultyInfo = async (driver) => {
  const divFaculty = await driver.findElement(By.css('#ctl00_cphCollegeNavBody_ucInstitutionMain_divFaculty.tabconstraint'));
  const facultyInfo = await makeObjectFromTable(await divFaculty.findElement(By.css('table')));

  return { key: `Faculty Information, ${facultyInfo.key.split(', ')[1]}`, value: facultyInfo.value };
};

const getProgramsInfo = async (driver) => {
  const divPrograms = await driver.findElement(By.css('#divctl00_cphCollegeNavBody_ucInstitutionMain_ctl07'));

  const headerRows = await divPrograms.findElements(By.css('thead th'));
  const categoryHeaders = await divPrograms.findElements(By.css('tbody tr.subrow.nb'));
  const headerAndContentRows = await divPrograms.findElements(By.css('tbody tr'));
  const programsInfo = {
    key: await divPrograms.findElement(By.css('.tablenames')).getText(),
    value: [],
    note: 'Data shown are for first majors.\nnull Program is not offered at this award level.',
  };
  let currentRowIndex = 1;
  await async.eachSeries(categoryHeaders, async (categoryHeader) => {
    const category = await categoryHeader.getText();

    await async.doWhilst(
      async () => {
        const program = {};
        const trClassList = await headerAndContentRows[currentRowIndex].getAttribute('class');

        if (trClassList.includes('level1indent')) {
          // It's a row (program) under the category
          program.category = category;
          const programInfo = await headerAndContentRows[currentRowIndex].findElements(By.css('td'));
          program.awardLevels = [];

          await async.eachOfSeries(headerRows, async (headerRow, index) => {
            if (index === 0) {
              // First column contains name of program
              program.name = await programInfo[index].getText();
            } else {
              let count = await programInfo[index].getText();
              const offersDistanceEducation = count.slice(-1) === 'd';
              count = offersDistanceEducation ? count.slice(0, -1) : count;

              program.awardLevels.push({
                name: capitalizeEachWord(await headerRow.getText()),
                count: count === '-' ? null : parseInt(count, 10),
                offersDistanceEducation,
              });
            }
          });
          programsInfo.value.push(program);
        }
      },
      async () => {
        const nextRowClassList = await headerAndContentRows[currentRowIndex].getAttribute('class');
        currentRowIndex += 1;
        return (
          !(nextRowClassList.includes('subrow') && nextRowClassList.includes('nb')) &&
          currentRowIndex < headerAndContentRows.length
        );
      },
    );
  });

  return programsInfo;
};

const updateUniversitiesData = async (driver) => {
  let universityElements = await driver.findElements(
    By.css(
      '#ctl00_cphCollegeNavBody_ucResultsMain_tblResults .resultsW, #ctl00_cphCollegeNavBody_ucResultsMain_tblResults .resultsY',
    ),
  );

  // Iterate over the university elements and scrape the data for each university
  await async.eachOfSeries(Array(universityElements.length).fill(), async (_, index) => {
    universityElements = await driver.findElements(
      By.css(
        '#ctl00_cphCollegeNavBody_ucResultsMain_tblResults .resultsW, #ctl00_cphCollegeNavBody_ucResultsMain_tblResults .resultsY',
      ),
    );
    // if (![7, 8].includes(index + 1)) {
    //   return;
    // } // Used to skip universities
    const universityElement = universityElements[index];
    const universityLinkElement = await universityElement.findElement(By.css('.infobutton + td a'));
    let university = [];

    // Hover over info button to make the brief info popup visible
    const actions = driver.actions({ async: true });
    await actions.move({ origin: await universityElement.findElement(By.css('.infobutton')) }).perform();

    const [, addressBrief] = (await universityElement.findElement(By.css('.infobutton + td')).getText()).split('\n');
    const infoButtonDisplay = await universityElement.findElement(By.css('.infobutton .iPop')).getCssValue('display');
    let [name, address, infoButtonInfo, ipeds] = [undefined, undefined, undefined, undefined];
    let infoDataExtracted = true;
    if (infoButtonDisplay === 'block') {
      const iTable = await universityElement.findElement(By.css('.infobutton .iPop .itables'));

      [name, ...address] = (await iTable.findElement(By.css('.pbe')).getText()).split('\n');

      infoButtonInfo = await makeObjectFromTable(iTable, 'tbody tr:not(:nth-child(1)):not(:has(p.ipeds))');
      ipeds = await getProperty(iTable, 'tr .ipeds.hoverID');
    } else {
      infoDataExtracted = false;
    }

    // Get more university data from university details page
    await universityLinkElement.click();
    await driver.wait(until.elementLocated(By.css('#ctl00_cphCollegeNavBody_divWidth .dashboard')), 30000);
    await driver.wait(until.elementLocated(By.id('ctl00_cphCollegeNavBody_ucInstitutionMain_dtpGeneral')), 30000);
    console.log(infoDataExtracted);

    if (!infoDataExtracted) {
      // Perform operation for when info button is changing display after hover
      const infoDiv = await driver.findElement(By.css('#ctl00_cphCollegeNavBody_divWidth .dashboard .collegedash'));
      [name, ...address] = (await infoDiv.findElement(By.css('div:nth-child(2) > span')).getText()).split('\n');
      infoButtonInfo = await makeObjectFromTable(infoDiv, 'div:nth-child(2) table tbody tr');
      ipeds = await getProperty(infoDiv, '.mapngo span');
    }

    university = [
      ...university,
      { key: 'name', value: name },
      { key: 'address', value: address.pop() },
      ...infoButtonInfo.value,
    ];
    const note = address.pop();
    if (note) university.push({ note });

    university.push({ key: 'City, State', value: addressBrief });
    ipeds = ipeds
      ?.split('ID: ')
      .slice(1)
      .map((id) => id.split(' | OPE ')[0]);
    const [ipedsId, opeId] = ipeds || [undefined, undefined];
    university.push({ key: 'IPEDS ID', value: ipedsId });
    university.push({ key: 'OPE ID', value: opeId });

    try {
      // Wait for the mentioned sections to become available
      await driver.findElement(By.id('divctl00_cphCollegeNavBody_ucInstitutionMain_ctl00'));
      // Click expand all button
      await driver.findElement(By.css('.fadeyell .expandcollapse a:first-child')).click();
      await driver.wait(
        until.elementLocated(By.css('#divctl00_cphCollegeNavBody_ucInstitutionMain_dtpGeneral.detailOn')),
        30000,
      );

      university = [...university, ...(await getGeneralInformation(driver))];
      university.push({
        key: 'website',
        value: await driver.findElement(By.css('.collegedash .layouttab tbody tr:nth-child(2) td a')).getText(),
      });

      university.push(await getFacultyInfo(driver));
      const programsInfo = await getProgramsInfo(driver);
      university.push(programsInfo);
    } catch (error) {
      console.log('Error: ', error);
      if (error.name === 'TimeoutError' || error.name === 'NoSuchElementError') {
        console.log(`-------------------TimeoutError---No ${university.name} Details Page--------------`);
        console.log(error.name);
        console.log(`-------------------TimeoutError---No ${university.name} Details Page--------------`);
      } else {
        console.log('Another type of error');
        throw error;
      }
    }

    university.push({ 'Crawl Id': `${currentState}-${currentPage}-${index + 1}` });
    universities.push(university);
    console.log(`--------------New University Added------------------------------------------------------`);
    console.log(university);
    console.log(`----------------------------------------------------------------------------------------`);
    console.log(`Current State: ${currentStateName} (${currentState} of ${totalStates})`);
    console.log(`Page: ${currentPage} of ${totalStatePages}`);
    console.log(`Total Parsed Universities: ${universities.length}`);
    console.log(`----------------New University Added-----------------------------------------------------`);

    await driver.navigate().back();

    // Wait for the navigation to complete
    await driver.wait(until.elementLocated(By.id('ctl00_cphCollegeNavBody_ucResultsMain_tblResults')), 30000);
  });

  return driver;
};

(async () => {
  // Launch the browser and open a new blank page
  let driver = await new Builder().forBrowser(Browser.EDGE).setEdgeOptions(new Options().headless()).build();

  try {
    await driver.get('https://nces.ed.gov/collegenavigator');

    // Check for all the option elements in the select element
    let optionElements = await driver
      .findElement(By.id('ctl00_cphCollegeNavBody_ucSearchMain_ucMapMain_lstState'))
      .findElements(By.css('select option:not([value="all"])'));

    totalStates = optionElements.length;
    currentState = 1;

    // Iterate over the option elements and scrape the data for each state
    await async.eachOfSeries(Array(optionElements.length).fill(), async (_, index) => {
      const stateSelectElement = await driver.findElement(By.id('ctl00_cphCollegeNavBody_ucSearchMain_ucMapMain_lstState'));
      optionElements = await driver
        .findElement(By.id('ctl00_cphCollegeNavBody_ucSearchMain_ucMapMain_lstState'))
        .findElements(By.css('select option:not([value="all"])'));

      const option = optionElements[index];
      const optionValue = await option.getAttribute('value');
      currentStateName = await option.getText();
      // Use the keyboard to select the next option element
      await stateSelectElement.sendKeys(Key.ARROW_DOWN);

      // if (![10, 19, 39].includes(index + 1)) {
      //   currentState += 1;
      //   console.log('Skipping state:', index + 1, await optionElements[index].getText());
      //   return;
      // } // Used to skip states

      // Click the show results button and wait for the navigation to complete
      const showResultsButton = await driver.findElement(By.id('ctl00_cphCollegeNavBody_ucSearchMain_btnSearch'));
      showResultsButton.click();
      await driver.wait(
        async () => (await driver.getCurrentUrl()) === `https://nces.ed.gov/collegenavigator/?s=${optionValue}`,
        30000,
      );

      let nextPage = await driver.findElements(
        By.css('#ctl00_cphCollegeNavBody_ucResultsMain_divPagingControls .colorful a:last-child'),
      );
      currentPage = 1;

      await async.doWhilst(
        async () => {
          const totalPages = await driver.findElements(
            By.css('#ctl00_cphCollegeNavBody_ucResultsMain_divPagingControls > a'),
          );
          const temp = (await totalPages.splice(-1)[0]?.getText()) || 1;
          totalStatePages = currentPage > temp ? totalStatePages : temp; // When on last page a:last-child will be the second to last page number

          // if ([26, 1, 2].includes(currentPage)) {
          //   driver = await updateUniversitiesData(driver);
          // } // Used to skip pages. Remove the next line when using this
          driver = await updateUniversitiesData(driver);

          nextPage = await driver.findElements(
            By.css('#ctl00_cphCollegeNavBody_ucResultsMain_divPagingControls .colorful a:last-child'),
          );
          if (nextPage[0] && (await nextPage[0].getText()) !== '« Previous Page') {
            console.log('Clicking next page');
            console.log('URL before:', await driver.getCurrentUrl());
            await nextPage[0].click();
            await driver.wait(
              async () =>
                (await driver.getCurrentUrl()) ===
                `https://nces.ed.gov/collegenavigator/?s=${optionValue}&pg=${currentPage + 1}`,
              30000,
            );
            await driver.wait(until.elementLocated(By.id('ctl00_cphCollegeNavBody_ucResultsMain_tblResults')), 30000);
            console.log('URL after:', await driver.getCurrentUrl());
          }
          currentPage += 1;
        },
        async () => currentPage <= totalStatePages, // nextPage.length > 0 && (await nextPage[0].getText()) === 'Next Page »',
      );
      currentState += 1;
    });
  } catch (error) {
    console.log('An error occurred:', error);
    // await driver.quit();
  } finally {
    // await loadUniversitiesData(universities);
    // Write the universities data to a JSON file
    const jsonData = JSON.stringify(universities);
    fs.writeFile(`${path.join(__dirname, 'universities.json')}`, jsonData, (err) => {
      if (err) {
        console.log('Error writing to the file:', err);
      } else {
        console.log('Universities JSON data has been saved');
      }
    });
    // Close the driver
    await driver.quit();
  }
})();
