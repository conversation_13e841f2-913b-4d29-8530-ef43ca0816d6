module.exports = {
  post: {
    summary: 'Sign up',
    description: 'Create a new user account.',
    tags: ['Authentication'],
    requestBody: {
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              password: {
                type: 'string',
                example: 'secretPassword',
              },
              email: {
                type: 'string',
                example: '<EMAIL>',
              },
              username: {
                type: 'string',
                example: 'simplyUnyk',
              },
              firstName: {
                type: 'string',
                example: 'Corlys',
              },
              middleName: {
                type: 'string',
                example: 'Belmont',
              },
              lastName: {
                type: 'string',
                example: 'Velaryon',
              },
            },
            required: ['email', 'password', 'firstName', 'lastName', 'username'],
          },
        },
      },
    },
    responses: {
      201: {
        description: 'Account Created',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: {
                  type: 'string',
                  example: 'SUCCESS',
                },
                message: {
                  type: 'string',
                  example: 'Your account has been created. Kindly check your email for verification.',
                },
              },
            },
          },
        },
      },
      400: {
        description: 'Bad request. Invalid input.',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: {
                  type: 'string',
                  example: 'FAILED',
                },
                message: {
                  type: 'string',
                  example: 'Bad request: Invalid input',
                },
              },
            },
          },
        },
      },
    },
  },
};
