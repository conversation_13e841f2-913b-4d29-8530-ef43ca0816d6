# Enhanced Post Ranking Algorithm (CosmosDB Compatible)

## Overview

The enhanced post ranking algorithm addresses the issue where all posts from one followed user would appear before posts from other followed users. The new system provides a comprehensive ranking approach that considers multiple factors to improve post distribution and user engagement.

**Important**: This implementation is specifically designed to be compatible with **Azure Cosmos DB with MongoDB API**, which has limitations compared to full MongoDB aggregation pipeline support.

## Key Improvements

### 1. **Multi-Factor Ranking System**
The algorithm now considers 7 major categories of ranking factors:

- **Relationship-based scoring**: Following, followers, mutual connections
- **Engagement metrics**: Reactions, comments, reposts, bookmarks
- **Content quality**: Media presence, text length, edited status
- **Freshness decay**: Time-based scoring with configurable decay rates
- **Author influence**: Follower count with logarithmic scaling
- **Diversity enforcement**: Prevents same author domination
- **Randomness factor**: Adds variety to prevent predictable feeds

### 2. **Configurable Weights**
All ranking factors use configurable weights that can be fine-tuned:

```javascript
const RANKING_WEIGHTS = {
  following: 2,           // Weight for following someone
  followedBy: 3,          // Weight for being followed by someone
  mutualConnection: 3,    // Weight for mutual following
  engagement: 0.3,        // Weight for engagement metrics
  hasMedia: 2,           // Weight for posts with media
  textLength: 1,         // Weight for posts with substantial text
  edited: 0.5,           // Weight for edited posts
  authorOnline: 1,       // Weight for online authors
  authorInfluence: 0.5,  // Weight for author influence (follower count)
  randomness: 1,         // Weight for random diversity factor
  freshness: {           // Time-based freshness weights
    veryFresh: 5,        // < 1 hour
    fresh: 3,            // < 6 hours
    recent: 1,           // < 24 hours
    week: 0.5,           // < 1 week
    old: 0.1,            // > 1 week
  },
  engagement_multipliers: {
    comments: 2,         // Comments are worth more than reactions
    reposts: 1.5,        // Reposts show strong engagement
    bookmarks: 1.2,      // Bookmarks show intent to revisit
  },
};
```

### 3. **Advanced Features**

#### **Time-Based Freshness Decay**
Posts receive different freshness scores based on their age:
- Very fresh (< 1 hour): 5 points
- Fresh (< 6 hours): 3 points
- Recent (< 24 hours): 1 point
- Week old (< 1 week): 0.5 points
- Old (> 1 week): 0.1 points

#### **Author Diversity Enforcement**
Uses MongoDB's `$setWindowFields` to prevent the same author from dominating the feed:
- Tracks post rank per author
- Applies diversity penalty for subsequent posts from the same author
- Reduces rank by 0.5 points for each additional post

#### **Engagement-Weighted Scoring**
Different engagement types have different values:
- Comments: 2x multiplier (most valuable)
- Reposts: 1.5x multiplier
- Bookmarks: 1.2x multiplier
- Reactions: 1x multiplier

#### **Author Influence Scaling**
Uses simplified tiered scaling for follower count (CosmosDB compatible):
```javascript
// Simplified influence scoring (no log10 dependency)
if (followerCount >= 1000) return 3 * weight; // High influence
if (followerCount >= 100) return 2 * weight;  // Medium influence
return 1 * weight; // Low influence
```

### 4. **CosmosDB-Compatible Aggregation Pipeline**
The algorithm uses a CosmosDB-compatible aggregation pipeline with post-processing:

**Aggregation Pipeline Stages:**
1. **$match**: Filter posts based on criteria
2. **$lookup**: Populate user and related data
3. **$addFields**: Calculate basic metrics (engagement, relationships)
4. **$addFields**: Calculate time-based and influence scores
5. **$addFields**: Calculate freshness and total engagement
6. **$addFields**: Calculate final comprehensive rank
7. **$sort**: Sort by rank and creation time

**Post-Processing (JavaScript):**
8. **Author Diversity**: Apply diversity penalty to prevent same author domination
9. **Random Factor**: Add randomness for feed variety
10. **Cleanup**: Remove all intermediate ranking properties, keep only `finalRank`
11. **Final Sort**: Re-sort with diversity and randomness applied
12. **Pagination**: Apply skip/limit after diversity processing

## CosmosDB Limitations & Solutions

### **Limitations Addressed:**

1. **`$setWindowFields` not supported**:
   - **Solution**: Implemented author diversity penalty in post-processing using JavaScript Map for tracking author post counts

2. **`$rand` operator not supported**:
   - **Solution**: Added random factor using JavaScript `Math.random()` in post-processing

3. **`$log10` may not be supported**:
   - **Solution**: Simplified author influence scoring using conditional logic instead of logarithmic scaling

4. **Limited aggregation pipeline features**:
   - **Solution**: Used only basic aggregation stages (`$match`, `$lookup`, `$addFields`, `$sort`) that are well-supported

### **Performance Considerations:**

- **Trade-off**: Post-processing requires loading more results into memory
- **Mitigation**: Algorithm still uses aggregation for heavy lifting (filtering, population, basic calculations)
- **Optimization**: Only diversity penalty and randomness are handled in post-processing
- **Clean Output**: All intermediate ranking properties are removed, only `finalRank` is kept in the response

## Benefits

### **Better Post Distribution**
- No single author dominates the feed
- Balanced mix of content from different followed users
- Fresh content gets priority while maintaining engagement relevance

### **Enhanced User Engagement**
- Quality content (with media, substantial text) gets boosted
- Active authors (online status) get slight preference
- Engagement history influences future post visibility

### **Configurable and Maintainable**
- All weights are externally configurable
- Easy to fine-tune algorithm behavior
- Comprehensive test coverage ensures reliability

### **Performance Optimized**
- Single aggregation pipeline for efficiency
- Minimal database queries
- Scalable for large datasets

## Testing

The algorithm includes comprehensive test coverage:
- Configuration validation
- Weight relationship verification
- Algorithm logic testing
- Integration testing

Run tests with:
```bash
npm test -- tests/post.ranking.test.js
```

## Future Enhancements

Potential areas for further improvement:
1. **Machine Learning Integration**: Use user interaction patterns to personalize weights
2. **A/B Testing Framework**: Test different weight configurations
3. **Real-time Adaptation**: Adjust weights based on user behavior
4. **Content Category Scoring**: Different weights for different post types
5. **Social Graph Analysis**: Consider friend-of-friend relationships

## Migration Notes

The enhanced algorithm is backward compatible:
- Existing `sortBy` parameter still works for manual sorting
- Default behavior (no `sortBy`) now uses the enhanced ranking
- All existing API endpoints remain unchanged
- No database schema changes required

## Configuration

To modify ranking weights, update the `RANKING_WEIGHTS` object in `src/services/post.service.js` or create an external configuration system that imports and modifies these values.
