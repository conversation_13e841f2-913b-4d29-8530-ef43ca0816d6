const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const { notificationTypes } = require('../services/shared/notification.handler');

const notificationSchema = new mongoose.Schema(
  {
    recipient: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },

    secondRecipient: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    post: { type: mongoose.Schema.Types.ObjectId, ref: 'Post' },
    comment: { type: mongoose.Schema.Types.ObjectId, ref: 'Comment' },
    resourceName: { type: String, enum: ['Post', 'Comment'] },

    actor: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }, // User who performed the action
    type: {
      type: String,
      enum: Object.values(notificationTypes),
    },
    group: { type: mongoose.Schema.Types.ObjectId, ref: 'Group' },
    title: String,
    message: String,
    read: { type: Boolean, default: false },
  },
  {
    timestamps: true,
  },
);

notificationSchema.index({ createdAt: 1 });

notificationSchema.plugin(toJSON);
notificationSchema.plugin(paginate);

const Notification = mongoose.model('Notification', notificationSchema);

module.exports = Notification;
