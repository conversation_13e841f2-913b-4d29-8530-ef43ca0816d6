module.exports = {
  get: {
    tags: ['Profile'],
    summary: 'Fetch resource Info in Profile',
    description: 'Fetches resource information in a profile',
    parameters: [
      {
        name: 'id',
        in: 'path',
        description: 'ID of the resource to fetch',
        required: true,
        type: 'string',
        format: 'objectId',
      },
      {
        name: 'resourceName',
        in: 'query',
        description: 'Name of the resource to fetch (e.g., Education, Certification, Project, etc.)',
        required: true,
        schema: {
          type: 'string',
          enum: ['Education', 'Certification', 'Project', 'Award', 'Volunteering', 'Experience', 'TestScore'],
        },
      },
    ],
    responses: {
      200: {
        description: 'Profile retrieved successfully',
        content: {
          'application/json': {
            schema: {
              oneOf: [
                { $ref: '#/components/schemas/Award' },
                { $ref: '#/components/schemas/Certification' },
                { $ref: '#/components/schemas/Education' },
                { $ref: '#/components/schemas/TestScore' },
                { $ref: '#/components/schemas/Experience' },
                { $ref: '#/components/schemas/Volunteering' },
                { $ref: '#/components/schemas/Project' },
              ],
            },
          },
        },
      },
    },
  },
};
