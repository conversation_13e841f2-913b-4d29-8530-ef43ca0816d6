module.exports = {
  get: {
    tags: ['Users'],
    summary: 'Get followers or following list for a user',
    parameters: [
      {
        in: 'path',
        name: 'id',
        schema: {
          type: 'string',
          description: 'User ID',
        },
        required: true,
        description: 'ID of the user to get follows information',
      },
      {
        in: 'query',
        name: 'sortBy',
        schema: {
          type: 'string',
          example: 'createdAt',
        },
        description: 'Sort criteria for posts (optional)',
      },
      {
        in: 'query',
        name: 'limit',
        schema: {
          type: 'integer',
          example: '10',
        },
        description: 'Maximum number of posts to return (optional)',
      },
      {
        in: 'query',
        name: 'page',
        schema: {
          type: 'integer',
          example: '2',
        },
        description: 'Page number for paginated results (optional)',
      },
      {
        in: 'query',
        name: 'type',
        schema: {
          type: 'string',
          enum: ['followers', 'following'],
          description: 'Specify whether to fetch followers or following',
        },
        required: true,
        description: 'Type of follows to retrieve (followers or following)',
      },
    ],
    responses: {
      200: {
        description: 'Follows information retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              anyOf: [
                {
                  properties: {
                    followers: {
                      type: 'array',
                      items: { type: 'string' },
                      description: 'List of followers',
                    },
                    count: {
                      type: 'integer',
                      example: 10,
                    },
                  },
                  required: ['followers'],
                },
                {
                  properties: {
                    following: {
                      type: 'array',
                      items: { type: 'string' },
                      description: 'List of following',
                    },
                    count: {
                      type: 'integer',
                      example: 10,
                    },
                  },
                  required: ['following'],
                },
              ],
            },
          },
        },
      },
    },
  },
};
