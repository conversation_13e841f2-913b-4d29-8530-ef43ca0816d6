const Error400 = {
  type: 'object',
  properties: {
    status: {
      type: 'string',
      example: 'FAILED',
    },
    message: {
      type: 'string',
      example: 'Bad request',
    },
  },
};

const Error404 = {
  type: 'object',
  properties: {
    status: {
      type: 'string',
      example: 'FAILED',
    },
    message: {
      type: 'string',
      example: 'Record not found',
    },
  },
};

const Error500 = {
  type: 'object',
  properties: {
    status: {
      type: 'string',
      example: 'FAILED',
    },
    message: {
      type: 'string',
      example: 'Internal server error',
    },
  },
};

module.exports = { Error400, Error404, Error500 };
