const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');
const { businessEnums } = require('../config/constants');

const serviceOfferSchema = new mongoose.Schema(
  {
    service: { type: mongoose.Schema.Types.ObjectId, ref: 'Service', required: true },
    name: { type: String, required: true, trim: true }, // e.g "Number of SOPs to review", "Number of SOPs to write", "Price"
    value: [
      {
        plan: { type: String, enum: Object.values(businessEnums.servicePlans), required: true, trim: true }, // e.g "Basic", "Standard", "Premium"
        offer: { type: String, required: true, trim: true }, // e.g "2", "Unlimited", "true", "false"
      },
    ],
  },
  {
    timestamps: true,
  },
);

serviceOfferSchema.index({ name: 1 });

serviceOfferSchema.plugin(toJSON);
serviceOfferSchema.plugin(paginate);

const ServiceOffer = mongoose.model('ServiceOffer', serviceOfferSchema);

module.exports = ServiceOffer;
