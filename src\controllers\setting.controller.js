const httpStatus = require('http-status');
const { settingService } = require('../services');
const catchAsync = require('../utils/catchAsync');

const getSettingById = catchAsync(async (req, res) => {
  const setting = await settingService.getSettingById(req.user.setting);

  return res.status(httpStatus.OK).json({ data: setting, message: 'Setting retrieved successfully', status: 'SUCCESS' });
});

const updateSetting = catchAsync(async (req, res) => {
  await settingService.updateSettingById(req.user.setting, req.body);
  return res.status(httpStatus.OK).json({ message: `Your preference setting is saved`, status: 'SUCCESS' });
});

module.exports = {
  updateSetting,
  getSettingById,
};
