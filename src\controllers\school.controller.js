const httpStatus = require('http-status');
const { schoolService } = require('../services');
const catchAsync = require('../utils/catchAsync');
const { pick } = require('../utils/pick');

const createSchool = catchAsync(async (req, res) => {
  const school = await schoolService.createSchool(req.body, req.files, req.user._id);
  res.status(httpStatus.CREATED).send(school);
});

const addSchoolPrograms = catchAsync(async (req, res) => {
  const school = await schoolService.addSchoolPrograms({ programs: req.body }, req.params.id, req.user._id, true);
  res.status(httpStatus.CREATED).send(school);
});

const getschoolsEnums = catchAsync(async (req, res) => {
  const enums = await schoolService.getschoolsEnums();
  res.status(httpStatus.OK).send(enums);
});

const getSchools = catchAsync(async (req, res) => {
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const filter = pick(req.query, ['name', 'country', 'state', 'city', 'programTypes', 'searchText']);
  const schools = await schoolService.getSchools(filter, options, req.query.brief === 'true');
  res.status(httpStatus.OK).send(schools);
});

const getSchoolById = catchAsync(async (req, res) => {
  const school = await schoolService.getSchoolById(req.params.id);
  res.status(httpStatus.OK).send(school);
});

const getSchoolProgram = catchAsync(async (req, res) => {
  const program = await schoolService.getSchoolProgram(req.params.programId);
  res.status(httpStatus.OK).send(program);
});

const updateSchool = catchAsync(async (req, res) => {
  const school = await schoolService.updateSchool(req.params.id, req.body, req.files);
  res.status(httpStatus.OK).send(school);
});

const updateSchoolProgram = catchAsync(async (req, res) => {
  const program = await schoolService.updateSchoolProgram(req.params.programId, req.body);
  res.status(httpStatus.OK).send(program);
});

const deleteSchool = catchAsync(async (req, res) => {
  await schoolService.deleteSchool(req.params.id);
  res.status(httpStatus.OK).send({ message: 'School deleted successfully' });
});

const deleteSchoolProgram = catchAsync(async (req, res) => {
  await schoolService.deleteSchoolProgram(req.params.programId);
  res.status(httpStatus.OK).send({ message: 'School program deleted successfully' });
});

const getSchoolStats = catchAsync(async (req, res) => {
  const stats = await schoolService.getSchoolStats();
  res.status(httpStatus.OK).send({ data: stats, status: 'success', message: 'School stats retrieved successfully' });
});

module.exports = {
  createSchool,
  addSchoolPrograms,
  getschoolsEnums,
  getSchools,
  getSchoolById,
  updateSchool,
  getSchoolProgram,
  updateSchoolProgram,
  deleteSchool,
  deleteSchoolProgram,
  getSchoolStats,
};
