const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');

const schoolSchema = new mongoose.Schema(
  {
    name: { type: String, required: true, trim: true, unique: true },
    abbreviation: { type: String, trim: true },
    about: { type: String, trim: true },
    logo: { type: mongoose.Schema.Types.ObjectId, ref: 'File' },
    banner: { type: mongoose.Schema.Types.ObjectId, ref: 'File' },
    country: { type: mongoose.Schema.Types.ObjectId, ref: 'Country', required: true },
    state: { type: mongoose.Schema.Types.ObjectId, ref: 'State', required: true },
    city: { type: mongoose.Schema.Types.ObjectId, ref: 'City', required: true },
    website: { type: String, trim: true },
    currency: { type: String, trim: true, default: 'USD' },
    minimumTuition: { type: Number },
    maximumTuition: { type: Number },
    applicationFee: { type: Number },
    programs: [{ type: mongoose.Schema.Types.ObjectId, ref: 'SchoolProgram' }],
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  },
  {
    timestamps: true,
  },
);

schoolSchema.index({ createdAt: 1, name: 'text', about: 'text' });

schoolSchema.plugin(toJSON);
schoolSchema.plugin(paginate);

const School = mongoose.model('School', schoolSchema);

module.exports = School;
