const httpStatus = require('http-status');
const moment = require('moment');
const logger = require('../config/logger');
const ApiError = require('../utils/ApiError');
const validateId = require('./shared/validateId');
const { Business, Order, OrderActivity, ServiceOffer, User, ServiceRevision, Referral } = require('../models');
const { notificationTypes } = require('./shared/notification.handler');
const fileService = require('./file.service');
const notificationService = require('./notification.service');
const { businessEnums, azureContainers } = require('../config/constants');
const { scheduleMarkRevisionAsCompleted } = require('../utils/jobs/jobSchedulers');

const createServiceRevision = async (user, dataParam, files) => {
  const data = { ...dataParam };
  validateId(data.order, 'Order');

  const order = await Order.findById(data.order).populate('service');
  if (!order) {
    logger.error('Order not found');
    throw new ApiError(httpStatus.NOT_FOUND, 'Order not found');
  }

  if (order.client.toString() !== user._id.toString()) {
    logger.error('You are unauthorized to perform this action');
    throw new ApiError(httpStatus.FORBIDDEN, 'You are unauthorized to perform this action');
  }

  if (order.status === businessEnums.orderStatuses.REVISION_REQUESTED) {
    logger.error('Order already has a revision request');
    throw new ApiError(httpStatus.BAD_REQUEST, 'Order already has a revision request');
  }

  // to get the maximum revisions allowed for the service plan
  const serviceOffer = await ServiceOffer.findOne({
    service: order.service._id,
    // 'value.plan': order.plan,
    name: businessEnums.requiredOffers.MAXIMUM_REVISIONS[0],
  }).select({ value: { $elemMatch: { plan: order.plan } } });

  if (!serviceOffer.value[0].offer) {
    logger.error('Encountered service plan without Maximum Revisions offer');
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'An error occurred while processing your request');
  }

  // const maxRevisions = parseInt(serviceOffer.value.find((v) => v.plan === order.plan).offer, 10);
  // const remainingRevisions = maxRevisions - order.revisionCount;
  if (parseInt(serviceOffer.value[0].offer, 10) <= 0) {
    // if (remainingRevisions <= 0) {
    logger.error('Allowed revisions for plan exhausted');
    throw new ApiError(httpStatus.BAD_REQUEST, 'Allowed revisions for plan exhausted');
  }

  if (files) {
    data.supportingDocuments = await fileService.processFileUploads(
      files.supportingDocuments,
      azureContainers.businessOrders,
    );
  }

  const serviceRevision = new ServiceRevision({
    ...data,
    service: order.service._id,
    client: user._id,
    provider: order.service.provider,
  });
  await serviceRevision.save();

  await Order.findByIdAndUpdate(order._id, {
    $inc: { revisionCount: 1 },
    $push: { revisions: serviceRevision._id },
    status: businessEnums.orderStatuses.REVISION_REQUESTED,
  });

  // log activity
  await OrderActivity.create({
    order: order._id,
    revision: serviceRevision._id,
    client: user._id,
    provider: order.service.provider,
    actor: businessEnums.orderActivityActors.CLIENT,
    action: businessEnums.orderActivityActions.REVISION_REQUESTED,
  });

  // notify provider
  const provider = await Business.findById(order.service.provider).populate('contactPerson');
  const details = { receiverType: 'provider', orderId: order._id, serviceName: order.service.name };
  await notificationService.createServiceNotification(user, provider, notificationTypes.REVISION_REQUESTED, details);

  return serviceRevision;
};

const getServiceRevisions = async (user, orderId, options) => {
  const filter = { order: orderId, $or: [{ client: user._id }, { provider: user._id }] };
  const serviceRevisions = await ServiceRevision.paginate(filter, {
    ...options,
    populate: { path: 'deliverable.files', select: 'url' },
  });
  return serviceRevisions;
};

const getServiceRevision = async (user, revisionId) => {
  validateId(revisionId, 'Service Revision');
  const serviceRevision = await ServiceRevision.findOne({
    _id: revisionId,
    $or: [{ client: user._id }, { provider: user._id }],
  });

  if (!serviceRevision) {
    logger.error('Service revision not found');
    throw new ApiError(httpStatus.NOT_FOUND, 'Service revision not found');
  }

  return serviceRevision;
};

const checkServiceRevisionExists = async (revisionId) => {
  validateId(revisionId, 'ServiceRevision');
  const serviceRevision = await ServiceRevision.findById(revisionId).populate([
    { path: 'provider', select: 'contactPerson name', populate: 'contactPerson' },
    { path: 'service', select: 'name' },
    { order: 'order', select: 'revisions' },
  ]);

  if (!serviceRevision) {
    logger.error('Service revision not found');
    throw new ApiError(httpStatus.NOT_FOUND, 'Service revision not found');
  }

  return serviceRevision;
};

const fulfillServiceRevision = async (user, revisionId, body, files) => {
  // provider fulfills a revision
  const serviceRevision = await checkServiceRevisionExists(revisionId);

  if (serviceRevision.provider.contactPerson.toString() !== user._id.toString()) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You are unauthorized to perform this action');
  }

  if (files) {
    serviceRevision.deliverable.files = await fileService.processFileUploads(
      files.deliverables,
      azureContainers.businessOrders,
    );
  }

  if (body.comment) serviceRevision.deliverable.comment = body.comment;
  serviceRevision.fulfilledAt = new Date();

  await serviceRevision.save();

  // update order status to awaiting approval
  await Order.findByIdAndUpdate(serviceRevision.order, { status: businessEnums.orderStatuses.AWAITING_CLIENT_APPROVAL });

  // log activity
  await OrderActivity.create({
    order: serviceRevision.order,
    revision: serviceRevision._id,
    client: serviceRevision.client,
    provider: serviceRevision.provider,
    actor: businessEnums.orderActivityActors.PROVIDER,
    action: businessEnums.orderActivityActions.REVISION_DELIVERED,
  });

  // notify client
  const client = await User.findById(serviceRevision.client);
  const details = { receiverType: 'client', orderId: serviceRevision.order, serviceName: serviceRevision.service.name };
  await notificationService.createServiceNotification(user, client, notificationTypes.REVISION_FULFILLED, details);

  // schedule revision completion
  const prevRevisionsCount = serviceRevision.order.revisions.length;
  await scheduleMarkRevisionAsCompleted({ revisionId, prevRevisionsCount });

  return serviceRevision;
};

const completeServiceRevision = async (user, revisionId) => {
  // client marks a revision as completed
  const serviceRevision = await checkServiceRevisionExists(revisionId);

  if (serviceRevision.client.toString() !== user._id.toString()) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You are unauthorized to perform this action');
  }

  // update order status to completed
  const order = await Order.findByIdAndUpdate(
    serviceRevision.order,
    { status: businessEnums.orderStatuses.COMPLETED },
    { new: true },
  );

  if (order.referral) await Referral.findByIdAndUpdate(order.referral, { bonusIsWithdrawable: true });

  // log activity
  await OrderActivity.create({
    order: serviceRevision.order,
    revision: serviceRevision._id,
    client: serviceRevision.client,
    provider: serviceRevision.provider,
    actor: businessEnums.orderActivityActors.CLIENT,
    action: businessEnums.orderActivityActions.REVISION_COMPLETED,
  });

  // notify provider that client has marked revision as completed
  const provider = await Business.findById(serviceRevision.provider).populate('contactPerson');
  const details = { receiverType: 'provider', orderId: serviceRevision.order, serviceName: serviceRevision.service.name };
  await notificationService.createServiceNotification(user, provider, notificationTypes.REVISION_COMPLETED, details);

  return serviceRevision;
};

const cancelServiceRevision = async (user, revisionId) => {
  // client cancels a revision
  const serviceRevision = await checkServiceRevisionExists(revisionId);

  if (serviceRevision.client.toString() !== user._id.toString()) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You are unauthorized to perform this action');
  }

  // ensure the order status is not completed
  const order = await Order.findById(serviceRevision.order);
  if (order.status === businessEnums.orderStatuses.COMPLETED) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Cannot cancel revision for a completed order');
  }

  // update order status to awaiting client approval
  await Order.findByIdAndUpdate(serviceRevision.order, { status: businessEnums.orderStatuses.AWAITING_CLIENT_APPROVAL });

  // log activity
  await OrderActivity.create({
    order: serviceRevision.order,
    revision: serviceRevision._id,
    client: serviceRevision.client,
    provider: serviceRevision.provider,
    actor: businessEnums.orderActivityActors.CLIENT,
    action: businessEnums.orderActivityActions.REVISION_CANCELLED,
  });

  // notify provider that client has cancelled revision
  const provider = await Business.findById(serviceRevision.provider).populate('contactPerson');
  const details = { receiverType: 'provider', orderId: serviceRevision.order, serviceName: serviceRevision.service.name };
  await notificationService.createServiceNotification(user, provider, notificationTypes.REVISION_CANCELLED, details);

  return serviceRevision;
};

const scheduleRevisionCompletion = async (revisionId, prevRevisionsCount) => {
  // prevRevisionsCount passed to the job must be the same as when it was created
  // serviceRevision.fulfilledAt must be more than 7 days ago
  // the order status must be awaiting client approval
  const revision = await checkServiceRevisionExists(revisionId);

  if (revision.order.status !== businessEnums.orderStatuses.AWAITING_CLIENT_APPROVAL) {
    logger.error('Order status is not awaiting client approval');
    throw new ApiError(httpStatus.BAD_REQUEST, 'Order status is not awaiting client approval');
  } else if (revision.fulfilledAt <= moment().subtract(7, 'days').toDate()) {
    logger.error('Revision has not been fulfilled for 7 days');
    throw new ApiError(httpStatus.BAD_REQUEST, 'Revision has not been fulfilled for 7 days');
  } else if (prevRevisionsCount !== revision.order.revisions.length) {
    logger.error('Number of revisions has changed');
    throw new ApiError(httpStatus.BAD_REQUEST, 'Number of revisions has changed');
  } else {
    await Order.findByIdAndUpdate(revision.order, { status: businessEnums.orderStatuses.COMPLETED });
  }
};

module.exports = {
  createServiceRevision,
  getServiceRevisions,
  getServiceRevision,
  fulfillServiceRevision,
  completeServiceRevision,
  cancelServiceRevision,
  scheduleRevisionCompletion,
};
