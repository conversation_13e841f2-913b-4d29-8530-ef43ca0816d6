const Joi = require('joi');

// Regex
const mimeTypes = ['image/.+', 'video/.+'];

const documentMimeTypes = [
  'image/.+',
  'application/pdf',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/msword',
];

const fileSchema = (maxSize = 2, mimetype = mimeTypes) =>
  Joi.object().keys({
    fieldname: Joi.string().required(),
    mimetype: Joi.string()
      .regex(new RegExp(mimetype.join('|')))
      // .valid(...mimetype)
      .required(),
    size: Joi.number()
      .max(maxSize * 1024 * 1024)
      .required(),
    originalname: Joi.string().required(),
    buffer: Joi.binary().required(),
    encoding: Joi.string().required(),
  });

const documentFileSchema = (maxSize = 5, mimetype = documentMimeTypes) =>
  Joi.object().keys({
    fieldname: Joi.string().required(),
    mimetype: Joi.string()
      .regex(new RegExp(mimetype.join('|')))
      // .valid(...mimetype)
      .required(),
    size: Joi.number()
      .max(maxSize * 1024 * 1024)
      .required(),
    originalname: Joi.string().required(),
    buffer: Joi.binary().required(),
    encoding: Joi.string().required(),
  });

module.exports = {
  fileSchema,
  documentMimeTypes,
  imageVideoMimeTypes: mimeTypes,
  documentFileSchema,
};
