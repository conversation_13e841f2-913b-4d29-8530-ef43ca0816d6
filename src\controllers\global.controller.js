const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { globalService, emailService, userService, notificationService, messageService } = require('../services');
const { pick } = require('../utils/pick');
const { capitalizeFirstChar } = require('../utils/stringFormatting');
const { ContactUs, User, Group, Profile } = require('../models');
const { emit } = require('../services/sockets/socket.shared');
const ApiError = require('../utils/ApiError');

const search = catchAsync(async (req, res) => {
  const filter = pick(req.query, ['searchText', 'tab']);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);

  const matchingForumPosts = await globalService.search(req.user, filter, options);
  res.status(httpStatus.OK).json({
    status: 'SUCCESS',
    data: matchingForumPosts,
    message: `${capitalizeFirstChar(filter.tab)} retrieved successfully`,
  });
});

const contactUs = catchAsync(async (req, res) => {
  const message = await ContactUs.create(req.body);
  if (!message) {
    throw new ApiError(httpStatus.BAD_REQUEST, `Message not sent`);
  }
  await emailService.sendContactUsEmail(req.body);
  return res.status(httpStatus.CREATED).json({ status: 'SUCCESS', message: 'message successfully sent' });
});

const report = catchAsync(async (req, res) => {
  await globalService.report(req.body, req.user._id);
  return res.status(httpStatus.CREATED).json({ status: 'SUCCESS', message: 'report successfully sent' });
});

const searchUsers = catchAsync(async (req, res) => {
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const data = await globalService.searchUsers(req.query.searchTerm, options);
  return res.status(httpStatus.OK).json({ message: 'Search records retrieved', data, status: 'SUCCESS' });
});

const generateSasUrl = catchAsync(async (req, res) => {
  const { fileIds } = req.query;
  const sasUrl = await globalService.generateSasUrl(fileIds);
  return res.status(httpStatus.OK).json({ status: 'SUCCESS', data: sasUrl });
});

const userAnalytics = catchAsync(async (req, res) => {
  const data = await globalService.userAnalytics(req.query);
  return res.status(httpStatus.OK).json({ status: 'SUCCESS', data });
});

const getLanguages = catchAsync(async (req, res) => {
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const filter = pick(req.query, ['name']);
  const data = await globalService.getLanguages(filter, options);
  return res.status(httpStatus.OK).json({ status: 'SUCCESS', data });
});

const getUserMetaData = async (socket, clientData, socketConnection) => {
  // returns all user data -> unread notifications, unread messages, messagesCount, user details
  // clientData => { unreadNotifs: { group: '85383784' }, unreadMessages: { group: '85383784' } }
  // implement validation for clientData

  const getUserPromise = userService.getUserById(socket.user._id, true);
  const getUnreadConversationsCountPromise = messageService.countUnreadConversations(socket.user._id);
  const getFollowsCountPromise = userService.countFollowership(socket.user._id);

  const getUnreadNotificationsPromise = notificationService.getNotifications(
    { recipient: socket.user._id, read: false },
    {},
  );
  const [user, unreadConversationsCount, unreadNotifications, followsCount] = await Promise.all([
    getUserPromise,
    getUnreadConversationsCountPromise,
    getUnreadNotificationsPromise,
    getFollowsCountPromise,
  ]);

  await emit(
    { user, unreadConversationsCount, unreadNotifications, followsCount },
    socket.user._id,
    'user-meta-data',
    socketConnection,
  );
};

const getAllUsersProfiles = catchAsync(async (req, res) => {
  const users = await User.find();
  const groups = await Group.find();
  const profiles = await Profile.find();

  const data = { users, groups, profiles };

  res.status(httpStatus.OK).json({ status: 'SUCCESS', data });
});

module.exports = {
  search,
  contactUs,
  report,
  searchUsers,
  generateSasUrl,
  userAnalytics,
  getLanguages,
  getUserMetaData,
  getAllUsersProfiles,
};
