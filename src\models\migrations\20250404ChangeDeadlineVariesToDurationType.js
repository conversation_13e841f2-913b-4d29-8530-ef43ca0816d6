const Async = require('async');
const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const Scholarship = require('../scholarship.model');
const { schoolsEnums } = require('../../config/constants');

const migrationName = 'change deadlineVaries to durationType property 2';

const changeDeadlineVariesToDurationType = async () => {
  const migrated = await GlobalVariable.findOne({ name: migrationName });
  if (migrated) return;

  const scholarships = await Scholarship.find({});

  let updateObject = {};

  await Async.eachSeries(scholarships, async (scholarship) => {
    if (scholarship.deadlineVaries) {
      updateObject = {
        ...updateObject,
        $set: {
          ...(updateObject.$set || {}),
          durationType: schoolsEnums.durationTypes.DEADLINE_VARIES,
        },
        $unset: {
          ...(updateObject.$unset || {}),
          deadline: '',
          deadlineVaries: '',
        },
      };
    } else if (!scholarship.deadlineVaries && !scholarship.durationType) {
      updateObject = {
        ...updateObject,
        $set: {
          ...(updateObject.$set || {}),
          durationType: schoolsEnums.durationTypes.DURATION_SPECIFIED,
        },
        $unset: {
          ...(updateObject.$unset || {}),
          deadlineVaries: '',
        },
      };
    }
    if (scholarship.durationType) {
      updateObject = {
        ...updateObject,
        $set: {
          ...(updateObject.$set || {}),
          durationType: scholarship.durationType.replace(
            'Deadline Specified',
            schoolsEnums.durationTypes.DURATION_SPECIFIED,
          ),
        },
      };
    }
    await Scholarship.updateOne({ _id: scholarship._id }, updateObject);
  });

  await GlobalVariable.create({ name: migrationName, value: 'true' });
  logger.info('The deadlineVaries properties have been changed to durationType');
};

module.exports = changeDeadlineVariesToDurationType;
