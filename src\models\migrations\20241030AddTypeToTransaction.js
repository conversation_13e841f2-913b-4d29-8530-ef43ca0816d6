const Async = require('async');
const logger = require('../../config/logger');
const Transaction = require('../transaction.model');
const GlobalVariable = require('../global.variable.model');
const { businessEnums } = require('../../config/constants');

const varName = 'Add type to transactions';

const addTypeToTransaction = async () => {
  const migrated = await GlobalVariable.findOne({ name: varName });

  if (migrated) {
    return;
  }

  const transactions = await Transaction.find();

  await Async.eachOfSeries(transactions, async (transaction, index) => {
    await Transaction.updateOne({ _id: transaction._id }, { type: businessEnums.transactionTypes.CREDIT_DEBIT });
    logger.info(`(${index})Updated type for transaction to ${transaction._id}`);
  });

  await GlobalVariable.create({ name: varName, value: 'true' });
  logger.info('Added type to transactions');
};

module.exports = addTypeToTransaction;
