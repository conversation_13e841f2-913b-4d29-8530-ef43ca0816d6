const express = require('express');

const router = express.Router();
const { auth, authExcept } = require('../../middlewares/auth');
const validate = require('../../middlewares/validate');
const { serviceController: ServiceController } = require('../../controllers');
const { serviceValidation: ServiceValidation, businessValidation } = require('../../validations');
const { uploadUnnamedFiles } = require('../../config/multer.config');
const { canGetServices, verifyBusinessOnboarding } = require('../../middlewares/cancan');

// Authentiticate routes except for the given array of routes
// router.use(authExcept([/^\/$/, /^\/[^/]+$/, /^\/[^/]+\/offers$/])); // authenticate except for routes that match "/", "/:id", "/:id/offers"
router.use(authExcept([/^\/$/, /^\/[^/]+$/, /^\/[^/]+\/offers$/, /^\/external\/[^/]+$/])); // authenticate except for routes that match "/", "/:id", "/:id/offers", "/external/:id"

// Begin Unauthenticated routes
router.get('/external', validate(ServiceValidation.getServices), canGetServices(), ServiceController.getServices); // Danger.. Add a middleware
router.get('/:id/offers', validate(ServiceValidation.getServiceOffers), ServiceController.getServiceOffers);
router.get('/external/:id', validate(ServiceValidation.getService), ServiceController.getService);
router.get('/reviews', validate(ServiceValidation.getReviews), ServiceController.getReviews);
// End Unauthenticated routes

router.use(['/', '/:id', '/:id/save'], auth()); // authenticate routes matching "/", "/:id", "/saved", etc

// service verifications
// fetch service verifications
router.get(
  '/get-verifications',
  auth('verifyServices'),
  validate(businessValidation.fetchVerifications),
  ServiceController.fetchServiceVerifications,
);

// fetch unverified services
// router.get(
//   '/unverified',
//   auth('verifyServices'),
//   validate(ServiceValidation.fetchUnverifiedServices),
//   ServiceController.fetchUnverifiedServices,
// );

// fetch parent verification
router.get(
  '/verification/:id',
  auth('verifyServices'),
  validate(ServiceValidation.getService),
  ServiceController.fetchParentVerification,
);

// fetch service verification
router.get(
  '/:id/verification',
  auth('verifyServices'),
  validate(ServiceValidation.getService),
  ServiceController.fetchServiceVerificationInfo,
);

// assign service verifier
router.post(
  '/:id/assign-verifier',
  auth('assignVerifier'),
  validate(businessValidation.assignVerifier),
  ServiceController.assignServiceVerifier,
);

// verify service
router.post(
  '/:id/verify',
  auth('verifyServices'),
  validate(ServiceValidation.verifyService),
  ServiceController.verifyService,
);

router.get('/', validate(ServiceValidation.getServices), canGetServices(), ServiceController.getServices); // Danger.. Add a middleware

router.post(
  '/',
  verifyBusinessOnboarding(),
  uploadUnnamedFiles(),
  validate(ServiceValidation.createService),
  ServiceController.addNewService,
);

router.get('/recently-viewed', ServiceController.getRecentlyViewedServices);
router.patch('/recently-viewed/:id', validate(ServiceValidation.getService), ServiceController.addToRecentlyViewedServices);
router.get('/saved', validate(ServiceValidation.getSavedServices), ServiceController.getSavedServices);
router.get('/:id', validate(ServiceValidation.getService), ServiceController.getService);
router.patch('/:id/save', validate(ServiceValidation.getService), ServiceController.manageSavedServices);

/*
 Routes that require business onboarding verification
 - This middleware checks if the business has completed the onboarding process before allowing access to these routes.
*/
router.use('/:id', verifyBusinessOnboarding());

router.patch('/:id', validate(ServiceValidation.updateService), ServiceController.updateService);
router.delete('/:id', validate(ServiceValidation.getService), ServiceController.deleteService);

// reviews
router.post('/reviews', validate(ServiceValidation.addReview), ServiceController.addReview);
router.get('/reviews', validate(ServiceValidation.getReviews), ServiceController.getReviews);
router.get('/reviews/:id', validate(ServiceValidation.getService), ServiceController.getReview);
// router.patch('/reviews/:id', validate(ServiceValidation.updateReview), ServiceController.updateReview);
router.delete('/reviews/:id', validate(ServiceValidation.getService), ServiceController.deleteReview);

// recently viewed

module.exports = router;
