const httpStatus = require('http-status');
// const { default: mongoose } = require('mongoose');
const Async = require('async');
const Joi = require('joi');
const stopword = require('stopword');
const logger = require('../config/logger');
const ApiError = require('../utils/ApiError');
const {
  Business,
  BusinessVerification,
  Account,
  File,
  Language,
  Referral,
  Setting,
  Profile,
  User,
  Certification,
  Education,
  Service,
} = require('../models');
const {
  azureContainers,
  registrationStatuses,
  userDefaultSettings,
  // clientUserTypes,
  businessEnums,
  clientUserTypes,
} = require('../config/constants');
const { wordsArrayToFilterArray } = require('./university.service');
const { businessValidation, authValidation } = require('../validations');
const accountService = require('./account.service');
const emailService = require('./email.service');
const fileService = require('./file.service');
const profileService = require('./profile.service');
// const tokenService = require('./token.service');
const validateId = require('./shared/validateId');
const { validateCountryStateCity } = require('./school.service');
// const { verifyOTP, sendOTP } = require('./auth.service');
const { capitalizeFirstChar } = require('../utils/stringFormatting');
const { validateObjectData, validateModelDataArray } = require('../utils/helperMethods');
const getStripeSupportedCountries = require('../models/migrations/seed/stripeSupportedCountries');
const { roleRights } = require('../config/roles');
const { defaultServiceRequirements } = require('../models/migrations/seed');
// const { basicUserPopulate } = require('./user.service');

const basicBusinessPopulate = {
  path: 'business',
  select: 'name coverPhoto profilePhoto displayName username online',
  populate: [
    { path: 'coverPhoto', select: 'url' },
    { path: 'profilePhoto', select: 'url' },
  ],
};

const businessPopulatePaths = (isInternal) => {
  const paths = [
    {
      path: 'contactPerson',
      select:
        'firstName lastName middleName username photo phone phoneVerified isEmailVerified email earliestPhoneVerifyRequest',
      populate: { path: 'photo', select: 'url' },
    },
    { path: 'coverPhoto', select: 'url' },
    { path: 'profilePhoto', select: 'url' },
    { path: 'city', select: 'name' },
    { path: 'state', select: 'name' },
    { path: 'country', select: 'name' },
    { path: 'languages', populate: { path: 'id', select: 'code name native' } },
    {
      path: 'profile',
      select: 'education certification skills occupation',
      populate: { path: 'education certification' },
    },
    { path: 'services', select: 'reviews', populate: { path: 'reviews', select: 'rating text' } },
  ];

  if (!isInternal) {
    paths.push(...[{ path: 'orders', select: 'status', $match: { status: businessEnums.orderStatuses.COMPLETED } }]);
  }
  return paths;
};

const businessSelectProps = (isOwner, isInternal) => {
  let props = '';
  if (!isOwner) {
    props =
      'username displayName coverPhoto profilePhoto about tagLine city state country languages referralCode verificationStatus online';
    if (isInternal) {
      props += 'orders';
    }
  }

  return props;
};

const getEnums = async (isPrivate) => {
  const data = Object.keys(businessEnums).reduce((acc, key) => {
    acc[key] = Object.values(businessEnums[key]);
    return acc;
  }, {});

  data.defaultServiceRequirements = defaultServiceRequirements;
  data.stripeSupportedCountries = await getStripeSupportedCountries();

  if (isPrivate) {
    // eslint-disable-next-line global-require
    const { basicUserPopulate } = require('./user.service');
    const userSelect = `${basicUserPopulate.select} roles`;
    const developers = await User.find({ roles: { $in: ['developer'] } }).select(userSelect);
    const admins = await User.find({ roles: { $in: ['admin'] } }).select(userSelect);
    const superAdmins = await User.find({ roles: { $in: ['superAdmin'] } }).select(userSelect);
    data.internalUsers = { developers, admins, superAdmins };
  }

  return data;
};

const createBusiness = async (userParam, data, files) => {
  const user = userParam;
  if (user.business) {
    logger.error('User already has an existing business');
    throw new ApiError(httpStatus.BAD_REQUEST, 'User already has an existing business');
  }

  if (await Business.findOne({ displayName: data.displayName })) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Business with the same name already exists');
  }
  const username = await Business.generateUsername(data.displayName);
  if (!username) {
    logger.error('Could not generate a unique username');
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Could not generate a unique username');
  }

  const businessData = { ...data, username };
  businessData.contactPerson = user._id;
  // validateId(businessData.city, 'City');
  // validateId(businessData.state, 'State');
  validateId(businessData.country, 'Country');

  const languages = validateObjectData(businessData.languages, businessValidation.validateLanguages, 'Invalid Languages');

  businessData.languages = languages;
  await Promise.all(
    businessData.languages.map(async (language) => {
      validateId(language.id, 'Language');
      const languageExists = await Language.findById(language.id);
      if (!languageExists) {
        logger.error(`Language with ID: ${language.id} not found`);
        throw new ApiError(httpStatus.NOT_FOUND, `Language with ID: ${language.id} not found`);
      }
    }),
  );

  await validateCountryStateCity(businessData.country, businessData.state, businessData.city);

  if (files.profilePhoto) {
    businessData.profilePhoto = await fileService.processFileUpload(
      files.profilePhoto[0],
      azureContainers.businessProfilePhotos,
    );
  }
  if (files.coverPhoto) {
    businessData.coverPhoto = await fileService.processFileUpload(files.coverPhoto[0], azureContainers.businessCoverPhotos);
  }

  const newAccount = await accountService.createAccount();
  businessData.account = newAccount._id;

  businessData.referralCode = await Referral.generateUniqueReferralCode();

  try {
    const createdBusiness = await Business.create(businessData);
    await User.findByIdAndUpdate(user._id, { business: createdBusiness._id });

    await Account.findByIdAndUpdate(newAccount._id, { business: createdBusiness._id });
    return createdBusiness;
  } catch (err) {
    logger.error(err);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'An error occurred while creating business');
  }
};

const updateBusinessProfile = async (profileId, dataParam) => {
  const data = dataParam;
  if (typeof profileId === 'string') validateId(profileId, 'Profile');
  let profile = typeof profileId === 'string' ? await Profile.findById(profileId) : profileId;
  if (!profile) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Profile not found');
  }

  const promises = [];
  if (profile.certification) {
    promises.push(Certification.deleteMany({ _id: { $in: profile.certification } }));
  }

  if (profile.education) {
    promises.push(Education.deleteMany({ _id: { $in: profile.education } }));
  }

  await Promise.all(promises);

  profile.certification = []; // Will be re-added in the next Async.eachSeries block
  profile.education = []; // Will be re-added in the next Async.eachSeries block
  await profile.save();

  await Async.eachSeries(['education', 'certification'], async (modelName) => {
    const { [modelName]: profileData } = data;
    if (profileData) {
      await Async.eachSeries(profileData, async (entry) => {
        profile = await profileService.addResourceInfo(profileId, capitalizeFirstChar(modelName), entry);
      });
      delete data[modelName];
    }
  });

  Object.assign(profile, data);
  await profile.save();
  return profile;
};

const updateBusiness = async (businessId, data, files, onboarding = false) => {
  validateId(businessId, 'Business');
  const updateBody = data;

  // const session = await mongoose.startSession();
  // session.startTransaction();

  // try {
  const business = await Business.findById(businessId).populate('contactPerson');
  const { contactPerson } = business;
  if (!business) {
    logger.error('Business not found');
    throw new ApiError(httpStatus.NOT_FOUND, 'Business not found');
  }

  if (updateBody.displayName) {
    const businessWithName = await Business.findOne({ displayName: { $regex: updateBody.displayName, $options: 'i' } });
    if (businessWithName) {
      if (businessWithName._id.toString() !== String(businessId)) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Business with the same name already exists');
      }
    } else {
      updateBody.username = await Business.generateUsername(updateBody.displayName);
    }
  }
  let { professionalInfo } = updateBody;
  // professionalInfo is only provided during onboarding
  // additionalInformation is only provided during onboarding
  if (professionalInfo) {
    try {
      professionalInfo = JSON.parse(professionalInfo);
    } catch (error) {
      logger.error(`Error: ${error.message}`);
      throw new ApiError(httpStatus.BAD_REQUEST, `Error: Invalid format for Professional Info`);
    }
    const { error } = businessValidation.professionalInfo.validate(professionalInfo);
    if (error) {
      logger.error(`Error: ${error.message}`);
      throw new ApiError(httpStatus.BAD_REQUEST, `Error: ${error.message}`);
    }

    let profile;
    if (business.profile) {
      profile = await updateBusinessProfile(String(business.profile), professionalInfo);
    } else {
      // Create profile
      profile = await Profile.create({ business: businessId });
      business.profile = profile._id;
      await business.save();

      profile = await updateBusinessProfile(profile, professionalInfo);
    }
  }

  if (updateBody?.additionalInfo) {
    let additionalInformation;
    try {
      additionalInformation = JSON.parse(updateBody?.additionalInfo);
    } catch (error) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid data format for additionalInfo');
    }
    const { error } = businessValidation.uploadAdditionalInfo.validate(additionalInformation);
    if (error) {
      logger.error(`Error: ${error.message}`);
      throw new ApiError(httpStatus.BAD_REQUEST, `Error: ${error.message}`);
    }

    updateBody.additionalInformation = additionalInformation;
    // business.additionalInformation = additionalInformation;
    // business.completedOnboardingStages = [
    //   ...(business.completedOnboardingStages || []),
    //   businessEnums.onboardingStages.ADDITIONAL_INFO,
    // ];
    // await business.save(); // No need to save here because updateBody will be saved at the end
    // notify admin
    if (business.onboardingStages === businessEnums.onboardingStages.COMPLETED) {
      const superAdmin = await User.findOne({ roles: { $in: ['superAdmin'] } });
      await emailService.sendNotifySuperAdminToVerifyBusiness(superAdmin, { business });
    }
  }

  // internal update - different from onboarding
  // professionalInfo is only provided during onboarding so this works for only business profile update
  if (business.profile && !professionalInfo && (updateBody.occupation || updateBody.skills)) {
    updateBody.skills = Array.isArray(updateBody.skills) ? updateBody.skills : [updateBody.skills];
    await profileService.updateBasicInformation(business.profile, {
      occupation: updateBody.occupation,
      skills: updateBody.skills,
    });
  }

  if (!contactPerson.setting) {
    // skip this block if user already has a setting from being onboarded as a student
    const setting = await Setting.create({ ...userDefaultSettings(), user: contactPerson._id });
    Object.assign(contactPerson, { setting: setting._id });
  }

  if (updateBody.languages) {
    // check languages exist in database
    const newLanguages = validateObjectData(updateBody.languages, businessValidation.validateLanguages);

    // await Promise.all(
    //   newLanguages.map(async (language) => {
    //     validateId(language.id, 'Language');
    //     const languageExists = await Language.findById(language.id);
    //     if (!languageExists) {
    //       logger.error(`Language with ID: ${language.id} not found`);
    //       throw new ApiError(httpStatus.NOT_FOUND, `Language with ID: ${language.id} not found`);
    //     }
    //   }),
    // );

    await validateModelDataArray(newLanguages, Language);
    updateBody.languages = newLanguages;
  }

  let phoneResponse;
  // Will not execute if phone number is already verified
  if (updateBody.phone) {
    // Remove spaces from phone number
    updateBody.phone = updateBody.phone.replace(/\s/g, '');
    if (!contactPerson.phoneVerified) {
      // Two scenarios: requesting for code and verifying the code
      let phoneData;
      try {
        phoneData = JSON.parse(updateBody.phone);
      } catch (error) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid data format for Phone Number');
      }
      if (phoneData.channel) {
        // It's a request to send OTP
        validateObjectData({ body: phoneData }, Joi.object().keys(authValidation.sendOTP));
        const existingUserWithPhone = await User.findOne({
          phone: phoneData.phone,
          email: { $not: { $regex: contactPerson.email, $options: 'i' } },
        });
        if (existingUserWithPhone) {
          throw new ApiError(httpStatus.BAD_REQUEST, 'Phone Number already in use.');
        }
        // eslint-disable-next-line global-require
        phoneResponse = await require('./auth.service').sendOTP(phoneData.phone, phoneData.channel, contactPerson);
      } else if (phoneData.otp) {
        // Its a request to verify OTP
        validateObjectData({ body: phoneData }, Joi.object().keys(authValidation.verifyOTP));
        // eslint-disable-next-line global-require
        phoneResponse = await require('./auth.service').verifyOTP(phoneData.phone, phoneData.otp);

        // Phone has not been verified
        if (phoneResponse.status === 'approved') {
          contactPerson.phone = phoneData.phone;
          contactPerson.phoneVerified = true;
        } else {
          throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid or expired code');
        }
      } else {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid data format for phone number');
      }
    } else {
      phoneResponse = { status: 'approved' };
    }
  }

  if (files?.profilePhoto) {
    const media = await File.findById(business.profilePhoto);
    if (media) media.remove();
    updateBody.profilePhoto = await fileService.processFileUpload(
      files.profilePhoto[0],
      azureContainers.businessProfilePhotos,
    );
  }

  if (files?.coverPhoto) {
    const media = await File.findById(business.coverPhoto);
    if (media) media.remove();
    updateBody.coverPhoto = await fileService.processFileUpload(files.coverPhoto[0], azureContainers.businessCoverPhotos);
  }

  const updatedBusiness = await Business.findByIdAndUpdate(businessId, { $set: updateBody }, { new: true });
  await contactPerson.save();

  // await session.commitTransaction();

  return onboarding && phoneResponse ? { business: updatedBusiness, phoneResponse } : updatedBusiness;
  // } catch (error) {
  //   logger.error(error);
  //   await session.abortTransaction();

  //   if (error instanceof ApiError) {
  //     throw new ApiError(error.statusCode, error.message);
  //   }
  //   throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'An error occurred while updating business profile');
  // } finally {
  //   session.endSession();
  // }
};

const onboardBusiness = async (req) => {
  // check if account is pendingStudent, else toggle to true if onboarded as a student
  // because a user can only be onboarded as a student or business
  if (req.user.pendingStudent === 'false' && !req.user.pendingBusiness) req.user.pendingBusiness = 'true';
  if (req.user.pendingBusiness === 'false') throw new ApiError(httpStatus.BAD_REQUEST, 'Onboarding already completed.');

  if (!req.user.pendingBusiness) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'Account is not a business account.');
  }
  const { completedOnboardingStages } = req.body;
  if (completedOnboardingStages) {
    req.body.completedOnboardingStages = Array.isArray(completedOnboardingStages)
      ? completedOnboardingStages
      : [completedOnboardingStages];
  }
  if (req.body.country) {
    if (!(await getStripeSupportedCountries()).find((country) => country.id === req.body.country)) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Country not supported at the moment');
    }
  }

  let business;
  if (req.body.submit === 'true') {
    // (1) Check the req.user.business exists
    if (!req.user.business) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'You cannot submit yet.');
    }

    // (2) Validate the data using the completeBusinessOnboarding validator
    const existingInfo = await Business.findById(req.user.business).populate('contactPerson').lean();
    const { error } = businessValidation.completeBusinessOnboard.validate(existingInfo);
    if (error) {
      logger.error(`Error: ${error.message}`);
      throw new ApiError(httpStatus.BAD_REQUEST, `Error: ${error.message}`);
    }
    // Validate that user has verified their phone number
    if (!existingInfo.contactPerson.phone || existingInfo.contactPerson.phoneVerified === false) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Phone number has not been verified');
    }

    business = await Business.findByIdAndUpdate(
      req.user.business,
      {
        onboardingStage: businessEnums.onboardingStages.COMPLETED,
        verificationStatus: existingInfo.verificationStatus || businessEnums.businessVerificationStatuses.NOT_STARTED,
      },
      { new: true },
    );

    // add check to ensure that business is verified before onboarding?
    // if (existingInfo.verificationStatus !== businessEnums.businessVerificationStatuses.APPROVED) {
    //   throw new ApiError(httpStatus.BAD_REQUEST, 'Business is not yet verified');
    // }

    // (3) Perform an update
    // business = await updateBusiness(req.user.business, req.body);
    if (business.verificationStatus === businessEnums.businessVerificationStatuses.CHANGES_REQUESTED) {
      const parentVerification = await BusinessVerification.findOne({ business: business._id }).sort({ createdAt: -1 });
      if (!parentVerification) {
        logger.error('Parent verification not found');
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'An error occurred while processing request');
      }
      logger.info(parentVerification._id, parentVerification.email);

      const verifier = await User.findById(parentVerification.assignee);
      if (!verifier) {
        logger.error('Verifier not found');
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'An error occurred while processing request');
      }

      // chages requested.. Create new verification entry
      const businessVerification = await BusinessVerification.create({
        business: business._id,
        parentVerification: parentVerification._id,
        assignee: parentVerification.assignee,
        assigner: parentVerification.assigner,
        assignedAt: Date.now(),
        verificationStatus: businessEnums.businessVerificationStatuses.IN_PROGRESS,
      });

      business.verifications = [...(business.verifications || []), businessVerification._id];
      business.verificationStatus = businessEnums.businessVerificationStatuses.IN_PROGRESS;
      await business.save();
      // implement notification to notify verifier
      await emailService.sendVerificationResubmitEmail(verifier, { business, businessVerification });
    }

    await User.findByIdAndUpdate(req.user._id, {
      registrationStatus: registrationStatuses.ONBOARDING_COMPLETE,
      pendingBusiness: 'false',
      $addToSet: { roles: clientUserTypes.BUSINESS },
    });
  } else if (!req.user.business) {
    /** If req.user.business does not exist, create business and update the user model
     * else perform an update
     * */
    business = await createBusiness(req.user, req.body, req.files);
    req.user.business = business._id;
    // await req.user.save();
  } else {
    // Perform an update
    business = await updateBusiness(req.user.business, req.body, req.files, true);
  }

  await req.user.save();
  return business;
};

const getBusinesses = async (filterParam, options) => {
  const filter = filterParam;
  filter.$and = [];
  if (filter.name) {
    const filterObject = [];
    const displayName = stopword.removeStopwords(filter.name.split(' '));
    filterObject.push({ $and: wordsArrayToFilterArray(displayName, 'displayName') });

    delete filter.name;
    filter.$and.push({ $or: filterObject });
  }

  filter.onboardingStage = businessEnums.onboardingStages.COMPLETED;
  filter.verificationStatus = businessEnums.businessVerificationStatuses.APPROVED;
  if (filter.$and.length === 0) delete filter.$and;

  const businesses = await Business.paginate(filter, {
    ...options,
    select: businessSelectProps(false, false),
    populate: businessPopulatePaths(true),
  });
  return businesses;
};

// const user = await User.findOne({ email: '<EMAIL>' });
// await emailService.sendVerificationResultEmail(user.email, {
//   business: await Business.findOne(),
//   // business,
//   status: 'changes_requested',
//   action: 'changes requested',
//   comment: 'Here is my comment',
// });
// throw Error('Not implemented');
const getBusiness = async (req) => {
  const isOwner = req.user?.business?.toString() === req.params.id;
  const businessId = req.params.id;

  // let isOwnerPopulatePaths = [...businessPopulatePaths];
  // if (isOwner) {
  //   isOwnerPopulatePaths = [...isOwnerPopulatePaths, { path: 'account' }];
  // }

  let filter;
  if (validateId(businessId, 'Business', false)) {
    filter = { _id: businessId };
  } else {
    filter = { username: businessId };
  }
  if (!isOwner) filter.verificationStatus = businessEnums.businessVerificationStatuses.APPROVED;

  const business = await Business.findOne(filter)
    .select(businessSelectProps(isOwner, !!req.user))
    .populate(businessPopulatePaths(!!req.user));

  if (!business) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Business not found');
  }

  return business;
};

const getBusinessByUsername = async (req) => {
  const { username: name } = req.params;
  const username = name.toLowerCase().replace(/\s+/g, '');
  const business = await Business.findOne({ username });
  if (!business) {
    logger.error('Business not found');
    throw new ApiError(httpStatus.NOT_FOUND, 'Business not found');
  }

  const isOwner = String(req.user?._id) === business._id;
  return Business.findById(business._id)
    .select(businessSelectProps(isOwner, !!req.user))
    .populate(businessPopulatePaths(!!req.user));
};

const deleteBusiness = async (userParam, businessId) => {
  const user = userParam;
  validateId(businessId, 'Business');

  const business = await Business.findById(businessId);
  if (!business) {
    logger.error('Business not found');
    throw new ApiError(httpStatus.NOT_FOUND, 'Business not found');
  }

  const services = await Service.find({ provider: businessId });
  if (services.length) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Please delete all associated services');
  }

  await Promise.all([
    business.logo ? File.findByIdAndDelete(business.logo) : Promise.resolve(),
    business.banner ? File.findByIdAndDelete(business.banner) : Promise.resolve(),
    Business.findByIdAndDelete(businessId),
  ]);

  await User.updateOne(
    { _id: user._id },
    { $unset: { business: '', pendingBusiness: '' }, $set: { pendingStudent: 'false' } },
  );
};

const addBusinessProfileResource = async (businessId, resourceName, data) => {
  validateId(businessId, 'Business');

  const business = await Business.findById(businessId);
  if (!business) {
    logger.error('Business not found');
    throw new ApiError(httpStatus.NOT_FOUND, 'Business not found');
  }

  await profileService.addResourceInfo(business.profile, capitalizeFirstChar(resourceName), data);
};

const updateBusinessProfileResource = async (businessId, resourceId, resourceName, data) => {
  validateId(businessId, 'Business');
  validateId(resourceId, capitalizeFirstChar(resourceName));

  const business = await Business.findById(businessId);
  if (!business) {
    logger.error('Business not found');
    throw new ApiError(httpStatus.NOT_FOUND, 'Business not found');
  }

  await profileService.updateResourceInfo(business.profile, resourceId, capitalizeFirstChar(resourceName), data);
};

const deleteBusinessProfileResource = async (businessId, resourceId, resourceName) => {
  validateId(businessId, 'Business');
  validateId(resourceId, capitalizeFirstChar(resourceName));

  const business = await Business.findById(businessId);
  if (!business) {
    logger.error('Business not found');
    throw new ApiError(httpStatus.NOT_FOUND, 'Business not found');
  }

  await profileService.deleteResourceInfo(business.profile, resourceId, capitalizeFirstChar(resourceName));
};

const fetchUnverifiedBusinesses = async (filter, options) => {
  const queryFilter = { ...filter };
  if (!queryFilter.verificationStatus) {
    queryFilter.verificationStatus = { $in: Object.values(businessEnums.businessVerificationStatuses) };
  }
  const { type, displayName } = queryFilter;
  if (displayName) {
    queryFilter.displayName = { $regex: displayName, $options: 'i' };
  }
  delete queryFilter.type;

  if (type === 'business') {
    const businesses = await Business.paginate(queryFilter, {
      ...options,
      select: 'displayName verificationStatus _id createdAt',
    });

    const countPipeline = [{ $group: { _id: '$verificationStatus', count: { $sum: 1 } } }];
    const [counts] = await Promise.all([Business.aggregate(countPipeline)]);

    const statusCounts = counts.reduce((acc, { _id, count }) => {
      acc[_id] = count;
      return acc;
    }, {});

    const returnData = { businesses, statusCounts };

    return returnData;
  }
};

const fetchVerifications = async (filter, options) => {
  const queryFilter = { ...filter };
  const { type, displayName } = queryFilter;
  delete queryFilter.type;

  if (type === 'business') {
    const dataPipeline = [
      { $match: { ...queryFilter, verificationStatus: { $exists: true } } },
      {
        $lookup: { from: 'businesses', localField: 'business', foreignField: '_id', as: 'businessDetails' },
      },
      // {
      //   $lookup: { from: 'users', localField: 'assignee', foreignField: '_id', as: 'assigneeDetails' },
      // },
      // {
      //   $lookup: { from: 'users', localField: 'assigner', foreignField: '_id', as: 'assignerDetails' },
      // },
      { $unwind: '$businessDetails' },
      ...(displayName ? [{ $match: { 'businessDetails.displayName': { $regex: displayName, $options: 'i' } } }] : []), // if displayName is provided
      {
        $project: {
          _id: 1,
          business: {
            _id: '$businessDetails._id',
            name: '$businessDetails.name',
            coverPhoto: '$businessDetails.coverPhoto',
            profilePhoto: '$businessDetails.profilePhoto',
            displayName: '$businessDetails.displayName',
            username: '$businessDetails.username',
          },
          assignee: 1,
          assigner: 1,
          comment: 1,
          verificationStatus: 1,
          assignedAt: 1,
          reviewedAt: 1,
          verifiedAt: 1,
        },
      },
      {
        $lookup: { from: 'files', localField: 'business.coverPhoto', foreignField: '_id', as: 'business.coverPhoto' },
      },
      {
        $lookup: { from: 'files', localField: 'business.profilePhoto', foreignField: '_id', as: 'business.profilePhoto' },
      },
      { $unwind: { path: '$business.coverPhoto', preserveNullAndEmptyArrays: true } },
      { $unwind: { path: '$business.profilePhoto', preserveNullAndEmptyArrays: true } },
      {
        $facet: {
          data: [{ $skip: (options.page - 1) * options.limit }, { $limit: options.limit }],
          total: [{ $count: 'totalRecords' }],
        },
      },
    ];

    if (options.sortBy) {
      const [sortField, sortOrder] = options.sortBy.split(':');
      const sortStage = { $sort: { [sortField]: sortOrder === 'asc' ? 1 : -1 } };
      dataPipeline.push(sortStage);
    }

    const countPipeline = [{ $group: { _id: '$verificationStatus', count: { $sum: 1 } } }];
    const [dataResults, countResults] = await Promise.all([
      BusinessVerification.aggregate(dataPipeline),
      BusinessVerification.aggregate(countPipeline),
    ]);

    const data = dataResults[0]?.data || [];
    const totalRecords = dataResults[0]?.total[0]?.totalRecords || 0;
    const counts = countResults;

    const statusCounts = counts.reduce((acc, { _id, count }) => {
      acc[_id] = count;
      return acc;
    }, {});

    return {
      data,
      totalRecords,
      statusCounts,
    };
  }
};

const assignVerifier = async (businessId, assignerId, verifierId, parentVerification) => {
  // assign business to verifier; ensure the user has the right of verifyBusiness
  validateId(verifierId, 'User');
  validateId(businessId, 'Business');

  const user = await User.findById(verifierId);
  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Verifier not found');
  }

  const business = await Business.findById(businessId);
  if (!business) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Business not found');
  }

  const userPermissions = user.roles.reduce((acc, role) => [...acc, ...roleRights.get(role)], []);
  if (!userPermissions.includes('verifyBusinesses')) {
    throw new ApiError(httpStatus.FORBIDDEN, 'User does not have the right to verify businesses');
  }

  if (business.verificationStatus === businessEnums.businessVerificationStatuses.APPROVED) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Business is already verified');
  }

  const businessVerification = await BusinessVerification.create({
    business: business._id,
    parentVerification,
    assignee: verifierId,
    assigner: assignerId,
    assignedAt: Date.now(),
    verificationStatus: businessEnums.businessVerificationStatuses.IN_PROGRESS,
  });

  business.verifications = [...(business.verifications || []), businessVerification._id];
  business.verificationStatus = businessEnums.businessVerificationStatuses.IN_PROGRESS;
  await business.save();
  // implement notification to notify verifier
  emailService.sendVerifierAssignedEmail(user, { business, businessVerification });
};

const verifyBusiness = async (businessId, verifierId, data) => {
  // verify business;
  validateId(businessId, 'Business');
  const business = await Business.findById(businessId).populate('contactPerson');
  if (!business) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Business not found');
  }

  const { businessVerification: businessVerificationId, verificationStatus, comment } = data;
  validateId(businessVerificationId, 'BusinessVerification');
  const businessVerification = await BusinessVerification.findById(businessVerificationId);
  if (!businessVerification) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Business verification not found');
  }

  if (businessVerification.assignee.toString() !== verifierId.toString()) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You do not have the right to verify this business');
  }

  if (business.verificationStatus === businessEnums.businessVerificationStatuses.APPROVED) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Business is already verified');
  }

  if (comment) businessVerification.comment = comment;
  businessVerification.reviewedAt = Date.now();

  let action;
  if (verificationStatus === businessEnums.businessVerificationStatuses.APPROVED) {
    businessVerification.verifiedAt = Date.now();
    await User.findByIdAndUpdate(business.contactPerson._id, {
      registrationStatus: registrationStatuses.ONBOARDING_COMPLETE,
      pendingBusiness: 'false',
    });
    action = businessEnums.businessVerificationStatuses.APPROVED;
  } else if (verificationStatus === businessEnums.businessVerificationStatuses.CHANGES_REQUESTED) {
    await User.findByIdAndUpdate(business.contactPerson._id, {
      registrationStatus: registrationStatuses.ACCOUNT_VERIFIED,
      pendingBusiness: 'true',
    });
    action = businessEnums.businessVerificationStatuses.CHANGES_REQUESTED;
  }

  await emailService.sendVerificationResultEmail(business.contactPerson.email, {
    business,
    status: verificationStatus,
    action,
    comment: businessVerification.comment,
  });

  businessVerification.verificationStatus = verificationStatus;
  business.verificationStatus = verificationStatus;

  await Promise.all([businessVerification.save(), business.save()]);
  // implement notification to notify business owner
};

const fetchBusinessVerificationInfo = async (businessId) => {
  validateId(businessId, 'Business');

  const business = await Business.findById(businessId);
  if (!business) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Business not found');
  }

  // if (business.verificationStatus === businessEnums.businessVerificationStatuses.APPROVED) {
  //   throw new ApiError(httpStatus.BAD_REQUEST, 'Business is already verified');
  // }

  // eslint-disable-next-line global-require
  const { basicUserPopulate } = require('./user.service');
  const businessVerification = await BusinessVerification.findOne({ business: businessId })
    .populate([
      // { path: 'parentVerification', populate: [{ path: 'assignee', select: basicUserPopulate.select }] },
      { path: 'assignee', select: basicUserPopulate.select },
      { path: 'assigner', select: basicUserPopulate.select },
    ])
    .sort({ createdAt: -1 });
  // if (businessVerification.assignee.toString() !== verifierId.toString()) {
  //   throw new ApiError(httpStatus.FORBIDDEN, 'Only assigned verifier can perform this action');
  // }
  return { business, businessVerification };
};

const fetchParentVerification = async (verificationId) => {
  validateId(verificationId, 'BusinessVerification');
  // await BusinessVerification.findByIdAndUpdate(verificationId, { parentVerification: '678ffba0b0bc905dbda91788' });

  const businessVerification = await BusinessVerification.findById(verificationId).select('-assignee -assigner -business');

  if (!businessVerification) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Business verification not found');
  }

  return businessVerification;
};

module.exports = {
  getEnums,
  createBusiness,
  onboardBusiness,
  getBusinesses,
  getBusiness,
  getBusinessByUsername,
  updateBusiness,
  deleteBusiness,
  addBusinessProfileResource,
  updateBusinessProfileResource,
  deleteBusinessProfileResource,
  basicBusinessPopulate,
  fetchVerifications,
  fetchBusinessVerificationInfo,
  fetchUnverifiedBusinesses,
  assignVerifier,
  verifyBusiness,
  fetchParentVerification,
};
