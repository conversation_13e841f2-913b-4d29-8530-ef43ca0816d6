module.exports = {
  get: {
    summary: 'Get forum posts',
    tags: ['Forum Posts'],
    parameters: [
      {
        in: 'query',
        name: 'userId',
        schema: {
          type: 'string',
          format: 'objectId',
        },
        description: 'ID of the user',
      },
      {
        in: 'query',
        name: 'sortBy',
        schema: {
          type: 'string',
        },
        description: 'Sort order for the posts',
      },
      {
        in: 'query',
        name: 'limit',
        schema: {
          type: 'integer',
          format: 'int32',
          example: '12',
        },
        description: 'Number of posts to return',
      },
      {
        in: 'query',
        name: 'page',
        schema: {
          type: 'integer',
          format: 'int32',
          example: '2',
        },
        description: 'Page number',
      },
      {
        in: 'query',
        name: 'tags',
        schema: {
          type: 'array',
          items: {
            type: 'string',
          },
        },
        description: 'tags to get post(s) by',
      },
      {
        name: 'category',
        in: 'query',
        description: 'Category of forum post of the resource to get',
        schema: {
          type: 'string',
          enum: [
            'All',
            'General Discussion',
            'Admissions',
            'Test Scores',
            'SAT',
            'ACT',
            'College Applications',
            'Scholarships',
            'Financial Aid',
            'Study Tips',
            'Student Life',
            'Internships',
            'Career Advice',
            'Graduate School',
          ],
        },
      },
    ],
    responses: {
      200: {
        description: 'Forum posts retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                data: {
                  type: 'object',
                  properties: {
                    results: {
                      type: 'array',
                      items: { $ref: '#/components/schemas/ForumPosts' },
                    },
                    page: { type: 'integer', example: '2' },
                    limit: { type: 'integer', example: '12' },
                    totalPages: { type: 'integer', example: '2' },
                    totalResults: { type: 'integer', example: '2' },
                  },
                  status: {
                    type: 'string',
                    example: 'SUCCESS',
                  },
                  message: {
                    type: 'string',
                    example: 'Forum Posts retrieved successfully',
                  },
                },
              },
            },
          },
        },
        // Add other responses as needed
      },
    },
  },
};
