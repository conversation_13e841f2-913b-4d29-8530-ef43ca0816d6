/* eslint-disable no-param-reassign */
const Async = require('async');
const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const Post = require('../post.model');

const loadMessagesToConversations = async () => {
  const migrated = await GlobalVariable.findOne({ name: 'Combine Reposts and Reposts with thoughts' });

  if (migrated) {
    return;
  }

  const posts = await Post.find();
  await Async.eachOfSeries(posts, async (post) => {
    await Async.eachOfSeries(post.reposts, async (repost) => {
      await Post.create({
        parentPost: post._id,
        user: repost,
        group: post.group || null,
        createdAt: post.updatedAt + Math.floor(Math.random() * 1000),
      });
    });
    await Post.updateOne(
      { _id: post._id },
      {
        $set: {
          reposts: [...new Set([...post.reposts, ...post.repostWithThought].map((t) => String(t)))],
          repostWithThought: [],
        },
      },
    );
  });
  await GlobalVariable.create({ name: 'Combine Reposts and Reposts with thoughts', value: 'true' });
  logger.info('Reposts and Reposts with thoughts combined');
};

module.exports = loadMessagesToConversations;
