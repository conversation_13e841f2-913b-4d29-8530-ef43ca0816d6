module.exports = {
  post: {
    tags: ['Authentication'],
    summary: "Verify user's email",
    description:
      'When the user clicks the link in the instructions sent to them, they are directed to the frontend with a token in the URL. The token is used to reset the password using this endpoint.',
    requestBody: {
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              token: { type: 'string', example: 'reset-password-token' },
              password: { type: 'string', example: 'newpassword123' },
              confirmPassword: { type: 'string', example: 'newpassword123' },
            },
            required: ['token', 'password', 'confirmPassword'],
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Password reset successful',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Password reset successful' },
                status: { type: 'string', example: 'SUCCESS' },
              },
            },
          },
        },
      },
      401: {
        description: 'The token provided in the request is invalid',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: {
                  type: 'string',
                  example: 'FAILED',
                },
                message: {
                  type: 'string',
                  example: 'Password reset failed. Link is invalid or expired.',
                },
              },
            },
          },
        },
      },
    },
  },
};
