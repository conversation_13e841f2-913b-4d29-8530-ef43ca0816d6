const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');

// Data source: https://github.com/dr5hn/countries-states-cities-database
// https://github.com/dr5hn/countries-states-cities-database/blob/master/countries%2Bstates%2Bcities.json
const stateSchema = new mongoose.Schema(
  {
    name: { type: String },
    stateCode: { type: String },
    cities: [{ type: mongoose.Schema.Types.ObjectId, ref: 'City' }],
  },
  {
    timestamps: true,
  },
);

stateSchema.index({ name: 1 });

stateSchema.plugin(toJSON);
stateSchema.plugin(paginate);

const State = mongoose.model('State', stateSchema);

module.exports = State;
