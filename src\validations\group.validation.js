const Joi = require('joi');
const { objectId } = require('./custom.validation');
const { fileSchema } = require('./file.validation');
const { groupSettingsEnums } = require('../config/constants');

const createGroup = {
  body: Joi.object().keys({
    name: Joi.string().required(),
    description: Joi.string(),
    rules: Joi.string(), // Because formData will have to be sent as string. Will be split into array (by '~^$') in controller
    type: Joi.string().valid('public', 'private').default('public'),
    enableInvitation: Joi.boolean().default(false),
  }),
  files: Joi.object().keys({
    coverPhoto: Joi.array().items(fileSchema(1)).max(1),
    profilePhoto: Joi.array().items(fileSchema(1)).max(1),
  }),
};

const joinGroup = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId),
  }),
};

const getGroup = {
  params: Joi.object().keys({
    id: Joi.string(),
  }),
};

const getUserGroups = {
  query: Joi.object().keys({
    sortBy: Joi.string(),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
    type: Joi.string().valid('public', 'private'),
    name: Joi.string(),
    searchText: Joi.string(),
    filter: Joi.string().valid('my_groups', 'requested_groups', 'discover_groups').default('my_groups'),
  }),
};

const getGroupMembers = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId), // group id
  }),
  query: Joi.object().keys({
    sortBy: Joi.string().valid(
      'createdAt:desc',
      'createdAt:asc',
      'name:asc',
      'name:desc',
      'updatedAt:asc',
      'updatedAt:desc',
    ),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
    // user: Joi.string().custom(objectId),
  }),
};

const searchGroup = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId),
  }),
  query: Joi.object().keys({
    searchText: Joi.string().required(),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
  }),
};

const deleteGroup = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId),
  }),
};

const updateGroup = {
  params: Joi.object().keys({
    id: Joi.required().custom(objectId),
  }),
  body: Joi.object().keys({
    name: Joi.string(),
    description: Joi.string(),
    rules: Joi.string().default(''),
    type: Joi.string().valid('public', 'private').default('public'),
    enableInvitation: Joi.boolean().default(false),
  }),
  files: Joi.object().keys({
    coverPhoto: Joi.array().items(fileSchema(1)).max(1),
    profilePhoto: Joi.array().items(fileSchema(1)).max(1),
  }),
};

const handleMembershipRequest = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required().description('ID of the group'), // group id
  }),
  query: Joi.object().keys({
    userId: Joi.string()
      .description('ID of the user')
      .when('all', {
        // when all does not exist, userId is required
        is: Joi.not('true'),
        then: Joi.required().description('ID of the user'),
        otherwise: Joi.forbidden(), // When 'all' is true, userId should not be provided
      }),
    action: Joi.string()
      .valid('approve', 'reject', 'coAdmin')
      .required()
      .description('Action to perform: "approve", "reject", or "coAdmin"'),
    all: Joi.string().valid('true'),
  }),
};

const leaveGroup = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required().description('ID of the group'), // group id
    userId: Joi.string().custom(objectId).required().description('ID of the user'),
  }),
  body: Joi.object().keys({
    newAdminId: Joi.string().custom(objectId),
  }),
};

const removeUsers = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required().description('ID of the group'), // group id
  }),
  body: Joi.object().keys({
    users: Joi.array().items(Joi.string().custom(objectId)).required(),
  }),
};

const handleCoAdmins = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required().description('ID of the group'), // group id
  }),
  body: Joi.object().keys({
    users: Joi.array().items(Joi.string().custom(objectId)).required(),
    action: Joi.string().valid('add', 'remove').required().description('Action to perform: "add" or "remove"'),
  }),
};

const getUserGroupSettings = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required().description('ID of the group'), // group id
  }),
};

const updateGroupSettings = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required().description('ID of the group'), // group id
  }),
  body: Joi.object().keys({
    posts: Joi.string().valid(...Object.values(groupSettingsEnums)),
    messages: Joi.string().valid(...Object.values(groupSettingsEnums)),
  }),
};

const sendInvite = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required().description('ID of the group'), // group id
  }),
  body: Joi.object().keys({
    mutuals: Joi.array().items(Joi.string().custom(objectId)).required(),
    inviteUrl: Joi.string().required(),
  }),
};

module.exports = {
  createGroup,
  joinGroup,
  getGroup,
  getUserGroups,
  searchGroup,
  updateGroup,
  deleteGroup,
  getGroupMembers,
  handleMembershipRequest,
  leaveGroup,
  updateGroupSettings,
  getUserGroupSettings,
  removeUsers,
  handleCoAdmins,
  sendInvite,
};
