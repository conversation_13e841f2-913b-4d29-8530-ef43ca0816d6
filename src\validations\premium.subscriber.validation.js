const Joi = require('joi');
const { businessEnums } = require('../config/constants');

const createSubscriber = {
  body: Joi.object().keys({
    period: Joi.string()
      .valid(...Object.values(businessEnums.premiumSubscriptionPeriods).map((p) => p[0]))
      .required(),
    periodCount: Joi.number().default(1),
    plan: Joi.string()
      .valid(...Object.values(businessEnums.premiumSubscriptionPlans).map((p) => p.name))
      .default('premium'),
  }),
};

const updateSubscription = {
  body: Joi.object().keys({
    period: Joi.string()
      .valid(...Object.values(businessEnums.premiumSubscriptionPeriods).map((p) => p[0]))
      // .when('type', { is: 'extend', then: Joi.required() })
      .when('type', { is: 'renew', then: Joi.required() })
      .when('type', { is: 'cancel', then: Joi.forbidden() }),
    periodCount: Joi.number().default(1),
    plan: Joi.string()
      .valid(...Object.values(businessEnums.premiumSubscriptionPlans).map((p) => p.name))
      .default('premium'),
    type: Joi.string().valid('renew', 'cancel').required(),
  }),
};

const getSessionStatus = {
  query: Joi.object().keys({
    sessionId: Joi.string(),
  }),
};

const getSubscriptions = {
  query: Joi.object().keys({
    active: Joi.string().valid('true', 'false'),
    user: Joi.string(), // Only internal users can specify this
    subscriber: Joi.string(), // Only internal users can specify this
    sortBy: Joi.string()
      .valid('startDate:asc', 'startDate:desc', 'expiresAt:asc', 'expiresAt:desc')
      .default('expiresAt:desc'),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
  }),
};

module.exports = {
  createSubscriber,
  getSessionStatus,
  getSubscriptions,
  updateSubscription,
};
