const express = require('express');

const router = express.Router();
const { auth, canManageResource } = require('../../middlewares/auth');
const validate = require('../../middlewares/validate');
const { groupController: GroupController } = require('../../controllers');
const { groupValidation: GroupValidation } = require('../../validations');
const { uploadGroupPhotos } = require('../../config/multer.config');
const { canGetGroup, canManageRequests, canRemoveFromGroup, canManageGroupSettings } = require('../../middlewares/cancan');

router.get('/:id/public', validate(GroupValidation.getGroup), GroupController.getGroupById);

router.use(auth());

// Create a new group
router.post('/', uploadGroupPhotos, validate(GroupValidation.createGroup), GroupController.createGroup);

// Retrieve a list of user's groups
router.get('/', validate(GroupValidation.getUserGroups), GroupController.getUserGroups);

// Retrieve group members
router.get('/:id/members', validate(GroupValidation.getGroupMembers), GroupController.getGroupMembers);

// Join Group (Send a request to join a group or just join if the group is public)
router.post('/:id/join', validate(GroupValidation.joinGroup), GroupController.joinGroup);

// router.get('/:id/search', validate(GroupValidation.searchGroup), GroupController.searchGroup);

// Retrieve group membership requests
router.get(
  '/:id/requests',
  canManageRequests(),
  validate(GroupValidation.getGroupMembers),
  GroupController.getGroupRequests,
);

// Leave a group (Remove a user from a group)
router.post(
  '/:id/leave-group/:userId',
  canRemoveFromGroup(),
  validate(GroupValidation.leaveGroup),
  GroupController.leaveGroup,
);

// Handle membership requests (approve/reject/co-admin)
router.post(
  '/:id/members/handle',
  canManageRequests(),
  validate(GroupValidation.handleMembershipRequest),
  GroupController.handleMembershipRequest,
);

router.patch('/:id/members/remove', canManageRequests(), validate(GroupValidation.removeUsers), GroupController.removeUsers);
router.patch(
  '/:id/members/coAdmins',
  canManageRequests(),
  validate(GroupValidation.handleCoAdmins),
  GroupController.handleCoAdmins,
);

// Retrieve group by ID
router.get('/:id', canGetGroup(), validate(GroupValidation.getGroup), GroupController.getGroupById);

// Update group information
router.patch(
  '/:id',
  canManageResource('Group'), // Only admin can edit group information
  uploadGroupPhotos,
  validate(GroupValidation.updateGroup),
  GroupController.updateGroup,
);

// get user group settings
router.get(
  '/:id/settings',
  canManageGroupSettings(),
  validate(GroupValidation.getUserGroupSettings),
  GroupController.getUserGroupSettings,
);

// update user group settings
router.patch(
  '/:id/settings',
  canManageGroupSettings(),
  validate(GroupValidation.updateGroupSettings),
  GroupController.updateGroupSettings,
);

// send invite link
router.post('/:id/invite', canGetGroup(), validate(GroupValidation.sendInvite), GroupController.sendInvite);

// Delete a group
router.delete('/:id', canManageResource('Group'), validate(GroupValidation.deleteGroup), GroupController.deleteGroup);

module.exports = router;
