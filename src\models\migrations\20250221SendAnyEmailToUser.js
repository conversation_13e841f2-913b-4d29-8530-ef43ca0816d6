/* eslint-disable no-param-reassign */
const Async = require('async');
const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const User = require('../user.model');
const TEMPLATES = require('../../public/emailTemplates');
const { sendEmail } = require('../../services/email.service');
const { capitalizeFirstChar } = require('../../utils/stringFormatting');

const varName = 'Send some email to a user or users';

const notifyUsersToCompleteProfileScript = async () => {
  const migrated = await GlobalVariable.findOne({ name: varName });
  if (migrated) {
    return;
  }

  const users = await User.find({ email: '' });
  await Async.eachOfSeries(users, async (user) => {
    const content = `<tr>
                  <td style="padding: 0;">
                    <p style="margin-top: 50px;">Hello ${capitalizeFirstChar(user.firstName || '')},</p>
                    <p>
                      We received the report about the difficulty you experienced with logging in. We are sorry for the inconvenience.
                    </p>
                    <p>
                      Kindly log in again and you'll receive an email to verify your account.
                    </p>
                  </td>
                </tr>

                <tr>
                  <td style="padding: 10px 0;">
                    <center>
                      <a href="https://unyked.com/login" style="background: #3772ff;border: none;font-weight: bold;padding: 10px;color: white;border-radius: 5px;font-size: 1rem;text-align: center;text-decoration: none;">
                        Login
                      </a>
                    </center>
                  </td>
                </tr>`;
    const html = TEMPLATES.getGenericEmailTemplate(content);
    const subject = 'We received your report'; // Sample subject
    await sendEmail({ to: user.email, subject, html, isNotif: true });
  });

  await GlobalVariable.create({ name: varName, value: 'true' });
  logger.info('Created Token for users to complete profile');
};

module.exports = notifyUsersToCompleteProfileScript;
