const GlobalVariable = require('../global.variable.model');
const Scholarship = require('../scholarship.model');
const logger = require('../../config/logger');

const generateUniqueNameForExistingScholarships = async () => {
  const generated = await GlobalVariable.findOne({ name: 'generate unique name for existing scholarships' });
  if (generated) {
    return;
  }

  const scholarships = await Scholarship.find({ uniqueName: { $exists: false } }).lean();
  await Promise.all(
    scholarships.map(async (scholarship) => {
      const uniqueName = scholarship.name
        .replaceAll(/[^a-zA-Z0-9\s.]+/g, '')
        .replaceAll(' ', '-')
        .toLowerCase()
        .replace(/^-+|-+$/g, '');
      await Scholarship.updateOne({ _id: scholarship._id }, { uniqueName });
    }),
  );

  await GlobalVariable.create({ name: 'generate unique name for existing scholarships', value: 'true' });
  logger.info('Unique name generated for existing scholarships');
};

module.exports = generateUniqueNameForExistingScholarships;
