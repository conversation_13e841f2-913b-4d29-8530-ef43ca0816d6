const express = require('express');

const router = express.Router();
const { auth, authExcept } = require('../../middlewares/auth');
const validate = require('../../middlewares/validate');
const { orderController: OrderController } = require('../../controllers');
const { orderValidation: OrderValidation } = require('../../validations');
const { uploadNamedFiles, uploadUnnamedFiles } = require('../../config/multer.config');

const uploadOrderSupportingDocuments = uploadNamedFiles({
  supportingDocuments: 12,
  deliverables: 12,
});

router.use(authExcept([/^\/disputes$/]));

router.post('/', validate(OrderValidation.createOrder), OrderController.initializeOrder);
router.patch(
  '/:sessionId/complete-create', // Payment Intent ID or order id
  uploadUnnamedFiles(),
  validate(OrderValidation.completeCreateOrder),
  OrderController.completeCreateOrder,
);
router.get('/activities', validate(OrderValidation.getOrderActivities), OrderController.getOrderActivities);
router.get('/', validate(OrderValidation.getOrders), OrderController.getOrders);
router.patch('/:id/complete', OrderController.completeOrder);
router.patch(
  '/:id/fulfill',
  uploadOrderSupportingDocuments,
  validate(OrderValidation.fulfillOrder),
  OrderController.fulfillOrder,
);
router.get('/disputes', auth('manageDisputes'), validate(OrderValidation.getDisputes), OrderController.getDisputes);
router.post('/:id/disputes', uploadUnnamedFiles(), validate(OrderValidation.reportDispute), OrderController.reportDispute);
router.post('/:id/cancel', validate(OrderValidation.cancelOrder), OrderController.cancelOrder);
router.post('/:id/request-extra-time', validate(OrderValidation.requestExtraTime), OrderController.requestExtraTime);
router.post('/:id/approve-extra-time', validate(OrderValidation.approveExtraTime), OrderController.approveExtraTime);
router.get('/:id', validate(OrderValidation.getOrder), OrderController.getOrder);

router.post(
  '/service-revisions/:orderId',
  uploadOrderSupportingDocuments,
  validate(OrderValidation.createServiceRevision),
  OrderController.createServiceRevision,
);
router.get('/:id/service-revisions', validate(OrderValidation.getServiceRevisions), OrderController.getServiceRevisions);
router.get(
  '/service-revisions/:revisionId',
  validate(OrderValidation.getServiceRevision),
  OrderController.getServiceRevision,
);
router.patch(
  '/service-revisions/:revisionId/fulfill',
  uploadOrderSupportingDocuments,
  validate(OrderValidation.fulfillServiceRevision),
  OrderController.fulfillServiceRevision,
);
router.patch(
  '/service-revisions/:revisionId/complete',
  validate(OrderValidation.updateServiceRevision),
  OrderController.completeServiceRevision,
);
router.patch(
  '/service-revisions/:revisionId/cancel',
  validate(OrderValidation.updateServiceRevision),
  OrderController.cancelServiceRevision,
);

module.exports = router;
