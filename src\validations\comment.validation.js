const Joi = require('joi');
const { objectId } = require('./custom.validation');
const { fileSchema } = require('./file.validation');

const createComment = {
  body: Joi.object().keys({
    text: Joi.string().required(),
    postId: Joi.string().required(),
    parentCommentId: Joi.string(),
  }),
  files: Joi.array().items(fileSchema()),
};

const getComments = {
  query: Joi.object().keys({
    postId: Joi.string().required().custom(objectId),
    userId: Joi.string().custom(objectId),
    parentCommentId: Joi.string().custom(objectId),
    sortBy: Joi.string().valid('createdAt:asc', 'createdAt:desc', 'updatedAt:asc', 'updatedAt:desc'),
    limit: Joi.number().integer(),
    page: Joi.number().integer(),
  }),
};

const getComment = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId),
  }),
};

const updateComment = {
  params: Joi.object().keys({
    id: Joi.required().custom(objectId),
  }),
  body: Joi.object()
    .keys({
      text: Joi.string(),
      deletedUrls: Joi.string(),
    })
    .min(1),
  files: Joi.array().items(fileSchema()),
};

const deleteComment = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId),
  }),
};

module.exports = {
  createComment,
  getComments,
  getComment,
  updateComment,
  deleteComment,
};
