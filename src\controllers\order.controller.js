const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { orderService, serviceRevisionService } = require('../services');
const { pick, removeUndefinedKeys } = require('../utils/pick');

const initializeOrder = catchAsync(async (req, res) => {
  const order = await orderService.initializeOrder({ ...req.body, client: req.user });
  res.status(httpStatus.CREATED).json({ status: 'SUCCESS', data: order, message: 'Order payment initiated successfully' });
});

const completeCreateOrder = catchAsync(async (req, res) => {
  const order = await orderService.completeCreateOrder(req);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: order, message: 'Order requirements received.' });
});

const getOrders = catchAsync(async (req, res) => {
  const filter = pick(req.query, ['status', 'provider']);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  removeUndefinedKeys(filter);

  // const isBusinessOwner = req.user && req.user.business && req.user.business.toString() === req.query.business;
  const orders = await orderService.getOrders(filter, options, req.user);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: orders, message: 'Orders retrieved successfully' });
});

const getOrder = catchAsync(async (req, res) => {
  const order = await orderService.getOrder(req.user, req.params.id, req.query.provider);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: order, message: 'Order retrieved successfully' });
});

const fulfillOrder = catchAsync(async (req, res) => {
  const order = await orderService.fulfillOrder(req.user, req.params.id, req.body, req.files);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: order, message: 'Order fulfilled successfully' });
});

const cancelOrder = catchAsync(async (req, res) => {
  await orderService.cancelOrder(req.user, req.params.id, req.body.reason);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'As per our policy, order cancelled successfully.' });
});

const completeOrder = catchAsync(async (req, res) => {
  const order = await orderService.completeOrder(req.user, req.params.id);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: order, message: 'Orders updated successfully' });
});

// revisions
const createServiceRevision = catchAsync(async (req, res) => {
  const serviceRevision = await serviceRevisionService.createServiceRevision(
    req.user,
    { ...req.body, order: req.params.orderId },
    req.files,
  );
  res
    .status(httpStatus.CREATED)
    .json({ status: 'SUCCESS', data: serviceRevision, message: 'Service revision created successfully' });
});

const getServiceRevisions = catchAsync(async (req, res) => {
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const serviceRevisions = await serviceRevisionService.getServiceRevisions(req.user, req?.params?.id, options);
  res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data: serviceRevisions, message: 'Service revisions retrieved successfully' });
});

const getServiceRevision = catchAsync(async (req, res) => {
  const serviceRevision = await serviceRevisionService.getServiceRevision(req.user, req.params.revisionId);
  res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data: serviceRevision, message: 'Service revision retrieved successfully' });
});

const fulfillServiceRevision = catchAsync(async (req, res) => {
  const { user, params, body, files } = req;
  const serviceRevision = await serviceRevisionService.fulfillServiceRevision(user, params?.revisionId, body, files);
  res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data: serviceRevision, message: 'Service revision fulfilled successfully' });
});

const completeServiceRevision = catchAsync(async (req, res) => {
  const serviceRevision = await serviceRevisionService.completeServiceRevision(req.user, req?.params?.revisionId);
  res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data: serviceRevision, message: 'Service revision completed successfully' });
});

const cancelServiceRevision = catchAsync(async (req, res) => {
  const serviceRevision = await serviceRevisionService.cancelServiceRevision(req.user, req?.params?.revisionId);
  res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data: serviceRevision, message: 'Service revision cancelled successfully' });
});

const getOrderActivities = catchAsync(async (req, res) => {
  const filter = pick(req.query, ['order']);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  removeUndefinedKeys(filter);

  const orderActivities = await orderService.getOrderActivities(req.user, filter, options);
  res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data: orderActivities, message: 'Order activities retrieved successfully' });
});

const reportDispute = catchAsync(async (req, res) => {
  await orderService.reportDispute(req);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Dispute reported successfully' });
});

const getDisputes = catchAsync(async (req, res) => {
  const filter = pick(req.query, ['order', 'reporter', 'provider', 'service', 'status']);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const disputes = await orderService.getDisputes(filter, options);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: disputes, message: 'Disputes retrieved successfully' });
});

const requestExtraTime = catchAsync(async (req, res) => {
  await orderService.requestExtraTime(req.user, req.params.id, req.body);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Extra time requested successfully' });
});

const approveExtraTime = catchAsync(async (req, res) => {
  await orderService.approveExtraTime(req.user, req.params.id, req.body);
  const message = req.body.requestApproved ? 'Extra time approved successfully' : 'Extra time denied successfully';
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message });
});

module.exports = {
  initializeOrder,
  completeCreateOrder,
  getOrders,
  getOrder,
  completeOrder,
  fulfillOrder,
  cancelOrder,
  reportDispute,
  getDisputes,

  createServiceRevision,
  getServiceRevisions,
  getServiceRevision,
  fulfillServiceRevision,
  completeServiceRevision,
  cancelServiceRevision,

  getOrderActivities,

  requestExtraTime,
  approveExtraTime,
};
