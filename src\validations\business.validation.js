const Joi = require('joi');
const { fileSchema } = require('./file.validation');
const { businessEnums } = require('../config/constants');

const professionalInfo = Joi.object().keys({
  occupation: Joi.string().required(),
  skills: Joi.array().items(Joi.string()).unique(),
  certification: Joi.array().items(Joi.object()), // Objects in array should include id in the case of an update
  education: Joi.array().items(Joi.object()), // Objects in array should include id in the case of an update
});

const businessOnboard = {
  body: Joi.object().keys({
    displayName: Joi.string(),
    about: Joi.string(),
    tagLine: Joi.string(),
    languages: Joi.string(),
    country: Joi.string(), // validate in logic against Stripe supported countries
    completedOnboardingStages: Joi.alternatives().try(
      Joi.string().valid(...Object.values(businessEnums.onboardingStages)),
      Joi.array()
        .items(Joi.string().valid(...Object.values(businessEnums.onboardingStages)))
        .unique(),
    ),
    phone: Joi.string(), // validated with authValidation.sendOTP and authValidation.verifyOTP
    submit: Joi.string().valid('true', 'false').required(),

    professionalInfo: Joi.string(),
    additionalInfo: Joi.string(),
    // occupation: Joi.string(),
    // skills: Joi.alternatives().try(Joi.string(), Joi.array().items(Joi.string()).unique()),
  }),
  files: Joi.object().keys({
    profilePhoto: Joi.array().items(fileSchema(1)).max(1),
    coverPhoto: Joi.array().items(fileSchema(1)).max(1),
  }),
};

const completeBusinessOnboard = Joi.object({
  displayName: Joi.string().required(),
  coverPhoto: Joi.object(),
  profilePhoto: Joi.object(),
  about: Joi.string().required(),
  tagLine: Joi.string().required(),
  languages: Joi.array()
    .items(
      Joi.object({
        _id: Joi.object().required(),
        id: Joi.object().required(),
        proficiency: Joi.string()
          .required()
          .valid(...Object.values(businessEnums.languageLevels)),
      }).required(),
    )
    .required(),
  completedOnboardingStages: Joi.array()
    .items(Joi.string().valid(...Object.values(businessEnums.onboardingStages)))
    .unique(),
  // address: Joi.string().required(),
  // city: Joi.object().required(),
  // state: Joi.object().required(),
  country: Joi.object().required(),
  contactPerson: Joi.object().required(),
}).unknown(true); // This allows extra fields to pass validation

// const createBusiness = Joi.object().keys({
//   body: Joi.object().keys({
//     displayName: Joi.string().required(),
//     about: Joi.string().required(),
//     tagLine: Joi.string(),
//     languages: Joi.string().required(),
//     address: Joi.string().required(),
//     city: Joi.string().required(),
//     state: Joi.string().required(),
//     country: Joi.string().required(),
//   }),
//   files: Joi.object().keys({
//     profilePhoto: Joi.array().items(fileSchema(1)).max(1),
//     coverPhoto: Joi.array().items(fileSchema(1)).max(1),
//   }),
// });

const validateProfessionalInfo = Joi.object().keys({
  occupation: Joi.string().required(),
  skills: Joi.array().items(Joi.string()).unique(),
  certification: Joi.array().items(Joi.object()),
  education: Joi.array().items(Joi.object()),
});

const getBusinesses = {
  query: Joi.object().keys({
    name: Joi.string(),
    sortBy: Joi.string().valid(
      'createdAt:desc',
      'createdAt:asc',
      'name:asc',
      'name:desc',
      'updatedAt:asc',
      'updatedAt:desc',
    ),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
  }),
};

const fetchVerifications = {
  query: Joi.object().keys({
    type: Joi.string().valid('service', 'business').required(),
    sortBy: Joi.string().valid(
      'createdAt:desc',
      'createdAt:asc',
      'displayName:asc',
      'displayName:desc',
      'updatedAt:asc',
      'updatedAt:desc',
    ),
    displayName: Joi.string(),
    serviceName: Joi.string(),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
    verificationStatus: Joi.string().valid(...Object.values(businessEnums.businessVerificationStatuses)),
  }),
};

const verifyBusiness = {
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
  body: Joi.object().keys({
    businessVerification: Joi.string().required(),
    verificationStatus: Joi.string()
      .valid(
        ...[
          businessEnums.businessVerificationStatuses.APPROVED,
          businessEnums.businessVerificationStatuses.CHANGES_REQUESTED,
        ],
      )
      .required(),
    comment: Joi.string().required(),
  }),
};

const assignVerifier = {
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
  body: Joi.object().keys({
    verifier: Joi.string().required(),
    parentVerification: Joi.string(),
  }),
};

const getBusiness = {
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
};

const getBusinessByUsername = {
  params: Joi.object().keys({
    username: Joi.string().required(),
  }),
};

const updateBusiness = {
  body: Joi.object().keys({
    displayName: Joi.string(),
    about: Joi.string(),
    tagLine: Joi.string(),
    languages: Joi.string(),
    // address: Joi.string(),
    // city: Joi.string(),
    // state: Joi.string(),
    // country: Joi.string(),
    // phone: Joi.string(),

    // professionalInfo: Joi.string(),
    occupation: Joi.string(),
    skills: Joi.alternatives().try(Joi.string(), Joi.array().items(Joi.string()).unique()),
  }),
  files: Joi.object().keys({
    profilePhoto: Joi.array().items(fileSchema(1)).max(1),
    coverPhoto: Joi.array().items(fileSchema(1)).max(1),
  }),
};

const addBusinessResource = {
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
  query: Joi.object({
    resourceName: Joi.string().required(),
  }),
};

const updateBusinessResource = {
  params: Joi.object().keys({
    id: Joi.string().required(),
    resourceId: Joi.string().required(),
  }),
  query: Joi.object({
    resourceName: Joi.string().required(),
  }),
};

const validateLanguages = Joi.array()
  .items(
    Joi.object().keys({
      id: Joi.string().required(),
      proficiency: Joi.string()
        .valid(...Object.values(businessEnums.languageLevels))
        .required(),
    }),
  )
  .unique('id');

const uploadAdditionalInfo = Joi.array().items(
  Joi.object({
    type: Joi.string().required(),
    portfolioLink: Joi.string().uri().optional(),
    description: Joi.alternatives().conditional('type', {
      is: 'other',
      then: Joi.string().required(),
      otherwise: Joi.string().allow(null).optional(),
    }),
  }),
);

module.exports = {
  businessOnboard,
  professionalInfo,
  completeBusinessOnboard,
  getBusinesses,
  getBusiness,
  getBusinessByUsername,
  updateBusiness,
  addBusinessResource,
  updateBusinessResource,
  validateLanguages,
  validateProfessionalInfo,
  uploadAdditionalInfo,
  fetchVerifications,
  verifyBusiness,
  assignVerifier,
};
