const express = require('express');
// const Async = require('async') ;
const helmet = require('helmet');
const xss = require('xss-clean');
const path = require('path');
const http = require('http');
const socket = require('socket.io');
const mongoSanitize = require('express-mongo-sanitize');
const compression = require('compression');
const cors = require('cors');
const passport = require('passport');
const httpStatus = require('http-status');
const cookieParser = require('cookie-parser');
const swaggerUi = require('swagger-ui-express');
const config = require('./config/config');
const morgan = require('./config/morgan');
const { jwtStrategy } = require('./config/passport');
const { authLimiter } = require('./middlewares/rateLimiter');
const routes = require('./routes/v1');
const { errorConverter, errorHandler } = require('./middlewares/error');
const ApiError = require('./utils/ApiError');
const rootRoute = require('./routes/v1/root');
const credentials = require('./middlewares/credentials');
const corsOptions = require('./config/corsOptions');
const swaggerJsDocs = require('./middlewares/swaggerDocs');
const { sockets } = require('./services/sockets');
const migrations = require('./models/migrations');
const { startAgenda } = require('./utils/jobs');

const app = express();
const server = http.createServer(app);
const io = socket(server, { cors: corsOptions });

if (config.env !== 'test') {
  app.use(morgan.successHandler);
  app.use(morgan.errorHandler);
}

// set security HTTP headers
app.use(helmet());

// app.use(express.json());

app.use(cookieParser());

app.use(express.urlencoded({ extended: true }));

// sanitize request data
app.use(xss());
app.use(mongoSanitize());

// gzip compression
app.use(compression());

// Handle options credentials check - before CORS!
// and fetch cookies credentials requirement
app.use(credentials);

// Cross Origin Resource Sharing
app.use(cors(corsOptions));

// jwt authentication
app.use(passport.initialize());
passport.use('jwt', jwtStrategy);

// Enable trust proxy

// limit repeated failed requests to auth endpoints
if (config.env === 'production' && process.env.ENVIRON !== 'development') {
  app.set('trust proxy', true);
  app.use('/v1/auth', authLimiter);
  // app.set('trust proxy', 1); // Trust Azure's proxy
}

app.use('^/$|/index(.html)?', rootRoute);
// v1 api routes
if (config.env === 'development' || process.env.ENVIRON === 'development') {
  app.use('/v1/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerJsDocs));
}
app.use('/v1', routes);

// Socket connections
sockets(io);
// Socket connections
migrations();

// agendas
startAgenda();

app.all('*', (req, res) => {
  res.status(404);
  if (req.accepts('html')) {
    res.sendFile(path.join(__dirname, 'views', '404.html'));
  } else if (req.accepts('json')) {
    res.json({ message: '404 Not Found' });
  } else {
    res.type('txt').send('404 Not Found');
  }
});

// send back 404 error for any unknown api request
app.use((req, res, next) => {
  next(new ApiError(httpStatus.NOT_FOUND, 'Not found'));
});

// convert error to ApiError, if needed
app.use(errorConverter);

// handle error
app.use(errorHandler);

module.exports = server;
