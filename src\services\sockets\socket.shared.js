// const logger = require('../../config/logger');
const { Setting } = require('../../models');

const emit = async (message, recipientId, eventName, socketConnectionParam, broadcast = false) => {
  // eslint-disable-next-line global-require
  const socketConnection = socketConnectionParam || require('./index').socketConnection;
  const event = eventName || 'notification';
  if (broadcast) {
    socketConnection.io.emit(event, { message });
    // logger.info(`Broadcasting ${message.message}`);
    // logger.info(`Broadcasting ${message}`);
    return;
  }
  if (eventName === 'notification') {
    // eslint-disable-next-line global-require
    const unreadNotifications = await require('../notification.service').getNotifications({
      recipient: recipientId,
      read: false,
    });
    await emit(unreadNotifications.totalResults, recipientId, 'unread-notifications');
  }
  const recipientIds = Array.isArray(recipientId) ? recipientId : [recipientId];
  [...new Set(recipientIds)].forEach((id) => {
    if (socketConnection.connectedUsers[id]) {
      // eslint-disable-next-line no-console
      console.log('Length of active sessions', socketConnection.connectedUsers[id].length);
      socketConnection.connectedUsers[id].forEach((socket) => {
        // if (eventName === 'message-received') logger.info(`Emitting ${eventName} to ${recipientId}`);
        socket.emit(event, { message });
      });
      // eslint-disable-next-line no-console
      console.log(`${eventName} event in socket. Receiver: ${recipientId}`);
    }
  });
};

const checkNotificationSetting = async (userId, notificationType) => {
  const settings = await Setting.findOne({ user: userId });
  return settings[notificationType];
};

module.exports = {
  emit,
  checkNotificationSetting,
};
