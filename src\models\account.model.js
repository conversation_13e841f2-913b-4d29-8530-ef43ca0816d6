const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');

const accountSchema = new mongoose.Schema(
  {
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    business: { type: mongoose.Schema.Types.ObjectId, ref: 'Business' },
    spendingBalance: { type: Number, default: 0 }, // balance that can be spent. Applies to users (clients)
    allTimeDeposit: { type: Number, default: 0 }, // total amount ever deposited. Applies to users (clients)

    withdrawableBalance: { type: Number, default: 0 },
    totalRefund: { type: Number, default: 0 },
    totalBalance: { type: Number, default: 0 },
    allTimeWithdrawal: { type: Number, default: 0 },

    transactions: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Transaction' }],
  },
  {
    timestamps: true,
  },
);

accountSchema.index({ createdAt: 1, updatedAt: 1 });

accountSchema.plugin(toJSON);
accountSchema.plugin(paginate);

const Account = mongoose.model('Account', accountSchema);

// check whether user or business is present
accountSchema.pre('validate', function (next) {
  if (!this.user && !this.business) {
    next(new Error('User or Business is required'));
  }
  next();
});

module.exports = Account;
