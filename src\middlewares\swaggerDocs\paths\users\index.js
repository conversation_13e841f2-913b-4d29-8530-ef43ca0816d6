// const getUsers = require('./get.users.path');
const getUserProfile = require('./get.user.profile.path');
const getUser = require('./get.user.path');
const updateUser = require('./update.user.path');
const getMutuals = require('./get.mutuals.path');
const changeUserPassword = require('./change.password.user.path');
const getFollowsInfo = require('./get.follows.path');
const followAPerson = require('./follow.user.path');
const getFollowershipCount = require('./get.followership.count.path');
// const changeUserPhoto = require('./change.user.photo.path');

module.exports = {
  // '/users': { ...getUsers },
  '/users/{id}': { ...getUser, ...updateUser },
  '/users/profile/{id}': { ...getUserProfile },
  '/users/{id}/change-password': { ...changeUserPassword },
  '/users/{id}/follow': { ...getFollowsInfo, ...followAPerson },
  '/users/{id}/follows-count': { ...getFollowershipCount },
  '/users/mutuals': { ...getMutuals },
  // '/users/:id/update-profile-photo': { ...changeUserPhoto },
};
