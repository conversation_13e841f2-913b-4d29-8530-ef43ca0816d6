module.exports.accountService = require('./account.service');
module.exports.activityLogService = require('./activity.log.service');
module.exports.authService = require('./auth.service');
module.exports.botRecordService = require('./bot.record.service');
module.exports.blogPostService = require('./blog.post.service');
module.exports.businessService = require('./business.service');
module.exports.careerJobsService = require('./career.jobs.service');
module.exports.commentService = require('./comment.service');
module.exports.countryService = require('./country.service');
module.exports.emailService = require('./email.service');
module.exports.feedbackService = require('./feedback.service');
module.exports.forumPostService = require('./forum.post.service');
module.exports.forumReplyService = require('./forum.reply.service');
module.exports.groupService = require('./group.service');
module.exports.globalService = require('./global.service');
module.exports.messageService = require('./message.service');
module.exports.notificationService = require('./notification.service');
module.exports.orderService = require('./order.service');
module.exports.userService = require('./user.service');
module.exports.tokenService = require('./token.service');
module.exports.fileService = require('./file.service');
module.exports.postService = require('./post.service');
module.exports.universityService = require('./university.service');
module.exports.profileService = require('./profile.service');
module.exports.premiumSubscriberService = require('./premium.subscriber.service');
module.exports.resourceLibraryService = require('./resource.library.service');
module.exports.scholarshipService = require('./scholarship.service');
module.exports.serviceService = require('./service.service');
module.exports.settingService = require('./setting.service');
module.exports.schoolService = require('./school.service');
module.exports.serviceRevisionService = require('./service.revision.service');
module.exports.subscriberService = require('./subscriber.service');
module.exports.issueReportService = require('./issue.report.service');
module.exports.transactionService = require('./transaction.service');
module.exports.serviceVerificationService = require('./service.verification.service');

module.exports.azureFileService = require('./azure.file.service');
