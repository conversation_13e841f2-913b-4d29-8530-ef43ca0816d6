module.exports = {
  type: 'object',
  properties: {
    data: {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          example: '507f1f77bcf86cd799439011',
        },
        first_name: {
          type: 'string',
          example: '<PERSON>',
        },
        last_name: {
          type: 'string',
          example: '<PERSON><PERSON>',
        },
        username: {
          type: 'string',
          example: 'johndoe',
        },
        email: {
          type: 'string',
          example: '<EMAIL>',
        },
        nationality: {
          type: 'string',
          example: 'US',
        },
        phone: {
          type: 'string',
          example: '****** 456 7890',
        },
        gender: {
          type: 'string',
          example: 'Male',
        },
        country_of_residence: {
          type: 'string',
          example: 'US',
        },
        targeted_countries: {
          type: 'array',
          items: {
            type: 'string',
            example: 'Canada',
          },
        },
        recovery_email: {
          type: 'string',
          example: '<EMAIL>',
        },
        roles: {
          type: 'array',
          items: {
            type: 'string',
            example: 'student',
          },
        },
        isEmailVerified: {
          type: 'boolean',
          example: true,
        },
      },
    },
    message: {
      type: 'string',
      example: 'User record retrieved successfully',
    },
    status: {
      type: 'string',
      example: 'SUCCESS',
    },
  },
};
