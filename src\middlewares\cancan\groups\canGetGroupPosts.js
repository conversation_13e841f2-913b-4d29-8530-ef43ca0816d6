const httpStatus = require('http-status');
const { Group } = require('../../../models');
const ApiError = require('../../../utils/ApiError');
const validateId = require('../../../services/shared/validateId');

const canGetGroupPosts = () => async (req, res, next) => {
  let group;

  if (req.query.group) {
    try {
      validateId(req.query.group, 'Post');
    } catch (error) {
      return next(error);
    }

    group = await Group.findById(req.query.group);
    if (
      group &&
      !(
        String(group?.admin) === String(req.user?._id) ||
        group?.coAdmins.includes(req.user?._id) ||
        group?.members.includes(req.user?._id)
      )
    ) {
      return next(new ApiError(httpStatus.FORBIDDEN, 'Forbidden: You are not allowed to perform this operation'));
    }
  }

  req.group = group;
  next();
};

module.exports = canGetGroupPosts;
