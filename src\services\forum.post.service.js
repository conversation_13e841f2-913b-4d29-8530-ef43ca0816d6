const httpStatus = require('http-status');
const Async = require('async');
const stopword = require('stopword');
const ApiError = require('../utils/ApiError');
const { uploadAndSaveMultipleBlobs, deleteManyFiles } = require('./azure.file.service');
const { wordsArrayToFilterArray } = require('./university.service');
const { ForumPost, File } = require('../models');
const { azureContainers } = require('../config/constants');

const createForumPost = async (data, files) => {
  if (!data.text && !data.topic) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Forum Post must have text');
  }
  try {
    const fileIds = files ? await uploadAndSaveMultipleBlobs(files, azureContainers.forums) : [];

    const forumPostBody = { ...data, media: fileIds };
    const createdForumPost = await ForumPost.create(forumPostBody);

    return createdForumPost;
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error creating forum post');
  }
};

const getForumPosts = async (filterParam, options) => {
  const filter = filterParam;
  filter.$and = [];
  if (filter.searchText) {
    const filterObject = [];
    const searchText = stopword.removeStopwords(filter.searchText.split(' ')); // Remove stopwords e.g 'of', 'the', 'a', 'an' etc
    filterObject.push({ $and: wordsArrayToFilterArray(searchText, 'topic') });
    filterObject.push({ $and: wordsArrayToFilterArray(searchText, 'text') });

    delete filter.searchText;
    filter.$and.push({ $or: filterObject });
  }

  if (filter.$and.length === 0) {
    delete filter.$and;
  }
  const forumPosts = await ForumPost.paginate(filter, {
    ...options,
    populate: [{ path: 'media', select: 'url -_id' }],
    select: 'topic text media replies updatedAt createdAt id',
  });
  return forumPosts;
};

const getForumPostById = async (forumPostId) => {
  const forumPost = await ForumPost.findById(forumPostId).populate([
    { path: 'media', select: 'url -_id' },
    {
      path: 'user',
      select: 'firstName lastName middleName username photo _id',
      populate: { path: 'photo', select: 'url -_id' },
    },
    { path: 'replies', populate: { path: 'media', select: 'url -_id' } },
  ]);
  if (!forumPost) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Forum post record not found');
  }
  return forumPost;
};

const updateForumPost = async (req) => {
  const { files, resourceRecord, body: updateBody } = req;
  const forumPost = resourceRecord || (await ForumPost.findById(req.params.id));

  if (!forumPost) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Forum post record not found');
  }
  if (updateBody.deletedUrls) {
    await Async.eachOfSeries(updateBody.deletedUrls.split(','), async (url) => {
      // eslint-disable-next-line no-useless-escape
      const urlExtract = url.match(/^(https?:\/\/[^\/\s]+\/[^\?\s]*)/)[0]?.replace(/\/$/, '');
      const file = await File.findOne({ url: urlExtract });
      if (file) {
        forumPost.media = forumPost.media.filter((fileId) => fileId.toString() !== file._id.toString());
        await file.remove();
      }
    });
  }

  try {
    if (files && files.length > 0) {
      const fileIds = await uploadAndSaveMultipleBlobs(files, azureContainers.forums);
      updateBody.media = [...forumPost.media, ...fileIds];
    }

    Object.assign(forumPost, updateBody);
    await forumPost.save();
    return forumPost;
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error updating forum post');
  }
};

const deleteForumPost = async (postId) => {
  const forumPost = await ForumPost.findById(postId);
  if (!forumPost) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Forum post not found');
  }

  if (forumPost.media && forumPost.media.length > 0) {
    await deleteManyFiles(forumPost.media);
  }
  await forumPost.remove();
  return forumPost;
};

const searchForumPosts = async (req) => {
  const { topic, text } = req.query;

  const queryConditions = {};

  if (topic) {
    // search in topic and text fields
    queryConditions.$or = [{ topic: { $regex: topic, $options: 'i' } }, { text: { $regex: topic, $options: 'i' } }];
  }

  if (text) {
    queryConditions.text = { $regex: text, $options: 'i' };
  }

  // const matchingForumPosts = await ForumPost.find({ topic: { $regex: 'h', $options: 'i' } });
  const matchingForumPosts = await ForumPost.find(queryConditions);
  // const matchingForumPosts = await ForumPost.find({ $text: { $search: text } });
  return matchingForumPosts;
};

module.exports = {
  createForumPost,
  getForumPosts,
  getForumPostById,
  updateForumPost,
  deleteForumPost,
  searchForumPosts,
};
