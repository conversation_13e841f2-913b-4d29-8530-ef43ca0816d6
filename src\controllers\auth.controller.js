const bcrypt = require('bcryptjs');
const httpStatus = require('http-status');
const { inProductionMode } = require('../config/environments');
const catchAsync = require('../utils/catchAsync');
const ApiError = require('../utils/ApiError');
const { userService, authService, tokenService, subscriberService } = require('../services');
const { Token, User } = require('../models');
const { tokenTypes } = require('../config/tokens');
const { pick } = require('../utils/pick');
const logger = require('../config/logger');

// const mongodbQueryUserPropsToRemove = '-password -refreshToken -__v';

const signup = catchAsync(async (req, res) => {
  const { returnUrl } = req.query;
  const { password, referrerBusiness, service } = req.body;
  ['referrerBusiness', 'service'].forEach((prop) => delete req.body[prop]);

  const salt = await bcrypt.genSalt(10);
  const hashedPwd = await bcrypt.hash(password, salt);

  const user = await userService.createUser({ ...req.body, password: hashedPwd });
  let referral;
  if (referrerBusiness || service) {
    referral = (await authService.generateReferralRecord(user._id, referrerBusiness, service))._id;
  }
  await User.findByIdAndUpdate(user._id, { referral });

  await userService.sendVerificationEmail(user.email, user, req.body.signupAs, returnUrl);

  await subscriberService.addSubscriber({
    email: req.body.email,
    subscribedFor: 'newsletter',
    subscribed: req.body.subscribed === 'true',
  });

  // No access token is sent. User must verify email to get access token
  res.status(httpStatus.CREATED).json({
    status: 'SUCCESS',
    message: `Your account has been created. Kindly check your email for verification.`,
    returnUrl,
  });
});

const getSelectedUserInfo = (user) => {
  if (!user._doc) return user; // Not a mongoose document instance. Most likely selected fields

  const selectedFields = ['_id', 'business', 'registrationStatus', 'pendingBusiness', 'pendingStudent'];
  const selectedInfo = pick(user._doc, selectedFields);
  selectedInfo.id = selectedInfo._id;
  delete selectedInfo._id;
  return selectedInfo;
};

const login = catchAsync(async (req, res) => {
  const { email, password } = req.body;
  const user = await authService.loginUserWithEmailAndPassword(email, password, req.query.returnUrl);

  const tokens = await tokenService.generateAuthTokens(user, undefined);

  res.status(httpStatus.OK).json({
    status: 'SUCCESS',
    message: `Login successful`,
    data: { tokens, user: { ...getSelectedUserInfo(user) } },
  });
});

const sendVerificationEmail = catchAsync(async (req, res) => {
  await userService.sendVerificationEmail(req.body.email, undefined, req.body.signupAs, req.query.returnUrl);
  res.status(httpStatus.OK).send({ message: 'Kindly check your email to complete account verification', status: 'SUCCESS' });
});

const verifyEmail = catchAsync(async (req, res) => {
  const { token, email, signupAs } = req.query;
  const status = await authService.verifyEmail(email, signupAs, token);
  const user = await userService.getUserByEmail(req.query.email);

  const tokens = await tokenService.generateAuthTokens(user, undefined, req.body.signupAs);
  res.status(status).send({
    message: 'Account verification is successful.',
    status: 'SUCCESS',
    data: { tokens, user: { ...getSelectedUserInfo(user) } },
  });
});

// const verifyDuplicateSignupEmail = catchAsync(async (req, res) => {
//   const { email, signupAs, token } = req.query;
//   const data = await userService.verifyDuplicateSignupEmail(email, signupAs, token);

//   res.status(httpStatus.OK).json({ data, message: 'Signup Completed, proceed to log in', status: 'SUCCESS' });
// });

const forgotPassword = catchAsync(async (req, res) => {
  await authService.forgotPassword(req.body.email);
  res
    .status(httpStatus.OK)
    .send({ message: 'Kindly follow the instructions in your email to reset your password', status: 'SUCCESS' });
});

const resetPassword = catchAsync(async (req, res) => {
  if (req.body.password !== req.body.confirmPassword) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Passwords do not match');
  }
  await authService.resetPassword(req.body.token, req.body.password);
  res.status(httpStatus.OK).send({ message: 'Password reset successful', status: 'SUCCESS' });
});

const refresh = catchAsync(async (req, res) => {
  const { refreshToken } = req.query;
  res.clearCookie('jwt', { httpOnly: true, sameSite: 'None', secure: inProductionMode });

  try {
    const { tokenDoc: decoded } = await tokenService.verifyToken(refreshToken, tokenTypes.REFRESH);
    const user = await userService.getUserById(decoded.user);

    if (!decoded) return res.status(httpStatus.OK).json({ status: 'FAILED', message: 'Unauthorized', code: 417 });
    if (!user) return res.status(httpStatus.OK).json({ status: 'FAILED', message: 'Something went wrong', code: 417 });

    const { access } = await tokenService.generateAuthTokens(user, undefined, decoded.clientUserType);

    res.status(httpStatus.CREATED).json({
      status: 'SUCCESS',
      message: `Token refreshed`,
      data: { tokens: { access, refresh: refreshToken }, userId: user._id },
    });
  } catch (error) {
    // Do nothing
    logger.error(error);
    res.status(httpStatus.OK).json({ status: 'FAILED', message: 'Unauthorized', code: 417 });
  }
});

const logout = async (req, res) => {
  const { cookies } = req;
  if (!cookies?.jwt) return res.sendStatus(204); // No content
  const refreshToken = cookies.jwt;

  try {
    const { tokenDoc: decoded } = await tokenService.verifyToken(refreshToken, tokenTypes.REFRESH);
    const foundUser = await userService.getUserById(decoded.user);
    if (!foundUser) {
      res.clearCookie('jwt', { httpOnly: true, sameSite: 'None', secure: inProductionMode });
      return res.sendStatus(204);
    }

    await Token.deleteMany({ user: decoded.user });

    res.clearCookie('jwt', { httpOnly: true, sameSite: 'None', secure: inProductionMode });
    res.status(httpStatus.OK).json({ message: 'logout successfull', status: 'SUCCESS' });
  } catch (error) {
    // Do nothing
  }
};

const usernameEmailAvail = catchAsync(async (req, res) => {
  const { email, username } = req.body;
  const response = await userService.usernameEmailAvail(email, username);

  res.status(httpStatus.OK).json({ data: response, message: 'Check successful', status: 'SUCCESS' });
});

const getGoogleOauthURL = catchAsync(async (req, res) => {
  const url = authService.getGoogleOauthURL();
  res.json({ url });
});

const getLinkedInOauthURL = catchAsync(async (req, res) => {
  const url = authService.getLinkedInOauthURL();
  res.json({ url });
});

const googleOauth = catchAsync(async (req, res) => {
  const { code, subscribed, signupAs, referrerBusiness, service } = req.query;
  const user = await authService.googleOauth(code, subscribed === 'true', signupAs, { referrerBusiness, service });
  const tokens = await tokenService.generateAuthTokens(user);

  res.status(httpStatus.OK).json({
    status: 'SUCCESS',
    message: `Login successful`,
    data: { tokens, user },
  });
});

const linkedInOauth = catchAsync(async (req, res) => {
  const { code, subscribed, signupAs, referrerBusiness, service } = req.query;
  const user = await authService.linkedInOauth(code, subscribed === 'true', signupAs, { referrerBusiness, service });
  const tokens = await tokenService.generateAuthTokens(user);

  res.status(httpStatus.OK).json({
    status: 'SUCCESS',
    message: `Login successful`,
    data: { tokens, user },
  });
});

const verifyAccess = catchAsync(async (req, res) => {
  // Tell whether the access token or refresh token is valid
  const { accessToken, refreshToken } = req.query;
  try {
    const { tokenDoc: decodedAccessToken } = await tokenService.verifyToken(accessToken, tokenTypes.ACCESS);
    if (decodedAccessToken)
      return res
        .status(httpStatus.OK)
        .json({ status: 'SUCCESS', message: `Token verification successful`, data: { access: 'valid' } });
  } catch (error) {
    // Do nothing
  }
  try {
    const { tokenDoc: decodedRefreshToken } = await tokenService.verifyToken(refreshToken, tokenTypes.REFRESH);
    if (decodedRefreshToken)
      return res
        .status(httpStatus.OK)
        .json({ status: 'SUCCESS', message: `Token verification successful`, data: { refresh: 'valid' } });
  } catch (error) {
    return res
      .status(httpStatus.FORBIDDEN)
      .json({ status: 'FAILED', message: 'Invalid token', data: { access: 'invalid', refresh: 'invalid' } });
  }
});

const sendOTP = catchAsync(async (req, res) => {
  const { phone, channel } = req.body;
  const data = await authService.sendOTP(phone, channel);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'OTP sent successfully', data });
});

const verifyOTP = catchAsync(async (req, res) => {
  const { phone, otp } = req.body;
  const data = await authService.verifyOTP(phone, otp, 'sms');
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'OTP verification successful', data });
});

const createIdVerification = catchAsync(async () => {
  // Using persona
  // const { user } = req;
  // const data = await authService.createIdVerification(user);
});

module.exports = {
  signup,
  login,
  sendVerificationEmail,
  forgotPassword,
  verifyEmail,
  // verifyDuplicateSignupEmail,
  refresh,
  logout,
  resetPassword,
  usernameEmailAvail,
  getGoogleOauthURL,
  googleOauth,
  getLinkedInOauthURL,
  linkedInOauth,
  verifyAccess,
  sendOTP,
  verifyOTP,
  createIdVerification,
  getSelectedUserInfo,
};
