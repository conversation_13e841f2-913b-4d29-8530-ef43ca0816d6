const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');

const { ObjectId } = mongoose.SchemaTypes;

/*
  Schema for providing service requirements
*/
const serviceRequirementProvideSchema = new mongoose.Schema(
  {
    order: { type: ObjectId, ref: 'Order', required: true },
    requirement: { type: ObjectId, ref: 'ServiceRequirementSpecify' },
    defaultRequirementId: { type: Number },

    files: [{ type: ObjectId, ref: 'File' }],
    choices: [{ type: String, trim: true }],
    text: { type: String, trim: true },
    // date: { type: Date },
    // number: { type: Number },
  },
  {
    timestamps: true,
  },
);

serviceRequirementProvideSchema.index({ name: 1 });

serviceRequirementProvideSchema.plugin(toJSON);
serviceRequirementProvideSchema.plugin(paginate);

const ServiceRequirementProvide = mongoose.model('ServiceRequirementProvide', serviceRequirementProvideSchema);

module.exports = ServiceRequirementProvide;
