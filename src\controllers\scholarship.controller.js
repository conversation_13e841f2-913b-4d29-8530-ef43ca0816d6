const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { scholarshipService } = require('../services');
const { pick, removeUndefinedKeys } = require('../utils/pick');
const { schoolsEnums } = require('../config/constants');
const { Scholarship } = require('../models');

const getScholarshipEnums = catchAsync(async (req, res) => {
  const programs = (
    await Scholarship.aggregate([{ $unwind: '$programs' }, { $group: { _id: '$programs' } }, { $sort: { _id: 1 } }])
  ).map((program) => program._id);

  if (!programs.find((program) => program.toLowerCase() === 'all programs')) {
    programs.unshift('All Programs');
  }

  const data = Object.keys(schoolsEnums).reduce((acc, key) => ({ ...acc, [key]: Object.values(schoolsEnums[key]) }), {});
  return res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data: { ...data, programs }, message: 'Scholarship enums fetched successfully' });
});

const getScholarships = catchAsync(async (req, res) => {
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const filter = pick(req.query, [
    'name',
    'deadline',
    'hostCountry',
    'fundingType',
    'program',
    'programType',
    'status',
    'tab',
  ]);

  removeUndefinedKeys(filter);
  const scholarships = await scholarshipService.getScholarships(req.user, filter, options);
  return res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data: scholarships, message: 'Scholarships fetched successfully' });
});

const getScholarship = catchAsync(async (req, res) => {
  const scholarship = await scholarshipService.getScholarship(req.params.id);
  return res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data: scholarship, message: 'Scholarship fetched successfully' });
});

const getScholarshipByUniqueName = catchAsync(async (req, res) => {
  const scholarship = await scholarshipService.getScholarshipByUniqueName(req.params.uniqueName);
  return res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data: scholarship, message: 'Scholarship fetched successfully' });
});

const addScholarship = catchAsync(async (req, res) => {
  const scholarship = await scholarshipService.addScholarship(req.body, req.file);
  return res.status(httpStatus.OK).json({ status: 'SUCCESS', data: scholarship, message: 'Scholarship added successfully' });
});

const updateScholarship = catchAsync(async (req, res) => {
  await scholarshipService.updateScholarship(req.params.id, req.body, req.file);
  return res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Scholarship updated successfully' });
});

const deleteScholarship = catchAsync(async (req, res) => {
  await scholarshipService.deleteScholarship(req.params.id);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Scholarship deleted successfully' });
});

const bookmarkScholarship = catchAsync(async (req, res) => {
  const bookmarkStatus = await scholarshipService.bookmarkScholarship(req.params.id, req.user);
  const message = bookmarkStatus ? 'bookmarked' : 'removed from bookmarks';
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: `Scholarship ${message}` });
});

const getBookmarkedScholarships = catchAsync(async (req, res) => {
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const filter = pick(req.query, ['status']);

  removeUndefinedKeys(filter);
  const scholarships = await scholarshipService.getBookmarkedScholarships(req.user, filter, options);
  return res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data: scholarships, message: 'Scholarships fetched successfully' });
});

module.exports = {
  getScholarshipEnums,
  getScholarships,
  getScholarship,
  addScholarship,
  updateScholarship,
  deleteScholarship,
  bookmarkScholarship,
  getBookmarkedScholarships,
  getScholarshipByUniqueName,
};
