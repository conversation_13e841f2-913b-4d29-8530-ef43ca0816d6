const Joi = require('joi');
const { fileSchema, documentMimeTypes, imageVideoMimeTypes } = require('./file.validation');
const { businessEnums } = require('../config/constants');
const { validateRequirementProvide } = require('./service.validation');

const createOrder = {
  body: Joi.object().keys({
    service: Joi.string().required(),
    plan: Joi.string()
      .valid(...Object.values(businessEnums.servicePlans))
      .required(),
  }),
};

const completeOrderBody = Joi.object()
  .keys({
    requirements: Joi.array().items(validateRequirementProvide).unique('requirement'),
    acceptDisclaimer: Joi.string().valid('true', 'false').required(),
    fileRequirements: Joi.array()
      .items(
        Joi.object().keys({
          id: Joi.string().required(),
          count: Joi.number().integer().default(1),
        }),
      )
      .unique('id'),
  })
  .custom((value, helpers) => {
    const allRequirements = [
      ...(value.requirements || []).map((requirement) => requirement.requirement || []),
      ...(value.fileRequirements || []).map((r) => r.id),
    ];
    if (allRequirements.length !== new Set(allRequirements).size) {
      return helpers.message('Requirement IDs must be unique');
    }
    return value;
  });

const completeCreateOrder = {
  params: Joi.object().keys({
    sessionId: Joi.string().required(),
  }),
  body: Joi.object().keys({
    requirements: Joi.string(),
    acceptDisclaimer: Joi.string().required(),
    fileRequirements: Joi.string(),
  }),
  files: Joi.array().items(fileSchema(5, documentMimeTypes)).max(12), // When the max is modified, it should also be modified from the route's middleware
};

const getOrder = {
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
  query: Joi.object().keys({
    provider: Joi.string(),
  }),
};

const getOrders = {
  query: Joi.object().keys({
    provider: Joi.string(),
    status: Joi.string().valid(...Object.values(businessEnums.orderStatuses)),
    sortBy: Joi.string().valid(
      'createdAt:desc',
      'createdAt:asc',
      'status:asc',
      'status:desc',
      'updatedAt:asc',
      'updatedAt:desc',
    ),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
  }),
};

const fulfillOrder = {
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
  body: {
    comment: Joi.string(),
  },
  files: Joi.object().keys({
    deliverables: Joi.array()
      .items(fileSchema(10, [...imageVideoMimeTypes, ...documentMimeTypes]))
      .max(5),
  }),
};

const cancelOrder = {
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
  body: Joi.object().keys({
    reason: Joi.string().required(),
  }),
};

const createServiceRevision = {
  params: Joi.object().keys({
    orderId: Joi.string().required(), // order ID
  }),
  body: Joi.object().keys({
    description: Joi.string().required(),
  }),
  files: Joi.object().keys({
    supportingDocuments: Joi.array().items(fileSchema(1, documentMimeTypes)).max(5),
  }),
};

const getServiceRevision = {
  params: Joi.object().keys({
    revisionId: Joi.string().required(),
  }),
};

const getServiceRevisions = {
  params: Joi.object().keys({
    id: Joi.string().required(), // order ID
  }),
  query: Joi.object().keys({
    sortBy: Joi.string().valid('createdAt:desc', 'createdAt:asc'),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
  }),
};

const fulfillServiceRevision = {
  params: Joi.object().keys({
    revisionId: Joi.string().required(), // revision ID
  }),
  body: Joi.object().keys({
    comment: Joi.string(),
  }),
  files: Joi.object().keys({
    deliverables: Joi.array().items(fileSchema(10, imageVideoMimeTypes)).max(5),
  }),
};

const updateServiceRevision = {
  params: Joi.object().keys({
    revisionId: Joi.string().required(), // revision ID
  }),
};

const getOrderActivities = {
  query: Joi.object().keys({
    order: Joi.string().required(),
    sortBy: Joi.string().valid('createdAt:desc', 'createdAt:asc'),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
  }),
};

const reportDispute = {
  params: Joi.object().keys({
    id: Joi.string().required(),
  }),
  body: Joi.object().keys({
    reason: Joi.string(),
  }),
  files: Joi.array().items(fileSchema(1, documentMimeTypes)).max(5),
};

const getDisputes = {
  query: Joi.object().keys({
    order: Joi.string(),
    reporter: Joi.string(),
    provider: Joi.string(),
    service: Joi.string(),
    status: Joi.string().valid(...Object.values(businessEnums.orderDisputeStatuses)),

    sortBy: Joi.string().valid('createdAt:desc', 'createdAt:asc'),
    limit: Joi.number().integer().default(10),
    page: Joi.number().integer().default(1),
  }),
};

const requestExtraTime = {
  params: Joi.object().keys({
    id: Joi.string().required(), // orderId
  }),
  body: Joi.object().keys({
    extensionDays: Joi.number().integer().required().min(1).max(7),
  }),
};

const approveExtraTime = {
  params: Joi.object().keys({
    id: Joi.string().required(), // orderId
  }),
  body: Joi.object().keys({
    approved: Joi.boolean().required(),
    extensionDays: Joi.number().integer().required().min(1).max(7),
  }),
};

module.exports = {
  createOrder,
  completeCreateOrder,
  completeOrderBody,
  getOrders,
  getOrder,
  fulfillOrder,
  cancelOrder,
  reportDispute,
  getDisputes,

  createServiceRevision,
  getServiceRevisions,
  getServiceRevision,
  fulfillServiceRevision,
  updateServiceRevision,

  getOrderActivities,
  requestExtraTime,
  approveExtraTime,
};
