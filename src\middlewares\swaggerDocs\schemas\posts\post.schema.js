module.exports = {
  type: 'object',
  properties: {
    text: {
      type: 'string',
      description: 'Text content of the post',
      example: 'This is a new post.',
    },
    media: {
      type: 'array',
      items: {
        type: 'string',
        example: '507f1f77bcf86cd799439011',
      },
      description: 'Array of file IDs for post (optional)',
    },
    comments: {
      type: 'array',
      items: {
        type: 'string',
        example: '507f1f77bcf86cd799439011',
      },
      description: 'Array of comment IDs for post (optional)',
    },
    reposts: {
      type: 'array',
      items: {
        type: 'string',
        example: '507f1f77bcf86cd799439011',
      },
      description: 'Array of user IDs for reposts (optional)',
    },
  },
};
