const projectSchemaSwagger = {
  type: 'object',
  properties: {
    id: { type: 'string', example: '6009c0eee65f6dce28fb3e50' },
    projectName: {
      type: 'string',
      description: 'Name of the project',
      example: 'Online Marketplace App',
    },
    description: {
      type: 'string',
      description: 'Description of the project',
      example: 'Developed a web-based platform for buying and selling products',
    },
    start: {
      type: 'string',
      description: 'Start date of the project',
      format: 'date',
      example: '2022-01-15',
    },
    end: {
      type: 'string',
      description: 'End date of the project (if completed)',
      format: 'date',
      example: '2022-06-30',
    },
    projectLink: {
      type: 'string',
      description: 'Link or URL related to the project',
      example: 'https://example.com/project',
    },
  },
};

module.exports = projectSchemaSwagger;
