const Async = require('async');
const GlobalVariable = require('../global.variable.model');
const User = require('../user.model');
const logger = require('../../config/logger');
const Subscriber = require('../subscriber.model');
// const { subscriberService } = require('../../services');

const addUsersToSubscribers = async () => {
  const addedSubscribers = await GlobalVariable.findOne({ name: 'add users to subscribers' });
  if (addedSubscribers) {
    return;
  }

  await Async.eachSeries(await User.find(), async (user) => {
    // eslint-disable-next-line global-require
    const { subscriberService } = require('../../services');
    const subscriberExists = await Subscriber.findOne({ email: user.email, subscribedFor: 'newsletter' });
    if (subscriberExists) {
      subscriberExists.subscribed = true;
      await subscriberExists.save();
    }
    await subscriberService.addSubscriber({ email: user.email, subscribedFor: 'newsletter' });
  });

  await GlobalVariable.create({ name: 'add users to subscribers', value: 'true' });
  logger.info('Added users to subscribers');
};

module.exports = addUsersToSubscribers;
