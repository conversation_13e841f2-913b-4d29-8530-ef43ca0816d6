const httpStatus = require('http-status');
const stopword = require('stopword');
const ApiError = require('../utils/ApiError');
const { ResourceLibrary } = require('../models');
const { azureContainers, resourceLibraryCategories, resourceLibraryCategoriesDescriptions } = require('../config/constants');
const { processFileUpload, deleteManyFiles } = require('./azure.file.service');
const validateId = require('./shared/validateId');

const selectedFields = 'title description categories tags file thumbnails programs programTypes createdAt updatedAt';

const addResource = async (resourceBodyParam, resourceFiles) => {
  const resourceBody = { ...resourceBodyParam };
  const { file: resourceFile, thumbnails } = resourceFiles;

  const { categories, tags } = resourceBody;
  if (categories) {
    resourceBody.categories = Array.isArray(categories) ? categories : [categories];
  }

  if (tags) {
    resourceBody.tags = Array.isArray(tags) ? tags : [tags];
  }

  ['programs', 'programTypes'].forEach((field) => {
    if (resourceBody[field]) {
      resourceBody[field] = Array.isArray(resourceBody[field]) ? resourceBody[field] : [resourceBody[field]];
    }
  });

  const data = { ...resourceBody };
  if (resourceFile[0]) {
    data.file = await processFileUpload(resourceFile[0], azureContainers.resourceLibrary);
  }

  if (thumbnails) {
    data.thumbnails = await Promise.all(
      thumbnails.map((thumbnail) => processFileUpload(thumbnail, azureContainers.resourceLibrary)),
    );
  }
  await ResourceLibrary.create(data);
};

const getResource = async (resourceId, isAdmin) => {
  validateId(resourceId, 'Resource');
  const resource = await ResourceLibrary.findById(resourceId)
    .populate([
      { path: 'file', select: 'url _id' },
      { path: 'thumbnails', select: 'url _id' },
    ])
    .select(isAdmin ? `${selectedFields} downloadsCount viewsCount` : selectedFields);
  if (!resource) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Resource not found');
  }
  return resource;
};

const getResources = async (filterParam, options, isAdmin) => {
  const filter = { ...filterParam };

  if (filter.searchText) {
    const searchText = stopword.removeStopwords(filter.searchText.split(' '));
    if (searchText.length > 0) {
      filter.$or = [
        ...searchText.map((item) => ({ title: { $regex: item, $options: 'i' } })),
        // eslint-disable-next-line security/detect-non-literal-regexp
        { tags: { $in: searchText.map((item) => new RegExp(item, 'i')) } }, // Match tags with regex
      ];
    }
    delete filter.searchText;
  }

  // if (filter.tags) {
  //   const tags = Array.isArray(filter.tags) ? filter.tags : [filter.tags];
  //   // eslint-disable-next-line security/detect-non-literal-regexp
  //   filter.tags = { $in: tags.map((tag) => new RegExp(tag, 'i')) };
  // }

  if (filter.categories) {
    const categories = Array.isArray(filter.categories) ? filter.categories : [filter.categories];
    filter.categories = { $in: categories };
  }

  if (filter.programType) {
    const programTypes = Array.isArray(filter.programType)
      ? filter.programType
      : filter.programType.split(',').map((programType) => programType.trim());
    filter.programTypes = { $in: programTypes };
    delete filter.programType;
  }

  if (filter.program) {
    const programs = Array.isArray(filter.program) ? filter.program : [filter.program];
    if (!programs.includes('All Programs')) {
      filter.programs = { $in: programs };
    }
    delete filter.program;
  }

  const resources = await ResourceLibrary.paginate(filter, {
    ...options,
    populate: [
      { path: 'file', select: 'url _id filename' },
      { path: 'thumbnails', select: 'url _id filename' },
    ],
    select: isAdmin ? `${selectedFields} downloadsCount viewsCount` : selectedFields,
  });
  return resources;
};

const updateResource = async (resourceId, updateBodyParam, resourceFiles) => {
  validateId(resourceId, 'Resource');

  const updateBody = { ...updateBodyParam };
  const { file: resourceFile, thumbnails } = resourceFiles;

  const { categories, tags } = updateBody;
  if (categories) {
    updateBody.categories = Array.isArray(categories) ? categories : [categories];
  }

  if (tags) {
    updateBody.tags = Array.isArray(tags) ? tags : [tags];
  }

  ['programs', 'programTypes'].forEach((field) => {
    if (updateBody[field]) {
      updateBody[field] = Array.isArray(updateBody[field]) ? updateBody[field] : [updateBody[field]];
    }
  });

  const resource = await ResourceLibrary.findById(resourceId);
  if (!resource) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Resource not found');
  }

  if (resourceFile && resourceFile[0]) {
    updateBody.file = await processFileUpload(resourceFile[0], azureContainers.resourceLibrary);
    if (resource.file) deleteManyFiles([resource.file]);
  }

  if (thumbnails) {
    updateBody.thumbnails = await Promise.all(
      thumbnails.map((thumbnail) => processFileUpload(thumbnail, azureContainers.resourceLibrary)),
    );
    if (resource.thumbnails) deleteManyFiles(resource.thumbnails);
  }

  await ResourceLibrary.updateOne({ _id: resourceId }, updateBody);
};

const deleteResource = async (resourceId) => {
  validateId(resourceId, 'Resource');

  const resource = await ResourceLibrary.findById(resourceId);
  if (!resource) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Resource not found');
  }

  if (resource.file) deleteManyFiles([resource.file]); // don't await
  await ResourceLibrary.deleteOne({ _id: resourceId });
};

const saveDownloads = async (resourceId, user, action) => {
  const resource = await ResourceLibrary.findById(resourceId);
  if (!resource) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Resource not found');
  }

  let incrementField = {};
  if (action === 'download') incrementField = { downloadsCount: 1 };
  else if (action === 'view') incrementField = { viewsCount: 1 };

  await ResourceLibrary.updateOne({ _id: resourceId }, { $inc: incrementField });
};

const getResourcesAnalytics = async (options) => {
  const { sortBy, limit, page } = options;
  const [sortField, sortOrder] = sortBy.split(':');
  const sort = { [sortField]: sortOrder === 'asc' ? 1 : -1 };

  const pipeline = [
    {
      $project: {
        title: 1,
        dateAdded: '$createdAt',
        pageViews: '$viewsCount',
        downloads: '$downloadsCount',
      },
    },
    { $sort: sort },
    { $skip: (page - 1) * limit },
    { $limit: parseInt(limit, 10) },
  ];

  const totalPipeline = [
    {
      $count: 'total',
    },
  ];

  const [analytics, totalResult] = await Promise.all([
    ResourceLibrary.aggregate(pipeline),
    ResourceLibrary.aggregate(totalPipeline),
  ]);

  const total = totalResult.length > 0 ? totalResult[0].total : 0;

  return {
    analytics,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
  };
};

const getResourceCategoriesData = async () => {
  const pipeline = [
    { $unwind: '$categories' },
    {
      $group: {
        _id: '$categories',
        templatesCount: { $sum: 1 },
      },
    },
    {
      $project: {
        category: '$_id',
        templatesCount: 1,
        _id: 0,
      },
    },
  ];

  const categoriesData = await ResourceLibrary.aggregate(pipeline);

  return categoriesData.map(({ category, templatesCount }) => ({
    category: resourceLibraryCategories.includes(category) ? category : 'Unknown',
    description: resourceLibraryCategoriesDescriptions[category] || 'No description available',
    templatesCount,
  }));
};

module.exports = {
  addResource,
  getResource,
  getResources,
  updateResource,
  deleteResource,
  saveDownloads,
  getResourcesAnalytics,
  getResourceCategoriesData,
};
