const httpStatus = require('http-status');
const { Group } = require('../../../models');
const ApiError = require('../../../utils/ApiError');
const validateId = require('../../../services/shared/validateId');

const canCreateGroupPost = () => async (req, res, next) => {
  // 9d56
  let group;
  if (req.query.group === 'true') {
    if (!req.body.group) {
      return next(new ApiError(httpStatus.BAD_REQUEST, 'Group ID is required'));
    }
    try {
      validateId(req.body.group, 'Group');
    } catch (error) {
      return next(error);
    }
    group = await Group.findById(req.body.group);
    if (!group) {
      return next(new ApiError(httpStatus.NOT_FOUND, `Group not found`));
    }
    if (
      !(
        String(group.admin) === String(req.user._id) ||
        group.coAdmins.includes(req.user._id) ||
        group.members.includes(req.user._id)
      )
    ) {
      return next(new ApiError(httpStatus.FORBIDDEN, 'Forbidden: You are not allowed to perform this operation'));
    }
  } else if (req.body.group) {
    return next(new ApiError(httpStatus.BAD_REQUEST, 'group is not allowed'));
  }

  req.group = group;
  next();
};

module.exports = canCreateGroupPost;
