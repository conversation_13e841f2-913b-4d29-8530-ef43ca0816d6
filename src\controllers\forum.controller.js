const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { forumPostService: ForumPostService, forumReplyService: ForumReplyService } = require('../services');
const { pick, removeUndefinedKeys } = require('../utils/pick');

const createForumPost = catchAsync(async (req, res) => {
  await ForumPostService.createForumPost({ ...req.body, user: req.user._id }, req.files);
  res.status(httpStatus.CREATED).json({ status: 'SUCCESS', message: 'Forum post created successfully' });
});

const getForumPosts = catchAsync(async (req, res) => {
  const filter = pick({ ...req.query, user: req.query.userId }, ['user', 'text', 'tags', 'category', 'searchText']);
  if (filter.category && filter.category.toLowerCase() === 'all') {
    filter.category = undefined;
  }
  if (filter.tags) {
    const tagsFilter = Array.isArray(filter.tags) ? filter.tags : [filter.tags];
    filter.tags = { $all: tagsFilter };
  }
  removeUndefinedKeys(filter);

  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const forumPosts = await ForumPostService.getForumPosts(filter, options);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: forumPosts, message: 'Forum posts retrieved successfully' });
});

const getForumPostById = catchAsync(async (req, res) => {
  const forumPost = await ForumPostService.getForumPostById(req?.params?.id);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: forumPost, message: 'Forum post retrieved successfully' });
});

const updateForumPost = catchAsync(async (req, res) => {
  await ForumPostService.updateForumPost(req);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Forum Post updated successfully' });
});

const deleteForumPost = catchAsync(async (req, res) => {
  const forumPostId = req?.params?.id;
  const deletedPost = await ForumPostService.deleteForumPost(forumPostId);
  res.status(httpStatus.OK).json({ message: `forum post with ID ${deletedPost._id} deleted`, status: 'SUCCESS' });
});

const searchForumPosts = catchAsync(async (req, res) => {
  const matchingForumPosts = ForumPostService.searchForumPosts(req);
  res
    .status(httpStatus.OK)
    .json({ status: 'SUCCESS', data: matchingForumPosts, message: 'Forum posts retrieved successfully' });
});

// forum  replies
const createForumReply = catchAsync(async (req, res) => {
  const forumPostId = req?.params?.id; // forum post id
  await ForumReplyService.createForumReply({ ...req.body, user: req.user._id, forumPost: forumPostId }, req.files);
  res.status(httpStatus.CREATED).json({ status: 'SUCCESS', message: 'Forum post reply created successfully' });
});

const getReplies = catchAsync(async (req, res) => {
  const forumPost = req?.params?.id;
  const { userId: user } = req.query;
  const filter = pick({ ...req.query, user, forumPost }, ['user', 'forumPost']);
  removeUndefinedKeys(filter);

  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const replies = await ForumReplyService.queryReplies(filter, options);
  res.status(httpStatus.OK).json({ data: replies, message: 'Replies retrieved successfully', status: 'SUCCESS' });
});

const getReplyById = catchAsync(async (req, res) => {
  const { id: forumReplyId } = req.params;
  const reply = await ForumReplyService.getReplyById(forumReplyId);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: reply, message: 'Reply retrieved successfully' });
});

const updateForumReply = catchAsync(async (req, res) => {
  await ForumReplyService.updateForumReply(req);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', message: 'Reply updated successfully' });
});

const voteForumReply = catchAsync(async (req, res) => {
  const forumReply = await ForumReplyService.voteAReply(req.body, req?.params?.id, req.user._id);
  res.status(httpStatus.OK).json({ status: 'SUCCESS', data: forumReply, message: 'Reply updated successfully' });
});

const deleteForumReply = catchAsync(async (req, res) => {
  const { forumPostId, id: forumReplyId } = req.params;
  const deletedReply = await ForumReplyService.deleteForumReply(forumPostId, forumReplyId);
  res.status(httpStatus.OK).json({ message: `Reply with ID ${deletedReply._id} deleted`, status: 'SUCCESS' });
});

module.exports = {
  createForumPost,
  getForumPosts,
  getForumPostById,
  updateForumPost,
  deleteForumPost,
  searchForumPosts,
  createForumReply,
  getReplies,
  getReplyById,
  updateForumReply,
  voteForumReply,
  deleteForumReply,
};
