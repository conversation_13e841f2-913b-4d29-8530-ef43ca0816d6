const Async = require('async');
const { v4: uuidv4 } = require('uuid');
const logger = require('../../config/logger');
const Transaction = require('../transaction.model');
const GlobalVariable = require('../global.variable.model');
const { businessEnums } = require('../../config/constants');

const varName = 'Add comment to transactions';

const addCommentToTransaction = async () => {
  const migrated = await GlobalVariable.findOne({ name: varName });

  if (migrated) {
    return;
  }

  const transactions = await Transaction.find();

  await Async.eachOfSeries(transactions, async (transactionParam) => {
    const transaction = transactionParam;
    if (transaction.type === businessEnums.transactionTypes.CREDIT_DEBIT) {
      transaction.comment = businessEnums.transactionComments.ORDER_PLACEMENT;
    } else if (transaction.type === businessEnums.transactionTypes.DEBIT) {
      transaction.comment = businessEnums.transactionComments.WITHDRAWAL;
    } else if (transaction.type === businessEnums.transactionTypes.REFUND) {
      transaction.comment = businessEnums.transactionComments.ORDER_REFUND;
    }

    transaction.reference = uuidv4();
    await transaction.save();
  });

  await GlobalVariable.create({ name: varName, value: 'true' });
  logger.info('Added comment to transactions');
};

module.exports = addCommentToTransaction;
