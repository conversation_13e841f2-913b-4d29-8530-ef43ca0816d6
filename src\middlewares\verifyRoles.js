const verifyRoles = (...allowedRoles) => {
  return (req, res, next) => {
    if (!req?.roles) return res.status(401).json({ message: 'Unauthorized: roles required in request' }); // Unauthorized
    const rolesArray = [...allowedRoles];
    const result = Object.values(req?.roles || {})
      .map((role) => rolesArray.includes(role))
      .find((val) => val === true);
    if (!result) return res.status(401).json({ message: "Unauthorized: client role isn't authorized for this resource" });
    next();
  };
};

module.exports = verifyRoles;
