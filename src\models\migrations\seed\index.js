const Async = require('async');
const { SchoolProgramType } = require('../../index');
const logger = require('../../../config/logger');
const { businessEnums } = require('../../../config/constants');

const defaultServiceRequirements = [
  {
    question: 'If your order is on behalf of a company, what is your industry?',
    type: businessEnums.serviceRequirementTypes.TEXT,
    optional: true,
    id: 1,
    plansIncluded: Object.values(businessEnums.servicePlans),
  },
  {
    question: 'Is this order part of a bigger project?',
    type: businessEnums.serviceRequirementTypes.SELECT,
    optional: true,
    options: ['Yes', 'No'],
    id: 2,
    plansIncluded: Object.values(businessEnums.servicePlans),
  },
];

const createDBSeed = async () => {
  const seedData = [
    {
      model: SchoolProgramType,
      data: [
        {
          name: 'Bachelor',
          abbreviation: 'BSc',
          description: 'Bachelor of Science',
        },
        {
          name: 'Master',
          abbreviation: 'MSc',
          description: 'Master of Science',
        },
        {
          name: 'Doctorate',
          abbreviation: 'PhD',
          description: 'Doctorate of Philosophy',
        },
      ],
      uniqueField: 'name',
    },
  ];

  await Async.eachOfSeries(seedData, async (seed) => {
    await Async.eachOfSeries(seed.data, async (data) => {
      const model = await seed.model.findOne({ [seed.uniqueField]: data[seed.uniqueField] });

      if (!model) {
        await seed.model.create(data);
        logger.info(`${seed.model.modelName} created: ${data[seed.uniqueField]}`);
      }
    });
  });

  logger.info('DB seed created');
};

module.exports = { createDBSeed, defaultServiceRequirements };
