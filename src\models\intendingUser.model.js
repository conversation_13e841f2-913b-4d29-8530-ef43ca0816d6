const mongoose = require('mongoose');
const validator = require('validator');
const { toJSON, paginate } = require('./plugins');

const intendingUserSchema = new mongoose.Schema(
  {
    email: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      lowercase: true,
      validate(value) {
        if (!validator.isEmail(value)) {
          throw new Error('Invalid email');
        }
      },
    },
    waitFor: { type: String, enum: ['counselors'] },
    subscribed: { type: Boolean, default: true },
  },
  {
    timestamps: true,
  },
);

intendingUserSchema.plugin(toJSON);
intendingUserSchema.plugin(paginate);

const IntendingUser = mongoose.model('IntendingUser', intendingUserSchema);

module.exports = IntendingUser;
