const mongoose = require('mongoose');

const { toJSON, paginate } = require('../plugins');
const { businessEnums } = require('../../config/constants');

const { ObjectId } = mongoose.SchemaTypes;

const premiumSubscriberSchema = new mongoose.Schema(
  {
    user: { type: ObjectId, ref: 'User' },
    plan: { type: String, enum: Object.values(businessEnums.premiumSubscriptionPlans).map((p) => p.name), required: true },
    // autoRenew: { type: Boolean, default: true },
    subscriptions: [{ type: ObjectId, ref: 'PremiumSubscription' }],
    cancelled: { type: Boolean, default: false },
    cancelledAt: { type: Date }, // On cancelling a subscription, the cancelledAt of the subscriber and that of the active Subscription will be set
  },
  {
    timestamps: true,
  },
);

premiumSubscriberSchema.index({ createdAt: 1, updatedAt: 1 });

premiumSubscriberSchema.plugin(toJSON);
premiumSubscriberSchema.plugin(paginate);

const Subcriber = mongoose.model('PremiumSubscriber', premiumSubscriberSchema);

module.exports = Subcriber;
