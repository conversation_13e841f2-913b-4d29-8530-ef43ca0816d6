const express = require('express');

const { activityLogValidation } = require('../../validations');
const validate = require('../../middlewares/validate');

const router = express.Router();
const { auth } = require('../../middlewares/auth');
const { activityLogController } = require('../../controllers');

router.use(auth());

router.get('/', validate(activityLogValidation.getUserActivityLogs), activityLogController.getUserActivityLogs);

module.exports = router;
