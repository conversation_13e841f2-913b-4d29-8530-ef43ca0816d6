const express = require('express');

const router = express.Router();
const validate = require('../../middlewares/validate');
const { countryController } = require('../../controllers');
const { countryValidation } = require('../../validations');
// const { auth } = require('../../middlewares/auth');

// router.use(auth());

router.get('/', countryController.getCountries);
router.get('/cities', validate(countryValidation.getCities), countryController.getCities);
router.get('/states', validate(countryValidation.getStates), countryController.getStates);

module.exports = router;
