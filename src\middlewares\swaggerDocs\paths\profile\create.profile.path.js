module.exports = {
  post: {
    summary: 'Create a profile for a user',
    tags: ['Profile'],
    requestBody: {
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              basicInformation: {
                type: 'object',
                properties: {
                  personalStatement: { type: 'string', description: 'Personal statement for the profile' },
                },
              },
              skills: {
                type: 'array',
                items: { type: 'string' },
                description: 'List of skills',
                example: ['Skill 1', 'Skill 2'],
              },
              hobbies: {
                type: 'array',
                items: { type: 'string' },
                description: 'List of hobbies',
                example: ['Hobby 1', 'Hobby 2'],
              },
            },
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Profile created successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string', example: 'SUCCESS' },
                message: { type: 'string', example: 'Profile created successfully' },
              },
            },
          },
        },
      },
      400: {
        description: 'Bad Request',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string', example: 'BAD_REQUEST' },
                message: {
                  type: 'string',
                  example: 'User profile already exists',
                },
              },
            },
          },
        },
      },
      500: {
        description: 'Internal Server Error',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: { type: 'string', example: 'INTERNAL_SERVER_ERROR' },
                message: {
                  type: 'string',
                  example: 'Error creating profile: Internal server error message',
                },
              },
            },
          },
        },
      },
    },
  },
};
