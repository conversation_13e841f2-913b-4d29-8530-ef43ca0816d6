const deleteResourceInfoSwaggerPath = {
  delete: {
    tags: ['Profile'],
    summary: 'Delete Resource Info in Profile',
    description: 'Deletes resource information in a profile',
    parameters: [
      {
        name: 'id',
        in: 'path',
        description: 'ID of the resource to delete',
        required: true,
        type: 'string',
        format: 'objectId',
      },
      {
        name: 'resourceName',
        in: 'query',
        description: 'Name of the resource to delete (e.g., Education, Certification, Project, etc.)',
        required: true,
        schema: {
          type: 'string',
          enum: ['Education', 'Certification', 'Project', 'Award', 'Volunteering', 'Experience', 'TestScore'],
        },
      },
    ],
    responses: {
      200: {
        description: 'Success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: {
                  type: 'string',
                },
                message: {
                  type: 'string',
                },
              },
            },
          },
        },
      },
    },
  },
};

module.exports = deleteResourceInfoSwaggerPath;
