const mongoose = require('mongoose');
const { toJSON, paginate } = require('../plugins');

const educationSchema = new mongoose.Schema(
  {
    schoolName: { type: String, trim: true, required: true },
    degreeType: { type: String, trim: true, required: true },
    major: { type: String, trim: true },
    start: { type: Date, trim: true, required: true },
    end: { type: Date, trim: true },
    current: { type: Boolean },
  },
  { timestamps: true },
);

educationSchema.plugin(toJSON);
educationSchema.plugin(paginate);

const Education = mongoose.model('Education', educationSchema);
module.exports = Education;
