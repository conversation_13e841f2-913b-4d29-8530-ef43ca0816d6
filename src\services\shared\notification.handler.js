/* eslint-disable no-param-reassign */
const httpStatus = require('http-status');
const ApiError = require('../../utils/ApiError');
const { capitalizeFirstChar } = require('../../utils/stringFormatting');

const notificationTypes = Object.freeze({
  FOLLOW: 'follow',
  UNFOLLOW: 'unfollow',
  JOIN_GROUP_REQUEST: 'join-group-request',
  ACCEPT_GROUP_REQUEST: 'accept-group-request',
  REJECT_GROUP_REQUEST: 'reject-group-request',
  REACTION: 'reaction',
  COMMENT: 'comment',
  REPOST: 'repost',
  COMMENT_OF_COMMENT: 'comment-of-comment',
  NEW_ADMIN: 'new-admin',
  NEW_RANDOM_ADMIN: 'new-random-admin',
  NEW_CO_ADMIN: 'new-co-admin',
  MESSAGE: 'message',
  G<PERSON>UP_INVITE: 'group-invite',
  GROUP_MESSAGE: 'group-message',
  GROUP_POST: 'group-post',
  MENTION: 'mention',
  NEW_ORDER: 'new-order',
  SERVICE_PROVIDED: 'service-provided',

  // order
  NEW_ORDER_CLIENT: 'new-order-client',
  NEW_ORDER_PROVIDER: 'new-order-provider',
  ORDER_REQUIREMENTS_SUBMITTED: 'order-requirements-submitted',
  ORDER_COMPLETED: 'order-completed',
  ORDER_DELIVERED: 'order-delivered',
  ORDER_CANCELLED: 'order-cancelled',
  DISPUTE_REPORTED: 'dispute-reported',
  EXTENSION_REQUESTED: 'extension-requested',
  EXTENSION_APPROVED: 'extension-approved',
  EXTENSION_DENIED: 'extension-denied',

  // Service revision
  REVISION_REQUESTED: 'revision-requested',
  REVISION_FULFILLED: 'revision-fulfilled',
  REVISION_COMPLETED: 'revision-completed',
  REVISION_CANCELLED: 'revision-cancelled',

  CLIENT_APPROVAL: 'client-approval',
  SERVICE_REPORTED: 'service-reported',

  // notify users to complete profile
  COMPLETE_PROFILE: 'complete-profile',
});

// const getMemberText = async (member) => {
//   // eslint-disable-next-line global-require
//   const { User, Business } = require('../../models');
//   if (member.user) {
//     return `@U{${member.user._id}**${member.user.firstName} ${member.user.lastName}}`;
//   }
//   return `@B{${member.business._id}**${member.business.name}}`;
// };

const generateNotificationMessage = async (sender, type, details) => {
  // @sender: ObjectId --> This is who is making the request
  // @details: Object --> commentId, postId, groupId, resourceName: [Commment, Post], resourceId, secondRecipient for a nested comment
  // @P - post, @U - user @G - group, @C - comment, @M - message
  let message = '';
  const letter = details?.resourceName?.toLowerCase() === 'comment' ? 'C' : 'P';

  if (sender?.firstName) {
    sender.firstName = capitalizeFirstChar(sender.firstName || '');
    sender.lastName = capitalizeFirstChar(sender.lastName || '');
  }

  if (details?.secondRecipient) {
    details.secondRecipient.firstName = capitalizeFirstChar(details.secondRecipient.firstName || '');
    details.secondRecipient.lastName = capitalizeFirstChar(details.secondRecipient.lastName || '');
  }

  switch (type) {
    case notificationTypes.GROUP_MESSAGE:
      message = `@U{${sender?.user?._id || ''}**${sender?.user?.firstName} ${sender?.user?.lastName}} sent a message to @M{${
        details.conversation._id
      }}`;
      break;

    case notificationTypes.MESSAGE:
      message = `@U{${sender?.user?._id || ''}**${sender?.user?.firstName || 'Someone'} ${
        sender?.user?.lastName || ''
      }} sent you a @M{${details.conversation._id}}`;
      break;

    case notificationTypes.FOLLOW:
      message = `@U{${sender._id}**${sender.firstName} ${sender.lastName}} followed you`;
      break;

    case notificationTypes.COMMENT:
      message = `@U{${sender._id}**${sender.firstName} ${sender.lastName}} commented on your @P{${details.postId}} @C{${
        details.resourceId || details.commentId
      }}`;
      break;

    case notificationTypes.COMMENT_OF_COMMENT:
      message = `@U{${sender._id}**${sender.firstName} ${sender.lastName}} replied ${
        String(sender._id) === String(details?.secondRecipient._id)
          ? 'your'
          : `@U2{${details?.secondRecipient._id}**${details?.secondRecipient.firstName} ${details?.secondRecipient.lastName}}'s`
      } comment on your @C{${details?.commentId}} @P{${details?.postId}} @PC{${details?.parentCommentId}}`;
      break;

    case notificationTypes.LIKE:
      message = `@U{${String(sender._id)}**${sender.firstName} ${sender.lastName}} liked your ${
        details.resourceName.toLowerCase() === 'post' ? '' : details.resourceName
      } @${letter}{${details.resourceId}}`;
      break;

    case notificationTypes.REACTION:
      message = `@U{${String(sender._id)}**${sender.firstName} ${sender.lastName}} ${details.action} your ${
        // eslint-disable-next-line no-nested-ternary
        letter === 'C'
          ? details.parentCommentId
            ? `reply to a @C{${details.resourceId}} @P{${details.postId}} @PC{${details.parentCommentId}}`
            : `${details.resourceName.toLowerCase()} on a @P{${details.postId}} @C{${details.resourceId}}`
          : `@P{${details.postId || details.resourceId}}`
      } `;
      break;

    case notificationTypes.REPOST:
      message = `@U{${String(sender._id)}**${sender.firstName} ${sender.lastName}} reposted your ${
        details.resourceName.toLowerCase() === 'post' ? '' : details.resourceName
      } @${letter}{${details.resourceId}}`;
      break;

    case notificationTypes.NEW_ADMIN:
      message = `You are the new admin of @G{${details.groupId}}`;
      break;

    case notificationTypes.NEW_RANDOM_ADMIN:
      message = `@U{${String(sender._id)}**${sender.firstName} ${sender.lastName}} has left @G{${
        details.groupId
      }} and you are now the admin.`;
      break;

    case notificationTypes.NEW_CO_ADMIN:
      message = `@U{${String(sender._id)}**${sender.firstName} ${sender.lastName}} made you a co-admin of @G{${
        details.groupId
      }}`;
      break;

    case notificationTypes.JOIN_GROUP_REQUEST:
      message = `@U{${String(sender._id)}**${sender.firstName} ${sender.lastName}} requested to join @G{${details.groupId}}`;
      break;

    case notificationTypes.ACCEPT_GROUP_REQUEST:
      message = `Your request to join @G{${details.groupId}} has been accepted`;
      break;

    case notificationTypes.REJECT_GROUP_REQUEST:
      message = `Your request to join @G{${details.groupId}} was rejected`;
      break;

    case notificationTypes.GROUP_POST:
      message = `New group post @P{${details.groupPostId}} by @U{${String(sender._id)}**${sender.firstName} ${
        sender.lastName
      }} in group @G{${details.groupId}}`;
      break;

    case notificationTypes.GROUP_INVITE:
      message = `@U{${String(sender._id)}**${sender.firstName} has invited you to join a group. Click <a href="${
        details.inviteUrl
      }">here</a> to join.`;
      break;

    case notificationTypes.MENTION:
      message = `@U{${String(sender._id)}**${sender.firstName} ${
        sender.lastName
      }} mentioned you in a ${details.resourceName.toLowerCase()} @${letter}{${details.resourceId}}`;
      break;

    // orders
    case notificationTypes.NEW_ORDER_PROVIDER:
      message = `A new order: @OP{${details.orderId}} has been placed by @U{${String(sender._id)}**${
        sender.firstName
      }}. Please review.`;
      break;

    case notificationTypes.NEW_ORDER_CLIENT:
      message = `You have placed a new order: @OC{${details.orderId}} `;
      break;

    case notificationTypes.ORDER_DELIVERED:
      message = `Your order: @OC{${details.orderId}} has been delivered.`;
      break;

    case notificationTypes.ORDER_REQUIREMENTS_SUBMITTED:
      message = `The requirements for the order: @OP{${details.orderId}} has been submitted by the client.`;
      break;

    case notificationTypes.ORDER_COMPLETED:
      message = `The order: @OC{${details.orderId}} has been completed by @U{${String(sender._id)}**${sender.firstName}}.`;
      break;

    case notificationTypes.ORDER_CANCELLED:
      message = `The order: @OC{${details.orderId}} has been cancelled by @U{${String(sender._id)}**${sender.firstName}}.`;
      break;

    case notificationTypes.DISPUTE_REPORTED:
      message = `A dispute has been reported on the order: @OP{${details.orderId}} by @U{${String(sender._id)}**${
        sender.firstName
      }}.`;
      break;

    case notificationTypes.EXTENSION_REQUESTED:
      message = `An extension has been requested for the order: @OC{${details.orderId}} by @U{${sender.contactPerson._id}**${sender.contactPerson.firstName}}.`;
      break;

    case notificationTypes.EXTENSION_APPROVED:
      message = `The extension request for the order: @OP{${details.orderId}} has been approved.`;
      break;

    case notificationTypes.EXTENSION_DENIED:
      message = `The extension request for the order: @OP{${details.orderId}} has been denied.`;
      break;

    case notificationTypes.SERVICE_PROVIDED:
      message = `Your service has been completed by @U{${sender.contactPerson._id}**${sender.contactPerson.firstName}}. Please review and approve.`;
      break;

    case notificationTypes.REVISION_REQUESTED:
      message = `A revision has been requested by @U{${String(sender._id)}**${
        sender.firstName
      }}. Please review the order: @OP{${details.orderId}}.`;
      break;

    case notificationTypes.REVISION_COMPLETED:
      message = `The fulfilled revision for the order (Order ID: $OP{details.orderId}) has been completed by the client.`;
      message = `The fulfilled revision for the order: @OP{${details.orderId}} has been completed by the client.`;
      break;

    case notificationTypes.REVISION_FULFILLED:
      message = `Your revision request for the order: @OP{${details.orderId}} has been fulfilled.`;
      break;

    case notificationTypes.REVISION_CANCELLED:
      message = `The revision request for the order: @OP{${details.orderId}} has been cancelled by the client.`;
      break;

    case notificationTypes.CLIENT_APPROVAL:
      message = `The service has been approved by @U{${String(sender._id)}**${sender.firstName}}.`;
      break;

    case notificationTypes.SERVICE_REPORTED:
      message = `A report has been filed regarding the service by @U{${String(sender._id)}**${
        sender.firstName
      }}. Please review the issue.`;
      break;

    case notificationTypes.COMPLETE_PROFILE:
      message = `Please complete your profile to access all features.`;
      break;

    default:
      throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid notification type');
  }

  return message;
};

module.exports = { generateNotificationMessage, notificationTypes };
