const { themeTypes } = require('../../../../config/constants');

module.exports = {
  get: {
    summary: 'Get setting by ID',
    description: 'Retrieve a setting based on its ID',
    tags: ['Settings'],
    responses: {
      200: {
        description: 'Successful response',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Setting retrieved successfully',
              data: {
                user: '651bb5bf4eb9240327eaaaa',
                theme: themeTypes.LIGHT,
                dateOfBirth: 'private',
                skills: 'public',
                hobbies: 'public',
                email: 'private',
                nationality: 'universities',
                phone: 'universities',
                countryOfResidence: 'public',
                targetedCountries: 'universities',
                preferredFieldOfStudy: 'public',
                preferredSchoolOfStudy: 'universities',
                preferredCountryOfStudy: 'universities',
                preferredDegreeOfStudy: 'universities',
                interestedInScholarship: 'universities',
                basicInformation: 'public',
                education: 'public',
                certification: 'public',
                testscore: 'public',
                experience: 'public',
                volunteer: 'universities',
                project: 'public',
                award: 'public',
                acceptGroupRequest: true,
                comment: true,
                commentOfComment: true,
                follow: true,
                groupMessage: true,
                joinGroupRequest: true,
                like: true,
                message: true,
                newAdmin: true,
                newCoAdmin: true,
                newRandomAdmin: true,
                rejectGroupRequest: true,
                repost: true,
                acceptGroupRequestNotification: true,
                commentNotification: true,
                commentOfCommentNotification: true,
                followNotification: 'true',
                groupMessageNotification: true,
                joinGroupRequestNotification: true,
                likeNotification: true,
                messageNotification: true,
                newAdminNotification: true,
                newCoAdminNotification: true,
                newRandomAdminNotification: true,
                rejectGroupRequestNotification: true,
                repostNotification: true,
                volunteering: 'public',
                preferredFieldsOfStudy: 'universities',
                preferredSchoolsOfStudy: 'universities',
                id: '6573218e67a934b9bd88qqqq',
              },
            },
          },
        },
      },
      500: {
        description: 'Internal server error',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'string' },
              },
            },
          },
        },
      },
    },
  },
};
