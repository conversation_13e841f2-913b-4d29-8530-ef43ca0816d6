const rateLimit = require('express-rate-limit');

// Custom keyGenerator to handle IP with port
const customKeyGenerator = (req) => {
  const forwardedFor = req.headers['x-forwarded-for'];
  const ip = forwardedFor ? forwardedFor.split(',')[0].trim() : req.ip;

  // If the IP contains a port, split it and return only the IP part
  const cleanIp = ip.includes(':') ? ip.split(':')[0] : ip;
  return cleanIp;
};

const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 20,
  keyGenerator: customKeyGenerator,
  skipSuccessfulRequests: true,
  trustProxy: true,
  handler: (req, res) => {
    res.status(429).json({ message: 'Too many requests from this IP, please try again later.' });
  },
});

module.exports = {
  authLimiter,
  customKeyGenerator,
};
