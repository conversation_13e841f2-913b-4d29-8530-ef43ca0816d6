const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');

const languageSchema = new mongoose.Schema({
  name: { type: String, required: true, trim: true, unique: true },
  code: { type: String, required: true, trim: true, unique: true },
  native: { type: String, required: true, trim: true, unique: true },
});

languageSchema.plugin(toJSON);
languageSchema.plugin(paginate);

languageSchema.index({ name: 1 });

const Language = mongoose.model('Language', languageSchema);

module.exports = Language;
