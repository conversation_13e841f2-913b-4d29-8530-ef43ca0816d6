const Async = require('async');
const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const Scholarship = require('../scholarship.model');

const migrationName = 'Rename scholarshipTypes to fundingTypes 3';

const renameScholarshipTypesToFundingTypes = async () => {
  const migrated = await GlobalVariable.findOne({ name: migrationName });
  if (migrated) return;

  const scholarships = await Scholarship.find({});

  await Async.eachSeries(scholarships, async (scholarship) => {
    if (!scholarship.fundingType) return;
    await Scholarship.updateOne(
      { _id: scholarship._id },
      {
        $set: {
          fundingType: scholarship.fundingType.replace(' Funding', ' Scholarship'),
        },
        $unset: {
          scholarshipType: '',
        },
      },
    );
  });

  await GlobalVariable.create({ name: migrationName, value: 'true' });
  logger.info('Email in profile settings marked as public');
};

module.exports = renameScholarshipTypesToFundingTypes;
