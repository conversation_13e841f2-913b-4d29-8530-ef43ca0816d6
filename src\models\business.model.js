const mongoose = require('mongoose');
const crypto = require('crypto');
const { toJSON, paginate } = require('./plugins');
const { businessEnums } = require('../config/constants');

const { ObjectId } = mongoose.SchemaTypes;

const businessSchema = new mongoose.Schema(
  {
    username: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      lowercase: true,
    },
    profile: { type: ObjectId, ref: 'Profile' },
    displayName: { type: String, required: true, trim: true, unique: true },
    coverPhoto: { type: ObjectId, ref: 'File', trim: true },
    profilePhoto: { type: ObjectId, ref: 'File', trim: true },
    about: { type: String, required: true },
    tagLine: { type: String },
    languages: [
      {
        id: { type: ObjectId, ref: 'Language', trim: true, required: true },
        proficiency: { type: String, required: true, trim: true, enum: Object.values(businessEnums.languageLevels) },
      },
    ],
    // address: { type: String, required: true, trim: true },
    city: { type: ObjectId, ref: 'City', trim: true },
    state: { type: ObjectId, ref: 'State', trim: true },
    country: { type: ObjectId, ref: 'Country', trim: true },
    contactPerson: { type: ObjectId, required: true, ref: 'User', trim: true },
    account: { type: ObjectId, ref: 'Account' },
    onboardingStage: {
      type: String,
      trim: true,
      enum: Object.values(businessEnums.onboardingStages),
      required: true,
      default: businessEnums.onboardingStages.BUSINESS_INFO,
    },
    orders: [{ type: ObjectId, ref: 'Order' }],
    completedOnboardingStages: [{ type: String, trim: true, enum: Object.values(businessEnums.onboardingStages) }],
    services: [{ type: ObjectId, ref: 'Service' }],
    stripeAccountId: { type: String, trim: true },
    stripeIsLinked: { type: Boolean, default: false },
    stripeIndividual: {
      first_name: { type: String, trim: true },
      middle_name: { type: String, trim: true },
      last_name: { type: String, trim: true },
    },
    stripeBusiness: {
      name: { type: String, trim: true },
    },
    referralCode: { type: String, trim: true },
    referrals: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Referral' }], // users referred by this business
    //
    verificationStatus: {
      type: String,
      trim: true,
      enum: Object.values(businessEnums.businessVerificationStatuses),
    },
    additionalInformation: [
      {
        type: { type: String, enum: Object.values(businessEnums.counselorVerificationDocumentTypes) },
        portfolioLink: { type: String },
        description: { type: String },
      },
    ],
    verifications: [{ type: ObjectId, ref: 'BusinessVerification' }],
    online: { type: Boolean, default: false },
  },
  {
    timestamps: true,
  },
);

businessSchema.index({ name: 1, createdAt: 1, updatedAt: 1 });

businessSchema.plugin(toJSON);
businessSchema.plugin(paginate);

businessSchema.statics.generateUsername = async function (name) {
  let username = name
    .replaceAll(/[^a-zA-Z\s]+/g, '')
    .replaceAll(' ', '-')
    .toLowerCase();
  let business = await this.findOne({ username });

  while (business) {
    const randomNumber = crypto.randomInt(0, 5999);
    username = `${username}${randomNumber}`;

    // eslint-disable-next-line no-await-in-loop
    business = await this.findOne({ username });
  }

  return username;
};

const Business = mongoose.model('Business', businessSchema);

module.exports = Business;
