const logger = require('../../config/logger');
const GlobalVariable = require('../global.variable.model');
const { visibilityTypes } = require('../../config/constants');
const Setting = require('../setting.model');

const migrationName = 'Make email private in profile';

const setEmailInProfileAsPublic = async () => {
  const migrated = await GlobalVariable.findOne({ name: migrationName });
  if (migrated) return;

  await Setting.updateMany(
    {},
    {
      $set: {
        email: visibilityTypes.PRIVATE,
      },
    },
  );

  await GlobalVariable.create({ name: migrationName, value: 'true' });
  logger.info('Email in profile settings marked as private');
};

module.exports = setEmailInProfileAsPublic;
