const mongoose = require('mongoose');
const app = require('./app');
const config = require('./config/config');
const logger = require('./config/logger');

let server;
mongoose.connect(config.mongoose.url, config.mongoose.options).then(() => {
  logger.info('Connected to MongoDB');
  server = app.listen(config.port, () => {
    logger.info(`Listening to port ${config.port}`);
  });
});
mongoose.set('runValidators', true);
mongoose.set('strictQuery', true);

const exitHandler = () => {
  if (server) {
    server.close(() => {
      logger.info('Server closed');
      process.exit(1);
    });
  } else {
    process.exit(1);
  }
};

// eslint-disable-next-line import/order
const { exec } = require('child_process');

exec('ffmpeg -version', (error, stdout) => {
  if (error) {
    // eslint-disable-next-line no-console
    console.error(`❌ Error checking FFmpeg: ${error.message}`);
    return;
  }
  // eslint-disable-next-line no-console
  console.log(`✅ FFmpeg Version:\n${stdout}`);
});

const unexpectedErrorHandler = (error) => {
  logger.error(error);
  exitHandler();
};

process.on('uncaughtException', unexpectedErrorHandler);
process.on('unhandledRejection', unexpectedErrorHandler);

process.on('SIGTERM', () => {
  logger.info('SIGTERM received');
  if (server) {
    server.close();
  }
});
