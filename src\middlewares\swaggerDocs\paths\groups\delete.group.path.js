module.exports = {
  delete: {
    summary: 'Delete a group',
    tags: ['Groups'],
    parameters: [
      {
        in: 'path',
        name: 'id',
        description: 'ID of the group to delete',
        required: true,
        schema: {
          type: 'string',
          example: '507f1f77bcf86cd799439011',
        },
      },
    ],
    responses: {
      200: {
        description: 'Group deleted successfully',
        content: {
          'application/json': {
            example: {
              status: 'SUCCESS',
              message: 'Group deleted successfully',
            },
          },
        },
      },
      404: {
        description: 'Group not found',
        content: {
          'application/json': {
            example: {
              status: 'ERROR',
              message: 'Group record not found',
            },
          },
        },
      },
    },
  },
};
