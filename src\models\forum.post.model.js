const mongoose = require('mongoose');
const { toJSON, paginate } = require('./plugins');

const forumPostSchema = new mongoose.Schema(
  {
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    topic: { type: String, trim: true, required: true },
    text: { type: String, trim: true, required: true },
    media: [{ type: mongoose.Schema.Types.ObjectId, ref: 'File' }],
    replies: [{ type: mongoose.Schema.Types.ObjectId, ref: 'ForumReply' }],
    solved: { type: Boolean, default: false },
    category: {
      type: String,
      enum: [
        'All',
        'General Discussion',
        'Admissions',
        'Test Scores',
        'SAT',
        'ACT',
        'College Applications',
        'Scholarships',
        'Financial Aid',
        'Study Tips',
        'Student Life',
        'Internships',
        'Career Advice',
        'Graduate School',
      ],
    },
    tags: [{ type: String, trim: true }],
  },
  { timestamps: true },
);

forumPostSchema.index({ createdAt: 1, updatedAt: 1 });

forumPostSchema.plugin(toJSON);
forumPostSchema.plugin(paginate);

forumPostSchema.pre('remove', async function (next) {
  try {
    await mongoose.model('ForumReply').deleteMany({ _id: { $in: this.replies } });
    next();
  } catch (error) {
    next(error);
  }
});

const ForumPost = mongoose.model('ForumPost', forumPostSchema);
module.exports = ForumPost;
